package com.wanshifu.demo;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

public class OrderTimeCalculatorTest {

    /**
     * 计算从 start 到 end 的总小时数，扣除每天 23:00-08:00 的夜间小时数
     */
    public static long calculateEffectiveHours(LocalDateTime start, LocalDateTime end) {
        if (end.isBefore(start)) {
            throw new IllegalArgumentException("结束时间必须在开始时间之后");
        }

        // 1. 总小时
        long totalHours = Duration.between(start, end).toHours();

        // 2. 累加所有夜间时段重叠小时数
        long blockedHours = 0;
        // 从 start 日期到 end 日期（含）逐天检查
        LocalDate date = start.toLocalDate();
        LocalDate lastDate = end.toLocalDate();
        while (!date.isAfter(lastDate)) {
            // 当天夜间段： [date 23:00, date+1 08:00)
            LocalDateTime blockStart = LocalDateTime.of(date, LocalTime.of(23, 0));
            LocalDateTime blockEnd   = LocalDateTime.of(date.plusDays(1), LocalTime.of(8, 0));

            // 计算 block 与 [start,end] 的重叠
            LocalDateTime overlapStart = max(start, blockStart);
            LocalDateTime overlapEnd   = min(end,   blockEnd);
            if (overlapEnd.isAfter(overlapStart)) {
                blockedHours += Duration.between(overlapStart, overlapEnd).toHours();
            }

            date = date.plusDays(1);
        }

        return totalHours - blockedHours;
    }

    private static LocalDateTime max(LocalDateTime a, LocalDateTime b) {
        return a.isAfter(b) ? a : b;
    }

    private static LocalDateTime min(LocalDateTime a, LocalDateTime b) {
        return a.isBefore(b) ? a : b;
    }

    // 测试
    public static void main(String[] args) {
        LocalDateTime orderTime = LocalDateTime.of(2025, 7, 7, 22, 0);
        LocalDateTime now       = LocalDateTime.of(2025, 7, 8, 9, 0);
        long hours = calculateEffectiveHours(orderTime, now);
        System.out.println("有效消费小时数：" + hours);
        // 解释：总时差 14h15m ≈ 14h，扣除 23:00-08:00 中的 2h30m → 2h，结果约 12h
    }
}
