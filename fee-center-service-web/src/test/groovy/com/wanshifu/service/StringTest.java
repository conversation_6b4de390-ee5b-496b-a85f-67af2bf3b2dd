package com.wanshifu.service;

import java.math.BigDecimal;

public class StringTest {

    public static void main(String[] args) {
//        String s = "masterInputPrice * AP45005071";
//        String replace = s.replace("AP45005071", "AP41140940");
//        System.out.println(replace);
//        int i = 10;
//        String s = i + 1 + "";
//        System.out.println(s);
//        BigDecimal cost = new BigDecimal("0.00");
//        boolean b = BigDecimal.ZERO.compareTo(cost) <= 0;
//        System.out.println(b);

//        try {
//            String s = null;
//            s.length();
//        } catch (Exception e) {
//            StackTraceElement[] stackTrace = e.getStackTrace();
//            for (StackTraceElement stackTraceElement : stackTrace) {
//                System.out.println(stackTraceElement.getClassName() + ":" + stackTraceElement.getMethodName() + ":" + stackTraceElement.getLineNumber());
//            }
//        }

//        String result = String.format("%s_%s", "hello", null);
//        System.out.println(result);


    }
}
