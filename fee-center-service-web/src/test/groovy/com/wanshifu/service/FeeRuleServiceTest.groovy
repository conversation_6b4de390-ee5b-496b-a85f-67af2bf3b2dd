package com.wanshifu.service

import com.wanshifu.FeeCenterServiceAppTest

import com.wanshifu.fee.center.domain.document.CalculateRuleData
import com.wanshifu.fee.center.domain.document.FeeRule
import com.wanshifu.fee.center.domain.request.feeRule.ServiceIdByDivisionIdsReq
import com.wanshifu.repository.FeeTemplateRepository

import javax.annotation.Resource


class FeeRuleServiceTest extends FeeCenterServiceAppTest {

    @Resource
    FeeRuleService feeRuleService
    @Resource
    FeeTemplateService feeTemplateService

    @Resource
    FeeTemplateRepository feeTemplateRepository


    def testSave() {
        given:
        def feeRule = new FeeRule()
        feeRule.feeRuleId = 1
        feeRule.bizRule = [masterId: '1']
        feeRule.calculateRuleData = new CalculateRuleData()
        feeRule.createTime = new Date()
        feeRule.modifyTime = new Date()
        feeRuleService.save(feeRule)

        expect:
        printf('content= %s', feeRule.id)
    }

    // 请生成Set<Long> getServiceIdsByDivision(ServiceIdByDivisionIdsReq req);的单元测试
    def testGetServiceIdsByDivision() {
        given:
        def req = new ServiceIdByDivisionIdsReq()
        req.divisionIdSet = ['1', '440306003']
        req.divisionCode = 'street'

        when:
        def result = feeRuleService.getServiceIdsByDivision(req)

        then:
        printf('content= %s', result)
    }
}
