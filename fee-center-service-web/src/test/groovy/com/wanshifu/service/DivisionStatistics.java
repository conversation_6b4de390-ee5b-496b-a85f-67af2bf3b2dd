package com.wanshifu.service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class DivisionStatistics {

    public static void main(String[] args) {
        List<Division> divisions = Arrays.asList(
                new Division("1", "2", "3"),
                new Division("1", "2", "4"),
                new Division("1", "3", "5"),
                new Division("2", "2", "6"),
                new Division("2", "3", "7"),
                new Division("2", "3", "8")
        );

        // Group by level1DivisionId, then level2DivisionId, and count level3DivisionId occurrences
        Map<String, Map<String, Long>> groupedResult = divisions.stream()
                .collect(Collectors.groupingBy(Division::getLevel1DivisionId,
                        Collectors.groupingBy(Division::getLevel2DivisionId, Collectors.counting())));

        // Print the result
        groupedResult.forEach((level1, level2Map) -> {
            System.out.println("Level1DivisionId: " + level1);
            level2Map.forEach((level2, count) -> {
                System.out.println("  Level2DivisionId: " + level2 + ", Count: " + count);
            });
        });
    }
}

class Division {
    String level1DivisionId;
    String level2DivisionId;
    String level3DivisionId;

    // Constructor, getters, and setters
    public Division(String level1DivisionId, String level2DivisionId, String level3DivisionId) {
        this.level1DivisionId = level1DivisionId;
        this.level2DivisionId = level2DivisionId;
        this.level3DivisionId = level3DivisionId;
    }

    public String getLevel1DivisionId() {
        return level1DivisionId;
    }

    public String getLevel2DivisionId() {
        return level2DivisionId;
    }

    public String getLevel3DivisionId() {
        return level3DivisionId;
    }

    @Override
    public String toString() {
        return "Division{" +
                "level1DivisionId='" + level1DivisionId + '\'' +
                ", level2DivisionId='" + level2DivisionId + '\'' +
                ", level3DivisionId='" + level3DivisionId + '\'' +
                '}';
    }
}
