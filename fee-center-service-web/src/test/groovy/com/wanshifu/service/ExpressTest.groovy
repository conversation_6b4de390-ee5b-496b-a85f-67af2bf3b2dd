package com.wanshifu.service

import com.ql.util.express.DefaultContext
import com.ql.util.express.ExpressRunner
import com.wanshifu.fee.center.domain.dto.ExpressResultInfo

class ExpressTest {
    static void main(String[] args) {

        DefaultContext<String,Object>  params = new DefaultContext<>()
        params.put("ALL_SERVICE_AP_SKU_NUMBER", new BigDecimal(1.00))
        params.put("AP45005075",1)

        params.put("STR", new String("年hi啊大的框架不卡就"))

        def obj = new ExpressRunner().execute("STR == '年Skjandj'",params,null,true,false)
        println obj

        String express = "import java.math.BigDecimal; import com.wanshifu.fee.center.domain.dto.ExpressResultInfo; if (ALL_SERVICE_AP_SKU_NUMBER > 3) { BigDecimal price = new BigDecimal(60); BigDecimal num = AP45005075 ; BigDecimal cost = price * num ; ExpressResultInfo returnResult = new ExpressResultInfo(); returnResult.setNumber(num); returnResult.setPrice(price); returnResult.setCost(cost); return returnResult; } if ( (ALL_SERVICE_AP_SKU_NUMBER <= 3) && (ALL_SERVICE_AP_SKU_NUMBER >= 1) ) { BigDecimal price = new BigDecimal(61); BigDecimal num = AP45005075; BigDecimal cost = price * num ; ExpressResultInfo returnResult = new ExpressResultInfo(); returnResult.setNumber(num); returnResult.setPrice(price); returnResult.setCost(cost); return returnResult; } ";
        ExpressResultInfo object = (ExpressResultInfo) new ExpressRunner().execute(express,params,null,true,false)
        println(object)
    }
}
