package com.wanshifu.service;

import java.util.ArrayList;
import java.util.LinkedList;

/**
 * @author: <PERSON>
 * @create: 2024-06-04 14:48
 * @description: 内存占用测试
 */
public class ListMemoryUsageTest {

    public static void main(String[] args) {
        int numberOfElements = 1000000;

        // ArrayList
        ArrayList<Integer> arrayList = new ArrayList<>(numberOfElements);
        for (int i = 0; i < numberOfElements; i++) {
            arrayList.add(i);
        }

        // LinkedList
        LinkedList<Integer> linkedList = new LinkedList<>();
        for (int i = 0; i < numberOfElements; i++) {
            linkedList.add(i);
        }

        // Output memory usage
        Runtime runtime = Runtime.getRuntime();
        runtime.gc(); // Request garbage collection
        long usedMemoryArrayList = runtime.totalMemory() - runtime.freeMemory();

        // Repeat for LinkedList
        runtime.gc(); // Request garbage collection
        long usedMemoryLinkedList = runtime.totalMemory() - runtime.freeMemory();

        System.out.println("Used memory by ArrayList: " + usedMemoryArrayList + " bytes");
        System.out.println("Used memory by LinkedList: " + usedMemoryLinkedList + " bytes");
    }
}
