package com.wanshifu.service;

import org.springframework.data.mongodb.core.query.Criteria;

/**
 * @author: <PERSON>
 * @create: 2024-04-03 11:28
 * @description: MongoDB的criteria测试
 */
public class MongoCriteriaTest {

    public static void main(String[] args) {
        Criteria criteria = Criteria.where("bizId").is("123456");
        criteria.and("bizTag").is("1");

        Criteria criteria2 = Criteria.where("bizId").is("56789");
        criteria2.and("bizTag").is("2");

        Criteria orOperator = criteria.orOperator(criteria2);
        System.out.println(orOperator.getCriteriaObject());
    }
}
