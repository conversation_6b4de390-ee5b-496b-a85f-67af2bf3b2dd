package com.wanshifu.service;

import com.aliyun.openservices.shade.org.apache.commons.lang3.RandomUtils;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * @author: <PERSON>
 * @create: 2024-01-17 17:30
 * @description: List test
 */
public class ListTest {

    public static void main(String[] args) {
        List<String> list = Arrays.asList("商品属性", "商品尺寸");

//        System.out.println("商品属性是什么".contains("商品属性"));

//        String skuName = "商品属";
//
//        List<String> keyWords = Arrays.asList("商品属性", "商品尺寸", "商品规格", "属性");
//        boolean matched = keyWords.stream().anyMatch(skuName::contains);
//        System.out.println(matched);

//        String part = "晃动(重新拆+安装（不含配件）)";
//        String part = "维修类型(漏水)";
//        String part = "晃动>>(重新拆+安装（不含配件）)";
//        StringBuilder result = new StringBuilder();


//        String[] subParts = part.trim().split("\\s*>>\\s*");
//        if (subParts.length == 2) {
//            result.append(subParts[1].trim()).append(" | ");
//        } else {
//            // 有的的分割是()，
//            String[] subParts2 = part.trim().split("\\(");
//            if (subParts2.length == 2) {
//                result.append(subParts2[1].replace(")", "").trim()).append(" | ");
//            } else {
//                // 有的的分割是（），
//                String[] subParts3 = part.trim().split("（");
//                if (subParts3.length == 2) {
//                    result.append(subParts3[1].replace("）", "").trim()).append(" | ");
//                } else {
//
//                    System.out.println("数据格式有误");
//                }
//            }
//        }
//        // 删除最后一个多余的" | "，包括空格是3个字符
//        if (result.length() > 0) {
//            result.delete(result.length() - 3, result.length());
//        }
//
//        System.out.println(result.toString());
//        List<String> strList = new ArrayList<>();
//        strList.forEach(e -> System.out.println(e));

//        int i = (int) (60 * 60 * RandomUtils.nextFloat(2F, 3F));
//        System.out.println(i);
//        A a = new A();
//        B b = new B();
//        a.setBigDecimal(new BigDecimal("2.34"));
//        BeanUtils.copyProperties(a, b);
//        System.out.println(b);

        List<String> list1 = Arrays.asList("1", "2", "3");
        list1.stream().filter(e -> e.equals("1")).forEach(list1::remove);
        System.out.println(list1);
    }
}


@Data
class A {
    private BigDecimal bigDecimal;
}

@Data
class B {
    private Double bigDecimal;
}