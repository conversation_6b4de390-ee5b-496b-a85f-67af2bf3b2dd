package com.wanshifu.service;

import com.wanshifu.fee.center.domain.document.FeeTemplate;
import com.wanshifu.fee.center.domain.request.template.FeeTemplatePageListRequest;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collections;
import java.util.List;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.junit.Assert.assertEquals;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.when;

/**
 * @author: <PERSON>
 * @create: 2024-02-29 17:48
 * @description: 计价模板单元测试
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class FeeTemplateService3Test {

    @InjectMocks
    private FeeTemplateServiceImpl feeTemplateService;

    @Mock
    private MongoTemplate mongoTemplate;

    @Mock
    private FeeTemplatePageListRequest request;

    @BeforeEach
    public void setUp() {
        // 在每个测试之前进行任何必要的设置
    }

    @Test
    public void getPageList_NoFilters_ReturnsEmptyPage() {
        when(request.getSceneCode()).thenReturn("sceneCode");
//        when(request.getServiceCategoryId()).thenReturn(null);
//        when(request.getServiceId()).thenReturn(null);
        when(request.pageNum).thenReturn(1);
        when(request.pageSize).thenReturn(10);

        Criteria criteria = new Criteria();
        Query query = new Query(criteria);
        when(mongoTemplate.count(query, FeeTemplate.class)).thenReturn(0L);

        Page<FeeTemplate> result = feeTemplateService.getPageList(request);

        assertEquals(0, result.getTotalElements());
    }

    @Test
    public void getPageList_WithServiceCategoryId_ReturnsFilteredPage() {
        when(request.getSceneCode()).thenReturn("sceneCode");
//        when(request.getServiceCategoryId()).thenReturn("serviceCategoryId");
//        when(request.getServiceId()).thenReturn(null);
        when(request.pageNum).thenReturn(1);
        when(request.pageSize).thenReturn(10);

        Criteria criteria = new Criteria();
        Query query = new Query(criteria);
        when(mongoTemplate.count(query, FeeTemplate.class)).thenReturn(1L);

        List<FeeTemplate> feeTemplates = Collections.singletonList(new FeeTemplate());
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(feeTemplates);

        Page<FeeTemplate> result = feeTemplateService.getPageList(request);

        assertEquals(1, result.getTotalElements());
    }

    @Test
    public void getPageList_WithServiceId_ReturnsFilteredPage() {
        when(request.getSceneCode()).thenReturn("sceneCode");
//        when(request.getServiceCategoryId()).thenReturn(null);
//        when(request.getServiceId()).thenReturn("serviceId");
        when(request.pageNum).thenReturn(1);
        when(request.pageSize).thenReturn(10);

        Criteria criteria = new Criteria();
        Query query = new Query(criteria);
        when(mongoTemplate.count(query, FeeTemplate.class)).thenReturn(1L);

        List<FeeTemplate> feeTemplates = Collections.singletonList(new FeeTemplate());
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(feeTemplates);

        Page<FeeTemplate> result = feeTemplateService.getPageList(request);

        assertEquals(1, result.getTotalElements());
    }

    @Test
    public void getPageList_WithBothFilters_ReturnsFilteredPage() {
        when(request.getSceneCode()).thenReturn("sceneCode");
//        when(request.getServiceCategoryId()).thenReturn("serviceCategoryId");
//        when(request.getServiceId()).thenReturn("serviceId");
        when(request.pageNum).thenReturn(1);
        when(request.pageSize).thenReturn(10);

        Criteria criteria = new Criteria();
        Query query = new Query(criteria);
        when(mongoTemplate.count(query, FeeTemplate.class)).thenReturn(1L);

        List<FeeTemplate> feeTemplates = Collections.singletonList(new FeeTemplate());
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(feeTemplates);

        Page<FeeTemplate> result = feeTemplateService.getPageList(request);

        assertEquals(1, result.getTotalElements());
    }

    @Test
    public void getPageList_NoFilters_ReturnsEmptyPage2() {
        when(request.getSceneCode()).thenReturn("sceneCode");
//        when(request.getServiceCategoryId()).thenReturn(null);
//        when(request.getServiceId()).thenReturn(null);
        when(request.pageNum).thenReturn(1);
        when(request.pageSize).thenReturn(10);

        Criteria criteria = new Criteria();
        Query query = new Query(criteria);
        when(mongoTemplate.count(query, FeeTemplate.class)).thenReturn(0L);

        Page<FeeTemplate> result = feeTemplateService.getPageList(request);

        assertEquals(0, result.getTotalElements());
    }

    @Test
    public void getPageList_WithServiceCategoryId_ReturnsFilteredPage2() {
        when(request.getSceneCode()).thenReturn("sceneCode");
//        when(request.getServiceCategoryId()).thenReturn("serviceCategoryId");
//        when(request.getServiceId()).thenReturn(null);
        when(request.pageNum).thenReturn(1);
        when(request.pageSize).thenReturn(10);

        Criteria criteria = new Criteria();
        Query query = new Query(criteria);
        when(mongoTemplate.count(query, FeeTemplate.class)).thenReturn(1L);

        List<FeeTemplate> feeTemplates = Collections.singletonList(new FeeTemplate());
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(feeTemplates);

        Page<FeeTemplate> result = feeTemplateService.getPageList(request);

        assertEquals(1, result.getTotalElements());
    }

    @Test
    public void getPageList_WithServiceId_ReturnsFilteredPage2() {
        when(request.getSceneCode()).thenReturn("sceneCode");
//        when(request.getServiceCategoryId()).thenReturn(null);
//        when(request.getServiceId()).thenReturn("serviceId");
        when(request.pageNum).thenReturn(1);
        when(request.pageSize).thenReturn(10);

        Criteria criteria = new Criteria();
        Query query = new Query(criteria);
        when(mongoTemplate.count(query, FeeTemplate.class)).thenReturn(1L);

        List<FeeTemplate> feeTemplates = Collections.singletonList(new FeeTemplate());
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(feeTemplates);

        Page<FeeTemplate> result = feeTemplateService.getPageList(request);

        assertEquals(1, result.getTotalElements());
    }

    @Test
    public void getPageList_WithBothFilters_ReturnsFilteredPage2() {
        when(request.getSceneCode()).thenReturn("sceneCode");
//        when(request.getServiceCategoryId()).thenReturn("serviceCategoryId");
//        when(request.getServiceId()).thenReturn("serviceId");
        when(request.pageNum).thenReturn(1);
        when(request.pageSize).thenReturn(10);

        Criteria criteria = new Criteria();
        Query query = new Query(criteria);
        when(mongoTemplate.count(query, FeeTemplate.class)).thenReturn(1L);

        List<FeeTemplate> feeTemplates = Collections.singletonList(new FeeTemplate());
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(feeTemplates);

        Page<FeeTemplate> result = feeTemplateService.getPageList(request);

        assertEquals(1, result.getTotalElements());
    }

    @Test
    public void getPageList_WithPagination_ReturnsPagedResults() {
        when(request.getSceneCode()).thenReturn("sceneCode");
//        when(request.getServiceCategoryId()).thenReturn(null);
//        when(request.getServiceId()).thenReturn(null);
        when(request.pageNum).thenReturn(2);
        when(request.pageSize).thenReturn(10);

        Criteria criteria = new Criteria();
        Query query = new Query(criteria);
        when(mongoTemplate.count(query, FeeTemplate.class)).thenReturn(20L);

        List<FeeTemplate> feeTemplates = Collections.singletonList(new FeeTemplate());
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(feeTemplates);

        Page<FeeTemplate> result = feeTemplateService.getPageList(request);

        assertEquals(20, result.getTotalElements());
        assertEquals(1, result.getContent().size());
    }

    @Test
    public void getPageList_WithMaxPagination_ReturnsPagedResults() {
        when(request.getSceneCode()).thenReturn("sceneCode");
//        when(request.getServiceCategoryId()).thenReturn(null);
//        when(request.getServiceId()).thenReturn(null);
        when(request.pageNum).thenReturn(1000);
        when(request.pageSize).thenReturn(10);

        Criteria criteria = new Criteria();
        Query query = new Query(criteria);
        when(mongoTemplate.count(query, FeeTemplate.class)).thenReturn(10000L);

        List<FeeTemplate> feeTemplates = Collections.singletonList(new FeeTemplate());
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(feeTemplates);

        Page<FeeTemplate> result = feeTemplateService.getPageList(request);

        assertEquals(10000, result.getTotalElements());
        assertEquals(1, result.getContent().size());
    }

    @Test
    public void getPageList_WithMinPagination_ReturnsPagedResults() {
        when(request.getSceneCode()).thenReturn("sceneCode");
//        when(request.getServiceCategoryId()).thenReturn(null);
//        when(request.getServiceId()).thenReturn(null);
        when(request.pageNum).thenReturn(1);
        when(request.pageSize).thenReturn(1);

        Criteria criteria = new Criteria();
        Query query = new Query(criteria);
        when(mongoTemplate.count(query, FeeTemplate.class)).thenReturn(10L);

        List<FeeTemplate> feeTemplates = Collections.singletonList(new FeeTemplate());
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(feeTemplates);

        Page<FeeTemplate> result = feeTemplateService.getPageList(request);

        assertEquals(10, result.getTotalElements());
        assertEquals(1, result.getContent().size());
    }

    @Test
    public void getPageList_WithInvalidSceneCode_ReturnsEmptyPage() {
        when(request.getSceneCode()).thenReturn("");
//        when(request.getServiceCategoryId()).thenReturn(null);
//        when(request.getServiceId()).thenReturn(null);
        when(request.pageNum).thenReturn(1);
        when(request.pageSize).thenReturn(10);

        Criteria criteria = new Criteria();
        Query query = new Query(criteria);
        when(mongoTemplate.count(query, FeeTemplate.class)).thenReturn(0L);

        Page<FeeTemplate> result = feeTemplateService.getPageList(request);

        assertEquals(0, result.getTotalElements());
    }

    @Test
    public void getPageList_WithInvalidServiceCategoryId_ReturnsEmptyPage() {
        when(request.getSceneCode()).thenReturn("sceneCode");
//        when(request.getServiceCategoryId()).thenReturn("");
//        when(request.getServiceId()).thenReturn(null);
        when(request.pageNum).thenReturn(1);
        when(request.pageSize).thenReturn(10);

        Criteria criteria = new Criteria();
        Query query = new Query(criteria);
        when(mongoTemplate.count(query, FeeTemplate.class)).thenReturn(0L);

        Page<FeeTemplate> result = feeTemplateService.getPageList(request);

        assertEquals(0, result.getTotalElements());
    }

    @Test
    public void getPageList_WithInvalidServiceId_ReturnsEmptyPage() {
        when(request.getSceneCode()).thenReturn("sceneCode");
//        when(request.getServiceCategoryId()).thenReturn(null);
//        when(request.getServiceId()).thenReturn("");
        when(request.pageNum).thenReturn(1);
        when(request.pageSize).thenReturn(10);

        Criteria criteria = new Criteria();
        Query query = new Query(criteria);
        when(mongoTemplate.count(query, FeeTemplate.class)).thenReturn(0L);

        Page<FeeTemplate> result = feeTemplateService.getPageList(request);

        assertEquals(0, result.getTotalElements());
    }

    @Test
    public void getPageList_WithEmptyRequest_ReturnsEmptyPage() {
        when(request.getSceneCode()).thenReturn(null);
//        when(request.getServiceCategoryId()).thenReturn(null);
//        when(request.getServiceId()).thenReturn(null);
        when(request.pageNum).thenReturn(1);
        when(request.pageSize).thenReturn(10);

        Criteria criteria = new Criteria();
        Query query = new Query(criteria);
        when(mongoTemplate.count(query, FeeTemplate.class)).thenReturn(0L);

        Page<FeeTemplate> result = feeTemplateService.getPageList(request);

        assertEquals(0, result.getTotalElements());
    }

    @Test
    public void getPageList_WithNullRequest_ReturnsEmptyPage() {
        request = null;

        Page<FeeTemplate> result = feeTemplateService.getPageList(request);

        assertEquals(0, result.getTotalElements());
    }

    @Test
    public void getPageList_WithLargePageSize_ReturnsPagedResults() {
        when(request.getSceneCode()).thenReturn("sceneCode");
//        when(request.getServiceCategoryId()).thenReturn(null);
//        when(request.getServiceId()).thenReturn(null);
        when(request.pageNum).thenReturn(1);
        when(request.pageSize).thenReturn(1000);

        Criteria criteria = new Criteria();
        Query query = new Query(criteria);
        when(mongoTemplate.count(query, FeeTemplate.class)).thenReturn(10000L);

        List<FeeTemplate> feeTemplates = Collections.singletonList(new FeeTemplate());
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(feeTemplates);

        Page<FeeTemplate> result = feeTemplateService.getPageList(request);

        assertEquals(10000, result.getTotalElements());
        assertEquals(1, result.getContent().size());
    }

    @Test
    public void getPageList_WithSmallPageSize_ReturnsPagedResults() {
        when(request.getSceneCode()).thenReturn("sceneCode");
//        when(request.getServiceCategoryId()).thenReturn(null);
//        when(request.getServiceId()).thenReturn(null);
        when(request.pageNum).thenReturn(1);
        when(request.pageSize).thenReturn(1);

        Criteria criteria = new Criteria();
        Query query = new Query(criteria);
        when(mongoTemplate.count(query, FeeTemplate.class)).thenReturn(10L);

        List<FeeTemplate> feeTemplates = Collections.singletonList(new FeeTemplate());
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(feeTemplates);

        Page<FeeTemplate> result = feeTemplateService.getPageList(request);

        assertEquals(10, result.getTotalElements());
        assertEquals(1, result.getContent().size());
    }
}
