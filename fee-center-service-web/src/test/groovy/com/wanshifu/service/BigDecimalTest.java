package com.wanshifu.service;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class BigDecimalTest {

    public static void main(String[] args) {
        // 创建一个 BigDecimal 对象
        BigDecimal number = new BigDecimal("123.456789");

        // 四舍五入保留两位小数
        BigDecimal roundedHalfUp = number.setScale(2, RoundingMode.HALF_UP);
        BigDecimal roundedHalfDown = number.setScale(2, RoundingMode.HALF_DOWN);
        BigDecimal roundedUp = number.setScale(2, RoundingMode.UP);
        BigDecimal roundedDown = number.setScale(2, RoundingMode.DOWN);
        BigDecimal roundedCeiling = number.setScale(2, RoundingMode.CEILING);
        BigDecimal roundedFloor = number.setScale(2, RoundingMode.FLOOR);
        BigDecimal roundedHalfEven = number.setScale(2, RoundingMode.HALF_EVEN);

        // 输出结果
        System.out.println("Original number: " + number);
        System.out.println("Rounded (HALF_UP): " + roundedHalfUp);
        System.out.println("Rounded (HALF_DOWN): " + roundedHalfDown);
        System.out.println("Rounded (UP): " + roundedUp);
        System.out.println("Rounded (DOWN): " + roundedDown);
        System.out.println("Rounded (CEILING): " + roundedCeiling);
        System.out.println("Rounded (FLOOR): " + roundedFloor);
        System.out.println("Rounded (HALF_EVEN): " + roundedHalfEven);
    }
}
