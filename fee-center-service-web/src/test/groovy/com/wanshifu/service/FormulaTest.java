package com.wanshifu.service;

import com.wanshifu.controller.FormulaController;
import com.wanshifu.fee.center.domain.request.formula.FormulaAddReq;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.mockito.Mockito.verify;

/**
 * @author: <PERSON>
 * @create: 2024-02-27 15:55
 * @description: 公式测试
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class FormulaTest {

    @Mock
    private FormulaService formulaService;

    @InjectMocks
    private FormulaController formulaController;

    @Test
    public void testAdd() {
        // Given
        FormulaAddReq req = new FormulaAddReq();
        // Set up any required data in the request object
        req.setBizIds("1,2,3");
        req.setFormulaName("公式名称-test1");
        req.setDivisionIds("2,3,4,5,6");
        // When
        formulaController.add(req);

        // Then
        verify(formulaService).add(req);
    }
}
