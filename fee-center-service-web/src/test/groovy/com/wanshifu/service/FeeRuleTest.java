package com.wanshifu.service;

import com.google.common.collect.Lists;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.fee.center.domain.biz.AutoReceiveOrderGuidePriceBizRule;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.document.BargainPriceEverydayFeeRule;
import com.wanshifu.fee.center.domain.document.CalculateRuleData;
import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.enums.DivisionTypeEnum;
import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.enums.TemplateStatusEnum;
import com.wanshifu.fee.center.domain.request.feeRule.*;
import com.wanshifu.framework.core.page.SimplePageInfo;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;


/**
 * @author: Chen Yong
 * @create: 2024-01-12 10:35
 * @description: 计价规则test
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class FeeRuleTest {

    @InjectMocks
    private FeeRuleServiceImpl feeRuleService;

    @Mock
    private MongoTemplate mongoTemplate;

    @InjectMocks
    private FeeRuleServiceImpl feeRuleServiceImpl;

    @Mock
    private AddressApi addressApi;


    @Mock
    private MongoDatabase mongoDatabase;

    @Mock
    private MongoCollection<BargainPriceEverydayFeeRule> mongoCollection;


    private DistrictDetailReq req;
    private BargainPriceEverydayFeeRule rule;

    @BeforeEach
    public void setUp() {
        req = new DistrictDetailReq();
        req.setCityName("城市");
        req.setLevel2DivisionId("城市id");
        req.setSceneCode("场景编码");
        req.setServiceId("服务id");
        req.setSkuNo("规格");

        rule = new BargainPriceEverydayFeeRule();
        rule.setCityName("城市");
        rule.setLevel2DivisionId("城市id");
        rule.setSceneCode("场景编码");
        rule.setServiceId("服务id");
        rule.setSkuNo("规格");

        List<BargainPriceEverydayFeeRule.DistrictDetail> districtDetailList = new ArrayList<>();
        BargainPriceEverydayFeeRule.DistrictDetail districtDetail = new BargainPriceEverydayFeeRule.DistrictDetail();
        districtDetail.setDistrictName("区县");
        districtDetail.setStreetNameList(new ArrayList<>());
        districtDetailList.add(districtDetail);
        rule.setDistrictDetailList(districtDetailList);
    }


    @Test
    public void testGetServiceIdsByDivision_Success() {
        // Mocking the data for the request
        ServiceIdByDivisionIdsReq request = new ServiceIdByDivisionIdsReq();
        request.setDivisionCode(DivisionTypeEnum.PROVINCE.code);
        Set<String> divisionIdSet = new HashSet<>(Arrays.asList("1", "2", "3"));
        request.setDivisionIdSet(divisionIdSet);


        // Mocking the data for the FeeRule objects
        List<FeeRule> mockFeeRuleList = Arrays.asList(
                mockFeeRule(1L, 101L, "group1", 1, "scene1", "Scene One", "Fee One", createBizRule("1"), createCalculateRuleData("data1")),
                mockFeeRule(2L, 102L, "group2", 2, "scene2", "Scene Two", "Fee Two", createBizRule("2"), createCalculateRuleData("data2")),
                mockFeeRule(3L, 103L, "group3", 3, "scene3", "Scene Three", "Fee Three", createBizRule("3"), createCalculateRuleData("data3"))

        );

        // Mocking MongoDB find operation
        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(mockFeeRuleList);

        // Performing the actual method invocation
        Set<Long> result = feeRuleService.getServiceIdsByDivision(request);

        // Verifying the expected result
        assertNotNull(result);
        assertEquals(new HashSet<>(Arrays.asList(1L, 2L, 3L)), result);

        // Verifying that the MongoDB find method was called with the correct criteria
        verify(mongoTemplate).find(any(Query.class), eq(FeeRule.class));
    }

    @Test
    public void testGetServiceIdsByDivision_EmptyResult() {
        // Mocking the data for the request
        ServiceIdByDivisionIdsReq request = new ServiceIdByDivisionIdsReq();
        request.setDivisionCode(DivisionTypeEnum.PROVINCE.code);
        Set<String> divisionIdSet = new HashSet<>(Arrays.asList("1", "2", "3"));
        request.setDivisionIdSet(divisionIdSet);

        // Mocking MongoDB find operation to return an empty list
        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(Collections.emptyList());

        // Performing the actual method invocation
        Set<Long> result = feeRuleService.getServiceIdsByDivision(request);

        // Verifying the expected result
        assertNull(result);

        // Verifying that the MongoDB find method was called with the correct criteria
        verify(mongoTemplate).find(any(Query.class), eq(FeeRule.class));
    }


    @Test
    public void testGetAttributeValuePriceByServiceId_ServiceIdInvalid_ReturnsEmptyMap() {
        Long serviceId = null;
        Long userId = 1L;

        Map<String, AttributeValuePriceResp> result = feeRuleServiceImpl.getAttributeValuePriceByServiceId(serviceId, userId);

        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetAttributeValuePriceByServiceId_WithUserId_ReturnsExpectedMap() {
        Long serviceId = 1L;
        Long userId = 1L;

        // Mocking the response for user-specific fee rule
        FeeRule userSpecificFeeRule = createUserSpecificFeeRule(serviceId, userId);
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(Lists.newArrayList(userSpecificFeeRule));

        Map<String, AttributeValuePriceResp> result = feeRuleServiceImpl.getAttributeValuePriceByServiceId(serviceId, userId);

        assertEquals(1, result.size());
        assertEquals(userSpecificFeeRule.getBizRule().get("feeName"), result.get("testSkuNo").getFeeName());
    }

    @Test
    public void testGetAttributeValuePriceByServiceId_WithoutUserId_ReturnsExpectedMap() {
        Long serviceId = 1L;
        Long userId = null;

        // Mocking the response for general fee rule
        FeeRule generalFeeRule = createGeneralFeeRule(serviceId);
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(Lists.newArrayList((generalFeeRule)));

        Map<String, AttributeValuePriceResp> result = feeRuleServiceImpl.getAttributeValuePriceByServiceId(serviceId, userId);

        assertEquals(1, result.size());
        assertEquals(generalFeeRule.getBizRule().get("feeName"), result.get("testSkuNo").getFeeName());
    }

    private FeeRule createUserSpecificFeeRule(Long serviceId, Long userId) {
        Map<String, String> bizRule = new HashMap<>();
        bizRule.put("serviceId", String.valueOf(serviceId));
        bizRule.put("userId", String.valueOf(userId));
        bizRule.put("divisionType", DivisionTypeEnum.COUNTRY.code);
        bizRule.put("feeName", "testFeeName");
        bizRule.put("masterInputPrice", "100");
        bizRule.put("feeUnit", "元");
        bizRule.put("skuNo", "testSkuNo");

        FeeRule feeRule = new FeeRule();
        feeRule.setBizRule(bizRule);
        feeRule.setSceneCode(SceneCodeEnum.PLATFORM_FIXED_PRICE_USER.getCode());
        feeRule.setDel(false);
        feeRule.setStatus(TemplateStatusEnum.ACTIVE.code);

        return feeRule;
    }

    private FeeRule createGeneralFeeRule(Long serviceId) {
        Map<String, String> bizRule = new HashMap<>();
        bizRule.put("serviceId", String.valueOf(serviceId));
        bizRule.put("divisionType", DivisionTypeEnum.COUNTRY.code);
        bizRule.put("feeName", "testFeeName");
        bizRule.put("masterInputPrice", "100");
        bizRule.put("feeUnit", "元");
        bizRule.put("skuNo", "testSkuNo");

        FeeRule feeRule = new FeeRule();
        feeRule.setBizRule(bizRule);
        feeRule.setSceneCode(SceneCodeEnum.PLATFORM_FIXED_PRICE.getCode());
        feeRule.setDel(false);
        feeRule.setStatus(TemplateStatusEnum.ACTIVE.code);

        return feeRule;
    }

    @Test
    public void getBargainPriceEverydayFeeRuleByServiceId_WithValidData_ShouldReturnExpectedResults() {
        // Arrange
        BargainPriceEverydayFeeRuleReq req = new BargainPriceEverydayFeeRuleReq();
        req.setServiceId("testServiceId");
        req.setSceneCode("testSceneCode");
        req.setSkuNo("testSkuNo");
        req.setLevel2DivisionId("testLevel2DivisionId");
        req.setPageNum(1);
        req.setPageSize(10);

        List<BargainPriceEverydayFeeRule> mockResults = new ArrayList<>();
        BargainPriceEverydayFeeRule rule = new BargainPriceEverydayFeeRule();
        rule.setServiceId("testServiceId");
        rule.setSceneCode("testSceneCode");
        rule.setSkuNo("testSkuNo");
        rule.setLevel2DivisionId("testLevel2DivisionId");
        rule.setDiscountPrice(100.0);
        rule.setOriginPrice(200.0);
        rule.setSavingPrice(100.0);
        mockResults.add(rule);

        AggregationResults<BargainPriceEverydayFeeRule> aggregationResults = new AggregationResults<>(mockResults, null);
        when(mongoTemplate.aggregate(any(Aggregation.class), any(String.class), any(Class.class))).thenReturn(aggregationResults);

        // Act
        SimplePageInfo<BargainPriceEverydayFeeRuleResp> result = feeRuleService.getBargainPriceEverydayFeeRuleByServiceId(req);

        // Assert
        assertEquals(1, result.getPages());
        assertEquals(1, result.getPageNum());
        assertEquals(10, result.getPageSize());
        assertEquals(1L, result.getTotal());
        assertEquals(1, result.getList().size());
        assertEquals(100.0, result.getList().get(0).getDiscountPrice().doubleValue(), 0.0);
        assertEquals(200.0, result.getList().get(0).getOriginPrice().doubleValue(), 0.0);
        assertEquals(100.0, result.getList().get(0).getSavingPrice().doubleValue(), 0.0);
    }

    @Test
    public void getBargainPriceEverydayFeeRuleByServiceId_NoResults_ShouldReturnEmptyPage() {
        // Arrange
        BargainPriceEverydayFeeRuleReq req = new BargainPriceEverydayFeeRuleReq();
        req.setServiceId("testServiceId");
        req.setSceneCode("testSceneCode");
        req.setSkuNo("testSkuNo");
        req.setLevel2DivisionId("testLevel2DivisionId");
        req.setPageNum(1);
        req.setPageSize(10);

        AggregationResults<BargainPriceEverydayFeeRule> aggregationResults = new AggregationResults<>(new ArrayList<>(), null);
        when(mongoTemplate.aggregate(any(Aggregation.class), any(String.class), any(Class.class))).thenReturn(aggregationResults);

        // Act
        SimplePageInfo<BargainPriceEverydayFeeRuleResp> result = feeRuleService.getBargainPriceEverydayFeeRuleByServiceId(req);

        // Assert
        assertEquals(0, result.getList().size());
        assertEquals(0L, result.getTotal());
    }

    @Test
    public void getPreGenerateDistrictDetail_InputDataInvalid_ReturnsNull() {
        DistrictDetailReq req = new DistrictDetailReq();
        req.setServiceId("validServiceId");
        req.setSkuNo("validSkuNo");
        req.setLevel2DivisionId("validLevel2DivisionId");
        req.setCityName("validCityName");

        // Missing sceneCode setup

        DistrictDetailResp result = feeRuleService.getPreGenerateDistrictDetail(req);

        assertNull(result);
    }

    @Test
    public void getPreGenerateDistrictDetail_NoMatchingFeeRules_ReturnsNull() {
        DistrictDetailReq req = new DistrictDetailReq();
        req.setServiceId("validServiceId");
        req.setSkuNo("validSkuNo");
        req.setLevel2DivisionId("validLevel2DivisionId");
        req.setCityName("validCityName");
        req.setSceneCode(SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.getCode());

        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(new ArrayList<>());

        DistrictDetailResp result = feeRuleService.getPreGenerateDistrictDetail(req);

        assertNull(result);
    }

    @Test
    public void getPreGenerateDistrictDetail_ValidInput_ReturnsDistrictDetailResp() {
        DistrictDetailReq req = new DistrictDetailReq();
        req.setServiceId("validServiceId");
        req.setSkuNo("validSkuNo");
        req.setLevel2DivisionId("validLevel2DivisionId");
        req.setCityName("validCityName");
        req.setSceneCode(SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.getCode());

        List<FeeRule> feeRuleList = new ArrayList<>();
        FeeRule feeRule = new FeeRule();
        Map<String, String> bizRule = new HashMap<>();
        bizRule.put(CommonBizRule.Fields.level3DivisionId, "level3Id");
        bizRule.put(CommonBizRule.Fields.level4DivisionId, "level4Id");
        feeRule.setBizRule(bizRule);
        feeRuleList.add(feeRule);

        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(feeRuleList);

        Address address = new Address();
        address.setDivisionId(Long.parseLong("level3Id"));
        address.setDivisionName("level3Name");

        Address address1 = new Address();
        address1.setDivisionId(Long.parseLong("level4Id"));
        address1.setDivisionName("level4Name");

        List<Address> addressList = new ArrayList<>();
        addressList.add(address);
        addressList.add(address1);

        when(addressApi.getDivisionInfoListByDivisionIds(anyString())).thenReturn(addressList);

        DistrictDetailResp result = feeRuleService.getPreGenerateDistrictDetail(req);

        assertNotNull(result);
        assertEquals(req.getCityName(), result.getCityName());
        assertNotNull(result.getDistrictDetailList());
        assertEquals(1, result.getDistrictDetailList().size());

        DistrictDetailResp.DistrictDetail districtDetail = result.getDistrictDetailList().get(0);
        assertEquals("level3Name", districtDetail.getDistrictName());
        assertNotNull(districtDetail.getStreetNameList());
        assertEquals(1, districtDetail.getStreetNameList().size());
        assertEquals("level4Name", districtDetail.getStreetNameList().get(0));
    }

    @Test
    public void getDistrictDetail_RuleIsNull_ReturnsNull() {
        when(mongoTemplate.findOne(any(Query.class), any(Class.class))).thenReturn(null);

        DistrictDetailResp resp = feeRuleService.getDistrictDetail(req);

        assertNull(resp);
    }

    @Test
    public void getDistrictDetail_RuleExistsWithEmptyDistrictDetailList_ReturnsEmptyList() {
        rule.setDistrictDetailList(new ArrayList<>());
        when(mongoTemplate.findOne(any(Query.class), any(Class.class))).thenReturn(rule);

        DistrictDetailResp resp = feeRuleService.getDistrictDetail(req);

        assertEquals(rule.getCityName(), resp.getCityName());
        assertEquals(0, resp.getDistrictDetailList().size());
    }

    @Test
    public void getDistrictDetail_RuleExistsWithNonEmptyDistrictDetailList_ReturnsMappedList() {
        when(mongoTemplate.findOne(any(Query.class), any(Class.class))).thenReturn(rule);

        DistrictDetailResp resp = feeRuleService.getDistrictDetail(req);

        assertEquals(rule.getCityName(), resp.getCityName());
        assertEquals(1, resp.getDistrictDetailList().size());
        assertEquals(rule.getDistrictDetailList().get(0).getDistrictName(), resp.getDistrictDetailList().get(0).getDistrictName());
    }





   /******************/
   @Test
   public void testGetServiceIdsByDivision_Success21() {
       // Mocking the data for the request
       ServiceIdByDivisionIdsReq request = new ServiceIdByDivisionIdsReq();
       request.setDivisionCode(DivisionTypeEnum.PROVINCE.code);
       Set<String> divisionIdSet = new HashSet<>(Arrays.asList("1", "2", "3"));
       request.setDivisionIdSet(divisionIdSet);


       // Mocking the data for the FeeRule objects
       List<FeeRule> mockFeeRuleList = Arrays.asList(
               mockFeeRule(1L, 101L, "group1", 1, "scene1", "Scene One", "Fee One", createBizRule("1"), createCalculateRuleData("data1")),
               mockFeeRule(2L, 102L, "group2", 2, "scene2", "Scene Two", "Fee Two", createBizRule("2"), createCalculateRuleData("data2")),
               mockFeeRule(3L, 103L, "group3", 3, "scene3", "Scene Three", "Fee Three", createBizRule("3"), createCalculateRuleData("data3"))

       );

       // Mocking MongoDB find operation
       when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(mockFeeRuleList);

       // Performing the actual method invocation
       Set<Long> result = feeRuleService.getServiceIdsByDivision(request);

       // Verifying the expected result
       assertNotNull(result);
       assertEquals(new HashSet<>(Arrays.asList(1L, 2L, 3L)), result);

       // Verifying that the MongoDB find method was called with the correct criteria
       verify(mongoTemplate).find(any(Query.class), eq(FeeRule.class));
   }

    @Test
    public void testGetServiceIdsByDivision_EmptyResult21() {
        // Mocking the data for the request
        ServiceIdByDivisionIdsReq request = new ServiceIdByDivisionIdsReq();
        request.setDivisionCode(DivisionTypeEnum.PROVINCE.code);
        Set<String> divisionIdSet = new HashSet<>(Arrays.asList("1", "2", "3"));
        request.setDivisionIdSet(divisionIdSet);

        // Mocking MongoDB find operation to return an empty list
        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(Collections.emptyList());

        // Performing the actual method invocation
        Set<Long> result = feeRuleService.getServiceIdsByDivision(request);

        // Verifying the expected result
        assertNull(result);

        // Verifying that the MongoDB find method was called with the correct criteria
        verify(mongoTemplate).find(any(Query.class), eq(FeeRule.class));
    }


    @Test
    public void testGetAttributeValuePriceByServiceId_ServiceIdInvalid_ReturnsEmptyMap21() {
        Long serviceId = null;
        Long userId = 1L;

        Map<String, AttributeValuePriceResp> result = feeRuleServiceImpl.getAttributeValuePriceByServiceId(serviceId, userId);

        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetAttributeValuePriceByServiceId_WithUserId_ReturnsExpectedMap21() {
        Long serviceId = 1L;
        Long userId = 1L;

        // Mocking the response for user-specific fee rule
        FeeRule userSpecificFeeRule = createUserSpecificFeeRule(serviceId, userId);
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(Lists.newArrayList(userSpecificFeeRule));

        Map<String, AttributeValuePriceResp> result = feeRuleServiceImpl.getAttributeValuePriceByServiceId(serviceId, userId);

        assertEquals(1, result.size());
        assertEquals(userSpecificFeeRule.getBizRule().get("feeName"), result.get("testSkuNo").getFeeName());
    }

    @Test
    public void testGetAttributeValuePriceByServiceId_WithoutUserId_ReturnsExpectedMap21() {
        Long serviceId = 1L;
        Long userId = null;

        // Mocking the response for general fee rule
        FeeRule generalFeeRule = createGeneralFeeRule(serviceId);
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(Lists.newArrayList((generalFeeRule)));

        Map<String, AttributeValuePriceResp> result = feeRuleServiceImpl.getAttributeValuePriceByServiceId(serviceId, userId);

        assertEquals(1, result.size());
        assertEquals(generalFeeRule.getBizRule().get("feeName"), result.get("testSkuNo").getFeeName());
    }



    @Test
    public void getBargainPriceEverydayFeeRuleByServiceId_WithValidData_ShouldReturnExpectedResults21() {
        // Arrange
        BargainPriceEverydayFeeRuleReq req = new BargainPriceEverydayFeeRuleReq();
        req.setServiceId("testServiceId");
        req.setSceneCode("testSceneCode");
        req.setSkuNo("testSkuNo");
        req.setLevel2DivisionId("testLevel2DivisionId");
        req.setPageNum(1);
        req.setPageSize(10);

        List<BargainPriceEverydayFeeRule> mockResults = new ArrayList<>();
        BargainPriceEverydayFeeRule rule = new BargainPriceEverydayFeeRule();
        rule.setServiceId("testServiceId");
        rule.setSceneCode("testSceneCode");
        rule.setSkuNo("testSkuNo");
        rule.setLevel2DivisionId("testLevel2DivisionId");
        rule.setDiscountPrice(100.0);
        rule.setOriginPrice(200.0);
        rule.setSavingPrice(100.0);
        mockResults.add(rule);

        AggregationResults<BargainPriceEverydayFeeRule> aggregationResults = new AggregationResults<>(mockResults, null);
        when(mongoTemplate.aggregate(any(Aggregation.class), any(String.class), any(Class.class))).thenReturn(aggregationResults);

        // Act
        SimplePageInfo<BargainPriceEverydayFeeRuleResp> result = feeRuleService.getBargainPriceEverydayFeeRuleByServiceId(req);

        // Assert
        assertEquals(1, result.getPages());
        assertEquals(1, result.getPageNum());
        assertEquals(10, result.getPageSize());
        assertEquals(1L, result.getTotal());
        assertEquals(1, result.getList().size());
        assertEquals(100.0, result.getList().get(0).getDiscountPrice().doubleValue(), 0.0);
        assertEquals(200.0, result.getList().get(0).getOriginPrice().doubleValue(), 0.0);
        assertEquals(100.0, result.getList().get(0).getSavingPrice().doubleValue(), 0.0);
    }

    @Test
    public void getBargainPriceEverydayFeeRuleByServiceId_NoResults_ShouldReturnEmptyPage22() {
        // Arrange
        BargainPriceEverydayFeeRuleReq req = new BargainPriceEverydayFeeRuleReq();
        req.setServiceId("testServiceId");
        req.setSceneCode("testSceneCode");
        req.setSkuNo("testSkuNo");
        req.setLevel2DivisionId("testLevel2DivisionId");
        req.setPageNum(1);
        req.setPageSize(10);

        AggregationResults<BargainPriceEverydayFeeRule> aggregationResults = new AggregationResults<>(new ArrayList<>(), null);
        when(mongoTemplate.aggregate(any(Aggregation.class), any(String.class), any(Class.class))).thenReturn(aggregationResults);

        // Act
        SimplePageInfo<BargainPriceEverydayFeeRuleResp> result = feeRuleService.getBargainPriceEverydayFeeRuleByServiceId(req);

        // Assert
        assertEquals(0, result.getList().size());
        assertEquals(0L, result.getTotal());
    }

    @Test
    public void getPreGenerateDistrictDetail_InputDataInvalid_ReturnsNull22() {
        DistrictDetailReq req = new DistrictDetailReq();
        req.setServiceId("validServiceId");
        req.setSkuNo("validSkuNo");
        req.setLevel2DivisionId("validLevel2DivisionId");
        req.setCityName("validCityName");

        // Missing sceneCode setup

        DistrictDetailResp result = feeRuleService.getPreGenerateDistrictDetail(req);

        assertNull(result);
    }

    @Test
    public void getPreGenerateDistrictDetail_NoMatchingFeeRules_ReturnsNull22() {
        DistrictDetailReq req = new DistrictDetailReq();
        req.setServiceId("validServiceId");
        req.setSkuNo("validSkuNo");
        req.setLevel2DivisionId("validLevel2DivisionId");
        req.setCityName("validCityName");
        req.setSceneCode(SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.getCode());

        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(new ArrayList<>());

        DistrictDetailResp result = feeRuleService.getPreGenerateDistrictDetail(req);

        assertNull(result);
    }

    @Test
    public void getPreGenerateDistrictDetail_ValidInput_ReturnsDistrictDetailResp22() {
        DistrictDetailReq req = new DistrictDetailReq();
        req.setServiceId("validServiceId");
        req.setSkuNo("validSkuNo");
        req.setLevel2DivisionId("validLevel2DivisionId");
        req.setCityName("validCityName");
        req.setSceneCode(SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.getCode());

        List<FeeRule> feeRuleList = new ArrayList<>();
        FeeRule feeRule = new FeeRule();
        Map<String, String> bizRule = new HashMap<>();
        bizRule.put(CommonBizRule.Fields.level3DivisionId, "level3Id");
        bizRule.put(CommonBizRule.Fields.level4DivisionId, "level4Id");
        feeRule.setBizRule(bizRule);
        feeRuleList.add(feeRule);

        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(feeRuleList);

        Address address = new Address();
        address.setDivisionId(Long.parseLong("level3Id"));
        address.setDivisionName("level3Name");

        Address address1 = new Address();
        address1.setDivisionId(Long.parseLong("level4Id"));
        address1.setDivisionName("level4Name");

        List<Address> addressList = new ArrayList<>();
        addressList.add(address);
        addressList.add(address1);

        when(addressApi.getDivisionInfoListByDivisionIds(anyString())).thenReturn(addressList);

        DistrictDetailResp result = feeRuleService.getPreGenerateDistrictDetail(req);

        assertNotNull(result);
        assertEquals(req.getCityName(), result.getCityName());
        assertNotNull(result.getDistrictDetailList());
        assertEquals(1, result.getDistrictDetailList().size());

        DistrictDetailResp.DistrictDetail districtDetail = result.getDistrictDetailList().get(0);
        assertEquals("level3Name", districtDetail.getDistrictName());
        assertNotNull(districtDetail.getStreetNameList());
        assertEquals(1, districtDetail.getStreetNameList().size());
        assertEquals("level4Name", districtDetail.getStreetNameList().get(0));
    }

    @Test
    public void getDistrictDetail_RuleIsNull_ReturnsNull22() {
        when(mongoTemplate.findOne(any(Query.class), any(Class.class))).thenReturn(null);

        DistrictDetailResp resp = feeRuleService.getDistrictDetail(req);

        assertNull(resp);
    }

    @Test
    public void getDistrictDetail_RuleExistsWithEmptyDistrictDetailList_ReturnsEmptyList22() {
        rule.setDistrictDetailList(new ArrayList<>());
        when(mongoTemplate.findOne(any(Query.class), any(Class.class))).thenReturn(rule);

        DistrictDetailResp resp = feeRuleService.getDistrictDetail(req);

        assertEquals(rule.getCityName(), resp.getCityName());
        assertEquals(0, resp.getDistrictDetailList().size());
    }

    @Test
    public void getDistrictDetail_RuleExistsWithNonEmptyDistrictDetailList_ReturnsMappedList22() {
        when(mongoTemplate.findOne(any(Query.class), any(Class.class))).thenReturn(rule);

        DistrictDetailResp resp = feeRuleService.getDistrictDetail(req);

        assertEquals(rule.getCityName(), resp.getCityName());
        assertEquals(1, resp.getDistrictDetailList().size());
        assertEquals(rule.getDistrictDetailList().get(0).getDistrictName(), resp.getDistrictDetailList().get(0).getDistrictName());
    }



    @Test
    public void testGetServiceIdsByDivision_Success22() {
        // Mocking the data for the request
        ServiceIdByDivisionIdsReq request = new ServiceIdByDivisionIdsReq();
        request.setDivisionCode(DivisionTypeEnum.PROVINCE.code);
        Set<String> divisionIdSet = new HashSet<>(Arrays.asList("1", "2", "3"));
        request.setDivisionIdSet(divisionIdSet);


        // Mocking the data for the FeeRule objects
        List<FeeRule> mockFeeRuleList = Arrays.asList(
                mockFeeRule(1L, 101L, "group1", 1, "scene1", "Scene One", "Fee One", createBizRule("1"), createCalculateRuleData("data1")),
                mockFeeRule(2L, 102L, "group2", 2, "scene2", "Scene Two", "Fee Two", createBizRule("2"), createCalculateRuleData("data2")),
                mockFeeRule(3L, 103L, "group3", 3, "scene3", "Scene Three", "Fee Three", createBizRule("3"), createCalculateRuleData("data3"))

        );

        // Mocking MongoDB find operation
        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(mockFeeRuleList);

        // Performing the actual method invocation
        Set<Long> result = feeRuleService.getServiceIdsByDivision(request);

        // Verifying the expected result
        assertNotNull(result);
        assertEquals(new HashSet<>(Arrays.asList(1L, 2L, 3L)), result);

        // Verifying that the MongoDB find method was called with the correct criteria
        verify(mongoTemplate).find(any(Query.class), eq(FeeRule.class));
    }

    @Test
    public void testGetServiceIdsByDivision_EmptyResult22() {
        // Mocking the data for the request
        ServiceIdByDivisionIdsReq request = new ServiceIdByDivisionIdsReq();
        request.setDivisionCode(DivisionTypeEnum.PROVINCE.code);
        Set<String> divisionIdSet = new HashSet<>(Arrays.asList("1", "2", "3"));
        request.setDivisionIdSet(divisionIdSet);

        // Mocking MongoDB find operation to return an empty list
        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(Collections.emptyList());

        // Performing the actual method invocation
        Set<Long> result = feeRuleService.getServiceIdsByDivision(request);

        // Verifying the expected result
        assertNull(result);

        // Verifying that the MongoDB find method was called with the correct criteria
        verify(mongoTemplate).find(any(Query.class), eq(FeeRule.class));
    }


    @Test
    public void testGetAttributeValuePriceByServiceId_ServiceIdInvalid_ReturnsEmptyMap22() {
        Long serviceId = null;
        Long userId = 1L;

        Map<String, AttributeValuePriceResp> result = feeRuleServiceImpl.getAttributeValuePriceByServiceId(serviceId, userId);

        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetAttributeValuePriceByServiceId_WithUserId_ReturnsExpectedMap22() {
        Long serviceId = 1L;
        Long userId = 1L;

        // Mocking the response for user-specific fee rule
        FeeRule userSpecificFeeRule = createUserSpecificFeeRule(serviceId, userId);
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(Lists.newArrayList(userSpecificFeeRule));

        Map<String, AttributeValuePriceResp> result = feeRuleServiceImpl.getAttributeValuePriceByServiceId(serviceId, userId);

        assertEquals(1, result.size());
        assertEquals(userSpecificFeeRule.getBizRule().get("feeName"), result.get("testSkuNo").getFeeName());
    }

    @Test
    public void testGetAttributeValuePriceByServiceId_WithoutUserId_ReturnsExpectedMap22() {
        Long serviceId = 1L;
        Long userId = null;

        // Mocking the response for general fee rule
        FeeRule generalFeeRule = createGeneralFeeRule(serviceId);
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(Lists.newArrayList((generalFeeRule)));

        Map<String, AttributeValuePriceResp> result = feeRuleServiceImpl.getAttributeValuePriceByServiceId(serviceId, userId);

        assertEquals(1, result.size());
        assertEquals(generalFeeRule.getBizRule().get("feeName"), result.get("testSkuNo").getFeeName());
    }



    @Test
    public void getBargainPriceEverydayFeeRuleByServiceId_WithValidData_ShouldReturnExpectedResults22() {
        // Arrange
        BargainPriceEverydayFeeRuleReq req = new BargainPriceEverydayFeeRuleReq();
        req.setServiceId("testServiceId");
        req.setSceneCode("testSceneCode");
        req.setSkuNo("testSkuNo");
        req.setLevel2DivisionId("testLevel2DivisionId");
        req.setPageNum(1);
        req.setPageSize(10);

        List<BargainPriceEverydayFeeRule> mockResults = new ArrayList<>();
        BargainPriceEverydayFeeRule rule = new BargainPriceEverydayFeeRule();
        rule.setServiceId("testServiceId");
        rule.setSceneCode("testSceneCode");
        rule.setSkuNo("testSkuNo");
        rule.setLevel2DivisionId("testLevel2DivisionId");
        rule.setDiscountPrice(100.0);
        rule.setOriginPrice(200.0);
        rule.setSavingPrice(100.0);
        mockResults.add(rule);

        AggregationResults<BargainPriceEverydayFeeRule> aggregationResults = new AggregationResults<>(mockResults, null);
        when(mongoTemplate.aggregate(any(Aggregation.class), any(String.class), any(Class.class))).thenReturn(aggregationResults);

        // Act
        SimplePageInfo<BargainPriceEverydayFeeRuleResp> result = feeRuleService.getBargainPriceEverydayFeeRuleByServiceId(req);

        // Assert
        assertEquals(1, result.getPages());
        assertEquals(1, result.getPageNum());
        assertEquals(10, result.getPageSize());
        assertEquals(1L, result.getTotal());
        assertEquals(1, result.getList().size());
        assertEquals(100.0, result.getList().get(0).getDiscountPrice().doubleValue(), 0.0);
        assertEquals(200.0, result.getList().get(0).getOriginPrice().doubleValue(), 0.0);
        assertEquals(100.0, result.getList().get(0).getSavingPrice().doubleValue(), 0.0);
    }

    @Test
    public void getBargainPriceEverydayFeeRuleByServiceId_NoResults_ShouldReturnEmptyPage21() {
        // Arrange
        BargainPriceEverydayFeeRuleReq req = new BargainPriceEverydayFeeRuleReq();
        req.setServiceId("testServiceId");
        req.setSceneCode("testSceneCode");
        req.setSkuNo("testSkuNo");
        req.setLevel2DivisionId("testLevel2DivisionId");
        req.setPageNum(1);
        req.setPageSize(10);

        AggregationResults<BargainPriceEverydayFeeRule> aggregationResults = new AggregationResults<>(new ArrayList<>(), null);
        when(mongoTemplate.aggregate(any(Aggregation.class), any(String.class), any(Class.class))).thenReturn(aggregationResults);

        // Act
        SimplePageInfo<BargainPriceEverydayFeeRuleResp> result = feeRuleService.getBargainPriceEverydayFeeRuleByServiceId(req);

        // Assert
        assertEquals(0, result.getList().size());
        assertEquals(0L, result.getTotal());
    }

    @Test
    public void getPreGenerateDistrictDetail_InputDataInvalid_ReturnsNull21() {
        DistrictDetailReq req = new DistrictDetailReq();
        req.setServiceId("validServiceId");
        req.setSkuNo("validSkuNo");
        req.setLevel2DivisionId("validLevel2DivisionId");
        req.setCityName("validCityName");

        // Missing sceneCode setup

        DistrictDetailResp result = feeRuleService.getPreGenerateDistrictDetail(req);

        assertNull(result);
    }

    @Test
    public void getPreGenerateDistrictDetail_NoMatchingFeeRules_ReturnsNull21() {
        DistrictDetailReq req = new DistrictDetailReq();
        req.setServiceId("validServiceId");
        req.setSkuNo("validSkuNo");
        req.setLevel2DivisionId("validLevel2DivisionId");
        req.setCityName("validCityName");
        req.setSceneCode(SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.getCode());

        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(new ArrayList<>());

        DistrictDetailResp result = feeRuleService.getPreGenerateDistrictDetail(req);

        assertNull(result);
    }

    @Test
    public void getPreGenerateDistrictDetail_ValidInput_ReturnsDistrictDetailResp21() {
        DistrictDetailReq req = new DistrictDetailReq();
        req.setServiceId("validServiceId");
        req.setSkuNo("validSkuNo");
        req.setLevel2DivisionId("validLevel2DivisionId");
        req.setCityName("validCityName");
        req.setSceneCode(SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.getCode());

        List<FeeRule> feeRuleList = new ArrayList<>();
        FeeRule feeRule = new FeeRule();
        Map<String, String> bizRule = new HashMap<>();
        bizRule.put(CommonBizRule.Fields.level3DivisionId, "level3Id");
        bizRule.put(CommonBizRule.Fields.level4DivisionId, "level4Id");
        feeRule.setBizRule(bizRule);
        feeRuleList.add(feeRule);

        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(feeRuleList);

        Address address = new Address();
        address.setDivisionId(Long.parseLong("level3Id"));
        address.setDivisionName("level3Name");

        Address address1 = new Address();
        address1.setDivisionId(Long.parseLong("level4Id"));
        address1.setDivisionName("level4Name");

        List<Address> addressList = new ArrayList<>();
        addressList.add(address);
        addressList.add(address1);

        when(addressApi.getDivisionInfoListByDivisionIds(anyString())).thenReturn(addressList);

        DistrictDetailResp result = feeRuleService.getPreGenerateDistrictDetail(req);

        assertNotNull(result);
        assertEquals(req.getCityName(), result.getCityName());
        assertNotNull(result.getDistrictDetailList());
        assertEquals(1, result.getDistrictDetailList().size());

        DistrictDetailResp.DistrictDetail districtDetail = result.getDistrictDetailList().get(0);
        assertEquals("level3Name", districtDetail.getDistrictName());
        assertNotNull(districtDetail.getStreetNameList());
        assertEquals(1, districtDetail.getStreetNameList().size());
        assertEquals("level4Name", districtDetail.getStreetNameList().get(0));
    }

    @Test
    public void getDistrictDetail_RuleIsNull_ReturnsNull21() {
        when(mongoTemplate.findOne(any(Query.class), any(Class.class))).thenReturn(null);

        DistrictDetailResp resp = feeRuleService.getDistrictDetail(req);

        assertNull(resp);
    }

    @Test
    public void getDistrictDetail_RuleExistsWithEmptyDistrictDetailList_ReturnsEmptyList21() {
        rule.setDistrictDetailList(new ArrayList<>());
        when(mongoTemplate.findOne(any(Query.class), any(Class.class))).thenReturn(rule);

        DistrictDetailResp resp = feeRuleService.getDistrictDetail(req);

        assertEquals(rule.getCityName(), resp.getCityName());
        assertEquals(0, resp.getDistrictDetailList().size());
    }

    @Test
    public void getDistrictDetail_RuleExistsWithNonEmptyDistrictDetailList_ReturnsMappedList21() {
        when(mongoTemplate.findOne(any(Query.class), any(Class.class))).thenReturn(rule);

        DistrictDetailResp resp = feeRuleService.getDistrictDetail(req);

        assertEquals(rule.getCityName(), resp.getCityName());
        assertEquals(1, resp.getDistrictDetailList().size());
        assertEquals(rule.getDistrictDetailList().get(0).getDistrictName(), resp.getDistrictDetailList().get(0).getDistrictName());
    }



    @Test
    public void testGetServiceIdsByDivision_Success2() {
        // Mocking the data for the request
        ServiceIdByDivisionIdsReq request = new ServiceIdByDivisionIdsReq();
        request.setDivisionCode(DivisionTypeEnum.PROVINCE.code);
        Set<String> divisionIdSet = new HashSet<>(Arrays.asList("1", "2", "3"));
        request.setDivisionIdSet(divisionIdSet);


        // Mocking the data for the FeeRule objects
        List<FeeRule> mockFeeRuleList = Arrays.asList(
                mockFeeRule(1L, 101L, "group1", 1, "scene1", "Scene One", "Fee One", createBizRule("1"), createCalculateRuleData("data1")),
                mockFeeRule(2L, 102L, "group2", 2, "scene2", "Scene Two", "Fee Two", createBizRule("2"), createCalculateRuleData("data2")),
                mockFeeRule(3L, 103L, "group3", 3, "scene3", "Scene Three", "Fee Three", createBizRule("3"), createCalculateRuleData("data3"))

        );

        // Mocking MongoDB find operation
        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(mockFeeRuleList);

        // Performing the actual method invocation
        Set<Long> result = feeRuleService.getServiceIdsByDivision(request);

        // Verifying the expected result
        assertNotNull(result);
        assertEquals(new HashSet<>(Arrays.asList(1L, 2L, 3L)), result);

        // Verifying that the MongoDB find method was called with the correct criteria
        verify(mongoTemplate).find(any(Query.class), eq(FeeRule.class));
    }

    @Test
    public void testGetServiceIdsByDivision_EmptyResult2() {
        // Mocking the data for the request
        ServiceIdByDivisionIdsReq request = new ServiceIdByDivisionIdsReq();
        request.setDivisionCode(DivisionTypeEnum.PROVINCE.code);
        Set<String> divisionIdSet = new HashSet<>(Arrays.asList("1", "2", "3"));
        request.setDivisionIdSet(divisionIdSet);

        // Mocking MongoDB find operation to return an empty list
        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(Collections.emptyList());

        // Performing the actual method invocation
        Set<Long> result = feeRuleService.getServiceIdsByDivision(request);

        // Verifying the expected result
        assertNull(result);

        // Verifying that the MongoDB find method was called with the correct criteria
        verify(mongoTemplate).find(any(Query.class), eq(FeeRule.class));
    }


    @Test
    public void testGetAttributeValuePriceByServiceId_ServiceIdInvalid_ReturnsEmptyMap2() {
        Long serviceId = null;
        Long userId = 1L;

        Map<String, AttributeValuePriceResp> result = feeRuleServiceImpl.getAttributeValuePriceByServiceId(serviceId, userId);

        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetAttributeValuePriceByServiceId_WithUserId_ReturnsExpectedMap2() {
        Long serviceId = 1L;
        Long userId = 1L;

        // Mocking the response for user-specific fee rule
        FeeRule userSpecificFeeRule = createUserSpecificFeeRule(serviceId, userId);
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(Lists.newArrayList(userSpecificFeeRule));

        Map<String, AttributeValuePriceResp> result = feeRuleServiceImpl.getAttributeValuePriceByServiceId(serviceId, userId);

        assertEquals(1, result.size());
        assertEquals(userSpecificFeeRule.getBizRule().get("feeName"), result.get("testSkuNo").getFeeName());
    }

    @Test
    public void testGetAttributeValuePriceByServiceId_WithoutUserId_ReturnsExpectedMap2() {
        Long serviceId = 1L;
        Long userId = null;

        // Mocking the response for general fee rule
        FeeRule generalFeeRule = createGeneralFeeRule(serviceId);
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(Lists.newArrayList((generalFeeRule)));

        Map<String, AttributeValuePriceResp> result = feeRuleServiceImpl.getAttributeValuePriceByServiceId(serviceId, userId);

        assertEquals(1, result.size());
        assertEquals(generalFeeRule.getBizRule().get("feeName"), result.get("testSkuNo").getFeeName());
    }



    @Test
    public void getBargainPriceEverydayFeeRuleByServiceId_WithValidData_ShouldReturnExpectedResults2() {
        // Arrange
        BargainPriceEverydayFeeRuleReq req = new BargainPriceEverydayFeeRuleReq();
        req.setServiceId("testServiceId");
        req.setSceneCode("testSceneCode");
        req.setSkuNo("testSkuNo");
        req.setLevel2DivisionId("testLevel2DivisionId");
        req.setPageNum(1);
        req.setPageSize(10);

        List<BargainPriceEverydayFeeRule> mockResults = new ArrayList<>();
        BargainPriceEverydayFeeRule rule = new BargainPriceEverydayFeeRule();
        rule.setServiceId("testServiceId");
        rule.setSceneCode("testSceneCode");
        rule.setSkuNo("testSkuNo");
        rule.setLevel2DivisionId("testLevel2DivisionId");
        rule.setDiscountPrice(100.0);
        rule.setOriginPrice(200.0);
        rule.setSavingPrice(100.0);
        mockResults.add(rule);

        AggregationResults<BargainPriceEverydayFeeRule> aggregationResults = new AggregationResults<>(mockResults, null);
        when(mongoTemplate.aggregate(any(Aggregation.class), any(String.class), any(Class.class))).thenReturn(aggregationResults);

        // Act
        SimplePageInfo<BargainPriceEverydayFeeRuleResp> result = feeRuleService.getBargainPriceEverydayFeeRuleByServiceId(req);

        // Assert
        assertEquals(1, result.getPages());
        assertEquals(1, result.getPageNum());
        assertEquals(10, result.getPageSize());
        assertEquals(1L, result.getTotal());
        assertEquals(1, result.getList().size());
        assertEquals(100.0, result.getList().get(0).getDiscountPrice().doubleValue(), 0.0);
        assertEquals(200.0, result.getList().get(0).getOriginPrice().doubleValue(), 0.0);
        assertEquals(100.0, result.getList().get(0).getSavingPrice().doubleValue(), 0.0);
    }

    @Test
    public void getBargainPriceEverydayFeeRuleByServiceId_NoResults_ShouldReturnEmptyPage2() {
        // Arrange
        BargainPriceEverydayFeeRuleReq req = new BargainPriceEverydayFeeRuleReq();
        req.setServiceId("testServiceId");
        req.setSceneCode("testSceneCode");
        req.setSkuNo("testSkuNo");
        req.setLevel2DivisionId("testLevel2DivisionId");
        req.setPageNum(1);
        req.setPageSize(10);

        AggregationResults<BargainPriceEverydayFeeRule> aggregationResults = new AggregationResults<>(new ArrayList<>(), null);
        when(mongoTemplate.aggregate(any(Aggregation.class), any(String.class), any(Class.class))).thenReturn(aggregationResults);

        // Act
        SimplePageInfo<BargainPriceEverydayFeeRuleResp> result = feeRuleService.getBargainPriceEverydayFeeRuleByServiceId(req);

        // Assert
        assertEquals(0, result.getList().size());
        assertEquals(0L, result.getTotal());
    }

    @Test
    public void getPreGenerateDistrictDetail_InputDataInvalid_ReturnsNull2() {
        DistrictDetailReq req = new DistrictDetailReq();
        req.setServiceId("validServiceId");
        req.setSkuNo("validSkuNo");
        req.setLevel2DivisionId("validLevel2DivisionId");
        req.setCityName("validCityName");

        // Missing sceneCode setup

        DistrictDetailResp result = feeRuleService.getPreGenerateDistrictDetail(req);

        assertNull(result);
    }

    @Test
    public void getPreGenerateDistrictDetail_NoMatchingFeeRules_ReturnsNull2() {
        DistrictDetailReq req = new DistrictDetailReq();
        req.setServiceId("validServiceId");
        req.setSkuNo("validSkuNo");
        req.setLevel2DivisionId("validLevel2DivisionId");
        req.setCityName("validCityName");
        req.setSceneCode(SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.getCode());

        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(new ArrayList<>());

        DistrictDetailResp result = feeRuleService.getPreGenerateDistrictDetail(req);

        assertNull(result);
    }

    @Test
    public void getPreGenerateDistrictDetail_ValidInput_ReturnsDistrictDetailResp2() {
        DistrictDetailReq req = new DistrictDetailReq();
        req.setServiceId("validServiceId");
        req.setSkuNo("validSkuNo");
        req.setLevel2DivisionId("validLevel2DivisionId");
        req.setCityName("validCityName");
        req.setSceneCode(SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.getCode());

        List<FeeRule> feeRuleList = new ArrayList<>();
        FeeRule feeRule = new FeeRule();
        Map<String, String> bizRule = new HashMap<>();
        bizRule.put(CommonBizRule.Fields.level3DivisionId, "level3Id");
        bizRule.put(CommonBizRule.Fields.level4DivisionId, "level4Id");
        feeRule.setBizRule(bizRule);
        feeRuleList.add(feeRule);

        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(feeRuleList);

        Address address = new Address();
        address.setDivisionId(Long.parseLong("level3Id"));
        address.setDivisionName("level3Name");

        Address address1 = new Address();
        address1.setDivisionId(Long.parseLong("level4Id"));
        address1.setDivisionName("level4Name");

        List<Address> addressList = new ArrayList<>();
        addressList.add(address);
        addressList.add(address1);

        when(addressApi.getDivisionInfoListByDivisionIds(anyString())).thenReturn(addressList);

        DistrictDetailResp result = feeRuleService.getPreGenerateDistrictDetail(req);

        assertNotNull(result);
        assertEquals(req.getCityName(), result.getCityName());
        assertNotNull(result.getDistrictDetailList());
        assertEquals(1, result.getDistrictDetailList().size());

        DistrictDetailResp.DistrictDetail districtDetail = result.getDistrictDetailList().get(0);
        assertEquals("level3Name", districtDetail.getDistrictName());
        assertNotNull(districtDetail.getStreetNameList());
        assertEquals(1, districtDetail.getStreetNameList().size());
        assertEquals("level4Name", districtDetail.getStreetNameList().get(0));
    }

    @Test
    public void getDistrictDetail_RuleIsNull_ReturnsNull2() {
        when(mongoTemplate.findOne(any(Query.class), any(Class.class))).thenReturn(null);

        DistrictDetailResp resp = feeRuleService.getDistrictDetail(req);

        assertNull(resp);
    }

    @Test
    public void getDistrictDetail_RuleExistsWithEmptyDistrictDetailList_ReturnsEmptyList2() {
        rule.setDistrictDetailList(new ArrayList<>());
        when(mongoTemplate.findOne(any(Query.class), any(Class.class))).thenReturn(rule);

        DistrictDetailResp resp = feeRuleService.getDistrictDetail(req);

        assertEquals(rule.getCityName(), resp.getCityName());
        assertEquals(0, resp.getDistrictDetailList().size());
    }

    @Test
    public void getDistrictDetail_RuleExistsWithNonEmptyDistrictDetailList_ReturnsMappedList2() {
        when(mongoTemplate.findOne(any(Query.class), any(Class.class))).thenReturn(rule);

        DistrictDetailResp resp = feeRuleService.getDistrictDetail(req);

        assertEquals(rule.getCityName(), resp.getCityName());
        assertEquals(1, resp.getDistrictDetailList().size());
        assertEquals(rule.getDistrictDetailList().get(0).getDistrictName(), resp.getDistrictDetailList().get(0).getDistrictName());
    }


    /****2024-10-31 start ****/

    @Test
    public void getPreGenerateDistrictDetail_ValidInput_ReturnsDistrictDetailResp223() {
        DistrictDetailReq req = new DistrictDetailReq();
        req.setServiceId("validServiceId");
        req.setSkuNo("validSkuNo");
        req.setLevel2DivisionId("validLevel2DivisionId");
        req.setCityName("validCityName");
        req.setSceneCode(SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.getCode());

        List<FeeRule> feeRuleList = new ArrayList<>();
        FeeRule feeRule = new FeeRule();
        Map<String, String> bizRule = new HashMap<>();
        bizRule.put(CommonBizRule.Fields.level3DivisionId, "level3Id");
        bizRule.put(CommonBizRule.Fields.level4DivisionId, "level4Id");
        feeRule.setBizRule(bizRule);
        feeRuleList.add(feeRule);

        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(feeRuleList);

        Address address = new Address();
        address.setDivisionId(Long.parseLong("level3Id"));
        address.setDivisionName("level3Name");

        Address address1 = new Address();
        address1.setDivisionId(Long.parseLong("level4Id"));
        address1.setDivisionName("level4Name");

        List<Address> addressList = new ArrayList<>();
        addressList.add(address);
        addressList.add(address1);

        when(addressApi.getDivisionInfoListByDivisionIds(anyString())).thenReturn(addressList);

        DistrictDetailResp result = feeRuleService.getPreGenerateDistrictDetail(req);

        assertNotNull(result);
        assertEquals(req.getCityName(), result.getCityName());
        assertNotNull(result.getDistrictDetailList());
        assertEquals(1, result.getDistrictDetailList().size());

        DistrictDetailResp.DistrictDetail districtDetail = result.getDistrictDetailList().get(0);
        assertEquals("level3Name", districtDetail.getDistrictName());
        assertNotNull(districtDetail.getStreetNameList());
        assertEquals(1, districtDetail.getStreetNameList().size());
        assertEquals("level4Name", districtDetail.getStreetNameList().get(0));
    }

    @Test
    public void getDistrictDetail_RuleIsNull_ReturnsNull223() {
        when(mongoTemplate.findOne(any(Query.class), any(Class.class))).thenReturn(null);

        DistrictDetailResp resp = feeRuleService.getDistrictDetail(req);

        assertNull(resp);
    }

    @Test
    public void getDistrictDetail_RuleExistsWithEmptyDistrictDetailList_ReturnsEmptyList223() {
        rule.setDistrictDetailList(new ArrayList<>());
        when(mongoTemplate.findOne(any(Query.class), any(Class.class))).thenReturn(rule);

        DistrictDetailResp resp = feeRuleService.getDistrictDetail(req);

        assertEquals(rule.getCityName(), resp.getCityName());
        assertEquals(0, resp.getDistrictDetailList().size());
    }

    @Test
    public void getDistrictDetail_RuleExistsWithNonEmptyDistrictDetailList_ReturnsMappedList223() {
        when(mongoTemplate.findOne(any(Query.class), any(Class.class))).thenReturn(rule);

        DistrictDetailResp resp = feeRuleService.getDistrictDetail(req);

        assertEquals(rule.getCityName(), resp.getCityName());
        assertEquals(1, resp.getDistrictDetailList().size());
        assertEquals(rule.getDistrictDetailList().get(0).getDistrictName(), resp.getDistrictDetailList().get(0).getDistrictName());
    }



    @Test
    public void testGetServiceIdsByDivision_Success223() {
        // Mocking the data for the request
        ServiceIdByDivisionIdsReq request = new ServiceIdByDivisionIdsReq();
        request.setDivisionCode(DivisionTypeEnum.PROVINCE.code);
        Set<String> divisionIdSet = new HashSet<>(Arrays.asList("1", "2", "3"));
        request.setDivisionIdSet(divisionIdSet);


        // Mocking the data for the FeeRule objects
        List<FeeRule> mockFeeRuleList = Arrays.asList(
                mockFeeRule(1L, 101L, "group1", 1, "scene1", "Scene One", "Fee One", createBizRule("1"), createCalculateRuleData("data1")),
                mockFeeRule(2L, 102L, "group2", 2, "scene2", "Scene Two", "Fee Two", createBizRule("2"), createCalculateRuleData("data2")),
                mockFeeRule(3L, 103L, "group3", 3, "scene3", "Scene Three", "Fee Three", createBizRule("3"), createCalculateRuleData("data3"))

        );

        // Mocking MongoDB find operation
        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(mockFeeRuleList);

        // Performing the actual method invocation
        Set<Long> result = feeRuleService.getServiceIdsByDivision(request);

        // Verifying the expected result
        assertNotNull(result);
        assertEquals(new HashSet<>(Arrays.asList(1L, 2L, 3L)), result);

        // Verifying that the MongoDB find method was called with the correct criteria
        verify(mongoTemplate).find(any(Query.class), eq(FeeRule.class));
    }

    @Test
    public void testGetServiceIdsByDivision_EmptyResult223() {
        // Mocking the data for the request
        ServiceIdByDivisionIdsReq request = new ServiceIdByDivisionIdsReq();
        request.setDivisionCode(DivisionTypeEnum.PROVINCE.code);
        Set<String> divisionIdSet = new HashSet<>(Arrays.asList("1", "2", "3"));
        request.setDivisionIdSet(divisionIdSet);

        // Mocking MongoDB find operation to return an empty list
        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(Collections.emptyList());

        // Performing the actual method invocation
        Set<Long> result = feeRuleService.getServiceIdsByDivision(request);

        // Verifying the expected result
        assertNull(result);

        // Verifying that the MongoDB find method was called with the correct criteria
        verify(mongoTemplate).find(any(Query.class), eq(FeeRule.class));
    }


    @Test
    public void testGetAttributeValuePriceByServiceId_ServiceIdInvalid_ReturnsEmptyMap223() {
        Long serviceId = null;
        Long userId = 1L;

        Map<String, AttributeValuePriceResp> result = feeRuleServiceImpl.getAttributeValuePriceByServiceId(serviceId, userId);

        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetAttributeValuePriceByServiceId_WithUserId_ReturnsExpectedMap223() {
        Long serviceId = 1L;
        Long userId = 1L;

        // Mocking the response for user-specific fee rule
        FeeRule userSpecificFeeRule = createUserSpecificFeeRule(serviceId, userId);
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(Lists.newArrayList(userSpecificFeeRule));

        Map<String, AttributeValuePriceResp> result = feeRuleServiceImpl.getAttributeValuePriceByServiceId(serviceId, userId);

        assertEquals(1, result.size());
        assertEquals(userSpecificFeeRule.getBizRule().get("feeName"), result.get("testSkuNo").getFeeName());
    }

    @Test
    public void testGetAttributeValuePriceByServiceId_WithoutUserId_ReturnsExpectedMap223() {
        Long serviceId = 1L;
        Long userId = null;

        // Mocking the response for general fee rule
        FeeRule generalFeeRule = createGeneralFeeRule(serviceId);
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(Lists.newArrayList((generalFeeRule)));

        Map<String, AttributeValuePriceResp> result = feeRuleServiceImpl.getAttributeValuePriceByServiceId(serviceId, userId);

        assertEquals(1, result.size());
        assertEquals(generalFeeRule.getBizRule().get("feeName"), result.get("testSkuNo").getFeeName());
    }



    @Test
    public void getBargainPriceEverydayFeeRuleByServiceId_WithValidData_ShouldReturnExpectedResults223() {
        // Arrange
        BargainPriceEverydayFeeRuleReq req = new BargainPriceEverydayFeeRuleReq();
        req.setServiceId("testServiceId");
        req.setSceneCode("testSceneCode");
        req.setSkuNo("testSkuNo");
        req.setLevel2DivisionId("testLevel2DivisionId");
        req.setPageNum(1);
        req.setPageSize(10);

        List<BargainPriceEverydayFeeRule> mockResults = new ArrayList<>();
        BargainPriceEverydayFeeRule rule = new BargainPriceEverydayFeeRule();
        rule.setServiceId("testServiceId");
        rule.setSceneCode("testSceneCode");
        rule.setSkuNo("testSkuNo");
        rule.setLevel2DivisionId("testLevel2DivisionId");
        rule.setDiscountPrice(100.0);
        rule.setOriginPrice(200.0);
        rule.setSavingPrice(100.0);
        mockResults.add(rule);

        AggregationResults<BargainPriceEverydayFeeRule> aggregationResults = new AggregationResults<>(mockResults, null);
        when(mongoTemplate.aggregate(any(Aggregation.class), any(String.class), any(Class.class))).thenReturn(aggregationResults);

        // Act
        SimplePageInfo<BargainPriceEverydayFeeRuleResp> result = feeRuleService.getBargainPriceEverydayFeeRuleByServiceId(req);

        // Assert
        assertEquals(1, result.getPages());
        assertEquals(1, result.getPageNum());
        assertEquals(10, result.getPageSize());
        assertEquals(1L, result.getTotal());
        assertEquals(1, result.getList().size());
        assertEquals(100.0, result.getList().get(0).getDiscountPrice().doubleValue(), 0.0);
        assertEquals(200.0, result.getList().get(0).getOriginPrice().doubleValue(), 0.0);
        assertEquals(100.0, result.getList().get(0).getSavingPrice().doubleValue(), 0.0);
    }

    @Test
    public void getBargainPriceEverydayFeeRuleByServiceId_NoResults_ShouldReturnEmptyPage213() {
        // Arrange
        BargainPriceEverydayFeeRuleReq req = new BargainPriceEverydayFeeRuleReq();
        req.setServiceId("testServiceId");
        req.setSceneCode("testSceneCode");
        req.setSkuNo("testSkuNo");
        req.setLevel2DivisionId("testLevel2DivisionId");
        req.setPageNum(1);
        req.setPageSize(10);

        AggregationResults<BargainPriceEverydayFeeRule> aggregationResults = new AggregationResults<>(new ArrayList<>(), null);
        when(mongoTemplate.aggregate(any(Aggregation.class), any(String.class), any(Class.class))).thenReturn(aggregationResults);

        // Act
        SimplePageInfo<BargainPriceEverydayFeeRuleResp> result = feeRuleService.getBargainPriceEverydayFeeRuleByServiceId(req);

        // Assert
        assertEquals(0, result.getList().size());
        assertEquals(0L, result.getTotal());
    }

    @Test
    public void getPreGenerateDistrictDetail_InputDataInvalid_ReturnsNull213() {
        DistrictDetailReq req = new DistrictDetailReq();
        req.setServiceId("validServiceId");
        req.setSkuNo("validSkuNo");
        req.setLevel2DivisionId("validLevel2DivisionId");
        req.setCityName("validCityName");

        // Missing sceneCode setup

        DistrictDetailResp result = feeRuleService.getPreGenerateDistrictDetail(req);

        assertNull(result);
    }

    @Test
    public void getPreGenerateDistrictDetail_NoMatchingFeeRules_ReturnsNull213() {
        DistrictDetailReq req = new DistrictDetailReq();
        req.setServiceId("validServiceId");
        req.setSkuNo("validSkuNo");
        req.setLevel2DivisionId("validLevel2DivisionId");
        req.setCityName("validCityName");
        req.setSceneCode(SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.getCode());

        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(new ArrayList<>());

        DistrictDetailResp result = feeRuleService.getPreGenerateDistrictDetail(req);

        assertNull(result);
    }

    @Test
    public void getPreGenerateDistrictDetail_ValidInput_ReturnsDistrictDetailResp213() {
        DistrictDetailReq req = new DistrictDetailReq();
        req.setServiceId("validServiceId");
        req.setSkuNo("validSkuNo");
        req.setLevel2DivisionId("validLevel2DivisionId");
        req.setCityName("validCityName");
        req.setSceneCode(SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.getCode());

        List<FeeRule> feeRuleList = new ArrayList<>();
        FeeRule feeRule = new FeeRule();
        Map<String, String> bizRule = new HashMap<>();
        bizRule.put(CommonBizRule.Fields.level3DivisionId, "level3Id");
        bizRule.put(CommonBizRule.Fields.level4DivisionId, "level4Id");
        feeRule.setBizRule(bizRule);
        feeRuleList.add(feeRule);

        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(feeRuleList);

        Address address = new Address();
        address.setDivisionId(Long.parseLong("level3Id"));
        address.setDivisionName("level3Name");

        Address address1 = new Address();
        address1.setDivisionId(Long.parseLong("level4Id"));
        address1.setDivisionName("level4Name");

        List<Address> addressList = new ArrayList<>();
        addressList.add(address);
        addressList.add(address1);

        when(addressApi.getDivisionInfoListByDivisionIds(anyString())).thenReturn(addressList);

        DistrictDetailResp result = feeRuleService.getPreGenerateDistrictDetail(req);

        assertNotNull(result);
        assertEquals(req.getCityName(), result.getCityName());
        assertNotNull(result.getDistrictDetailList());
        assertEquals(1, result.getDistrictDetailList().size());

        DistrictDetailResp.DistrictDetail districtDetail = result.getDistrictDetailList().get(0);
        assertEquals("level3Name", districtDetail.getDistrictName());
        assertNotNull(districtDetail.getStreetNameList());
        assertEquals(1, districtDetail.getStreetNameList().size());
        assertEquals("level4Name", districtDetail.getStreetNameList().get(0));
    }

    @Test
    public void getDistrictDetail_RuleIsNull_ReturnsNull213() {
        when(mongoTemplate.findOne(any(Query.class), any(Class.class))).thenReturn(null);

        DistrictDetailResp resp = feeRuleService.getDistrictDetail(req);

        assertNull(resp);
    }

    @Test
    public void getDistrictDetail_RuleExistsWithEmptyDistrictDetailList_ReturnsEmptyList213() {
        rule.setDistrictDetailList(new ArrayList<>());
        when(mongoTemplate.findOne(any(Query.class), any(Class.class))).thenReturn(rule);

        DistrictDetailResp resp = feeRuleService.getDistrictDetail(req);

        assertEquals(rule.getCityName(), resp.getCityName());
        assertEquals(0, resp.getDistrictDetailList().size());
    }

    @Test
    public void getDistrictDetail_RuleExistsWithNonEmptyDistrictDetailList_ReturnsMappedList213() {
        when(mongoTemplate.findOne(any(Query.class), any(Class.class))).thenReturn(rule);

        DistrictDetailResp resp = feeRuleService.getDistrictDetail(req);

        assertEquals(rule.getCityName(), resp.getCityName());
        assertEquals(1, resp.getDistrictDetailList().size());
        assertEquals(rule.getDistrictDetailList().get(0).getDistrictName(), resp.getDistrictDetailList().get(0).getDistrictName());
    }



    @Test
    public void testGetServiceIdsByDivision_Success23() {
        // Mocking the data for the request
        ServiceIdByDivisionIdsReq request = new ServiceIdByDivisionIdsReq();
        request.setDivisionCode(DivisionTypeEnum.PROVINCE.code);
        Set<String> divisionIdSet = new HashSet<>(Arrays.asList("1", "2", "3"));
        request.setDivisionIdSet(divisionIdSet);


        // Mocking the data for the FeeRule objects
        List<FeeRule> mockFeeRuleList = Arrays.asList(
                mockFeeRule(1L, 101L, "group1", 1, "scene1", "Scene One", "Fee One", createBizRule("1"), createCalculateRuleData("data1")),
                mockFeeRule(2L, 102L, "group2", 2, "scene2", "Scene Two", "Fee Two", createBizRule("2"), createCalculateRuleData("data2")),
                mockFeeRule(3L, 103L, "group3", 3, "scene3", "Scene Three", "Fee Three", createBizRule("3"), createCalculateRuleData("data3"))

        );

        // Mocking MongoDB find operation
        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(mockFeeRuleList);

        // Performing the actual method invocation
        Set<Long> result = feeRuleService.getServiceIdsByDivision(request);

        // Verifying the expected result
        assertNotNull(result);
        assertEquals(new HashSet<>(Arrays.asList(1L, 2L, 3L)), result);

        // Verifying that the MongoDB find method was called with the correct criteria
        verify(mongoTemplate).find(any(Query.class), eq(FeeRule.class));
    }

    @Test
    public void testGetServiceIdsByDivision_EmptyResult23() {
        // Mocking the data for the request
        ServiceIdByDivisionIdsReq request = new ServiceIdByDivisionIdsReq();
        request.setDivisionCode(DivisionTypeEnum.PROVINCE.code);
        Set<String> divisionIdSet = new HashSet<>(Arrays.asList("1", "2", "3"));
        request.setDivisionIdSet(divisionIdSet);

        // Mocking MongoDB find operation to return an empty list
        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(Collections.emptyList());

        // Performing the actual method invocation
        Set<Long> result = feeRuleService.getServiceIdsByDivision(request);

        // Verifying the expected result
        assertNull(result);

        // Verifying that the MongoDB find method was called with the correct criteria
        verify(mongoTemplate).find(any(Query.class), eq(FeeRule.class));
    }


    @Test
    public void testGetAttributeValuePriceByServiceId_ServiceIdInvalid_ReturnsEmptyMap23() {
        Long serviceId = null;
        Long userId = 1L;

        Map<String, AttributeValuePriceResp> result = feeRuleServiceImpl.getAttributeValuePriceByServiceId(serviceId, userId);

        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetAttributeValuePriceByServiceId_WithUserId_ReturnsExpectedMap23() {
        Long serviceId = 1L;
        Long userId = 1L;

        // Mocking the response for user-specific fee rule
        FeeRule userSpecificFeeRule = createUserSpecificFeeRule(serviceId, userId);
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(Lists.newArrayList(userSpecificFeeRule));

        Map<String, AttributeValuePriceResp> result = feeRuleServiceImpl.getAttributeValuePriceByServiceId(serviceId, userId);

        assertEquals(1, result.size());
        assertEquals(userSpecificFeeRule.getBizRule().get("feeName"), result.get("testSkuNo").getFeeName());
    }

    @Test
    public void testGetAttributeValuePriceByServiceId_WithoutUserId_ReturnsExpectedMap23() {
        Long serviceId = 1L;
        Long userId = null;

        // Mocking the response for general fee rule
        FeeRule generalFeeRule = createGeneralFeeRule(serviceId);
        when(mongoTemplate.find(any(Query.class), any(Class.class))).thenReturn(Lists.newArrayList((generalFeeRule)));

        Map<String, AttributeValuePriceResp> result = feeRuleServiceImpl.getAttributeValuePriceByServiceId(serviceId, userId);

        assertEquals(1, result.size());
        assertEquals(generalFeeRule.getBizRule().get("feeName"), result.get("testSkuNo").getFeeName());
    }



    @Test
    public void getBargainPriceEverydayFeeRuleByServiceId_WithValidData_ShouldReturnExpectedResults23() {
        // Arrange
        BargainPriceEverydayFeeRuleReq req = new BargainPriceEverydayFeeRuleReq();
        req.setServiceId("testServiceId");
        req.setSceneCode("testSceneCode");
        req.setSkuNo("testSkuNo");
        req.setLevel2DivisionId("testLevel2DivisionId");
        req.setPageNum(1);
        req.setPageSize(10);

        List<BargainPriceEverydayFeeRule> mockResults = new ArrayList<>();
        BargainPriceEverydayFeeRule rule = new BargainPriceEverydayFeeRule();
        rule.setServiceId("testServiceId");
        rule.setSceneCode("testSceneCode");
        rule.setSkuNo("testSkuNo");
        rule.setLevel2DivisionId("testLevel2DivisionId");
        rule.setDiscountPrice(100.0);
        rule.setOriginPrice(200.0);
        rule.setSavingPrice(100.0);
        mockResults.add(rule);

        AggregationResults<BargainPriceEverydayFeeRule> aggregationResults = new AggregationResults<>(mockResults, null);
        when(mongoTemplate.aggregate(any(Aggregation.class), any(String.class), any(Class.class))).thenReturn(aggregationResults);

        // Act
        SimplePageInfo<BargainPriceEverydayFeeRuleResp> result = feeRuleService.getBargainPriceEverydayFeeRuleByServiceId(req);

        // Assert
        assertEquals(1, result.getPages());
        assertEquals(1, result.getPageNum());
        assertEquals(10, result.getPageSize());
        assertEquals(1L, result.getTotal());
        assertEquals(1, result.getList().size());
        assertEquals(100.0, result.getList().get(0).getDiscountPrice().doubleValue(), 0.0);
        assertEquals(200.0, result.getList().get(0).getOriginPrice().doubleValue(), 0.0);
        assertEquals(100.0, result.getList().get(0).getSavingPrice().doubleValue(), 0.0);
    }

    @Test
    public void getBargainPriceEverydayFeeRuleByServiceId_NoResults_ShouldReturnEmptyPage23() {
        // Arrange
        BargainPriceEverydayFeeRuleReq req = new BargainPriceEverydayFeeRuleReq();
        req.setServiceId("testServiceId");
        req.setSceneCode("testSceneCode");
        req.setSkuNo("testSkuNo");
        req.setLevel2DivisionId("testLevel2DivisionId");
        req.setPageNum(1);
        req.setPageSize(10);

        AggregationResults<BargainPriceEverydayFeeRule> aggregationResults = new AggregationResults<>(new ArrayList<>(), null);
        when(mongoTemplate.aggregate(any(Aggregation.class), any(String.class), any(Class.class))).thenReturn(aggregationResults);

        // Act
        SimplePageInfo<BargainPriceEverydayFeeRuleResp> result = feeRuleService.getBargainPriceEverydayFeeRuleByServiceId(req);

        // Assert
        assertEquals(0, result.getList().size());
        assertEquals(0L, result.getTotal());
    }

    @Test
    public void getPreGenerateDistrictDetail_InputDataInvalid_ReturnsNull23() {
        DistrictDetailReq req = new DistrictDetailReq();
        req.setServiceId("validServiceId");
        req.setSkuNo("validSkuNo");
        req.setLevel2DivisionId("validLevel2DivisionId");
        req.setCityName("validCityName");

        // Missing sceneCode setup

        DistrictDetailResp result = feeRuleService.getPreGenerateDistrictDetail(req);

        assertNull(result);
    }

    @Test
    public void getPreGenerateDistrictDetail_NoMatchingFeeRules_ReturnsNull23() {
        DistrictDetailReq req = new DistrictDetailReq();
        req.setServiceId("validServiceId");
        req.setSkuNo("validSkuNo");
        req.setLevel2DivisionId("validLevel2DivisionId");
        req.setCityName("validCityName");
        req.setSceneCode(SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.getCode());

        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(new ArrayList<>());

        DistrictDetailResp result = feeRuleService.getPreGenerateDistrictDetail(req);

        assertNull(result);
    }

    @Test
    public void getPreGenerateDistrictDetail_ValidInput_ReturnsDistrictDetailResp23() {
        DistrictDetailReq req = new DistrictDetailReq();
        req.setServiceId("validServiceId");
        req.setSkuNo("validSkuNo");
        req.setLevel2DivisionId("validLevel2DivisionId");
        req.setCityName("validCityName");
        req.setSceneCode(SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.getCode());

        List<FeeRule> feeRuleList = new ArrayList<>();
        FeeRule feeRule = new FeeRule();
        Map<String, String> bizRule = new HashMap<>();
        bizRule.put(CommonBizRule.Fields.level3DivisionId, "level3Id");
        bizRule.put(CommonBizRule.Fields.level4DivisionId, "level4Id");
        feeRule.setBizRule(bizRule);
        feeRuleList.add(feeRule);

        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(feeRuleList);

        Address address = new Address();
        address.setDivisionId(Long.parseLong("level3Id"));
        address.setDivisionName("level3Name");

        Address address1 = new Address();
        address1.setDivisionId(Long.parseLong("level4Id"));
        address1.setDivisionName("level4Name");

        List<Address> addressList = new ArrayList<>();
        addressList.add(address);
        addressList.add(address1);

        when(addressApi.getDivisionInfoListByDivisionIds(anyString())).thenReturn(addressList);

        DistrictDetailResp result = feeRuleService.getPreGenerateDistrictDetail(req);

        assertNotNull(result);
        assertEquals(req.getCityName(), result.getCityName());
        assertNotNull(result.getDistrictDetailList());
        assertEquals(1, result.getDistrictDetailList().size());

        DistrictDetailResp.DistrictDetail districtDetail = result.getDistrictDetailList().get(0);
        assertEquals("level3Name", districtDetail.getDistrictName());
        assertNotNull(districtDetail.getStreetNameList());
        assertEquals(1, districtDetail.getStreetNameList().size());
        assertEquals("level4Name", districtDetail.getStreetNameList().get(0));
    }

    @Test
    public void getDistrictDetail_RuleIsNull_ReturnsNull23() {
        when(mongoTemplate.findOne(any(Query.class), any(Class.class))).thenReturn(null);

        DistrictDetailResp resp = feeRuleService.getDistrictDetail(req);

        assertNull(resp);
    }

    @Test
    public void getDistrictDetail_RuleExistsWithEmptyDistrictDetailList_ReturnsEmptyList23() {
        rule.setDistrictDetailList(new ArrayList<>());
        when(mongoTemplate.findOne(any(Query.class), any(Class.class))).thenReturn(rule);

        DistrictDetailResp resp = feeRuleService.getDistrictDetail(req);

        assertEquals(rule.getCityName(), resp.getCityName());
        assertEquals(0, resp.getDistrictDetailList().size());
    }

    @Test
    public void getDistrictDetail_RuleExistsWithNonEmptyDistrictDetailList_ReturnsMappedList23() {
        when(mongoTemplate.findOne(any(Query.class), any(Class.class))).thenReturn(rule);

        DistrictDetailResp resp = feeRuleService.getDistrictDetail(req);

        assertEquals(rule.getCityName(), resp.getCityName());
        assertEquals(1, resp.getDistrictDetailList().size());
        assertEquals(rule.getDistrictDetailList().get(0).getDistrictName(), resp.getDistrictDetailList().get(0).getDistrictName());
    }
    /*****2024-10-31 end */






    private FeeRule createUserSpecificFeeRule2(Long serviceId, Long userId) {
        Map<String, String> bizRule = new HashMap<>();
        bizRule.put("serviceId", String.valueOf(serviceId));
        bizRule.put("userId", String.valueOf(userId));
        bizRule.put("divisionType", DivisionTypeEnum.COUNTRY.code);
        bizRule.put("feeName", "testFeeName");
        bizRule.put("masterInputPrice", "100");
        bizRule.put("feeUnit", "元");
        bizRule.put("skuNo", "testSkuNo");

        FeeRule feeRule = new FeeRule();
        feeRule.setBizRule(bizRule);
        feeRule.setSceneCode(SceneCodeEnum.PLATFORM_FIXED_PRICE_USER.getCode());
        feeRule.setDel(false);
        feeRule.setStatus(TemplateStatusEnum.ACTIVE.code);

        return feeRule;
    }

    private FeeRule createGeneralFeeRule2(Long serviceId) {
        Map<String, String> bizRule = new HashMap<>();
        bizRule.put("serviceId", String.valueOf(serviceId));
        bizRule.put("divisionType", DivisionTypeEnum.COUNTRY.code);
        bizRule.put("feeName", "testFeeName");
        bizRule.put("masterInputPrice", "100");
        bizRule.put("feeUnit", "元");
        bizRule.put("skuNo", "testSkuNo");

        FeeRule feeRule = new FeeRule();
        feeRule.setBizRule(bizRule);
        feeRule.setSceneCode(SceneCodeEnum.PLATFORM_FIXED_PRICE.getCode());
        feeRule.setDel(false);
        feeRule.setStatus(TemplateStatusEnum.ACTIVE.code);

        return feeRule;
    }




    // Helper method to create a mock BizRule
    private Map<String, String> createBizRule(String serviceId) {
        Map<String, String> bizRule = new HashMap<>();
        bizRule.put(AutoReceiveOrderGuidePriceBizRule.Fields.serviceId, serviceId);
        return bizRule;
    }

    private FeeRule mockFeeRule(Long feeRuleId, Long templateId, String group, int templateVersion,
                                String sceneCode, String sceneName, String feeName,
                                Map<String, String> bizRule, CalculateRuleData calculateRuleData) {
        FeeRule feeRule = new FeeRule();
        feeRule.setFeeRuleId(feeRuleId);
        feeRule.setTemplateId(templateId);
        feeRule.setGroup(group);
        feeRule.setTemplateVersion(templateVersion);
        feeRule.setSceneCode(sceneCode);
        feeRule.setSceneName(sceneName);
        feeRule.setFeeName(feeName);
        feeRule.setBizRule(bizRule);
        feeRule.setCalculateRuleData(calculateRuleData);
        return feeRule;
    }

    // Helper method to create a mock CalculateRuleData
    private CalculateRuleData createCalculateRuleData(String data) {
        CalculateRuleData calculateRuleData = new CalculateRuleData();
        // Set properties for CalculateRuleData as needed
        calculateRuleData.setExpress(data);
        return calculateRuleData;
    }

}
