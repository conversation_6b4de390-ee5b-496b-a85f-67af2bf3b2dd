package com.wanshifu.service;

import com.wanshifu.fee.center.domain.document.CalculateRuleData;
import com.wanshifu.fee.center.domain.document.Formula;
import com.wanshifu.fee.center.domain.dto.ExpressInfo;
import com.wanshifu.repository.FormulaRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

/**
 * @author: <PERSON>
 * @create: 2024-03-07 11:04
 * @description: 公式编译
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class CalculateRuleDataTest {

    @Mock
    private FormulaRepository formulaRepository;

    @Test
    public void testHandleFormulas() {
        MockitoAnnotations.initMocks(this);

        CalculateRuleData calculateRuleData = new CalculateRuleData();
        calculateRuleData.setFormulaIds(Arrays.asList("1", "2"));

        Formula formula1 = new Formula();
        formula1.setFormulaId(1L);
        ExpressInfo expressInfo = new ExpressInfo();
        expressInfo.setExpressInfoUnits(new ArrayList<>());
        calculateRuleData.setExpressInfo(expressInfo);
        formula1.setFormulaContent(calculateRuleData);

        Formula formula2 = new Formula();
        formula2.setFormulaId(2L);
        ExpressInfo expressInfo2 = new ExpressInfo();
        expressInfo2.setExpressInfoUnits(new ArrayList<>());
        calculateRuleData.setExpressInfo(expressInfo2);
        formula2.setFormulaContent(calculateRuleData);
        when(formulaRepository.findByFormulaId(1L)).thenReturn(formula1);
        when(formulaRepository.findByFormulaId(2L)).thenReturn(formula2);

//        handleFormulas(calculateRuleData);

        assertEquals("expectedValue", calculateRuleData.getExpress());
        // Add more assertions based on the expected behavior of the method
    }

    @Test
    public void testHandleFormulasWithNullCalculateRuleData() {
        MockitoAnnotations.initMocks(this);

        CalculateRuleData calculateRuleData = null;

//        handleFormulas(calculateRuleData);

        // Add assertions to verify the behavior when calculateRuleData is null
    }

    @Test
    public void testHandleFormulasWithEmptyFormulaList() {
        MockitoAnnotations.initMocks(this);

        CalculateRuleData calculateRuleData = new CalculateRuleData();
        calculateRuleData.setFormulaIds(new ArrayList<>());

//        handleFormulas(calculateRuleData);

        // Add assertions to verify the behavior when formulaList is empty
    }

    @Test
    public void testHandleFormulasWithException() {
        MockitoAnnotations.initMocks(this);

        CalculateRuleData calculateRuleData = new CalculateRuleData();
        calculateRuleData.setFormulaIds(Arrays.asList("1"));

        when(formulaRepository.findByFormulaId(1L)).thenThrow(new RuntimeException("Error"));

        // Add assertions to verify the behavior when an exception is thrown
    }
}
