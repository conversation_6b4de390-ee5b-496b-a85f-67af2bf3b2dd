package com.wanshifu.service;

import com.wanshifu.fee.center.domain.document.DynamicFeeRule;
import com.wanshifu.fee.center.domain.enums.DynamicFeeRuleStatusEnum;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.repository.DynamicFeeRuleRepository;
import com.wanshifu.repository.FeeRuleRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;
import static org.springframework.test.util.AssertionErrors.assertEquals;

@RunWith(SpringRunner.class)
@SpringBootTest
public class DynamicRuleTest {

    @Mock
    private DynamicFeeRuleRepository dynamicFeeRuleRepository;

    @Mock
    private MongoTemplate mongoTemplate;

    @Mock
    private FeeRuleRepository feeRuleRepository;

    @InjectMocks
    private DynamicFeeRuleServiceImpl dynamicFeeRuleService;

    // Additional setup not covered in original code, if needed
    @BeforeEach
    void setUp() {
        // Setup common mock behaviors and objects here
    }

    @Test
    void auditShouldThrowExceptionWhenDynamicFeeRuleDoesNotExist() {
        Long nonExistentId = 999L;
        when(mongoTemplate.findOne(any(Query.class), eq(DynamicFeeRule.class))).thenReturn(null);

        assertThrows(BusException.class, () ->
                        dynamicFeeRuleService.audit(nonExistentId, DynamicFeeRuleStatusEnum.PASS),
                "Expected audit to throw, but it didn't");
    }

    @Test
    void auditShouldUpdateDynamicFeeRuleStatus() {
        DynamicFeeRule dynamicFeeRule = new DynamicFeeRule();
        dynamicFeeRule.setDynamicFeeRuleId(1L);
        dynamicFeeRule.setStatus(DynamicFeeRuleStatusEnum.AUDIT.code);

        when(mongoTemplate.findOne(any(Query.class), eq(DynamicFeeRule.class))).thenReturn(dynamicFeeRule);

        DynamicFeeRule updatedRule = dynamicFeeRuleService.audit(1L, DynamicFeeRuleStatusEnum.PASS);

        assertNotNull(updatedRule);
        assertEquals(DynamicFeeRuleStatusEnum.PASS.code + "" , updatedRule.getStatus(), "Status should be updated to PASS");
    }

    // Example of an additional edge case test
    @Test
    void auditShouldNotUpdateWhenStatusIsNotAudit() {
        DynamicFeeRule dynamicFeeRule = new DynamicFeeRule();
        dynamicFeeRule.setDynamicFeeRuleId(1L);
        dynamicFeeRule.setStatus(DynamicFeeRuleStatusEnum.PASS.code);

        when(mongoTemplate.findOne(any(Query.class), eq(DynamicFeeRule.class))).thenReturn(dynamicFeeRule);

        DynamicFeeRule result = dynamicFeeRuleService.audit(1L, DynamicFeeRuleStatusEnum.PASS);

        assertEquals(DynamicFeeRuleStatusEnum.PASS.code + "", result.getStatus(), "Status should not be updated since it's not in audit state.");
    }



}
