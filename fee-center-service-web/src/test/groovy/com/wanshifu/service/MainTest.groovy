package com.wanshifu.service

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.serializer.SerializerFeature
import com.wanshifu.domain.request.SceneInfoQueryPageReq
import com.wanshifu.fee.center.domain.document.DynamicCalculateRuleData
import com.wanshifu.fee.center.domain.document.DynamicFeeRule
import com.wanshifu.fee.center.domain.document.FeeRuleDraft
import com.wanshifu.fee.center.domain.document.SceneInfo
import com.wanshifu.fee.center.domain.dto.ExpressInfo
import com.wanshifu.fee.center.domain.request.DynamicFeeRulePageReq
import com.wanshifu.fee.center.domain.request.DynamicFeeRuleSaveReq

class MainTest {
    static void main(String[] args) {
//        def dynamic = DynamicFeeRule.newInstance()
//        dynamic.dynamicCalculateRuleData = DynamicCalculateRuleData.newInstance()
//        println JSON.toJSONString(dynamic,SerializerFeature.WriteNullListAsEmpty,SerializerFeature.WriteNullStringAsEmpty,SerializerFeature.WriteNullNumberAsZero,SerializerFeature.DisableCircularReferenceDetect)
//        println JSON.toJSONString(DynamicFeeRuleSaveReq.newInstance(),SerializerFeature.WriteNullListAsEmpty,SerializerFeature.WriteNullStringAsEmpty,SerializerFeature.WriteNullNumberAsZero,SerializerFeature.DisableCircularReferenceDetect)
//        def express= ExpressInfo.newInstance()
//        def unit =  ExpressInfo.ExpressInfoUnit.newInstance()
//        unit.condition = ExpressInfo.Condition.newInstance()
//        express.expressInfoUnits = [unit]
//        println JSON.toJSONString(express,SerializerFeature.WriteNullListAsEmpty,SerializerFeature.WriteNullStringAsEmpty,SerializerFeature.WriteNullNumberAsZero,SerializerFeature.DisableCircularReferenceDetect)
//
//        def str= JSON.toJSONString(SceneInfoQueryPageReq.newInstance(),SerializerFeature.WriteNullListAsEmpty,SerializerFeature.WriteNullStringAsEmpty,SerializerFeature.WriteNullNumberAsZero,SerializerFeature.DisableCircularReferenceDetect)
//        println(str)
//
//        println JSON.toJSONString(SceneInfo.newInstance(),SerializerFeature.WriteNullStringAsEmpty,SerializerFeature.WriteNullNumberAsZero,SerializerFeature.DisableCircularReferenceDetect)


        // 创建一个ArrayList，并添加一些元素
        List<Integer> firstList = new ArrayList<>(Arrays.asList(1, 2, 3, 4, 5));

        // 创建另一个列表作为比较对象
        List<Integer> secondList = Arrays.asList(2, 4, 6, 8);

        // 使用retainAll来修改firstList，只保留同时也在secondList中的元素
        boolean result = firstList.retainAll(secondList);
        System.out.println("Was the first list modified? " + result);
        System.out.println("The first list is now: " + firstList);

        // 再次尝试，此时firstList已经只有2和4
        result = firstList.retainAll(secondList);
        System.out.println("Was the first list modified again? " + result);
        System.out.println("The first list is still: " + firstList);
    }
}
