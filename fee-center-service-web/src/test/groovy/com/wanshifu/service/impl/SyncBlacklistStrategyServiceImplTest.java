package com.wanshifu.service.impl;

import com.wanshifu.fee.center.domain.document.SyncBlacklistStrategy;
import org.junit.Test;
import org.junit.Before;
import org.junit.After;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * SyncBlacklistStrategyServiceImpl Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>08/14/2025</pre>
 */
@SpringBootTest()
@RunWith(SpringRunner.class)
public class SyncBlacklistStrategyServiceImplTest {

    @Resource
    private SyncBlacklistStrategyServiceImpl syncBlacklistStrategyServiceImpl;

    @Before
    public void before() throws Exception {
    }

    @After
    public void after() throws Exception {
    }

    /**
     * Method: add(SyncBlacklistStrategySaveReq req)
     */
    @Test
    public void testAdd() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: modify(SyncBlacklistStrategySaveReq req)
     */
    @Test
    public void testModify() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: delete(Long strategyId)
     */
    @Test
    public void testDelete() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: batchUpdateStatus(SyncBlacklistStrategyBatchUpdateStatusReq req)
     */
    @Test
    public void testBatchUpdateStatus() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: findPage(SyncBlacklistStrategyPageReq req)
     */
    @Test
    public void testFindPage() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: selectListBySceneCode(String sceneCode)
     */
    @Test
    public void testSelectListBySceneCode() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: detail(Long strategyId)
     */
    @Test
    public void testDetail() throws Exception {
//TODO: Test goes here... 
    }

    /**
     * Method: selectBySceneCodeAndServiceIdAndUserId(String sceneCode, Long serviceId, String userId)
     */
    @Test
    public void testSelectBySceneCodeAndServiceIdAndUserId() throws Exception {
//TODO: Test goes here...
        List<SyncBlacklistStrategy> blacklistStrategyList = syncBlacklistStrategyServiceImpl.selectBySceneCodeAndServiceIdAndUserId("platform_fixed_price_user", 89504868L, "777");
        System.out.println(blacklistStrategyList);
        List<SyncBlacklistStrategy> blacklistStrategyList2 = syncBlacklistStrategyServiceImpl.selectBySceneCodeAndServiceIdAndUserId("platform_fixed_price_user", 89504868L, "1777");
        System.out.println(blacklistStrategyList2);
    }


} 
