package com.wanshifu.service.impl.indicator

import com.wanshifu.fee.center.domain.document.DynamicPricingIndicator
import com.wanshifu.fee.center.domain.enums.AdjustPriceIndicatorEnum
import com.wanshifu.fee.center.domain.enums.IndicatorOptionalConditionEnum
import com.wanshifu.fee.center.domain.response.bigdata.BigDataResp
import com.wanshifu.fee.center.domain.response.bigdata.OrderAvgPushDistance
import com.wanshifu.infrastructure.gateway.BigdataGateway
import com.wanshifu.service.impl.IndicatorContext
import spock.lang.Specification

class AveragePushDistanceIndicatorProviderTest extends Specification {

    def selector = Mock(IndicatorValueSelector)
    def gateway = Mock(BigdataGateway)
    def provider = new AveragePushDistanceIndicatorProvider(gateway, selector)

    def "supports 方法应该只对 AVERAGE_PUSH_DISTANCE 类型返回 true"() {
        expect:
        provider.supports(AdjustPriceIndicatorEnum.AVERAGE_PUSH_DISTANCE) == true
        provider.supports(AdjustPriceIndicatorEnum.FIXED) == false
    }

    def "当成功从大数据获取距离后，应正确过滤并调用选择器"() {
        given: "一个带订单号的上下文"
        def context = Mock(IndicatorContext)
        context.getOrderNo() >> "test-order-123"

        and: "大数据网关成功返回平均距离"
        def avgDistance = new OrderAvgPushDistance(avgPushDistance: 5000.0) // 5公里
        gateway.getOrderAvgPushDistance("test-order-123") >> new BigDataResp<>(avgDistance)

        and: "一个匹配5公里规则的指标"
        def matchingValue = new DynamicPricingIndicator.IndicatorValue(from: 4000.0, to: 6000.0)
        def indicator = new DynamicPricingIndicator(
                indicatorOptionalCondition: IndicatorOptionalConditionEnum.GREATER_THAN_AND_LESS_THAN_OR_EQUAL_TO.getCode(),
                indicatorValueList: [matchingValue]
        )
        def indicators = [indicator]

        and: "设置选择器的预期行为"
        selector.selectBestValue([matchingValue]) >> Optional.of(matchingValue)

        when:
        def result = provider.getValues(context, indicators)

        then:
        result.size() == 1
        result[0] == matchingValue
    }

    def "当从大数据获取距离失败时，应返回空列表"() {
        given: "一个带订单号的上下文"
        def context = Mock(IndicatorContext)
        context.getOrderNo() >> "test-order-456"

        and: "大数据网关返回错误"
        gateway.getOrderAvgPushDistance("test-order-456") >> new BigDataResp<>(-1, "error")
        def indicators = [new DynamicPricingIndicator()]

        when:
        def result = provider.getValues(context, indicators)

        then:
        result.isEmpty()
        0 * selector.selectBestValue(_) // 验证选择器从未被调用
    }
}
