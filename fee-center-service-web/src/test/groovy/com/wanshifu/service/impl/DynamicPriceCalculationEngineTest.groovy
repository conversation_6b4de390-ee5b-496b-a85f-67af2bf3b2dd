package com.wanshifu.service.impl

import com.wanshifu.fee.center.domain.document.DynamicFeeRule
import com.wanshifu.fee.center.domain.document.DynamicPricingIndicator
import com.wanshifu.fee.center.domain.enums.AdjustPriceIndicatorEnum
import com.wanshifu.fee.center.domain.enums.AdjustPriceTypeEnum
import com.wanshifu.fee.center.domain.enums.DynamicSymbolEnum
import com.wanshifu.fee.center.domain.enums.IndicatorOptionalConditionEnum
import com.wanshifu.fee.center.domain.request.calculate.CalculateServiceDynamicPriceRequest
import com.wanshifu.service.impl.indicator.AutoCalculatedIndicatorProvider
import com.wanshifu.service.impl.indicator.FixedIndicatorProvider
import com.wanshifu.service.impl.indicator.IndicatorProvider
import spock.lang.Specification

import java.math.BigDecimal

class DynamicPriceCalculationEngineTest extends Specification {

    def fixedProvider = Mock(IndicatorProvider)
    def autoProvider = Mock(AutoCalculatedIndicatorProvider)

    // 将所有 provider 放入一个列表中，模拟 Spring 的依赖注入
    def allProviders = [fixedProvider]

    def engine = new DynamicPriceCalculationEngine(allProviders, autoProvider)

    def "引擎应该能正确合并、分发和汇总来自不同提供者的调价结果"() {
        given: "一个计价请求"
        def request = new CalculateServiceDynamicPriceRequest(originalCost: new BigDecimal("100.00"))

        and: "一个包含固定调价规则的规则列表"
        def fixedIndicator = new DynamicPricingIndicator(
                indicatorCode: AdjustPriceIndicatorEnum.FIXED.getCode(),
                indicatorOptionalCondition: IndicatorOptionalConditionEnum.GREATER_THAN_OR_EQUAL_TO.getCode()
        )
        def rule = new DynamicFeeRule(dynamicPricingIndicators: [fixedIndicator])

        and: "自动计算提供者返回一个按距离调价的规则"
        def distanceIndicator = new DynamicPricingIndicator(
                indicatorCode: AdjustPriceIndicatorEnum.AVERAGE_PUSH_DISTANCE.getCode(),
                indicatorOptionalCondition: IndicatorOptionalConditionEnum.GREATER_THAN_OR_EQUAL_TO.getCode()
        )
        autoProvider.fetchAutoCalculatedIndicators(_) >> [distanceIndicator]

        and: "设置固定调价提供者的行为"
        fixedProvider.supports(AdjustPriceIndicatorEnum.FIXED) >> true
        def fixedValue = new DynamicPricingIndicator.IndicatorValue(
                adjustPriceAction: DynamicSymbolEnum.ADD.getCode(),
                adjustPriceType: AdjustPriceTypeEnum.FIXED.getCode(),
                adjustValue: 10.0 // 加10元
        )
        // 模拟固定调价策略返回一个+10元的结果
        fixedProvider.getValues(_, [fixedIndicator]) >> [fixedValue]

        and: "创建一个模拟的距离调价提供者，并加入到引擎的提供者列表中"
        def distanceProvider = Mock(IndicatorProvider)
        distanceProvider.supports(AdjustPriceIndicatorEnum.AVERAGE_PUSH_DISTANCE) >> true
        def distanceValue = new DynamicPricingIndicator.IndicatorValue(
                adjustPriceAction: DynamicSymbolEnum.ADD.getCode(),
                adjustPriceType: AdjustPriceTypeEnum.PERCENT.getCode(),
                adjustValue: 5.0 // 价格增加5%
        )
        // 模拟距离策略返回一个+5%的结果
        distanceProvider.getValues(_, [distanceIndicator]) >> [distanceValue]
        engine.indicatorProviders.add(distanceProvider)

        when: "执行计价"
        def response = engine.calculate(request, [rule])

        then: "最终价格应该是 100 + 10 + (100 * 0.05) = 115.00"
        response.getDynamicFeeCost() == new BigDecimal("115.00")
    }

    def "当没有任何规则生效时，应返回原始价格"() {
        given:
        def request = new CalculateServiceDynamicPriceRequest(originalCost: new BigDecimal("100.00"))
        def rule = new DynamicFeeRule(dynamicPricingIndicators: []) // 规则列表为空

        and:
        autoProvider.fetchAutoCalculatedIndicators(_) >> [] // 自动计算也为空

        when:
        def response = engine.calculate(request, [rule])

        then:
        response.getDynamicFeeCost() == new BigDecimal("100.00")
    }
}
