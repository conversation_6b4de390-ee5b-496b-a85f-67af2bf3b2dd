package com.wanshifu.service.impl.indicator

import com.wanshifu.fee.center.domain.document.DynamicPricingIndicator
import com.wanshifu.fee.center.domain.enums.AdjustPriceTypeEnum
import spock.lang.Specification
import spock.lang.Unroll

class IndicatorValueSelectorTest extends Specification {

    def selector = new IndicatorValueSelector()

    def "当候选列表为空或无效时，应该返回空 Optional"() {
        when:
        def result = selector.selectBestValue(candidates)

        then:
        !result.isPresent()

        where:
        candidates << [null, [], [null]]
    }

    @Unroll
    def "当只有一种调价类型时，应该选择 adjustValue 最小的那个"() {
        given:
        def v1 = new DynamicPricingIndicator.IndicatorValue(adjustPriceType: type, adjustValue: 100.0)
        def v2 = new DynamicPricingIndicator.IndicatorValue(adjustPriceType: type, adjustValue: 50.0)
        def v3 = new DynamicPricingIndicator.IndicatorValue(adjustPriceType: type, adjustValue: 200.0)
        def candidates = [v1, v2, v3]

        when:
        def result = selector.selectBestValue(candidates)

        then:
        result.isPresent()
        result.get() == v2

        where:
        type << [AdjustPriceTypeEnum.FIXED.getCode(), AdjustPriceTypeEnum.PERCENT.getCode()]
    }

    def "当同时存在固定和百分比类型时，应该优先选择百分比类型中最小的那个"() {
        given:
        def fixed1 = new DynamicPricingIndicator.IndicatorValue(adjustPriceType: AdjustPriceTypeEnum.FIXED.getCode(), adjustValue: 10.0) // 最小的固定值
        def fixed2 = new DynamicPricingIndicator.IndicatorValue(adjustPriceType: AdjustPriceTypeEnum.FIXED.getCode(), adjustValue: 20.0)

        def percent1 = new DynamicPricingIndicator.IndicatorValue(adjustPriceType: AdjustPriceTypeEnum.PERCENT.getCode(), adjustValue: 5.0) // 最小的百分比
        def percent2 = new DynamicPricingIndicator.IndicatorValue(adjustPriceType: AdjustPriceTypeEnum.PERCENT.getCode(), adjustValue: 8.0)

        def candidates = [fixed1, fixed2, percent1, percent2]

        when:
        def result = selector.selectBestValue(candidates)

        then:
        result.isPresent()
        result.get() == percent1 // 验证是否优先选择了最小的百分比值
    }

    def "当只有固定类型，但存在 adjustValue 为 null 的情况，应忽略 null 值并选择最小的有效值"() {
        given:
        def v1 = new DynamicPricingIndicator.IndicatorValue(adjustPriceType: AdjustPriceTypeEnum.FIXED.getCode(), adjustValue: 100.0)
        def v2 = new DynamicPricingIndicator.IndicatorValue(adjustPriceType: AdjustPriceTypeEnum.FIXED.getCode(), adjustValue: null) // 无效值
        def v3 = new DynamicPricingIndicator.IndicatorValue(adjustPriceType: AdjustPriceTypeEnum.FIXED.getCode(), adjustValue: 50.0)
        def candidates = [v1, v2, v3]

        when:
        def result = selector.selectBestValue(candidates)

        then:
        result.isPresent()
        result.get() == v3
    }
}
