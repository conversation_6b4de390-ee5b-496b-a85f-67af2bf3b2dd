package com.wanshifu.service.impl.indicator

import com.wanshifu.fee.center.domain.document.DynamicPricingIndicator
import com.wanshifu.fee.center.domain.enums.AdjustPriceIndicatorEnum
import com.wanshifu.service.impl.IndicatorContext
import spock.lang.Specification

class FixedIndicatorProviderTest extends Specification {

    def selector = Mock(IndicatorValueSelector)
    def provider = new FixedIndicatorProvider(selector)

    def "supports 方法应该只对 FIXED 类型返回 true"() {
        expect:
        provider.supports(AdjustPriceIndicatorEnum.FIXED) == true
        provider.supports(AdjustPriceIndicatorEnum.ORDER_TIME_ELAPSED) == false
        provider.supports(AdjustPriceIndicatorEnum.AVERAGE_PUSH_DISTANCE) == false
    }

    def "getValues 方法应该收集所有候选值并调用选择器"() {
        given: "一堆固定调价规则"
        def value1 = new DynamicPricingIndicator.IndicatorValue(adjustValue: 10.0)
        def value2 = new DynamicPricingIndicator.IndicatorValue(adjustValue: 20.0)
        def indicator1 = new DynamicPricingIndicator(indicatorValueList: [value1])
        def indicator2 = new DynamicPricingIndicator(indicatorValueList: [value2])
        def indicators = [indicator1, indicator2]
        def context = Mock(IndicatorContext)

        and: "设置选择器的预期行为"
        // 当选择器被调用时，我们让它返回 value1 作为最优选择
        selector.selectBestValue([value1, value2]) >> Optional.of(value1)

        when: "调用 provider 的 getValues 方法"
        def result = provider.getValues(context, indicators)

        then: "结果应该是选择器返回的最优值"
        result.size() == 1
        result[0] == value1
    }

    def "当选择器没有返回任何值时，getValues 应该返回空列表"() {
        given:
        def value1 = new DynamicPricingIndicator.IndicatorValue(adjustValue: 10.0)
        def indicators = [new DynamicPricingIndicator(indicatorValueList: [value1])]
        def context = Mock(IndicatorContext)

        and:
        // 模拟选择器返回一个空的 Optional
        selector.selectBestValue(_) >> Optional.empty()

        when:
        def result = provider.getValues(context, indicators)

        then:
        result.isEmpty()
    }
}
