package com.wanshifu.service.impl.indicator

import com.wanshifu.fee.center.domain.document.DynamicPricingIndicator
import com.wanshifu.fee.center.domain.enums.AdjustPriceIndicatorEnum
import com.wanshifu.fee.center.domain.enums.IndicatorOptionalConditionEnum
import com.wanshifu.service.impl.IndicatorContext
import spock.lang.Specification

import java.time.LocalDateTime

class OrderTimeElapsedIndicatorProviderTest extends Specification {

    def selector = Mock(IndicatorValueSelector)
    def provider = new OrderTimeElapsedIndicatorProvider(selector)

    def "supports 方法应该只对 ORDER_TIME_ELAPSED 类型返回 true"() {
        expect:
        provider.supports(AdjustPriceIndicatorEnum.ORDER_TIME_ELAPSED) == true
        provider.supports(AdjustPriceIndicatorEnum.FIXED) == false
    }

    def "getValues 方法应该基于时间差过滤并调用选择器"() {
        given: "一个下单时间在2小时前的上下文"
        def context = Mock(IndicatorContext)
        context.getOrderSubmitTime() >> LocalDateTime.now().minusHours(2)

        and: "一个需要匹配2小时以上的规则和一个不匹配的规则"
        def matchingValue = new DynamicPricingIndicator.IndicatorValue(from: 1.0, to: 3.0) // 匹配 [1, 3] 小时
        def nonMatchingValue = new DynamicPricingIndicator.IndicatorValue(from: 3.0, to: 5.0)
        def indicator = new DynamicPricingIndicator(
                indicatorOptionalCondition: IndicatorOptionalConditionEnum.GREATER_THAN_AND_LESS_THAN_OR_EQUAL_TO.getCode(),
                indicatorValueList: [matchingValue, nonMatchingValue]
        )
        def indicators = [indicator]

        and: "设置选择器的预期行为"
        // 期望选择器接收到的是经过时间过滤后的唯一候选值
        selector.selectBestValue([matchingValue]) >> Optional.of(matchingValue)

        when:
        def result = provider.getValues(context, indicators)

        then:
        result.size() == 1
        result[0] == matchingValue
    }

    def "当上下文没有下单时间时，应返回空列表"() {
        given:
        def context = Mock(IndicatorContext)
        context.getOrderSubmitTime() >> null // 模拟没有下单时间
        def indicators = [new DynamicPricingIndicator()]

        when:
        def result = provider.getValues(context, indicators)

        then:
        result.isEmpty()
        0 * selector.selectBestValue(_) // 验证选择器从未被调用
    }
}
