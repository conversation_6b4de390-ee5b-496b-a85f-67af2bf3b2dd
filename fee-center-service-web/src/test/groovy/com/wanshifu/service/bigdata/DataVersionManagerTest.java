package com.wanshifu.service.bigdata;

import com.wanshifu.fee.center.domain.response.bigdata.InsertTimestamp;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.infrastructure.gateway.BigdataGateway;
import com.wanshifu.service.bigdata.impl.DataVersionManagerImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 数据版本管理器测试
 * 
 * <AUTHOR> Assistant
 */
@ExtendWith(MockitoExtension.class)
class DataVersionManagerTest {
    
    @Mock
    private RedisHelper redisHelper;
    
    @Mock
    private BigdataGateway bigdataGateway;
    
    @InjectMocks
    private DataVersionManagerImpl dataVersionManager;
    
    private static final String SCENE_CODE = "bargain_price_everyday";
    private static final String VERSION_1 = "1234567890";
    private static final String VERSION_2 = "1234567891";
    
    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }
    
    @Test
    void testIsVersionChanged_WhenVersionsAreDifferent_ShouldReturnTrue() {
        // Given
        when(redisHelper.get(anyString())).thenReturn(VERSION_1);
        
        InsertTimestamp timestamp = new InsertTimestamp();
        timestamp.setInsertTimestamp(Long.valueOf(VERSION_2));
        when(bigdataGateway.getTimestamp(SCENE_CODE)).thenReturn(timestamp);
        
        // When
        boolean result = dataVersionManager.isVersionChanged(SCENE_CODE);
        
        // Then
        assertTrue(result);
        verify(redisHelper).get(anyString());
        verify(bigdataGateway).getTimestamp(SCENE_CODE);
    }
    
    @Test
    void testIsVersionChanged_WhenVersionsAreSame_ShouldReturnFalse() {
        // Given
        when(redisHelper.get(anyString())).thenReturn(VERSION_1);
        
        InsertTimestamp timestamp = new InsertTimestamp();
        timestamp.setInsertTimestamp(Long.valueOf(VERSION_1));
        when(bigdataGateway.getTimestamp(SCENE_CODE)).thenReturn(timestamp);
        
        // When
        boolean result = dataVersionManager.isVersionChanged(SCENE_CODE);
        
        // Then
        assertFalse(result);
    }
    
    @Test
    void testIsVersionChanged_WhenCurrentVersionIsNull_ShouldReturnTrue() {
        // Given
        when(redisHelper.get(anyString())).thenReturn(null);
        
        InsertTimestamp timestamp = new InsertTimestamp();
        timestamp.setInsertTimestamp(Long.valueOf(VERSION_1));
        when(bigdataGateway.getTimestamp(SCENE_CODE)).thenReturn(timestamp);
        
        // When
        boolean result = dataVersionManager.isVersionChanged(SCENE_CODE);
        
        // Then
        assertTrue(result);
    }
    
    @Test
    void testGetCurrentVersion_ShouldReturnCachedVersion() {
        // Given
        when(redisHelper.get(anyString())).thenReturn(VERSION_1);
        
        // When
        String result = dataVersionManager.getCurrentVersion(SCENE_CODE);
        
        // Then
        assertEquals(VERSION_1, result);
        verify(redisHelper).get(anyString());
    }
    
    @Test
    void testUpdateCurrentVersion_ShouldSetVersionInCache() {
        // When
        dataVersionManager.updateCurrentVersion(SCENE_CODE, VERSION_1);
        
        // Then
        verify(redisHelper).set(anyString(), eq(VERSION_1), anyInt());
    }
    
    @Test
    void testGetLatestVersionFromBigdata_WhenSuccessful_ShouldReturnVersion() {
        // Given
        InsertTimestamp timestamp = new InsertTimestamp();
        timestamp.setInsertTimestamp(Long.valueOf(VERSION_1));
        when(bigdataGateway.getTimestamp(SCENE_CODE)).thenReturn(timestamp);
        
        // When
        String result = dataVersionManager.getLatestVersionFromBigdata(SCENE_CODE);
        
        // Then
        assertEquals(VERSION_1, result);
        verify(bigdataGateway).getTimestamp(SCENE_CODE);
    }
    
    @Test
    void testGetLatestVersionFromBigdata_WhenFailed_ShouldReturnNull() {
        // Given
        when(bigdataGateway.getTimestamp(SCENE_CODE)).thenReturn(null);
        
        // When
        String result = dataVersionManager.getLatestVersionFromBigdata(SCENE_CODE);
        
        // Then
        assertNull(result);
    }
    
    @Test
    void testClearVersionCache_ShouldDeleteBothKeys() {
        // When
        dataVersionManager.clearVersionCache(SCENE_CODE);
        
        // Then
        verify(redisHelper, times(2)).del(anyString());
    }
}
