package com.wanshifu.service.bigdata;

import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.response.bigdata.BizRule;
import com.wanshifu.service.bigdata.impl.DataValidatorImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据验证器测试
 * 
 * <AUTHOR> Assistant
 */
@ExtendWith(MockitoExtension.class)
class DataValidatorTest {
    
    @InjectMocks
    private DataValidatorImpl dataValidator;
    
    private BizRule validBizRule;
    
    @BeforeEach
    void setUp() {
        validBizRule = new BizRule();
        validBizRule.setServiceId(12345L);
        validBizRule.setSkuNo("SKU001");
        validBizRule.setMasterInputPrice(new BigDecimal("100.00"));
        validBizRule.setDivisionType("4");
        validBizRule.setSkuType("STANDARD_SKU");
    }
    
    @Test
    void testValidateBasic_WithValidData_ShouldReturnTrue() {
        // When
        boolean result = dataValidator.validateBasic(validBizRule);
        
        // Then
        assertTrue(result);
    }
    
    @Test
    void testValidateBasic_WithNullBizRule_ShouldReturnFalse() {
        // When
        boolean result = dataValidator.validateBasic(null);
        
        // Then
        assertFalse(result);
    }
    
    @Test
    void testValidateBasic_WithNullServiceId_ShouldReturnFalse() {
        // Given
        validBizRule.setServiceId(null);
        
        // When
        boolean result = dataValidator.validateBasic(validBizRule);
        
        // Then
        assertFalse(result);
    }
    
    @Test
    void testValidateBasic_WithBlankSkuNo_ShouldReturnFalse() {
        // Given
        validBizRule.setSkuNo("");
        
        // When
        boolean result = dataValidator.validateBasic(validBizRule);
        
        // Then
        assertFalse(result);
    }
    
    @Test
    void testValidatePrice_WithValidPrice_ShouldReturnTrue() {
        // When
        boolean result = dataValidator.validatePrice(validBizRule);
        
        // Then
        assertTrue(result);
    }
    
    @Test
    void testValidatePrice_WithNullPrice_ShouldReturnFalse() {
        // Given
        validBizRule.setMasterInputPrice(null);
        
        // When
        boolean result = dataValidator.validatePrice(validBizRule);
        
        // Then
        assertFalse(result);
    }
    
    @Test
    void testValidatePrice_WithZeroPrice_ShouldReturnFalse() {
        // Given
        validBizRule.setMasterInputPrice(BigDecimal.ZERO);
        
        // When
        boolean result = dataValidator.validatePrice(validBizRule);
        
        // Then
        assertFalse(result);
    }
    
    @Test
    void testValidatePrice_WithNegativePrice_ShouldReturnFalse() {
        // Given
        validBizRule.setMasterInputPrice(new BigDecimal("-10.00"));
        
        // When
        boolean result = dataValidator.validatePrice(validBizRule);
        
        // Then
        assertFalse(result);
    }
    
    @Test
    void testValidateDivision_WithValidDivisionType_ShouldReturnTrue() {
        // When
        boolean result = dataValidator.validateDivision(validBizRule);
        
        // Then
        assertTrue(result);
    }
    
    @Test
    void testValidateDivision_WithInvalidDivisionType_ShouldReturnFalse() {
        // Given
        validBizRule.setDivisionType("invalid");
        
        // When
        boolean result = dataValidator.validateDivision(validBizRule);
        
        // Then
        assertFalse(result);
    }
    
    @Test
    void testValidateService_WithValidService_ShouldReturnTrue() {
        // When
        boolean result = dataValidator.validateService(validBizRule);
        
        // Then
        assertTrue(result);
    }
    
    @Test
    void testValidateService_WithInvalidServiceId_ShouldReturnFalse() {
        // Given
        validBizRule.setServiceId(-1L);
        
        // When
        boolean result = dataValidator.validateService(validBizRule);
        
        // Then
        assertFalse(result);
    }
    
    @Test
    void testValidateSkuType_WithValidSkuType_ShouldReturnTrue() {
        // When
        boolean result = dataValidator.validateSkuType(validBizRule, SceneCodeEnum.BARGAIN_PRICE_EVERYDAY);
        
        // Then
        assertTrue(result);
    }
    
    @Test
    void testValidateSkuType_WithInvalidSkuType_ShouldReturnFalse() {
        // Given
        validBizRule.setSkuType("INVALID_SKU");
        
        // When
        boolean result = dataValidator.validateSkuType(validBizRule, SceneCodeEnum.BARGAIN_PRICE_EVERYDAY);
        
        // Then
        assertFalse(result);
    }
}
