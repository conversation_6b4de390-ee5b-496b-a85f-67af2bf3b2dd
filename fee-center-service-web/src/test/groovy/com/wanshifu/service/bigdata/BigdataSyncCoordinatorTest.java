package com.wanshifu.service.bigdata;

import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.service.bigdata.impl.BigdataSyncCoordinatorImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 大数据同步协调器测试
 * 
 * <AUTHOR> Assistant
 */
@ExtendWith(MockitoExtension.class)
class BigdataSyncCoordinatorTest {
    
    @Mock
    private DataVersionManager dataVersionManager;
    
    @Mock
    private BigdataDataFetcher bigdataDataFetcher;
    
    @Mock
    private FeeRuleDataProcessor feeRuleDataProcessor;
    
    @Mock
    private FeeRuleDataPersistence feeRuleDataPersistence;
    
    @Mock
    private TaskExecutionManager taskExecutionManager;
    
    @Mock
    private SceneProcessingStrategy sceneProcessingStrategy;
    
    @Mock
    private RedisHelper redisHelper;
    
    @InjectMocks
    private BigdataSyncCoordinatorImpl syncCoordinator;
    
    private static final String SCENE_CODE = "bargain_price_everyday";
    private static final String VERSION_1 = "1234567890";
    private static final String VERSION_2 = "1234567891";
    
    @BeforeEach
    void setUp() {
        // 设置页面大小
        ReflectionTestUtils.setField(syncCoordinator, "pageSize", 1000);
        
        // 设置场景处理策略列表
        List<SceneProcessingStrategy> strategies = Arrays.asList(sceneProcessingStrategy);
        ReflectionTestUtils.setField(syncCoordinator, "sceneProcessingStrategies", strategies);
        
        // 默认策略支持所有场景
        when(sceneProcessingStrategy.supports(any(SceneCodeEnum.class))).thenReturn(true);
        when(sceneProcessingStrategy.getPriority()).thenReturn(1);
    }
    
    @Test
    void testSyncData_WithValidSceneCode_ShouldExecuteSuccessfully() {
        // Given
        when(dataVersionManager.isVersionChanged(SCENE_CODE)).thenReturn(true);
        when(bigdataDataFetcher.isConnectionHealthy()).thenReturn(true);
        when(feeRuleDataPersistence.isConnectionHealthy()).thenReturn(true);
        when(dataVersionManager.getCurrentVersion(SCENE_CODE)).thenReturn(VERSION_1);
        when(dataVersionManager.getLatestVersionFromBigdata(SCENE_CODE)).thenReturn(VERSION_2);
        when(taskExecutionManager.waitForAllTasks(anyLong())).thenReturn(true);
        
        // When
        assertDoesNotThrow(() -> syncCoordinator.syncData(SCENE_CODE));
        
        // Then
        verify(dataVersionManager).isVersionChanged(SCENE_CODE);
        verify(redisHelper).set(anyString(), eq("1"), anyInt());
        verify(redisHelper).del(anyString());
    }
    
    @Test
    void testSyncData_WithInvalidSceneCode_ShouldThrowException() {
        // Given
        when(dataVersionManager.isVersionChanged("invalid")).thenReturn(true);
        when(bigdataDataFetcher.isConnectionHealthy()).thenReturn(true);
        when(feeRuleDataPersistence.isConnectionHealthy()).thenReturn(true);
        
        // When & Then
        assertThrows(BusException.class, () -> syncCoordinator.syncData("invalid"));
    }
    
    @Test
    void testCheckPreconditions_WhenVersionNotChanged_ShouldReturnFalse() {
        // Given
        when(dataVersionManager.isVersionChanged(SCENE_CODE)).thenReturn(false);
        
        // When
        boolean result = syncCoordinator.checkPreconditions(SCENE_CODE);
        
        // Then
        assertFalse(result);
        verify(dataVersionManager).isVersionChanged(SCENE_CODE);
    }
    
    @Test
    void testCheckPreconditions_WhenBigdataConnectionUnhealthy_ShouldReturnFalse() {
        // Given
        when(dataVersionManager.isVersionChanged(SCENE_CODE)).thenReturn(true);
        when(bigdataDataFetcher.isConnectionHealthy()).thenReturn(false);
        
        // When
        boolean result = syncCoordinator.checkPreconditions(SCENE_CODE);
        
        // Then
        assertFalse(result);
        verify(bigdataDataFetcher).isConnectionHealthy();
    }
    
    @Test
    void testCheckPreconditions_WhenDatabaseConnectionUnhealthy_ShouldReturnFalse() {
        // Given
        when(dataVersionManager.isVersionChanged(SCENE_CODE)).thenReturn(true);
        when(bigdataDataFetcher.isConnectionHealthy()).thenReturn(true);
        when(feeRuleDataPersistence.isConnectionHealthy()).thenReturn(false);
        
        // When
        boolean result = syncCoordinator.checkPreconditions(SCENE_CODE);
        
        // Then
        assertFalse(result);
        verify(feeRuleDataPersistence).isConnectionHealthy();
    }
    
    @Test
    void testCheckPreconditions_WhenAllHealthy_ShouldReturnTrue() {
        // Given
        when(dataVersionManager.isVersionChanged(SCENE_CODE)).thenReturn(true);
        when(bigdataDataFetcher.isConnectionHealthy()).thenReturn(true);
        when(feeRuleDataPersistence.isConnectionHealthy()).thenReturn(true);
        
        // When
        boolean result = syncCoordinator.checkPreconditions(SCENE_CODE);
        
        // Then
        assertTrue(result);
    }
    
    @Test
    void testGetSyncStatus_WhenSyncing_ShouldReturnSyncingStatus() {
        // Given
        when(redisHelper.get(anyString())).thenReturn("1");
        
        // When
        String status = syncCoordinator.getSyncStatus(SCENE_CODE);
        
        // Then
        assertEquals("同步中", status);
    }
    
    @Test
    void testGetSyncStatus_WhenNotInitialized_ShouldReturnNotInitializedStatus() {
        // Given
        when(redisHelper.get(anyString())).thenReturn(null);
        when(dataVersionManager.getCurrentVersion(SCENE_CODE)).thenReturn(null);
        
        // When
        String status = syncCoordinator.getSyncStatus(SCENE_CODE);
        
        // Then
        assertEquals("未初始化", status);
    }
    
    @Test
    void testStopSync_ShouldSetTerminateFlagAndClearLock() {
        // When
        syncCoordinator.stopSync(SCENE_CODE);
        
        // Then
        verify(taskExecutionManager).setTerminateFlag();
        verify(redisHelper).del(anyString());
    }
}
