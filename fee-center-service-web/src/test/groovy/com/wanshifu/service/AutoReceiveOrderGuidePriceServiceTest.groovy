package com.wanshifu.service

import com.wanshifu.FeeCenterServiceAppTest
import com.wanshifu.repository.BatchTaskInfoRepository
import com.wanshifu.repository.SceneInfoRepository
import com.wanshifu.strategy.batch.AutoReceiveOrderGuidePriceBatchTableHandler
import org.bson.types.ObjectId

import javax.annotation.Resource

class AutoReceiveOrderGuidePriceServiceTest extends FeeCenterServiceAppTest {

    @Resource
    AutoReceiveOrderGuidePriceBatchTableHandler autoReceiveOrderGuidePriceBatchTableHandler

    @Resource
    BatchTaskInfoRepository batchTaskInfoRepository

    @Resource
    SceneInfoRepository sceneInfoRepository


    def testConsumeTask() {
        given:
        //street 652cd60e1c3b7069546be455 district 652cd6a01c3b7069546be456
        def task = batchTaskInfoRepository.findOne(new ObjectId("652cde7a1c3b7031f8de3c71"))
        def sceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(task.sceneCode)
        autoReceiveOrderGuidePriceBatchTableHandler.handleBatchTask(sceneInfo,task)

        expect:
        printf('content= %s', task.id)
    }
}
