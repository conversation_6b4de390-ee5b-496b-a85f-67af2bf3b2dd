package com.wanshifu.service;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.fee.center.domain.biz.AutoReceiveOrderGuidePriceBizRule;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.FeeTemplate;
import com.wanshifu.fee.center.domain.enums.BizRuleConditionModeEnum;
import com.wanshifu.fee.center.domain.enums.DivisionTypeEnum;
import com.wanshifu.fee.center.domain.request.FeeTemplateQueryByConditionReq;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.junit.Assert.assertEquals;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.when;

/**
 * @author: Chen Yong
 * @create: 2024-02-29 17:48
 * @description: 计价模板单元测试
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class FeeTemplateServiceTest2 {

    @Mock
    private MongoTemplate mongoTemplate;

    @InjectMocks
    private FeeTemplateService feeTemplateService;

    @Test
    public void testQueryServiceByConditionWithResults() {
        // Mock input request
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setPageNum(1);
        request.setPageSize(10);

        // Mock criteria and aggregation results
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(JSONObject.class)).getUniqueMappedResult())
                .thenReturn(new JSONObject().fluentPut("count", 1L));

        // Mock aggregation results
        List<FeeTemplate> mockResults = new ArrayList<>();
        mockResults.add(new FeeTemplate());
        AggregationResults<FeeTemplate> aggregationResults = new AggregationResults<>(mockResults, null);
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(FeeTemplate.class))).thenReturn(aggregationResults);

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify the results
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(1, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithNoResults() {
        // Mock input request
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setPageNum(1);
        request.setPageSize(10);

        // Mock criteria and aggregation results
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(JSONObject.class)).getUniqueMappedResult())
                .thenReturn(null);

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify the results
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
        assertEquals(0, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithEmptyRequest() {
        // Mock an empty request
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify that the result is not null and contains no elements
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
        assertEquals(0, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithNullAggregationResult() {
        // Mock input request
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setPageNum(1);
        request.setPageSize(10);

        // Mock criteria and null aggregation result
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(JSONObject.class)).getUniqueMappedResult())
                .thenReturn(null);

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify that the result is not null and contains no elements
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
        assertEquals(0, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithMultipleResults() {
        // Mock input request
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setPageNum(1);
        request.setPageSize(5);

        // Mock criteria and aggregation results with multiple entries
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(JSONObject.class)).getUniqueMappedResult())
                .thenReturn(new JSONObject().fluentPut("count", 10L));

        // Mock aggregation results with 10 entries
        List<FeeTemplate> mockResults = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            mockResults.add(new FeeTemplate());
        }
        AggregationResults<FeeTemplate> aggregationResults = new AggregationResults<>(mockResults, null);
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(FeeTemplate.class))).thenReturn(aggregationResults);

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify that the result contains 5 entries and the total count is 10
        assertNotNull(result);
        assertEquals(5, result.getContent().size());
        assertEquals(10, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithSceneCode() {
        // Mock input request with scene code
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setSceneCode("SCENE001");

        // Mock criteria and aggregation results
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(FeeTemplate.Fields.sceneCode).is("SCENE001");
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(JSONObject.class)).getUniqueMappedResult())
                .thenReturn(new JSONObject().fluentPut("count", 1L));

        // Mock aggregation results
        List<FeeTemplate> mockResults = new ArrayList<>();
        mockResults.add(new FeeTemplate());
        AggregationResults<FeeTemplate> aggregationResults = new AggregationResults<>(mockResults,null);
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(FeeTemplate.class))).thenReturn(aggregationResults);

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify the results
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(1, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithSceneName() {
        // Mock input request with scene name
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setSceneName("Scene Name");

        // Mock criteria and aggregation results
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(FeeTemplate.Fields.sceneName).regex(".*Scene Name.*");
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(JSONObject.class)).getUniqueMappedResult())
                .thenReturn(new JSONObject().fluentPut("count", 1L));

        // Mock aggregation results
        List<FeeTemplate> mockResults = new ArrayList<>();
        mockResults.add(new FeeTemplate());
        AggregationResults<FeeTemplate> aggregationResults = new AggregationResults<>(mockResults, null);
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(FeeTemplate.class))).thenReturn(aggregationResults);

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify the results
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(1, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithEmptyBizRule() {
        // Mock input request with empty biz rule
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setBizRule(Collections.emptyList());

        // Mock criteria and aggregation results
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(JSONObject.class)).getUniqueMappedResult())
                .thenReturn(new JSONObject().fluentPut("count", 0L));

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify the results
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
        assertEquals(0, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithFuzzyMatch() {
        // Mock input request with fuzzy match condition
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        FeeTemplateQueryByConditionReq.BizRuleCondition condition = new FeeTemplateQueryByConditionReq.BizRuleCondition();
        condition.setKey("key");
        condition.setValue("value");
        condition.setMode(BizRuleConditionModeEnum.FUZZY_MATCH.code);
        request.setBizRule(Collections.singletonList(condition));

        // Mock criteria and aggregation results
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + "key").regex(".*value.*");
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(JSONObject.class)).getUniqueMappedResult())
                .thenReturn(new JSONObject().fluentPut("count", 1L));

        // Mock aggregation results
        List<FeeTemplate> mockResults = new ArrayList<>();
        mockResults.add(new FeeTemplate());
        AggregationResults<FeeTemplate> aggregationResults = new AggregationResults<>(mockResults, null);
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(FeeTemplate.class))).thenReturn(aggregationResults);

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify the results
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(1, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithAllMatch() {
        // Mock input request with all match condition
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        FeeTemplateQueryByConditionReq.BizRuleCondition condition = new FeeTemplateQueryByConditionReq.BizRuleCondition();
        condition.setKey("key");
        condition.setValue("value");
        condition.setMode(BizRuleConditionModeEnum.ALL_MATCH.code);
        request.setBizRule(Collections.singletonList(condition));

        // Mock criteria and aggregation results
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + "key").is("value");
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(JSONObject.class)).getUniqueMappedResult())
                .thenReturn(new JSONObject().fluentPut("count", 1L));

        // Mock aggregation results
        List<FeeTemplate> mockResults = new ArrayList<>();
        mockResults.add(new FeeTemplate());
        AggregationResults<FeeTemplate> aggregationResults = new AggregationResults<>(mockResults, null);
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(FeeTemplate.class))).thenReturn(aggregationResults);

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify the results
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(1, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithInvalidCondition() {
        // Mock input request with invalid condition
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        FeeTemplateQueryByConditionReq.BizRuleCondition condition = new FeeTemplateQueryByConditionReq.BizRuleCondition();
        condition.setKey(null);
        condition.setValue("value");
        condition.setMode(BizRuleConditionModeEnum.ALL_MATCH.code);
        request.setBizRule(Collections.singletonList(condition));

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify that the result is empty
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
        assertEquals(0, result.getTotalElements());
    }


    @Test
    public void testQueryServiceByConditionWithCityDivision() {
        // Mock input request with city division code
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setSceneName(DivisionTypeEnum.CITY.code);
        request.setBizRule(new ArrayList<>());

        // Mock criteria and aggregation results
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.divisionType).is(DivisionTypeEnum.CITY.code)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.level2DivisionId).in("CITY001");
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(JSONObject.class)).getUniqueMappedResult())
                .thenReturn(new JSONObject().fluentPut("count", 1L));

        // Mock aggregation results
        List<FeeTemplate> mockResults = new ArrayList<>();
        mockResults.add(new FeeTemplate());
        AggregationResults<FeeTemplate> aggregationResults = new AggregationResults<>(mockResults, null);
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(FeeTemplate.class))).thenReturn(aggregationResults);

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify the results
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(1, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithInvalidDivision() {
        // Mock input request with invalid division code
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setSceneName(DivisionTypeEnum.CITY.code);
        request.setBizRule(new ArrayList<>());

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify that the result is empty
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
        assertEquals(0, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithEmptyDivisionIdSet() {
        // Mock input request with empty division ID set
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setSceneName(DivisionTypeEnum.CITY.code);
        request.setBizRule(new ArrayList<>());

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify that the result is empty
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
        assertEquals(0, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithTopLevelNodes() {
        // Mock input request with top-level division code
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setSceneCode(DivisionTypeEnum.COUNTRY.code);

        // Mock criteria and aggregation results
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.divisionType).is(DivisionTypeEnum.COUNTRY.code);
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(JSONObject.class)).getUniqueMappedResult())
                .thenReturn(new JSONObject().fluentPut("count", 1L));

        // Mock aggregation results
        List<FeeTemplate> mockResults = new ArrayList<>();
        mockResults.add(new FeeTemplate());
        AggregationResults<FeeTemplate> aggregationResults = new AggregationResults<>(mockResults, null);
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(FeeTemplate.class))).thenReturn(aggregationResults);

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify the results
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(1, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithNoResults2() {
        // Mock input request with no matching criteria
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setSceneCode(DivisionTypeEnum.CITY.code);
        request.setStatusList(new ArrayList<>());

        // Mock criteria and aggregation results
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.divisionType).is(DivisionTypeEnum.CITY.code)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.level2DivisionId).in("CITY001");
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(JSONObject.class)).getUniqueMappedResult())
                .thenReturn(null);

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify that the result is empty
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
        assertEquals(0, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithNullRequest() {
        // Mock input request as null
        FeeTemplateQueryByConditionReq request = null;

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify that the result is empty
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
        assertEquals(0, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithMultipleResults3() {
        // Mock input request with multiple results
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setSceneCode("SCENE001");

        // Mock criteria and aggregation results
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(FeeTemplate.Fields.sceneCode).is("SCENE001");
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(JSONObject.class)).getUniqueMappedResult())
                .thenReturn(new JSONObject().fluentPut("count", 3L));

        // Mock aggregation results with 3 entries
        List<FeeTemplate> mockResults = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            mockResults.add(new FeeTemplate());
        }
        AggregationResults<FeeTemplate> aggregationResults = new AggregationResults<>(mockResults, null);
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(FeeTemplate.class))).thenReturn(aggregationResults);

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify the results
        assertNotNull(result);
        assertEquals(3, result.getContent().size());
        assertEquals(3, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithEmptySceneCode() {
        // Mock input request with empty scene code
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setSceneCode("");

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify that the result is empty
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
        assertEquals(0, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithEmptySceneName() {
        // Mock input request with empty scene name
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setSceneName("");

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify that the result is empty
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
        assertEquals(0, result.getTotalElements());
    }


    @Test
    public void testQueryServiceByConditionWithValidRequest() {
        // Mock input request with valid criteria
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setSceneCode("SCENE001");
        request.setSceneName("Scene Name");
        FeeTemplateQueryByConditionReq.BizRuleCondition condition = new FeeTemplateQueryByConditionReq.BizRuleCondition();
        condition.setKey("key");
        condition.setValue("value");
        condition.setMode(BizRuleConditionModeEnum.ALL_MATCH.code);
        request.setBizRule(Collections.singletonList(condition));

        // Mock criteria and aggregation results
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(FeeTemplate.Fields.sceneCode).is("SCENE001")
                .and(FeeTemplate.Fields.sceneName).regex(".*Scene Name.*")
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + "key").is("value");
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(JSONObject.class)).getUniqueMappedResult())
                .thenReturn(new JSONObject().fluentPut("count", 1L));

        // Mock aggregation results
        List<FeeTemplate> mockResults = new ArrayList<>();
        mockResults.add(new FeeTemplate());
        AggregationResults<FeeTemplate> aggregationResults = new AggregationResults<>(mockResults, null);
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(FeeTemplate.class))).thenReturn(aggregationResults);

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify the results
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(1, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithEmptyRequest4() {
        // Mock input request as empty
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify that the result is empty
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
        assertEquals(0, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithNoMatchingResults() {
        // Mock input request with no matching results
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setSceneCode("SCENE002");

        // Mock criteria and null aggregation result
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(FeeTemplate.Fields.sceneCode).is("SCENE002");
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(JSONObject.class)).getUniqueMappedResult())
                .thenReturn(null);

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify that the result is empty
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
        assertEquals(0, result.getTotalElements());
    }


    @Test
    public void testQueryServiceByConditionWithValidSceneCodeAndName() {
        // Mock input request with valid scene code and name
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setSceneCode("SCENE001");
        request.setSceneName("Sample Scene");

        // Mock criteria and aggregation results
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(FeeTemplate.Fields.sceneCode).is("SCENE001")
                .and(FeeTemplate.Fields.sceneName).is("Sample Scene");
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(JSONObject.class)).getUniqueMappedResult())
                .thenReturn(new JSONObject().fluentPut("count", 1L));

        // Mock aggregation results
        List<FeeTemplate> mockResults = new ArrayList<>();
        mockResults.add(new FeeTemplate());
        AggregationResults<FeeTemplate> aggregationResults = new AggregationResults<>(mockResults, null);
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(FeeTemplate.class))).thenReturn(aggregationResults);

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify the results
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(1, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithEmptySceneCodeAndName() {
        // Mock input request with empty scene code and name
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setSceneCode("");
        request.setSceneName("");

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify that the result is empty
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
        assertEquals(0, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithNullRequest6() {
        // Mock input request as null
        FeeTemplateQueryByConditionReq request = null;

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify that the result is empty
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
        assertEquals(0, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithValidSceneName() {
        // Mock input request with valid scene name
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setSceneName("Sample Scene");

        // Mock criteria and aggregation results
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(FeeTemplate.Fields.sceneName).regex(".*Sample Scene.*");
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(JSONObject.class)).getUniqueMappedResult())
                .thenReturn(new JSONObject().fluentPut("count", 1L));

        // Mock aggregation results
        List<FeeTemplate> mockResults = new ArrayList<>();
        mockResults.add(new FeeTemplate());
        AggregationResults<FeeTemplate> aggregationResults = new AggregationResults<>(mockResults, null);
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(FeeTemplate.class))).thenReturn(aggregationResults);

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify the results
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(1, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithEmptySceneName7() {
        // Mock input request with empty scene name
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setSceneName("");

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify that the result is empty
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
        assertEquals(0, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithNullRequest7() {
        // Mock input request as null
        FeeTemplateQueryByConditionReq request = null;

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify that the result is empty
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
        assertEquals(0, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithValidBizRuleCondition() {
        // Mock input request with valid biz rule condition
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        FeeTemplateQueryByConditionReq.BizRuleCondition condition = new FeeTemplateQueryByConditionReq.BizRuleCondition();
        condition.setKey("key");
        condition.setValue("value");
        condition.setMode(BizRuleConditionModeEnum.ALL_MATCH.code);
        request.setBizRule(Collections.singletonList(condition));

        // Mock criteria and aggregation results
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + "key").is("value");
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(JSONObject.class)).getUniqueMappedResult())
                .thenReturn(new JSONObject().fluentPut("count", 1L));

        // Mock aggregation results
        List<FeeTemplate> mockResults = new ArrayList<>();
        mockResults.add(new FeeTemplate());
        AggregationResults<FeeTemplate> aggregationResults = new AggregationResults<>(mockResults, null);
        when(mongoTemplate.aggregate(any(TypedAggregation.class), eq(FeeTemplate.class))).thenReturn(aggregationResults);

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify the results
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(1, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithEmptyBizRuleCondition() {
        // Mock input request with empty biz rule condition
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setBizRule(Collections.emptyList());

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify that the result is empty
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
        assertEquals(0, result.getTotalElements());
    }

    @Test
    public void testQueryServiceByConditionWithNullRequest8() {
        // Mock input request as null
        FeeTemplateQueryByConditionReq request = null;

        // Call the method
        Page<FeeTemplate> result = feeTemplateService.queryServiceByCondition(request);

        // Verify that the result is empty
        assertNotNull(result);
        assertEquals(0, result.getContent().size());
        assertEquals(0, result.getTotalElements());
    }
}
