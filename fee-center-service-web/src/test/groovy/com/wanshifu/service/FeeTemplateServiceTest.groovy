package com.wanshifu.service

import com.alibaba.fastjson.JSON
import com.wanshifu.FeeCenterServiceAppTest
import com.wanshifu.fee.center.domain.constant.PunConstant
import com.wanshifu.fee.center.domain.document.BizRuleMapping
import com.wanshifu.fee.center.domain.document.CalculateResult
import com.wanshifu.fee.center.domain.document.CalculateRuleData
import com.wanshifu.fee.center.domain.document.FeeRule
import com.wanshifu.fee.center.domain.document.FeeTemplate
import com.wanshifu.repository.FeeTemplateRepository
import org.springframework.data.mongodb.core.MongoTemplate

import javax.annotation.Resource

class FeeTemplateServiceTest extends FeeCenterServiceAppTest {

    @Resource
    FeeRuleService feeRuleService
    @Resource
    FeeTemplateService feeTemplateService

    @Resource
    FeeTemplateRepository feeTemplateRepository

    @Resource
    MongoTemplate mongoTemplate;




    def testSave() {
        given:
        def feeRule = new FeeRule()
        feeRule.feeRuleId = 1
        feeRule.bizRule = [masterId: '1']
        feeRule.calculateRuleData = new CalculateRuleData()
        feeRule.createTime = new Date()
        feeRule.modifyTime = new Date()
        feeRuleService.save(feeRule)

        expect:
        printf('content= %s', feeRule.id)
    }

    def testAllIndexName(){
        given:
        List<String>  bizRuleMappingIndex = mongoTemplate.indexOps(BizRuleMapping.class).getIndexInfo().collect {it.name}.findAll {it.contains(PunConstant.DOT)}
        List<String>  calculateResultIndex = mongoTemplate.indexOps(CalculateResult.class).getIndexInfo().collect {it.name}.findAll {it.contains(PunConstant.DOT)}
        List<String>  feeTemplateIndex = mongoTemplate.indexOps(FeeTemplate.class).getIndexInfo().collect {it.name}.findAll {it.contains(PunConstant.DOT)}
        List<String>  feeRuleIndex = mongoTemplate.indexOps(FeeRule.class).getIndexInfo().collect {it.name}.findAll {it.contains(PunConstant.DOT)}


        expect:
        printf('db.bizRuleMapping.dropIndexes(%s)', JSON.toJSONString(bizRuleMappingIndex))
        println()
        printf('db.calculateResult.dropIndexes(%s)', JSON.toJSONString(calculateResultIndex))
        println()
        printf('db.feeTemplate.dropIndexes(%s)', JSON.toJSONString(feeTemplateIndex))
        println()
        printf('db.feeRule.dropIndexes(%s)', JSON.toJSONString(feeRuleIndex))

    }
}
