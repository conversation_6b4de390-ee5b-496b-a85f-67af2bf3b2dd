package com.wanshifu.service;

import com.wanshifu.domain.dto.ApplyOrderCalculateDTO;
import com.wanshifu.fee.center.domain.response.ApplyOrderCalculateResp;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.strategy.apply.CommonSceneFeeRuleStrategy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

/**
 * @author: <PERSON>
 * @create: 2024-03-28 15:25
 * @description: 共用算价策略
 */
public class CommonSceneFeeRuleStrategyTest {

    @InjectMocks
    CommonSceneFeeRuleStrategy commonSceneFeeRuleStrategy;

    @Mock
    SnowFlakeGenerator snowFlakeGenerator;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void apply_shouldReturnValidResponse_whenValidInputProvided() {
        // Given
        ApplyOrderCalculateDTO applyOrderCalculateDTO = new ApplyOrderCalculateDTO(null);
        applyOrderCalculateDTO.setApplyOrderCalculateResp(new ApplyOrderCalculateResp());
        applyOrderCalculateDTO.setSceneCode(Arrays.asList("common"));
        applyOrderCalculateDTO.setServiceInfos(new ArrayList<>());

        when(snowFlakeGenerator.INSTANCE.generate()).thenReturn(Long.valueOf("1234567890"));

        // When
        ApplyOrderCalculateResp result = commonSceneFeeRuleStrategy.apply(applyOrderCalculateDTO);

        // Then
        assertNotNull(result);
        assertEquals("1234567890", result.getTid());
    }

    @Test
    void apply_shouldThrowException_whenNoServiceInfosProvided() {
        // Given
        ApplyOrderCalculateDTO applyOrderCalculateDTO = new ApplyOrderCalculateDTO(null);
        applyOrderCalculateDTO.setApplyOrderCalculateResp(new ApplyOrderCalculateResp());
        applyOrderCalculateDTO.setSceneCode(Arrays.asList("common"));

        // When & Then
        assertThrows(BusException.class, () -> commonSceneFeeRuleStrategy.apply(applyOrderCalculateDTO));
    }

    @Test
    void apply_shouldThrowException_whenNoSceneCodeProvided() {
        // Given
        ApplyOrderCalculateDTO applyOrderCalculateDTO = new ApplyOrderCalculateDTO(null);
        applyOrderCalculateDTO.setApplyOrderCalculateResp(new ApplyOrderCalculateResp());

        // When & Then
        assertThrows(BusException.class, () -> commonSceneFeeRuleStrategy.apply(applyOrderCalculateDTO));
    }



    @Test
    void apply_shouldReturnValidResponse_whenMultipleSceneCodesProvided() {
        // Given
        ApplyOrderCalculateDTO applyOrderCalculateDTO = new ApplyOrderCalculateDTO(null);
        applyOrderCalculateDTO.setApplyOrderCalculateResp(new ApplyOrderCalculateResp());
        applyOrderCalculateDTO.setSceneCode(Arrays.asList("common", "extra"));
        applyOrderCalculateDTO.setServiceInfos(new ArrayList<>());

        when(snowFlakeGenerator.INSTANCE.generate()).thenReturn(Long.valueOf("1234567890"));

        // When
        ApplyOrderCalculateResp result = commonSceneFeeRuleStrategy.apply(applyOrderCalculateDTO);

        // Then
        assertNotNull(result);
        assertEquals("1234567890", result.getTid());
    }

    @Test
    void apply_shouldThrowException_whenNullSceneCodeProvided() {
        // Given
        ApplyOrderCalculateDTO applyOrderCalculateDTO = new ApplyOrderCalculateDTO(null);
        applyOrderCalculateDTO.setApplyOrderCalculateResp(new ApplyOrderCalculateResp());
        applyOrderCalculateDTO.setSceneCode(null);

        // When & Then
        assertThrows(BusException.class, () -> commonSceneFeeRuleStrategy.apply(applyOrderCalculateDTO));
    }

    @Test
    void apply_shouldThrowException_whenNullServiceInfosProvided() {
        // Given
        ApplyOrderCalculateDTO applyOrderCalculateDTO = new ApplyOrderCalculateDTO(null);
        applyOrderCalculateDTO.setApplyOrderCalculateResp(new ApplyOrderCalculateResp());
        applyOrderCalculateDTO.setSceneCode(Arrays.asList("common"));
        applyOrderCalculateDTO.setServiceInfos(null);

        // When & Then
        assertThrows(BusException.class, () -> commonSceneFeeRuleStrategy.apply(applyOrderCalculateDTO));
    }
}
