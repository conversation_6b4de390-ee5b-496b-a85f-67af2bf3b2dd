package com.wanshifu.service

import com.wanshifu.fee.center.domain.dto.ExpressResultInfo;

/**
 * @author: <PERSON>
 * @create: 2024-03-14 21:50
 * @description: 计价脚本测试
 */
if (ALL_SERVICE_AP_SKU_NUMBER == 1) {
    BigDecimal price = new BigDecimal(100);
    BigDecimal num = new BigDecimal(AP45005075);
    BigDecimal cost = price.multiply(num);
    ExpressResultInfo returnResult =
            new ExpressResultInfo();
    returnResult.setNumber(num);
    returnResult.setPrice(price);
    returnResult.setCost(cost);
    return returnResult;
}
if (ALL_SERVICE_AP_SKU_NUMBER > 1) {
    BigDecimal price = new BigDecimal(40);
    BigDecimal num = new BigDecimal(AP45005075);
    BigDecimal cost = price.multiply(num);
    ExpressResultInfo returnResult = new ExpressResultInfo();
    returnResult.setNumber(num); returnResult.setPrice(price);
    returnResult.setCost(cost); return returnResult;
}
if (ALL_SERVICE_AP_SKU_NUMBER > 2) {
    BigDecimal price = new BigDecimal(30);
    BigDecimal num = new BigDecimal(AP45005075);
    BigDecimal cost = price.multiply(num);
    ExpressResultInfo returnResult = new ExpressResultInfo();
    returnResult.setNumber(num);
    returnResult.setPrice(price);
    returnResult.setCost(cost);
    return returnResult;
}