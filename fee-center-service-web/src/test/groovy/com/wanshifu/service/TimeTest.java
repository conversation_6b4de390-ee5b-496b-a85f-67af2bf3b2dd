package com.wanshifu.service;

import java.time.Instant;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;

public class TimeTest {

    public static void main(String[] args) {
//        LocalTime now = LocalTime.now();
//        int hour = now.getHour();
//        boolean b = hour >= 7 && hour <= 16;
//        System.out.println(b);

        // 使用Asia/Shanghai标识北京时间
        ZoneId beijingZone = ZoneId.of("Asia/Shanghai");
        ZonedDateTime now = ZonedDateTime.now(beijingZone);
        LocalTime currentTime = now.toLocalTime();

        LocalTime nightTime = LocalTime.of(23, 0);
        LocalTime morningTime = LocalTime.of(6, 0);

        if (currentTime.isBefore(morningTime)) {
            System.out.println("早于6点：" + 0);
            return;
        }

        if (currentTime.isBefore(nightTime)) {
            System.out.println("相差：" + ChronoUnit.MILLIS.between(currentTime, nightTime));
            return;
        }

        System.out.println("其它情况");
    }
}
