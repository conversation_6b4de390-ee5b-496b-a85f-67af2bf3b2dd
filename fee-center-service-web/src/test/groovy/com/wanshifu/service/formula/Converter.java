package com.wanshifu.service.formula;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import java.util.*;

public class Converter {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static String convertJsonStructure(String inputJson) {
        try {
            JsonNode inputNode = objectMapper.readTree(inputJson);
            ObjectNode resultNode = objectMapper.createObjectNode();

            // 创建expressInfo对象
            ObjectNode expressInfo = objectMapper.createObjectNode();
            ArrayNode expressInfoUnits = objectMapper.createArrayNode();

            // 处理conditionEntities
            JsonNode conditionEntities = inputNode.get("conditionEntities");
            Set<String> allParams = new HashSet<>();
            StringBuilder expressBuilder = new StringBuilder();
            expressBuilder.append("import java.math.BigDecimal; import com.wanshifu.fee.center.domain.dto.ExpressResultInfo; ");

            if (conditionEntities != null && conditionEntities.isArray()) {
                for (JsonNode entity : conditionEntities) {
                    ObjectNode unit = objectMapper.createObjectNode();
                    ObjectNode condition = objectMapper.createObjectNode();

                    // 构建条件
                    JsonNode conditionParams = entity.get("skuRuleConditionParams");
                    if (conditionParams != null && conditionParams.isArray()) {
                        String conditionStr = buildCondition(conditionParams, allParams);

                        // 这里简化处理，取第一个参数作为主要参数
                        JsonNode firstParam = conditionParams.get(0);
                        if (firstParam != null) {
                            String paramNo = firstParam.get("paramNo").asText();
                            String term = firstParam.get("term").asText();
                            String threshold = firstParam.get("conditionThresholding").asText();

                            condition.put("param", convertParamName(paramNo));
                            condition.put("operator", convertOperator(term));
                            condition.put("value", threshold);
                            allParams.add(convertParamName(paramNo));
                        }
                    }

                    // 设置价格表达式
                    String priceExpress = entity.get("priceExpress").asText();
                    unit.set("condition", condition);
                    unit.put("priceExpress", "masterInputPrice"); // 根据业务逻辑可能需要调整

                    expressInfoUnits.add(unit);

                    // 构建执行表达式
                    expressBuilder.append(buildExecuteExpression(conditionParams, priceExpress));
                }
            }

            // 添加默认条件
            ObjectNode defaultUnit = objectMapper.createObjectNode();
            ObjectNode defaultCondition = objectMapper.createObjectNode();
            defaultCondition.put("param", "1");
            defaultCondition.put("operator", "==");
            defaultCondition.put("value", "1");
            defaultUnit.set("condition", defaultCondition);
            defaultUnit.put("priceExpress", "masterInputPrice");
            expressInfoUnits.add(defaultUnit);

            // 添加默认执行逻辑
            expressBuilder.append("if (1 == 1) { ")
                    .append("BigDecimal price = (masterInputPrice); ")
                    .append("BigDecimal num = (null); ")
                    .append("BigDecimal cost = price * num; ")
                    .append("ExpressResultInfo returnResult = new ExpressResultInfo(); ")
                    .append("returnResult.setNumber(num); ")
                    .append("returnResult.setPrice(price); ")
                    .append("returnResult.setCost(cost); ")
                    .append("return returnResult; } ");

            expressInfo.set("expressInfoUnits", expressInfoUnits);
            resultNode.set("expressInfo", expressInfo);

            // 设置表达式
            resultNode.put("express", expressBuilder.toString());

            // 设置参数列表
            allParams.add("masterInputPrice");
            ArrayNode paramList = objectMapper.createArrayNode();
            for (String param : allParams) {
                paramList.add(param);
            }
            resultNode.set("expressionParamList", paramList);

            // 设置参数映射（空对象）
            ObjectNode paramMap = objectMapper.createObjectNode();
            resultNode.set("expressionParamMap", paramMap);

            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(resultNode);

        } catch (Exception e) {
            throw new RuntimeException("JSON转换失败", e);
        }
    }

    private static String buildCondition(JsonNode conditionParams, Set<String> allParams) {
        StringBuilder conditionBuilder = new StringBuilder();

        for (int i = 0; i < conditionParams.size(); i++) {
            JsonNode param = conditionParams.get(i);
            String paramNo = param.get("paramNo").asText();
            String term = param.get("term").asText();
            String threshold = param.get("conditionThresholding").asText();

            String convertedParam = convertParamName(paramNo);
            allParams.add(convertedParam);

            conditionBuilder.append(convertedParam)
                    .append(" ")
                    .append(term)
                    .append(" ")
                    .append(threshold);

            if (i < conditionParams.size() - 1) {
                String connector = param.get("nextConditionConnectorChar").asText();
                conditionBuilder.append(" ").append(connector).append(" ");
            }
        }

        return conditionBuilder.toString();
    }

    private static String buildExecuteExpression(JsonNode conditionParams, String priceExpress) {
        StringBuilder expressBuilder = new StringBuilder();

        // 构建if条件
        expressBuilder.append("if (");
        for (int i = 0; i < conditionParams.size(); i++) {
            JsonNode param = conditionParams.get(i);
            String paramNo = param.get("paramNo").asText();
            String term = param.get("term").asText();
            String threshold = param.get("conditionThresholding").asText();

            String convertedParam = convertParamName(paramNo);
            expressBuilder.append(convertedParam)
                    .append(" ")
                    .append(term)
                    .append(" ")
                    .append(threshold);

            if (i < conditionParams.size() - 1) {
                String connector = param.get("nextConditionConnectorChar").asText();
                expressBuilder.append(" ").append("&&".equals(connector) ? "&&" : "||").append(" ");
            }
        }
        expressBuilder.append(") { ");

        // 构建执行体
        expressBuilder.append("BigDecimal price = (")
                .append(priceExpress)
                .append("); ")
                .append("BigDecimal num = (null); ")
                .append("BigDecimal cost = price * num; ")
                .append("ExpressResultInfo returnResult = new ExpressResultInfo(); ")
                .append("returnResult.setNumber(num); ")
                .append("returnResult.setPrice(price); ")
                .append("returnResult.setCost(cost); ")
                .append("return returnResult; } ");

        return expressBuilder.toString();
    }

    private static String convertParamName(String paramNo) {
        // 根据业务逻辑转换参数名
        // 这里假设AP开头的参数转换为特定格式
        if (paramNo.startsWith("AP")) {
            return "AP_SKU_PRICE"; // 简化处理，实际可能需要更复杂的映射
        }
        return paramNo;
    }

    private static String convertOperator(String term) {
        switch (term) {
            case ">":
                return ">";
            case ">=":
                return ">=";
            case "<":
                return "<";
            case "<=":
                return "<=";
            case "==":
            case "=":
                return "==";
            case "!=":
                return "!=";
            default:
                return term;
        }
    }

    // 测试方法
    public static void main(String[] args) {
        String inputJson = "{\n" +
                "  \"allExpressVariables\": [\n" +
                "    \"AP45005071\",\n" +
                "    \"AP41136799\"\n" +
                "  ],\n" +
                "  \"conditionEntities\": [\n" +
                "    {\n" +
                "      \"priceExpress\": \"80\",\n" +
                "      \"priceExpressVariables\": [],\n" +
                "      \"skuRuleConditionParams\": [\n" +
                "        {\n" +
                "          \"conditionThresholding\": \"0\",\n" +
                "          \"nextConditionConnectorChar\": \"and\",\n" +
                "          \"paramName\": \"当前属性值\",\n" +
                "          \"paramNo\": \"AP41136799\",\n" +
                "          \"term\": \">\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"conditionThresholding\": \"120\",\n" +
                "          \"nextConditionConnectorChar\": \"and\",\n" +
                "          \"paramName\": \"当前属性值\",\n" +
                "          \"paramNo\": \"AP41136799\",\n" +
                "          \"term\": \"<=\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"skuRuleConditionParamsVariables\": [\n" +
                "        \"AP41136799\"\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"priceExpress\": \"93\",\n" +
                "      \"priceExpressVariables\": [],\n" +
                "      \"skuRuleConditionParams\": [\n" +
                "        {\n" +
                "          \"conditionThresholding\": \"120\",\n" +
                "          \"nextConditionConnectorChar\": \"and\",\n" +
                "          \"paramName\": \"当前属性值\",\n" +
                "          \"paramNo\": \"AP41136799\",\n" +
                "          \"term\": \">\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"conditionThresholding\": \"150\",\n" +
                "          \"nextConditionConnectorChar\": \"and\",\n" +
                "          \"paramName\": \"当前属性值\",\n" +
                "          \"paramNo\": \"AP41136799\",\n" +
                "          \"term\": \"<=\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"skuRuleConditionParamsVariables\": [\n" +
                "        \"AP41136799\"\n" +
                "      ]\n" +
                "    }\n" +
                "  ],\n" +
                "  \"feeNumberExpress\": \"AP45005071\",\n" +
                "  \"feeNumberExpressVariables\": [\n" +
                "    \"AP45005071\"\n" +
                "  ],\n" +
                "  \"isCirculateFee\": false,\n" +
                "  \"isStepPrice\": false,\n" +
                "  \"rangeCondition\": true\n" +
                "}";

        try {
            String result = convertJsonStructure(inputJson);
            System.out.println("转换结果：");
            System.out.println(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
