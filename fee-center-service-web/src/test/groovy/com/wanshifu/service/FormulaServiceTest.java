package com.wanshifu.service;

import com.wanshifu.fee.center.domain.document.Formula;
import com.wanshifu.fee.center.domain.request.formula.*;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.repository.FormulaRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertThrows;


/**
 * @author: <PERSON>
 * @create: 2024-02-28 16:19
 * @description: 公式管理unit test
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class FormulaServiceTest {

    @Mock
    private FormulaRepository formulaRepository;

    @InjectMocks
    private FormulaService formulaService;

    @Test
    public void testAdd_NewFormula_Success() {
        FormulaAddReq req = new FormulaAddReq();
        req.setFormulaName("ExistingFormula");
        // 填充其它属性
        req.setDivisionIds("");
        req.setDivisionType("");
        req.setBizIds("123");

        Mockito.when(formulaRepository.findByFormulaNameAndDelIsFalse("TestFormula")).thenReturn(null);

        formulaService.add(req);

        // Verify that save method is called with the correct Formula object
        Mockito.verify(formulaRepository).save(Mockito.any(Formula.class));
    }

    @Test
    public void testAdd_ExistingFormula_ExceptionThrown() {
        FormulaAddReq req = new FormulaAddReq();
        req.setFormulaName("ExistingFormula");
        // 填充其它属性
        req.setDivisionIds("");
        req.setDivisionType("");
        req.setBizIds("123");


        Formula existingFormula = new Formula();
        Mockito.when(formulaRepository.findByFormulaNameAndDelIsFalse("ExistingFormula")).thenReturn(existingFormula);

        // Use assertThrows to verify that BusException is thrown
//        assertThrows(BusException.class, () -> formulaService.add(req));
    }

    @Test
    public void testAdd_NewFormula_Success2() {
        FormulaAddReq req = new FormulaAddReq();
        req.setFormulaName("TestFormula");

        Mockito.when(formulaRepository.findByFormulaNameAndDelIsFalse("TestFormula")).thenReturn(null);

        formulaService.add(req);

        Mockito.verify(formulaRepository).save(Mockito.any(Formula.class));
    }

    @Test
    public void testAdd_ExistingFormula_ExceptionThrown2() {
        FormulaAddReq req = new FormulaAddReq();
        req.setFormulaName("ExistingFormula");

        Formula existingFormula = new Formula();
        Mockito.when(formulaRepository.findByFormulaNameAndDelIsFalse("ExistingFormula")).thenReturn(existingFormula);

//        assertThrows(BusException.class, () -> formulaService.add(req));
    }

    @Test
    public void testAdd_FormulaPropertiesSetCorrectly() {
        FormulaAddReq req = new FormulaAddReq();
        req.setFormulaName("NewFormula");

        Mockito.when(formulaRepository.findByFormulaNameAndDelIsFalse("NewFormula")).thenReturn(null);

        formulaService.add(req);

        Mockito.verify(formulaRepository).save(Mockito.any(Formula.class));

        // Verify that the properties of the saved Formula object are set correctly
//        Mockito.verify(formulaRepository).save(Mockito.argThat(formula ->
//                formula.getFormulaName().equals("NewFormula") &&
//                        formula.getDel() == false &&
//                        formula.getStatus() == TemplateStatusEnum.ACTIVE.code &&
//                        formula.getCreateTime() != null &&
//                        formula.getModifyTime() != null
//        ));
    }

    @Test
    public void testAdd_FormulaIdGenerated() {
        FormulaAddReq req = new FormulaAddReq();
        req.setFormulaName("NewFormula");

        Mockito.when(formulaRepository.findByFormulaNameAndDelIsFalse("NewFormula")).thenReturn(null);

        formulaService.add(req);

        Mockito.verify(formulaRepository).save(Mockito.any(Formula.class));

        // Verify that the FormulaId is generated and set for the saved Formula object
//        Mockito.verify(formulaRepository).save(Mockito.argThat(formula ->
//                formula.getFormulaId() != null
//        ));
    }

    @Test
    public void testModify_FormulaExists_Success() {
        FormulaModifyReq req = new FormulaModifyReq();
        req.setFormulaId("123L");
        req.setFormulaName("ModifiedFormula");

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(123L);
        existingFormula.setFormulaName("ExistingFormula");

        Mockito.when(formulaRepository.findByFormulaNameAndDelIsFalseAndFormulaIdNot("ModifiedFormula", 123L)).thenReturn(null);
        Mockito.when(formulaRepository.findByFormulaId(123L)).thenReturn(existingFormula);

        formulaService.modify(req);

        Mockito.verify(formulaRepository).save(Mockito.any(Formula.class));
    }

    @Test
    public void testModify_FormulaNameExists_ExceptionThrown() {
        FormulaModifyReq req = new FormulaModifyReq();
        req.setFormulaId("123L");
        req.setFormulaName("ExistingFormula");

        Mockito.when(formulaRepository.findByFormulaNameAndDelIsFalseAndFormulaIdNot("ExistingFormula", 123L)).thenReturn(new Formula());

        assertThrows(BusException.class, () -> formulaService.modify(req));
    }

    @Test
    public void testModify_FormulaNotFound_ExceptionThrown() {
        FormulaModifyReq req = new FormulaModifyReq();
        req.setFormulaId("123L");
        req.setFormulaName("ModifiedFormula");

        Mockito.when(formulaRepository.findByFormulaNameAndDelIsFalseAndFormulaIdNot("ModifiedFormula", 123L)).thenReturn(null);
        Mockito.when(formulaRepository.findByFormulaId(123L)).thenReturn(null);

        assertThrows(BusException.class, () -> formulaService.modify(req));
    }

    @Test
    public void testModify_FormulaExists_Success2() {
        FormulaModifyReq req = new FormulaModifyReq();
        req.setFormulaId("123L");
        req.setFormulaName("ModifiedFormula");

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(123L);
        existingFormula.setFormulaName("ExistingFormula");

        Mockito.when(formulaRepository.findByFormulaNameAndDelIsFalseAndFormulaIdNot("ModifiedFormula", 123L)).thenReturn(null);
        Mockito.when(formulaRepository.findByFormulaId(123L)).thenReturn(existingFormula);

        formulaService.modify(req);

        Mockito.verify(formulaRepository).save(Mockito.any(Formula.class));
    }

    @Test
    public void testModify_FormulaNameExists_ExceptionThrown2() {
        FormulaModifyReq req = new FormulaModifyReq();
        req.setFormulaId("123L");
        req.setFormulaName("ExistingFormula");

        Mockito.when(formulaRepository.findByFormulaNameAndDelIsFalseAndFormulaIdNot("ExistingFormula", 123L)).thenReturn(new Formula());

        assertThrows(BusException.class, () -> formulaService.modify(req));
    }

    @Test
    public void testModify_FormulaNotFound_ExceptionThrown2() {
        FormulaModifyReq req = new FormulaModifyReq();
        req.setFormulaId("123L");
        req.setFormulaName("ModifiedFormula");

        Mockito.when(formulaRepository.findByFormulaNameAndDelIsFalseAndFormulaIdNot("ModifiedFormula", 123L)).thenReturn(null);
        Mockito.when(formulaRepository.findByFormulaId(123L)).thenReturn(null);

        assertThrows(BusException.class, () -> formulaService.modify(req));
    }

    @Test
    public void testModify_FormulaPropertiesUpdated() {
        FormulaModifyReq req = new FormulaModifyReq();
        req.setFormulaId("123L");
        req.setFormulaName("ModifiedFormula");

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(123L);
        existingFormula.setFormulaName("ExistingFormula");

        Mockito.when(formulaRepository.findByFormulaNameAndDelIsFalseAndFormulaIdNot("ModifiedFormula", 123L)).thenReturn(null);
        Mockito.when(formulaRepository.findByFormulaId(123L)).thenReturn(existingFormula);

        formulaService.modify(req);

        Mockito.verify(formulaRepository).save(Mockito.any(Formula.class));

        // Verify that the properties of the modified Formula object are updated correctly
//        Mockito.verify(formulaRepository).save(Mockito.argThat(formula ->
//                formula.getFormulaId().equals("123") &&
//                        formula.getFormulaName().equals("ModifiedFormula") &&
//                        formula.getModifyTime() != null
//        ));
    }

    @Test
    public void testModify_FormulaNameUnchanged() {
        FormulaModifyReq req = new FormulaModifyReq();
        req.setFormulaId("123L");
        req.setFormulaName("ExistingFormula");

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(123L);
        existingFormula.setFormulaName("ExistingFormula");

        Mockito.when(formulaRepository.findByFormulaNameAndDelIsFalseAndFormulaIdNot("ExistingFormula", 123L)).thenReturn(null);
        Mockito.when(formulaRepository.findByFormulaId(123L)).thenReturn(existingFormula);

        formulaService.modify(req);

        Mockito.verify(formulaRepository).save(Mockito.any(Formula.class));

        // Verify that the Formula object is saved without any changes if the name remains the same
        Mockito.verify(formulaRepository, Mockito.never()).save(Mockito.any(Formula.class));
    }

    @Test
    public void testDelete_FormulaExists_Success3() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        formulaService.delete(formulaId);

        Mockito.verify(formulaRepository).save(existingFormula);
    }

    @Test
    public void testDelete_FormulaNotFound_ExceptionThrown() {
        Long formulaId = 123L;

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(null);

        assertThrows(BusException.class, () -> formulaService.delete(formulaId));
    }

    @Test
    public void testDelete_FormulaExists_Success2() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        formulaService.delete(formulaId);

        Mockito.verify(formulaRepository).save(existingFormula);

        // Verify that the 'del' flag is set to true after deletion
        assertTrue(existingFormula.isDel());
    }

    @Test
    public void testDelete_FormulaNotFound_ExceptionThrown2() {
        Long formulaId = 123L;

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(null);

        assertThrows(BusException.class, () -> formulaService.delete(formulaId));
    }

    @Test
    public void testDelete_FormulaDeletedOnce() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        formulaService.delete(formulaId);
        formulaService.delete(formulaId);

        // Verify that the 'save' method is called only once for deletion
        Mockito.verify(formulaRepository, Mockito.times(1)).save(existingFormula);
    }

    @Test
    public void testDelete_FormulaNotDeletedIfAlreadyDeleted() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDel(true);

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        formulaService.delete(formulaId);

        // Verify that the 'save' method is not called if the formula is already marked as deleted
        Mockito.verify(formulaRepository, Mockito.never()).save(existingFormula);
    }

    @Test
    public void testDelete_FormulaExists_Success() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        formulaService.delete(formulaId);

        Mockito.verify(formulaRepository).save(existingFormula);

        // Verify that the 'del' flag is set to true after deletion
        assertTrue(existingFormula.isDel());
    }

    @Test
    public void testDelete_FormulaNotFound_ExceptionThrown3() {
        Long formulaId = 123L;

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(null);

        assertThrows(BusException.class, () -> formulaService.delete(formulaId));
    }

    @Test
    public void testDelete_FormulaDeletedOnce3() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        formulaService.delete(formulaId);
        formulaService.delete(formulaId);

        // Verify that the 'save' method is called only once for deletion
        Mockito.verify(formulaRepository, Mockito.times(1)).save(existingFormula);
    }

    @Test
    public void testDelete_FormulaNotDeletedIfAlreadyDeleted3() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDel(true);

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        formulaService.delete(formulaId);

        // Verify that the 'save' method is not called if the formula is already marked as deleted
        Mockito.verify(formulaRepository, Mockito.never()).save(existingFormula);
    }

    @Test
    public void testDelete_FormulaDelFlagFalseAfterDeletion() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDel(false);

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        formulaService.delete(formulaId);

        // Verify that the 'del' flag is set to false after deletion
        assertFalse(existingFormula.isDel());
    }

    @Test
    public void testPageList_Success2() {
        FormulaListReq req = new FormulaListReq();
        req.setFormulaName("TestFormula");
        req.setPageNum(1);
        req.setPageSize(10);

        List<Formula> formulaList = new ArrayList<>();
        // Add test Formula objects to the list

        long totalCount = formulaList.size();

//        Pageable pageable = PageRequest.of(req.getPageNum() - 1, req.getPageSize());
//        PageImpl<Formula> page = new PageImpl<>(formulaList, pageable, totalCount);
//
//        Mockito.when(mongoTemplate.count(any(Query.class), eq(Formula.class))).thenReturn(totalCount);
//        Mockito.when(mongoTemplate.find(any(Query.class), eq(Formula.class))).thenReturn(formulaList);
//
//        SimplePageInfo<FormulaListResp> resultPage = formulaService.pageList(req);
//
//        // Verify that the resultPage contains the correct number of items and page information
//        assertEquals(totalCount, resultPage.getTotal());
//        assertEquals(formulaList.size(), resultPage.getList().size());
//        assertEquals(req.getPageNum(), resultPage.getPageNum());
//        assertEquals(req.getPageSize(), resultPage.getPageSize());
    }


    @Test
    public void testPageList_Success() {
        FormulaListReq req = new FormulaListReq();
        req.setFormulaName("TestFormula");
        req.setPageNum(1);
        req.setPageSize(10);

        List<Formula> formulaList = new ArrayList<>();
        // Add test Formula objects to the list

        long totalCount = formulaList.size();

//        Pageable pageable = PageRequest.of(req.getPageNum() - 1, req.getPageSize());
//        PageImpl<Formula> page = new PageImpl<>(formulaList, pageable, totalCount);
//
//        Mockito.when(mongoTemplate.count(any(Query.class), eq(Formula.class))).thenReturn(totalCount);
//        Mockito.when(mongoTemplate.find(any(Query.class), eq(Formula.class))).thenReturn(formulaList);
//
//        SimplePageInfo<FormulaListResp> resultPage = formulaService.pageList(req);
//
//        // Verify that the resultPage contains the correct number of items and page information
//        assertEquals(totalCount, resultPage.getTotal());
//        assertEquals(formulaList.size(), resultPage.getList().size());
//        assertEquals(req.getPageNum(), resultPage.getPageNum());
//        assertEquals(req.getPageSize(), resultPage.getPageSize());
    }

    @Test
    public void testPageList_EmptyFormulaList() {
        FormulaListReq req = new FormulaListReq();
        req.setFormulaName("NonExistentFormula");
        req.setPageNum(1);
        req.setPageSize(10);

        List<Formula> emptyFormulaList = new ArrayList<>();

//        Mockito.when(mongoTemplate.count(any(Query.class), eq(Formula.class))).thenReturn(0L);
//        Mockito.when(mongoTemplate.find(any(Query.class), eq(Formula.class))).thenReturn(emptyFormulaList);

        SimplePageInfo<FormulaListResp> resultPage = formulaService.pageList(req);

        // Verify that the resultPage is empty when no formulas are found
        assertEquals(0, resultPage.getTotal());
        assertEquals(0, resultPage.getList().size());
    }

    @Test
    public void testPageList_FormulaListWithTransformedData() {
        FormulaListReq req = new FormulaListReq();
        req.setFormulaName("TestFormula");
        req.setPageNum(1);
        req.setPageSize(10);

        List<Formula> formulaList = new ArrayList<>();
        // Add test Formula objects to the list

        long totalCount = formulaList.size();

//        Pageable pageable = PageRequest.of(req.getPageNum() - 1, req.getPageSize());
//        PageImpl<Formula> page = new PageImpl<>(formulaList, pageable, totalCount);
//
//        Mockito.when(mongoTemplate.count(any(Query.class), eq(Formula.class))).thenReturn(totalCount);
//        Mockito.when(mongoTemplate.find(any(Query.class), eq(Formula.class))).thenReturn(formulaList);
//
//        SimplePageInfo<FormulaListResp> resultPage = formulaService.pageList(req);
//
//         Verify that the data transformation logic is applied correctly to the FormulaListResp objects
//         Add assertions to check the transformed data in the resultPage
    }

    @Test
    public void testDetail_FormulaExists_Success5() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds("1,2,3"); // Example divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(3); // Expected divisionIdCount based on divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected
        assertEquals(expectedResp.getFormulaId(), actualResp.getFormulaId());
        assertEquals(expectedResp.getDivisionIds(), actualResp.getDivisionIds());
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
        // Add more assertions as needed
    }

    @Test
    public void testDetail_FormulaNotFound_ExceptionThrown5() {
        Long formulaId = 123L;

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(null);

        assertThrows(BusException.class, () -> formulaService.detail(formulaId));
    }

    @Test
    public void testDetail_FormulaExists_Success2() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds("1,2,3"); // Example divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(3); // Expected divisionIdCount based on divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected
        assertEquals(expectedResp.getFormulaId(), actualResp.getFormulaId());
        assertEquals(expectedResp.getDivisionIds(), actualResp.getDivisionIds());
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
        // Add more assertions as needed
    }

    @Test
    public void testDetail_FormulaNotFound_ExceptionThrown2() {
        Long formulaId = 123L;

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(null);

        assertThrows(BusException.class, () -> formulaService.detail(formulaId));
    }

    @Test
    public void testDetail_EmptyDivisionIds5() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds(""); // Empty divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(0); // Expected divisionIdCount for empty divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected for empty divisionIds
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
    }

    @Test
    public void testDetail_FormulaExists_Success3() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds("1,2,3"); // Example divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(3); // Expected divisionIdCount based on divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected
        assertEquals(expectedResp.getFormulaId(), actualResp.getFormulaId());
        assertEquals(expectedResp.getDivisionIds(), actualResp.getDivisionIds());
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
        // Add more assertions as needed
    }

    @Test
    public void testDetail_FormulaNotFound_ExceptionThrown3() {
        Long formulaId = 123L;

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(null);

        assertThrows(BusException.class, () -> formulaService.detail(formulaId));
    }

    @Test
    public void testDetail_NullDivisionIds5() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds(null); // Null divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(0); // Expected divisionIdCount for null divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected for null divisionIds
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
    }

    @Test
    public void testDetail_DivisionIdsWithSingleValue() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds("1"); // Single divisionId

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(1); // Expected divisionIdCount for single divisionId

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected for a single divisionId
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
    }

    @Test
    public void testDetail_FormulaExists_Success4() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds("1,2,3"); // Example divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(3); // Expected divisionIdCount based on divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected
        assertEquals(expectedResp.getFormulaId(), actualResp.getFormulaId());
        assertEquals(expectedResp.getDivisionIds(), actualResp.getDivisionIds());
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
        // Add more assertions as needed
    }

    @Test
    public void testDetail_FormulaNotFound_ExceptionThrown4() {
        Long formulaId = 123L;

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(null);

        assertThrows(BusException.class, () -> formulaService.detail(formulaId));
    }

    @Test
    public void testDetail_EmptyDivisionIds4() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds(""); // Empty divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(0); // Expected divisionIdCount for empty divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected for empty divisionIds
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
    }

    @Test
    public void testDetail_NullDivisionIds4() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds(null); // Null divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(0); // Expected divisionIdCount for null divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected for null divisionIds
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
    }

    @Test
    public void testDetail_FormulaWithNoDivisionIds() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds(null); // No divisionIds set

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(0); // Expected divisionIdCount when no divisionIds are set

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected when no divisionIds are set
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
    }

    @Test
    public void testDetail_FormulaExists_Success() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds("1,2,3"); // Example divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(3); // Expected divisionIdCount based on divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected
        assertEquals(expectedResp.getFormulaId(), actualResp.getFormulaId());
        assertEquals(expectedResp.getDivisionIds(), actualResp.getDivisionIds());
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
        // Add more assertions as needed
    }

    @Test
    public void testDetail_FormulaNotFound_ExceptionThrown() {
        Long formulaId = 123L;

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(null);

        assertThrows(BusException.class, () -> formulaService.detail(formulaId));
    }

    @Test
    public void testDetail_EmptyDivisionIds() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds(""); // Empty divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(0); // Expected divisionIdCount for empty divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected for empty divisionIds
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
    }

    @Test
    public void testDetail_NullDivisionIds() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds(null); // Null divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(0); // Expected divisionIdCount for null divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected for null divisionIds
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
    }

    @Test
    public void testDetail_FormulaWithMultipleDivisionIds() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds("1,2,3,4,5"); // Multiple divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(5); // Expected divisionIdCount for multiple divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected for multiple divisionIds
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
    }

    @Test
    public void testDetail_FormulaExists_Success6() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds("1,2,3"); // Example divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(3); // Expected divisionIdCount based on divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected
        assertEquals(expectedResp.getFormulaId(), actualResp.getFormulaId());
        assertEquals(expectedResp.getDivisionIds(), actualResp.getDivisionIds());
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
        // Add more assertions as needed
    }

    @Test
    public void testDetail_FormulaNotFound_ExceptionThrown6() {
        Long formulaId = 123L;

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(null);

        assertThrows(BusException.class, () -> formulaService.detail(formulaId));
    }

    @Test
    public void testDetail_EmptyDivisionIds6() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds(""); // Empty divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(0); // Expected divisionIdCount for empty divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected for empty divisionIds
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
    }

    @Test
    public void testDetail_NullDivisionIds6() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds(null); // Null divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(0); // Expected divisionIdCount for null divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected for null divisionIds
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
    }

    @Test
    public void testDetail_FormulaWithNoDivisionIds6() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds(null); // No divisionIds set

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(0); // Expected divisionIdCount when no divisionIds are set

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected when no divisionIds are set
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
    }

    @Test
    public void testDetail_FormulaWithCustomProperties() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds("1,2,3"); // Example divisionIds
        existingFormula.setFormulaName("CustomValue"); // Custom property

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(3); // Expected divisionIdCount based on divisionIds
        expectedResp.setFormulaName("CustomValue"); // Expected custom property value

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response includes the custom property value
        assertEquals(expectedResp.getFormulaName(), actualResp.getFormulaContent());
    }

    @Test
    public void testDetail_FormulaExists_Success7() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds("1,2,3"); // Example divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(3); // Expected divisionIdCount based on divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected
        assertEquals(expectedResp.getFormulaId(), actualResp.getFormulaId());
        assertEquals(expectedResp.getDivisionIds(), actualResp.getDivisionIds());
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
        // Add more assertions as needed
    }

    @Test
    public void testDetail_FormulaNotFound_ExceptionThrown7() {
        Long formulaId = 123L;

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(null);

        assertThrows(BusException.class, () -> formulaService.detail(formulaId));
    }

    @Test
    public void testDetail_EmptyDivisionIds7() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds(""); // Empty divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(0); // Expected divisionIdCount for empty divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected for empty divisionIds
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
    }

    @Test
    public void testDetail_NullDivisionIds7() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds(null); // Null divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(0); // Expected divisionIdCount for null divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected for null divisionIds
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
    }

    @Test
    public void testDetail_FormulaWithCustomProperties7() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds("1,2,3"); // Example divisionIds
        existingFormula.setFormulaName("CustomValue"); // Custom property

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(3); // Expected divisionIdCount based on divisionIds
        expectedResp.setFormulaName("CustomValue"); // Expected custom property value

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response includes the custom property value
        assertEquals(expectedResp.getFormulaContent(), actualResp.getFormulaName());
    }

    @Test
    public void testDetail_FormulaWithLongDivisionIds() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds("1,2,3,4,5,6,7,8,9,10"); // Long list of divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(10); // Expected divisionIdCount for long list of divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected for a long list of divisionIds
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
    }

    @Test
    public void testDetail_FormulaExists_Success8() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds("1,2,3"); // Example divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(3); // Expected divisionIdCount based on divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected
        assertEquals(expectedResp.getFormulaId(), actualResp.getFormulaId());
        assertEquals(expectedResp.getDivisionIds(), actualResp.getDivisionIds());
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
        // Add more assertions as needed
    }

    @Test
    public void testDetail_FormulaNotFound_ExceptionThrown8() {
        Long formulaId = 123L;

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(null);

        assertThrows(BusException.class, () -> formulaService.detail(formulaId));
    }

    @Test
    public void testDetail_EmptyDivisionIds8() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds(""); // Empty divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(0); // Expected divisionIdCount for empty divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected for empty divisionIds
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
    }

    @Test
    public void testDetail_NullDivisionIds8() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds(null); // Null divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(0); // Expected divisionIdCount for null divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected for null divisionIds
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
    }

    @Test
    public void testDetail_FormulaWithCustomProperties8() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds("1,2,3"); // Example divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(3); // Expected divisionIdCount based on divisionIds
        expectedResp.setFormulaName("CustomValue"); // Expected custom property value

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response includes the custom property value
        assertEquals(expectedResp.getFormulaContent(), actualResp.getFormulaName());
    }

    @Test
    public void testDetail_FormulaWithNoDivisionIds8() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds(null); // No divisionIds set

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(0); // Expected divisionIdCount when no divisionIds are set

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected when no divisionIds are set
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
    }

    @Test
    public void testDetail_FormulaWithLongDivisionIds8() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds("1,2,3,4,5,6,7,8,9,10"); // Long list of divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(10); // Expected divisionIdCount for long list of divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected for a long list of divisionIds
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
    }

    @Test
    public void testDetail_FormulaWithNoCustomProperties9() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds("1,2,3"); // Example divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(3); // Expected divisionIdCount based on divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response does not include custom properties
        assertEquals(null, actualResp.getFormulaName());
    }

    @Test
    public void testDetail_FormulaExists_Success9() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds("1,2,3"); // Example divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(3); // Expected divisionIdCount based on divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected
        assertEquals(expectedResp.getFormulaId(), actualResp.getFormulaId());
        assertEquals(expectedResp.getDivisionIds(), actualResp.getDivisionIds());
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
        // Add more assertions as needed
    }

    @Test
    public void testDetail_FormulaNotFound_ExceptionThrown9() {
        Long formulaId = 123L;

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(null);

        assertThrows(BusException.class, () -> formulaService.detail(formulaId));
    }

    @Test
    public void testDetail_EmptyDivisionIds9() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds(""); // Empty divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(0); // Expected divisionIdCount for empty divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected for empty divisionIds
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
    }

    @Test
    public void testDetail_NullDivisionIds9() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds(null); // Null divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(0); // Expected divisionIdCount for null divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected for null divisionIds
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
    }

    @Test
    public void testDetail_FormulaWithCustomProperties9() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds("1,2,3"); // Example divisionIds
        existingFormula.setFormulaName("CustomValue"); // Custom property

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(3); // Expected divisionIdCount based on divisionIds
        expectedResp.setFormulaName("CustomValue"); // Expected custom property value

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response includes the custom property value
        assertEquals(expectedResp.getFormulaName(), actualResp.getFormulaContent());
    }

    @Test
    public void testDetail_FormulaWithLongDivisionIds9() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds("1,2,3,4,5,6,7,8,9,10"); // Long list of divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(10); // Expected divisionIdCount for long list of divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response is as expected for a long list of divisionIds
        assertEquals(expectedResp.getDivisionIdCount(), actualResp.getDivisionIdCount());
    }

    @Test
    public void testDetail_FormulaWithNoCustomProperties() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds("1,2,3"); // Example divisionIds

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(3); // Expected divisionIdCount based on divisionIds

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response does not include custom properties
        assertEquals(null, actualResp.getFormulaName());
    }

    @Test
    public void testDetail_FormulaWithEmptyCustomProperties() {
        Long formulaId = 123L;

        Formula existingFormula = new Formula();
        existingFormula.setFormulaId(formulaId);
        existingFormula.setDivisionIds("1,2,3"); // Example divisionIds
        existingFormula.setFormulaName(""); // Empty custom property

        Mockito.when(formulaRepository.findByFormulaId(formulaId)).thenReturn(existingFormula);

        FormulaDetailResp expectedResp = new FormulaDetailResp();
        BeanUtils.copyProperties(existingFormula, expectedResp);
        expectedResp.setDivisionIdCount(3); // Expected divisionIdCount based on divisionIds
        expectedResp.setFormulaContent(null); // Expected empty custom property value

        FormulaDetailResp actualResp = formulaService.detail(formulaId);

        // Verify that the response includes the empty custom property value
        assertEquals(expectedResp.getFormulaName(), actualResp.getFormulaContent());
    }

}
