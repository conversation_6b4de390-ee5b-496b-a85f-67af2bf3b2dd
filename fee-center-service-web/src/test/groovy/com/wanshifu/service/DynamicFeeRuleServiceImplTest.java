package com.wanshifu.service;

import com.wanshifu.fee.center.domain.document.DynamicFeeRule;
import com.wanshifu.fee.center.domain.enums.DivisionTypeEnum;
import com.wanshifu.fee.center.domain.enums.DynamicFeeRuleStatusEnum;
import com.wanshifu.fee.center.domain.enums.FeeTypeTagEnum;
import com.wanshifu.fee.center.domain.request.DynamicFeeRuleSaveReq;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.repository.DynamicFeeRuleRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import com.wanshifu.fee.center.domain.document.*;
import com.wanshifu.repository.FeeRuleRepository;
import org.springframework.data.mongodb.core.query.Query;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest
public class DynamicFeeRuleServiceImplTest {


    @Mock
    private DynamicFeeRuleRepository dynamicFeeRuleRepository;

    @Mock
    private MongoTemplate mongoTemplate;

    private DynamicFeeRule dynamicFeeRule;

    @InjectMocks
    private DynamicFeeRuleServiceImpl dynamicFeeRuleService;

    @Mock
    private FormulaService formulaService;

    @Mock
    private FeeRuleRepository feeRuleRepository;


    private DynamicFeeRuleSaveReq dynamicFeeRuleSaveReq;

    @BeforeEach
    void setUp() {
        dynamicFeeRuleSaveReq = new DynamicFeeRuleSaveReq();
        dynamicFeeRuleSaveReq.setSceneCode("testScene");
        dynamicFeeRuleSaveReq.setDivisionType("testDivisionType");
        dynamicFeeRuleSaveReq.setFeeTypeTag("testFeeTypeTag");
        dynamicFeeRuleSaveReq.setStartTime(new Date());
        dynamicFeeRuleSaveReq.setEndTime(new Date());
        // 其他属性设置...
        dynamicFeeRule = new DynamicFeeRule();
        dynamicFeeRule.setDynamicFeeRuleId(1L);
        dynamicFeeRule.setStatus(DynamicFeeRuleStatusEnum.AUDIT.code);
        dynamicFeeRule.setSceneCode("sceneCode");
        dynamicFeeRule.setUserIds(Arrays.asList(1L, 2L));
        dynamicFeeRule.setBizTag("bizTag");
        dynamicFeeRule.setDivisionType(DivisionTypeEnum.PROVINCE.code);
//        dynamicFeeRule.setTemplateIds(Arrays.asList(1L, 2L));
        dynamicFeeRule.setFeeTypeTag(FeeTypeTagEnum.SERVICE_FEE.code);
        dynamicFeeRule.setDivisionIds(Arrays.asList(1L, 2L));
        dynamicFeeRule.setDynamicCalculateRuleData(new DynamicCalculateRuleData());
        dynamicFeeRule.setStartTime(new Date());
        dynamicFeeRule.setEndTime(new Date());
    }

    @Test
    void testSave_success() {
        // Arrange
        DynamicFeeRule dynamicFeeRule = new DynamicFeeRule();
        dynamicFeeRule.setDynamicFeeRuleId(1L);
        dynamicFeeRule.setStatus(DynamicFeeRuleStatusEnum.AUDIT.code);
        dynamicFeeRule.setModifyTime(new Date());
        dynamicFeeRule.setCreateTime(new Date());
        dynamicFeeRule.setDivisionTypeName(DivisionTypeEnum.CITY.name);
        dynamicFeeRule.setFeeTypeTagName(FeeTypeTagEnum.SERVICE_FEE.name);

        when(dynamicFeeRuleRepository.save(any(DynamicFeeRule.class))).thenReturn(dynamicFeeRule);
        when(mongoTemplate.exists(any(), eq(DynamicFeeRule.class))).thenReturn(false);

        // Act
        DynamicFeeRule result = dynamicFeeRuleService.save(dynamicFeeRuleSaveReq);

        // Assert
        assertNotNull(result);
        assertEquals(DynamicFeeRuleStatusEnum.AUDIT.code, result.getStatus());
        assertEquals(DivisionTypeEnum.CITY.name, result.getDivisionTypeName());
        assertEquals(FeeTypeTagEnum.SERVICE_FEE.name, result.getFeeTypeTagName());
        verify(dynamicFeeRuleRepository, times(1)).save(any(DynamicFeeRule.class));
        verify(mongoTemplate, times(1)).exists(any(), eq(DynamicFeeRule.class));
    }

    @Test
    void testSave_repeatRule() {
        // Arrange
        when(mongoTemplate.exists(any(), eq(DynamicFeeRule.class))).thenReturn(true);

        // Act & Assert
        BusException exception = assertThrows(BusException.class, () -> {
            dynamicFeeRuleService.save(dynamicFeeRuleSaveReq);
        });

        assertEquals("该规则条件与已有数据规则重复", exception.getMessage());
        verify(dynamicFeeRuleRepository, times(0)).save(any(DynamicFeeRule.class));
        verify(mongoTemplate, times(1)).exists(any(), eq(DynamicFeeRule.class));
    }

    @Test
    void testSave_divisionTypeError() {
        // Arrange
        dynamicFeeRuleSaveReq.setDivisionType("invalidDivisionType");

        // Act & Assert
        BusException exception = assertThrows(BusException.class, () -> {
            dynamicFeeRuleService.save(dynamicFeeRuleSaveReq);
        });

        assertEquals("divisionType错误", exception.getMessage());
        verify(dynamicFeeRuleRepository, times(0)).save(any(DynamicFeeRule.class));
        verify(mongoTemplate, times(0)).exists(any(), eq(DynamicFeeRule.class));
    }

    @Test
    void testSave_feeTypeTagError() {
        // Arrange
        dynamicFeeRuleSaveReq.setFeeTypeTag("invalidFeeTypeTag");

        // Act & Assert
        BusException exception = assertThrows(BusException.class, () -> {
            dynamicFeeRuleService.save(dynamicFeeRuleSaveReq);
        });

        assertEquals("feeTypeTag错误", exception.getMessage());
        verify(dynamicFeeRuleRepository, times(0)).save(any(DynamicFeeRule.class));
        verify(mongoTemplate, times(0)).exists(any(), eq(DynamicFeeRule.class));
    }


    @Test
    public void testAudit_DynamicFeeRuleExistsAndStatusIsAudit() {
        when(mongoTemplate.findOne(any(Query.class), eq(DynamicFeeRule.class))).thenReturn(dynamicFeeRule);

        DynamicFeeRule result = dynamicFeeRuleService.audit(1L, DynamicFeeRuleStatusEnum.AUDIT);

        assertNotNull(result);
        assertEquals(DynamicFeeRuleStatusEnum.AUDIT.code, result.getStatus());
        verify(mongoTemplate, times(1)).findOne(any(Query.class), eq(DynamicFeeRule.class));
    }

    @Test
    public void testAudit_DynamicFeeRuleDoesNotExist() {
        when(mongoTemplate.findOne(any(Query.class), eq(DynamicFeeRule.class))).thenReturn(null);

        BusException exception = assertThrows(BusException.class, () -> {
            dynamicFeeRuleService.audit(1L, DynamicFeeRuleStatusEnum.AUDIT);
        });

        assertEquals("调价规则不存在或者不是待审核状态", exception.getMessage());
        verify(mongoTemplate, times(1)).findOne(any(Query.class), eq(DynamicFeeRule.class));
    }

    @Test
    public void testAudit_DynamicFeeRuleStatusIsNotAudit() {
        dynamicFeeRule.setStatus(DynamicFeeRuleStatusEnum.REJECT.code);

        when(mongoTemplate.findOne(any(Query.class), eq(DynamicFeeRule.class))).thenReturn(dynamicFeeRule);

        BusException exception = assertThrows(BusException.class, () -> {
            dynamicFeeRuleService.audit(1L, DynamicFeeRuleStatusEnum.AUDIT);
        });

        assertEquals("调价规则不存在或者不是待审核状态", exception.getMessage());
        verify(mongoTemplate, times(1)).findOne(any(Query.class), eq(DynamicFeeRule.class));
    }

    @Test
    public void testAudit_FeeRuleMatching() {
        when(mongoTemplate.findOne(any(Query.class), eq(DynamicFeeRule.class))).thenReturn(dynamicFeeRule);
        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(Collections.singletonList(new FeeRule()));

        dynamicFeeRuleService.audit(1L, DynamicFeeRuleStatusEnum.AUDIT);

        verify(mongoTemplate, times(1)).findOne(any(Query.class), eq(DynamicFeeRule.class));
        verify(mongoTemplate, times(1)).find(any(Query.class), eq(FeeRule.class));
        verify(feeRuleRepository, times(1)).save(anyList());
        verify(dynamicFeeRuleRepository, times(1)).save(any(DynamicFeeRule.class));
    }

    @Test
    public void testAudit_FeeRuleMatchingWithFormulaService() {
        when(mongoTemplate.findOne(any(Query.class), eq(DynamicFeeRule.class))).thenReturn(dynamicFeeRule);
        when(mongoTemplate.find(any(Query.class), eq(FeeRule.class))).thenReturn(Collections.singletonList(new FeeRule()));
        when(formulaService.handleFormulas(any(CalculateRuleData.class), any(DynamicCalculateRuleData.class), anyString(), anyString(), anyString())).thenReturn(new DynamicFeeRuleCalculateRuleData());

        dynamicFeeRuleService.audit(1L, DynamicFeeRuleStatusEnum.AUDIT);

        verify(mongoTemplate, times(1)).findOne(any(Query.class), eq(DynamicFeeRule.class));
        verify(mongoTemplate, times(1)).find(any(Query.class), eq(FeeRule.class));
        verify(formulaService, times(1)).handleFormulas(any(CalculateRuleData.class), any(DynamicCalculateRuleData.class), anyString(), anyString(), anyString());
        verify(feeRuleRepository, times(1)).save(anyList());
        verify(dynamicFeeRuleRepository, times(1)).save(any(DynamicFeeRule.class));
    }
}
