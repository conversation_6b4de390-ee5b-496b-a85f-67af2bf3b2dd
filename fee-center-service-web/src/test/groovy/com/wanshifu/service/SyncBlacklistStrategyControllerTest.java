package com.wanshifu.service;

import com.wanshifu.controller.SyncBlacklistStrategyController;
import com.wanshifu.fee.center.domain.document.SyncBlacklistStrategy;
import com.wanshifu.fee.center.domain.enums.DivisionTypeEnum;
import com.wanshifu.fee.center.domain.enums.LimitDivisionTypeEnum;
import com.wanshifu.fee.center.domain.request.strategy.SyncBlacklistStrategyBatchUpdateStatusReq;
import com.wanshifu.fee.center.domain.request.strategy.SyncBlacklistStrategyPageReq;
import com.wanshifu.fee.center.domain.request.strategy.SyncBlacklistStrategyPageResp;
import com.wanshifu.fee.center.domain.request.strategy.SyncBlacklistStrategySaveReq;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.repository.SyncBlacklistStrategyRepository;
import com.wanshifu.service.impl.SyncBlacklistStrategyServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.HashSet;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class SyncBlacklistStrategyControllerTest {

    private MockMvc mockMvc;

    @Mock
    private SyncBlacklistStrategyService syncBlacklistStrategyService;

    @InjectMocks
    private SyncBlacklistStrategyController syncBlacklistStrategyController;

    @Mock
    private SyncBlacklistStrategyRepository syncBlacklistStrategyRepository;

    @InjectMocks
    private SyncBlacklistStrategyServiceImpl syncBlacklistStrategyServiceImpl;

    @Mock
    private MongoTemplate mongoTemplate;


    @BeforeEach
    public void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(syncBlacklistStrategyController).build();
    }

    @Test
    public void add_ValidRequest_Success() throws Exception {
        doNothing().when(syncBlacklistStrategyService).add(any(SyncBlacklistStrategySaveReq.class));

        mockMvc.perform(post("/syncBlacklistStrategy/add")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{"
                                + "\"divisionType\": \"country\","
                                + "\"sceneCode\": \"testSceneCode\","
                                + "\"strategyName\": \"Test Strategy\""
                                + "}"))
                .andExpect(status().isOk());
    }

    @Test
    public void add_InvalidRequest_BusException() throws Exception {
        doThrow(new BusException("Non-country, limit division type cannot be empty"))
                .when(syncBlacklistStrategyService)
                .add(any(SyncBlacklistStrategySaveReq.class));

        mockMvc.perform(post("/syncBlacklistStrategy/add")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{"
                                + "\"divisionType\": \"city\","
                                + "\"sceneCode\": \"testSceneCode\","
                                + "\"strategyName\": \"Test Strategy\""
                                + "}"))
                .andExpect(status().isInternalServerError());
    }

    @Test
    public void add_LimitDivisionTypeNotCountry_DivisionIdsNotEmpty() throws Exception {
        Set<Long> divisionIds = new HashSet<>();
        divisionIds.add(1L);

        doNothing().when(syncBlacklistStrategyService).add(any(SyncBlacklistStrategySaveReq.class));

        mockMvc.perform(post("/syncBlacklistStrategy/add")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{"
                                + "\"divisionType\": \"city\","
                                + "\"limitDivisionType\": \"district\","
                                + "\"divisionIds\": [1],"
                                + "\"sceneCode\": \"testSceneCode\","
                                + "\"strategyName\": \"Test Strategy\""
                                + "}"))
                .andExpect(status().isOk());
    }

    @Test
    public void add_LimitDivisionTypeCountry_DivisionIdsEmpty() throws Exception {
        doNothing().when(syncBlacklistStrategyService).add(any(SyncBlacklistStrategySaveReq.class));

        mockMvc.perform(post("/syncBlacklistStrategy/add")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{"
                                + "\"divisionType\": \"country\","
                                + "\"limitDivisionType\": \"country\","
                                + "\"sceneCode\": \"testSceneCode\","
                                + "\"strategyName\": \"Test Strategy\""
                                + "}"))
                .andExpect(status().isOk());
    }


    @Test
    public void modify_ValidRequest_ShouldReturnOk() throws Exception {
        SyncBlacklistStrategySaveReq req = createValidRequest();
        doNothing().when(syncBlacklistStrategyService).modify(any(SyncBlacklistStrategySaveReq.class));

        mockMvc.perform(post("/syncBlacklistStrategy/modify")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"strategyId\":\"1\",\"strategyName\":\"Test Strategy\",\"divisionType\":\"country\",\"limitDivisionType\":\"country\"}"))
                .andExpect(status().isOk());

        verify(syncBlacklistStrategyService).modify(any(SyncBlacklistStrategySaveReq.class));
    }

    @Test
    public void modify_NonCountryDivisionTypeWithEmptyLimitDivisionType_ShouldThrowException() throws Exception {
        SyncBlacklistStrategySaveReq req = createValidRequest();
        req.setDivisionType(DivisionTypeEnum.CITY.code);
        req.setLimitDivisionType("");

        mockMvc.perform(post("/syncBlacklistStrategy/modify")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"strategyId\":\"1\",\"strategyName\":\"Test Strategy\",\"divisionType\":\"city\",\"limitDivisionType\":\"\"}"))
                .andExpect(status().isBadRequest());

        verify(syncBlacklistStrategyService).modify(any(SyncBlacklistStrategySaveReq.class));
    }

    @Test
    public void modify_NonCountryLimitDivisionTypeWithEmptyDivisionIds_ShouldThrowException() throws Exception {
        SyncBlacklistStrategySaveReq req = createValidRequest();
        req.setLimitDivisionType(LimitDivisionTypeEnum.CITY.code);
        req.setDivisionIds(new HashSet<>());

        mockMvc.perform(post("/syncBlacklistStrategy/modify")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"strategyId\":\"1\",\"strategyName\":\"Test Strategy\",\"divisionType\":\"country\",\"limitDivisionType\":\"city\",\"divisionIds\":[]}"))
                .andExpect(status().isBadRequest());

        verify(syncBlacklistStrategyService).modify(any(SyncBlacklistStrategySaveReq.class));
    }

    private SyncBlacklistStrategySaveReq createValidRequest() {
        SyncBlacklistStrategySaveReq req = new SyncBlacklistStrategySaveReq();
        req.setStrategyId("1");
        req.setStrategyName("Test Strategy");
        req.setDivisionType(DivisionTypeEnum.COUNTRY.code);
        req.setLimitDivisionType(LimitDivisionTypeEnum.COUNTRY.code);
        Set<Long> divisionIds = new HashSet<>();
        divisionIds.add(1L);
        req.setDivisionIds(divisionIds);
        return req;
    }


    @Test
    public void delete_ValidStrategyId_ShouldReturnNoContent() throws Exception {
        // Arrange
        String strategyId = "1";
        doNothing().when(syncBlacklistStrategyService).delete(Long.valueOf(strategyId));

        // Act & Assert
        mockMvc.perform(post("/syncBlacklistStrategy/delete")
                        .param("strategyId", strategyId))
                .andExpect(status().isNoContent());

        verify(syncBlacklistStrategyService).delete(Long.valueOf(strategyId));
    }

    @Test
    public void delete_InvalidStrategyId_ShouldReturnBadRequest() throws Exception {
        // Arrange
        String strategyId = "invalid";

        // Act & Assert
        mockMvc.perform(post("/syncBlacklistStrategy/delete")
                        .param("strategyId", strategyId))
                .andExpect(status().isBadRequest());
    }

    @Test
    public void delete_EmptyStrategyId_ShouldReturnBadRequest() throws Exception {
        // Arrange
        String strategyId = "";

        // Act & Assert
        mockMvc.perform(post("/syncBlacklistStrategy/delete")
                        .param("strategyId", strategyId))
                .andExpect(status().isBadRequest());
    }

    @Test
    public void delete_NoStrategyId_ShouldReturnBadRequest() throws Exception {
        // Act & Assert
        mockMvc.perform(post("/syncBlacklistStrategy/delete"))
                .andExpect(status().isBadRequest());
    }

    @Test
    public void detail_ValidStrategyId_ReturnsStrategy() throws Exception {
        // Arrange
        String strategyId = "1";
        SyncBlacklistStrategy strategy = new SyncBlacklistStrategy();
        strategy.setId("1");
        strategy.setStrategyName("Test Strategy");
        when(syncBlacklistStrategyService.detail(Long.valueOf(strategyId))).thenReturn(strategy);

        // Act & Assert
        mockMvc.perform(post("/syncBlacklistStrategy/detail")
                        .param("strategyId", strategyId))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"id\":\"1\",\"strategyName\":\"Test Strategy\"}"));
    }

    @Test
    public void detail_InvalidStrategyId_ReturnsNotFound() throws Exception {
        // Arrange
        String strategyId = "invalid";
        when(syncBlacklistStrategyService.detail(Long.valueOf(strategyId))).thenReturn(null);

        // Act & Assert
        mockMvc.perform(post("/syncBlacklistStrategy/detail")
                        .param("strategyId", strategyId))
                .andExpect(status().isNotFound());
    }

    @Test
    public void findPage_ValidRequest_ReturnsExpectedResponse() throws Exception {
        // Arrange
        SyncBlacklistStrategyPageResp responseItem = new SyncBlacklistStrategyPageResp();
        responseItem.setStrategyId("1");
        responseItem.setStrategyName("Test Strategy");

        SimplePageInfo<SyncBlacklistStrategyPageResp> pageInfo = new SimplePageInfo<>();
        pageInfo.setList(java.util.Collections.singletonList(responseItem));
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(20);
        pageInfo.setPages(1);
        pageInfo.setTotal(1L);

        when(syncBlacklistStrategyService.findPage(any(SyncBlacklistStrategyPageReq.class))).thenReturn(pageInfo);

        // Act & Assert
        mockMvc.perform(post("/syncBlacklistStrategy/findPage")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"pageNum\":1,\"pageSize\":20,\"sceneCode\":\"SC001\"}"))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"list\":[{\"strategyId\":\"1\",\"strategyName\":\"Test Strategy\"}],\"pageNum\":1,\"pageSize\":20,\"pages\":1,\"total\":1}"));
    }


    @Test
    void modify_StrategyNameExists_ThrowsException() {
        // Arrange
        SyncBlacklistStrategySaveReq req = new SyncBlacklistStrategySaveReq();
        req.setStrategyId("1");
        req.setStrategyName("Test Strategy");

        when(mongoTemplate.findOne(any(Query.class), eq(SyncBlacklistStrategy.class)))
                .thenReturn(new SyncBlacklistStrategy());

        // Act & Assert
        assertThrows(BusException.class, () -> syncBlacklistStrategyService.modify(req));
    }

    @Test
    void modify_StrategyNotFound_ThrowsException() {
        // Arrange
        SyncBlacklistStrategySaveReq req = new SyncBlacklistStrategySaveReq();
        req.setStrategyId("1");
        req.setStrategyName("Test Strategy");

        when(syncBlacklistStrategyRepository.findByStrategyId(anyLong())).thenReturn(null);

        // Act & Assert
        assertThrows(BusException.class, () -> syncBlacklistStrategyService.modify(req));
    }

    @Test
    void modify_ValidRequest_UpdatesStrategy() {
        // Arrange
        SyncBlacklistStrategySaveReq req = new SyncBlacklistStrategySaveReq();
        req.setStrategyId("1");
        req.setStrategyName("Test Strategy");

        SyncBlacklistStrategy strategy = new SyncBlacklistStrategy();
        strategy.setStrategyId(1L);
        strategy.setStrategyName("Old Strategy Name");

        when(syncBlacklistStrategyRepository.findByStrategyId(anyLong())).thenReturn(strategy);

        // Act
        syncBlacklistStrategyService.modify(req);

        // Assert
        verify(syncBlacklistStrategyRepository, times(1)).save(any(SyncBlacklistStrategy.class));
        assertEquals("Test Strategy", strategy.getStrategyName());
    }

    @Test
    public void delete_ValidStrategyId_ShouldDeleteStrategy() {
        Long strategyId = 1L;

        // Arrange
        doNothing().when(syncBlacklistStrategyRepository).deleteByStrategyId(strategyId);

        // Act
        syncBlacklistStrategyService.delete(strategyId);

        // Assert
        verify(syncBlacklistStrategyRepository).deleteByStrategyId(strategyId);
    }

    @Test
    public void batchUpdateStatus_ValidRequest_StatusUpdatedSuccessfully() {
        // Arrange
        Set<String> strategyIds = new HashSet<>();
        strategyIds.add("1");
        strategyIds.add("2");
        strategyIds.add("3");

        SyncBlacklistStrategyBatchUpdateStatusReq req = new SyncBlacklistStrategyBatchUpdateStatusReq();
        req.setOperator("operator");
        req.setStatus(1);
        req.setStrategyIds(strategyIds);

        // Act
        syncBlacklistStrategyService.batchUpdateStatus(req);

        // Assert
        ArgumentCaptor<Query> queryCaptor = ArgumentCaptor.forClass(Query.class);
        ArgumentCaptor<Update> updateCaptor = ArgumentCaptor.forClass(Update.class);

        verify(mongoTemplate, times(1)).updateMulti(queryCaptor.capture(), updateCaptor.capture(), eq(SyncBlacklistStrategy.class));

//        assertEquals(3, queryCaptor.getValue().getCriteria().size());
//        assertEquals("operator", updateCaptor.getValue().get(SyncBlacklistStrategy.Fields.operator, String.class));
//        assertEquals(1, updateCaptor.getValue().get(BaseDocument.Fields.status, Integer.class));
    }

}
