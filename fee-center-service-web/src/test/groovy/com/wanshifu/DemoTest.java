package com.wanshifu;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.wanshifu.framework.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
public class DemoTest {

    public static void main(String[] args) throws ParseException {

//        Set<String> s1 = new HashSet<>();
//        Set<String> s2 = new HashSet<>();
//        s1.add("1");
//        s1.add("2");
//        s1.add("3");
//
//        s2.add("2");
//        s2.add("1");
//        s2.add("3");
//
//        System.out.println(s1.equals(s2));
//        try {
//            String s = null;
//            s.length();
//        } catch (Exception e) {
//            StringWriter sw = new StringWriter();
//            PrintWriter pw = new PrintWriter(sw);
//            e.printStackTrace(pw);
//            log.error("空指针异常堆栈：\n{}", sw);
//        }

//        Date now = new Date();
//        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        Date parse = df.parse("2025-05-28 11:10:00");
//        boolean after = now.after(parse);
//        System.out.println(after);

//        Map<String, String> map = new HashMap<>();
//        map.put("1", "  one  ");
//        map.put("2", "  two  ");
//        map.compute("1", (k, v) -> {
//            if (v != null) {
//                return v.trim();
//            }
//            return v;
//        });
//        map.compute("2", (k, v) -> {
//            if (v!= null) {
//                return v.trim();
//            }
//            return v;
//        });
//        System.out.println(map);

//        AtomicReference<LocalDateTime> orderSubmitLocalTime = new AtomicReference<>();
//        LocalDateTime localDateTime = orderSubmitLocalTime.get();
//        System.out.println(localDateTime);

        String dateStr = "2025-07-07 12:12:12";
        Date date = DateUtil.parse(dateStr, DatePattern.NORM_DATETIME_PATTERN);
        LocalDateTime localDateTime = DateUtil.toLocalDateTime(date);
        System.out.println(localDateTime);


    }
}
