package com.wanshifu

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig
import com.wanshifu.framework.test.GroovySpockBaseTest
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.cloud.netflix.feign.EnableFeignClients
import org.springframework.context.annotation.EnableAspectJAutoProxy
import org.springframework.scheduling.annotation.EnableAsync
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.test.context.ContextConfiguration
import org.springframework.transaction.annotation.EnableTransactionManagement

/**
 * Title
 * Author <EMAIL>
 * Time 2018/6/4.
 * Version v1.0
 */
@ContextConfiguration(classes = [FeeCenterServiceApplication])
@SpringBootTest(classes = [FeeCenterServiceApplication])
@EnableTransactionManagement
@EnableFeignClients(basePackages = "com.wanshifu")
@EnableAsync
@EnableApolloConfig
@EnableAspectJAutoProxy
@EnableScheduling
class FeeCenterServiceAppTest extends GroovySpockBaseTest {

}