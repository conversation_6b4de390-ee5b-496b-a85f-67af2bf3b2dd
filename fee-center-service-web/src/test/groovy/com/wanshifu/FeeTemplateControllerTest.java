package com.wanshifu;

import com.wanshifu.controller.FeeTemplateController;
import com.wanshifu.fee.center.domain.document.FeeTemplate;
import com.wanshifu.fee.center.domain.request.FeeTemplateQueryByConditionReq;
import com.wanshifu.fee.center.domain.response.GetFeeTemplateServiceIdAndNameResp;
import com.wanshifu.fee.center.domain.request.template.GetServiceIdAndNameBySceneCodeAndServiceNameReq;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.service.FeeTemplateService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

public class FeeTemplateControllerTest {

    @Mock
    private FeeTemplateService feeTemplateService;

    @InjectMocks
    private FeeTemplateController feeTemplateController;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getServiceIdAndNameBySceneCodeAndServiceName_NoTemplates_ReturnsEmptyPage() {
        GetServiceIdAndNameBySceneCodeAndServiceNameReq req = new GetServiceIdAndNameBySceneCodeAndServiceNameReq();
        req.setSceneCode("sceneCode");
        req.setServiceName("serviceName");

        Page<FeeTemplate> emptyPage = new PageImpl<>(Collections.emptyList(), new PageRequest(0, 20), 0);
        when(feeTemplateService.queryServiceByCondition(any(FeeTemplateQueryByConditionReq.class))).thenReturn(emptyPage);

        SimplePageInfo<GetFeeTemplateServiceIdAndNameResp> result = feeTemplateController.getServiceIdAndNameBySceneCodeAndServiceName(req);

        assertEquals(0, result.getTotal());
        assertEquals(0, result.getPages());
        assertEquals(0, result.getList().size());
    }

    @Test
    public void getServiceIdAndNameBySceneCodeAndServiceName_WithTemplates_ReturnsPopulatedPage() {
        GetServiceIdAndNameBySceneCodeAndServiceNameReq req = new GetServiceIdAndNameBySceneCodeAndServiceNameReq();
        req.setSceneCode("sceneCode");
        req.setServiceName("serviceName");

        FeeTemplate feeTemplate = new FeeTemplate();
        Map<String, String> bizRule = new HashMap<>();
        bizRule.put("serviceId", "serviceId1");
        bizRule.put("serviceName", "serviceName1");
        feeTemplate.setBizRule(bizRule);

        Page<FeeTemplate> page = new PageImpl<>(Collections.singletonList(feeTemplate), new PageRequest(0, 20), 1);
        when(feeTemplateService.queryServiceByCondition(any(FeeTemplateQueryByConditionReq.class))).thenReturn(page);

        SimplePageInfo<GetFeeTemplateServiceIdAndNameResp> result = feeTemplateController.getServiceIdAndNameBySceneCodeAndServiceName(req);

        assertEquals(1, result.getTotal());
        assertEquals(1, result.getPages());
        assertEquals(1, result.getList().size());
        assertEquals("serviceId1", result.getList().get(0).getServiceId());
        assertEquals("serviceName1", result.getList().get(0).getServiceName());
    }

    @Test
    public void getServiceIdAndNameBySceneCodeAndServiceName_NoTemplates_ReturnsEmptyPage2() {
        GetServiceIdAndNameBySceneCodeAndServiceNameReq req = new GetServiceIdAndNameBySceneCodeAndServiceNameReq();
        req.setSceneCode("sceneCode");
        req.setServiceName("serviceName");

        Page<FeeTemplate> emptyPage = new PageImpl<>(Collections.emptyList(), new PageRequest(0, 20), 0);
        when(feeTemplateService.queryServiceByCondition(any(FeeTemplateQueryByConditionReq.class))).thenReturn(emptyPage);

        SimplePageInfo<GetFeeTemplateServiceIdAndNameResp> result = feeTemplateController.getServiceIdAndNameBySceneCodeAndServiceName(req);

        assertEquals(0, result.getTotal());
        assertEquals(0, result.getPages());
        assertEquals(0, result.getList().size());
    }

    @Test
    public void getServiceIdAndNameBySceneCodeAndServiceName_WithTemplates_ReturnsPopulatedPage2() {
        GetServiceIdAndNameBySceneCodeAndServiceNameReq req = new GetServiceIdAndNameBySceneCodeAndServiceNameReq();
        req.setSceneCode("sceneCode");
        req.setServiceName("serviceName");

        FeeTemplate feeTemplate = new FeeTemplate();
        Map<String, String> bizRule = new HashMap<>();
        bizRule.put("serviceId", "serviceId1");
        bizRule.put("serviceName", "serviceName1");
        feeTemplate.setBizRule(bizRule);

        Page<FeeTemplate> page = new PageImpl<>(Collections.singletonList(feeTemplate), new PageRequest(0, 20), 1);
        when(feeTemplateService.queryServiceByCondition(any(FeeTemplateQueryByConditionReq.class))).thenReturn(page);

        SimplePageInfo<GetFeeTemplateServiceIdAndNameResp> result = feeTemplateController.getServiceIdAndNameBySceneCodeAndServiceName(req);

        assertEquals(1, result.getTotal());
        assertEquals(1, result.getPages());
        assertEquals(1, result.getList().size());
        assertEquals("serviceId1", result.getList().get(0).getServiceId());
        assertEquals("serviceName1", result.getList().get(0).getServiceName());
    }

    @Test
    public void getServiceIdAndNameBySceneCodeAndServiceName_EmptySceneCode_ThrowsBusException() {
        GetServiceIdAndNameBySceneCodeAndServiceNameReq req = new GetServiceIdAndNameBySceneCodeAndServiceNameReq();
        req.setSceneCode("");
        req.setServiceName("serviceName");

        BusException exception = assertThrows(BusException.class, () -> feeTemplateController.getServiceIdAndNameBySceneCodeAndServiceName(req));

        assertEquals("场景为空", exception.getMessage());
    }
}