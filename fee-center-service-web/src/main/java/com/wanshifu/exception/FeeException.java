package com.wanshifu.exception;

import lombok.Data;

@Data
public class FeeException extends RuntimeException {
    private String code = "-1";
    private String msg;

    public FeeException(String code, String message) {
        super(message);
        this.msg = message;
        this.code = code;
    }

    public FeeException(String message) {
        super(message);
        this.msg = message;
    }

    public FeeException(String message, Throwable cause) {
        super(message, cause);
        this.msg = message;
    }

    public FeeException(Throwable cause) {
        super(cause);
        this.msg = cause.getMessage();
    }

    protected FeeException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
        this.msg = message;
    }
}
