package com.wanshifu.controller.master2c;

import com.google.common.base.Strings;
import com.wanshifu.domain.request.master2c.*;
import com.wanshifu.fee.center.domain.document.master2c.ServePrice2C4MasterDetail;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.infrastructure.utils.CommonUtils;
import com.wanshifu.service.master2c.ServePriceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("servePrice2C4Master")
@Api(tags = "C端师傅服务价格")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ServePrice2C4MasterController {

    private final ServePriceService servePriceService;

    @ApiOperation("添加C端师傅价格")
    @PostMapping("add")
    public void add(@RequestBody @Validated ServePrice2C4MasterAddReq req) {
        validateParams(req);
        servePriceService.add(req);
    }


    @ApiOperation("修改C端师傅价格")
    @PostMapping("modify")
    public void modify(@RequestBody @Validated ServePrice2C4MasterModifyReq req) {
        validateParams(req);
        servePriceService.modify(req);
    }


    @ApiOperation("查看C端师傅价格详情")
    @PostMapping("getDetail")
    public ServePrice2C4MasterDetail getDetail(@RequestParam("priceId") String priceId) {
        return servePriceService.getDetail(Long.parseLong(priceId));
    }


    @ApiOperation("审核通过C端师傅价格")
    @PostMapping("approve")
    public void approve(@RequestParam("priceId") String priceId) {
        servePriceService.approve(Long.parseLong(priceId));
    }


    @ApiOperation("驳回C端师傅价格")
    @PostMapping("reject")
    public void reject(@RequestParam("priceId") String priceId, @RequestParam("reason") String reason) {
        servePriceService.reject(Long.parseLong(priceId), CommonUtils.getCurrentLoginName(), reason);
    }


    @ApiOperation("停用C端师傅价格")
    @PostMapping("inactive")
    public void inactive(@RequestParam("priceId") String priceId) {
        servePriceService.inactive(Long.parseLong(priceId));
    }

    @ApiOperation("查询C端师傅价格分页列表")
    @PostMapping("getPageList")
    public SimplePageInfo<ServePrice2C4MasterPageResp> getPageList(@RequestBody ServePrice2C4MasterPageReq req) {
        return servePriceService.getPageList(req);
    }

    @ApiOperation("查询C端师傅价格角标")
    @PostMapping("getCount")
    public ServePrice2C4MasterCountResp getCount() {
        return servePriceService.getCount();
    }


    @ApiOperation("查询C端师傅价格草稿分页列表(改价列表)")
    @PostMapping("getDraftPageList")
    public SimplePageInfo<DraftServePrice2C4MasterPageResp> getDraftPageList(@RequestBody ServePrice2C4MasterPageReq req) {
        return servePriceService.getDraftPageList(req);
    }


    @ApiOperation("查询正式价格配置是否存在可用草稿(改价列表)")
    @PostMapping("getIsExistDraftByPriceId")
    public DraftServePriceIsExistResp getIsExistDraftByPriceId(@RequestBody @Validated DraftServePriceIsExistReq req) {
        return servePriceService.getIsExistDraftByPriceId(req);
    }

    @ApiOperation("添加C端师傅价格草稿(改价)")
    @PostMapping("addDraft")
    public void addDraft(@RequestBody @Validated DraftServePrice2C4MasterAddReq req) {
        validateParams(req);
        servePriceService.addDraft(req);
    }

    @ApiOperation("修改C端师傅价格草稿(改价)")
    @PostMapping("modifyDraft")
    public void modifyDraft(@RequestBody @Validated DraftServePrice2C4MasterModifyReq req) {
        validateParams(req);
        servePriceService.modifyDraft(req);
    }

    @ApiOperation("查看C端师傅价格草稿详情(改价)")
    @PostMapping("getDraftDetail")
    public DraftServePrice2C4MasterDetail getDraftDetail(@RequestParam("draftId") String draftId) {
        return servePriceService.getDraftDetail(Long.parseLong(draftId));
    }


    @ApiOperation("审核通过C端师傅价格草稿(改价)")
    @PostMapping("approveDraft")
    public void approveDraft(@RequestParam("draftId") String draftId) {
        servePriceService.approveDraft(Long.parseLong(draftId));
    }


    @ApiOperation("驳回C端师傅价格草稿(改价)")
    @PostMapping("rejectDraft")
    public void rejectDraft(@RequestParam("draftId") String draftId, @RequestParam("reason") String reason) {
        servePriceService.rejectDraft(Long.parseLong(draftId), CommonUtils.getCurrentLoginName(), reason);
    }


    @ApiOperation("取消C端师傅价格草稿(改价)")
    @PostMapping("cancelDraft")
    public void cancelDraft(@RequestParam("draftId") String draftId) {
        String remark = "操作人：".concat(CommonUtils.getCurrentLoginName()).concat("进行取消。");
        servePriceService.cancelDraft(Long.parseLong(draftId), remark);
    }

    private void validateParams(ServePrice2C4MasterAddReq req) {
        if  (req == null) {
            throw new BusException("参数为空");
        }
        Date startTime = req.getStartTime();
        if (startTime == null) {
            throw new BusException("开始生效时间不能为空");
        }
        if  (startTime.getTime() <= System.currentTimeMillis()) {
            throw new BusException("开始生效时间不能小于等于当前时间");
        }

        String configName = req.getConfigName();
        if (!Strings.isNullOrEmpty(configName)) {
            configName = configName.trim();
            if (configName.length() > 50) {
                throw new BusException("服务配置名称不能超过50个字符");
            }
        }

        List<Long> cityIds = req.getCityIds();

        ServePrice2C4MasterAddReq.CityGroup cityGroup = req.getCityGroup();
        if (CollectionUtils.isEmpty(cityIds) && Objects.isNull(cityGroup)) {
            throw new BusException("必须选择城市或者城市组");
        }

        List<ServePrice2C4MasterAddReq.ServeConfig> serveConfigs = req.getServeConfigs();
        if (CollectionUtils.isNotEmpty(serveConfigs)) {
            Set<Long> configIds = serveConfigs.stream().map(ServePrice2C4MasterAddReq.ServeConfig::getServeConfigId).collect(Collectors.toSet());
            if (serveConfigs.size() > configIds.size()) {
                throw new BusException("参数选择存在重复，请检查！");
            }
        }
    }
}
