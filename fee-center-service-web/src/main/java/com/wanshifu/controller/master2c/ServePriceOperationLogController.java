package com.wanshifu.controller.master2c;

import com.wanshifu.fee.center.domain.document.master2c.ServePriceOperationLog;
import com.wanshifu.fee.center.domain.document.master2c.ServePrice2C4MasterDetail;
import com.wanshifu.service.master2c.ServePriceOperationLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("servePriceOperationLog")
@Api(tags = "C端师傅服务价格操作日志")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ServePriceOperationLogController {

    private final ServePriceOperationLogService servePriceOperationLogService;

    @ApiOperation("获取服务价格操作日志")
    @GetMapping("getServePriceOperationLog")
    public ServePriceOperationLog getServePriceOperationLog(@RequestParam("priceId") String priceId) {
        return servePriceOperationLogService.getServePriceOperationLog(Long.parseLong(priceId));
    }


    @ApiOperation("查看编辑前价格详情快照")
    @GetMapping("getPriceDetailSnapshot")
    public ServePrice2C4MasterDetail getPriceDetailSnapshot(@RequestParam("logId") String logId) {
        return servePriceOperationLogService.getPriceDetailSnapshot(Long.parseLong(logId));
    }
}
