package com.wanshifu.controller;

import com.alibaba.fastjson.JSON;
import com.wanshifu.fee.center.api.PriceCalculatorApi;
import com.wanshifu.fee.center.domain.request.calculate.*;
import com.wanshifu.infrastructure.utils.EnvUtil;
import com.wanshifu.service.PriceCalculatorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Api("价格计算")
@Slf4j
@RestController
@RequestMapping("priceCalculator")
public class PriceCalculatorController implements PriceCalculatorApi {

    private final PriceCalculatorService priceCalculatorService;

    @ApiOperation("标准计价")
    @Override
    @RequestMapping("standardPricing")
    public StandardPricingResponse standardPricing(@RequestBody @Validated StandardPricingRequest request) {
        log.info("standardPricing request: {}", JSON.toJSONString(request));
        return priceCalculatorService.standardPricing(request);
    }

    @ApiOperation("计价并比价")
    @Override
    @RequestMapping("calculateAndComparePrice")
    public CalculateAndComparePriceResponse calculateAndComparePrice(@RequestBody @Validated CalculateAndComparePriceRequest request) {
        log.info("calculateAndComparePrice request: {}", JSON.toJSONString(request));
        return priceCalculatorService.calculateAndComparePrice(request);
    }


    @ApiOperation("计算动态价（服务维度，即调价单元为“服务”）")
    @Override
    @PostMapping("calculateServiceDynamicPrice")
    public CalculateServiceDynamicPriceResponse calculateServiceDynamicPrice(@RequestBody @Validated CalculateServiceDynamicPriceRequest request) {
        log.info("calculateServiceDynamicPrice request: {}", JSON.toJSONString(request));
        return priceCalculatorService.calculateServiceDynamicPrice(request);
    }
}
