package com.wanshifu.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.wanshifu.adapter.api.ServiceApi;
import com.wanshifu.adapter.api.ServiceSkuApi;
import com.wanshifu.adapter.dto.AreaListDto;
import com.wanshifu.adapter.dto.account.ServiceSkuRuleUserRelationDto;
import com.wanshifu.adapter.dto.area.AreaListReq;
import com.wanshifu.adapter.dto.attribute.sku.ServiceSkuReq;
import com.wanshifu.adapter.dto.service.sku.ServiceSkuResp;
import com.wanshifu.adapter.enums.AreaListSceneEnum;
import com.wanshifu.adapter.enums.AreaListTypeEnum;
import com.wanshifu.adapter.enums.ProductLineEnum;
import com.wanshifu.adapter.enums.sku.ServiceSkuRuleBaseResp;
import com.wanshifu.adapter.enums.sku.ServiceSkuRuleResp;
import com.wanshifu.api.FileUploadUtils;
import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.bean.FileUploadResp;
import com.wanshifu.domain.request.rule.UpdateActivityPriceRefreshRequest;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.biz.PlatformFixedPriceBizRule;
import com.wanshifu.fee.center.domain.constant.Constant;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.*;
import com.wanshifu.fee.center.domain.enums.*;
import com.wanshifu.fee.center.domain.request.FeeTemplateConfigureReq;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.MapUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import static org.springframework.data.mongodb.core.query.Criteria.where;

/**
 * @author: Chen Yong
 * @create: 2024-04-23 9:27
 * @description: 刷数据专用Controller
 */
@Slf4j
@Api(tags = "刷数据")
@RestController
@RequestMapping("flushData")
public class FlushDataController {

    @Resource
    private ServiceSkuApi serviceSkuApi;
    @Resource
    private ServiceApi serviceApi;
    @Resource
    private FeeTemplateController feeTemplateController;
    @Resource
    private FeeRuleService feeRuleService;
    @Resource
    private FeeTemplateService feeTemplateService;
    @Resource
    private AddressApi addressApi;
    @Resource
    private SceneInfoService sceneInfoService;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Resource
    private ExecutorService executorService;
    @Resource
    private BizRuleMappingService bizRuleMappingService;
    @Resource
    private RefreshDataService refreshDataService;


    @ApiOperation("根据serviceId、skuNo、bizTag、newPrice更新 师傅招募活动价、合约师傅的价格")
    @PostMapping("updateFeeRulePrice")
    public void updateFeeRulePrice(@RequestBody @Validated List<UpdateActivityPriceRefreshRequest> requestList) {
        refreshDataService.updateFeeRulePrice(requestList);
    }


    @ApiOperation("将bizRuleMapping表中的数据转换结构后刷到FeeTemplateMapping中")
    @GetMapping("flushBizRuleMappingToTemplateMapping")
    public void flushBizRuleMappingToTemplateMapping() {
        bizRuleMappingService.flushBizRuleMappingToTemplateMapping();
    }


    /**
     * TODO bizRule中的sceneCode和sceneName字段均为冗余的，其实不应该存在，后续应该去掉，只保留FeeTemplate外层的即可
     */
    @ApiOperation("修正FeeTemplate的bizRule中的sceneCode和sceneName字段")
    @PostMapping("correctSceneInfoOfTemplate")
    public void correctSceneInfoOfTemplate() {
        feeTemplateService.correctSceneInfoOfTemplate();
    }


    @ApiOperation("计价模板-补全-serviceCategoryId")
    @GetMapping("feeTemplate/completeServiceCategoryId")
    public void completeServiceCategoryId(@RequestParam(value = "sceneCode", required = false) String sceneCode) {
//        SceneInfo sceneInfo = sceneInfoService.query(sceneCode);
//        if (Objects.isNull(sceneInfo)) {
//            throw new BusException("场景不存在");
//        }
        feeTemplateService.completeServiceCategoryId(sceneCode, "系统补全serviceCategoryId字段，不影响价格");
    }

    @ApiOperation("修正【协议师傅】场景下的FeeRule的status字段")
    @PostMapping("feeRule/correctFeeRuleStatus")
    public void correctFeeRuleStatus() {
        feeRuleService.correctStatus();
    }


    @ApiOperation("校正定价Id（模板Id）")
    @GetMapping("feeRule/correctTemplateId")
    public void correctTemplateId(@RequestParam("sceneCode") String sceneCode,
                                  @RequestParam("bizTagSet") Set<String> bizTagSet) {
        feeRuleService.correctTemplateId(sceneCode, bizTagSet);
    }


    @ApiOperation("根据一级商品类目Id刷计价规则数据(分页)(总包一口价)")
    @PostMapping("flushFeeRuleByLv1GoodsCategoryIdPage4Enterprise")
    public List<String> flushFeeRuleByLv1GoodsCategoryIdPage4Enterprise(@RequestParam("lv1GoodsCategoryId") Long lv1GoodsCategoryId,
                                                                        @RequestParam(value = "serviceTypeId", required = false) Long serviceTypeId,
                                                                        @RequestParam("sceneCode") String sceneCode,
                                                                        @RequestParam("productLine") String productLine,
                                                                        @RequestParam("serviceModeId") Long serviceModeId,
                                                                        @RequestParam("pageSize") Integer pageSize,
                                                                        @RequestParam("pageNum") Integer pageNum) {
        /**
         * 思路：
         * 1、根据一级商品类目Id查找服务SKU数据
         * 2、根据服务SKU数据查找计价模板数据
         * 3、如果SKU是简单类型（即pricingMode=1），则直接将计价模板Id更新到sku rule中，构建成一条feeRule。
         * 4、如果SKU是复杂类型（即pricingMode=2），则需要根据skuId查找sku rule，再根据skuRuleId查找地区、用户信息
         * 5、根据skuRuleId、地区、用户信息构建成一条feeRule
         * （每一条sku rule 最多对应一条地区、用户信息，所以skuRuleId+地区+用户可以生成唯一的一条feeRule）
         */
        SceneInfo sceneInfo = sceneInfoService.query(sceneCode);
        if (sceneInfo == null) {
            throw new BusException("场景不存在或已删除");
        }
        String sceneName = sceneInfo.getSceneName();
        // 平台一口价，全国
        List<ServiceSkuRuleResp> platform_fixed_price_nationwide_resultList = new ArrayList<>();
        // 平台一口价，地区
        List<ServiceSkuRuleResp> platform_fixed_price_region_resultList = new ArrayList<>();
        // 商家定制一口价，全国
        List<ServiceSkuRuleResp> platform_fixed_price_user_nationwide_resultList = new ArrayList<>();
        // 商家定制一口价，地区
        List<ServiceSkuRuleResp> platform_fixed_price_user_region_resultList = new ArrayList<>();
        // 其实返回的是ServiceSku + 费用名称
        List<ServiceSkuRuleResp> serviceSkuRuleList = serviceSkuApi.getServiceSkuRule(lv1GoodsCategoryId, serviceTypeId, productLine, serviceModeId);
        if (com.wanshifu.framework.utils.CollectionUtils.isEmpty(serviceSkuRuleList)) {
            log.error("未找到服务SKU规则数据");
            return Collections.emptyList();
        }

        int size = serviceSkuRuleList.size();
        log.error("serviceSkuRuleList数据条数:{}", size);

        List<String> resultList = new ArrayList<>();

        for (int i = (pageNum - 1) * pageSize; (i < pageNum * pageSize && i < size); i++) {
            ServiceSkuRuleResp ruleResp = serviceSkuRuleList.get(i);
//            String price = ruleResp.getPrice();
            Long skuId = ruleResp.getSkuId();
            Long serviceId = ruleResp.getServiceId();
            String skuNo = ruleResp.getSkuNo();


            Byte pricingMode = ruleResp.getPricingMode();
//            String bizTag = ruleResp.getBizTag();
            List<FeeTemplate> feeTemplateList = feeTemplateService.selectByServiceIdAndSkuNo(sceneCode, serviceId.toString(), skuNo);
            if (com.wanshifu.framework.utils.CollectionUtils.isEmpty(feeTemplateList)) {
                log.error("sceneCode:{}, serviceId:{}, skuNo:{} 未找到计价模板", sceneCode, serviceId, skuNo);
                continue;
            }
            FeeTemplate feeTemplate = feeTemplateList.get(0);
            Long templateId = feeTemplate.getTemplateId();
            String attributeDisplayName = feeTemplate.getBizRule().get(CommonBizRule.Fields.attributeDisplayName);
            // 2：复杂模式-通过sku规则计价
            ruleResp.setFeeName(attributeDisplayName);
            ruleResp.setTemplateId(templateId == null ? "" : templateId.toString());
            platform_fixed_price_nationwide_resultList.add(ruleResp);

            if (Byte.valueOf("2").equals(pricingMode)) {
                List<ServiceSkuRuleBaseResp> ruleBaseRespList = serviceSkuApi.getServiceSkuRuleBySkuIdAndProductLine(skuId, productLine);
                if (com.wanshifu.framework.utils.CollectionUtils.isEmpty(ruleBaseRespList)) {
                    log.error("未找到服务SKU规则数据, serviceId:{}, skuNo:{}", serviceId, skuNo);
                    continue;
                }
                // 根据skuRuleId 查找 地区、用户信息
                Set<Long> skuRuleIdList = ruleBaseRespList.stream().map(ServiceSkuRuleBaseResp::getSkuRuleId).collect(Collectors.toSet());
                AreaListReq areaListReq = new AreaListReq();
                areaListReq.setListType(AreaListTypeEnum.white.name());
                areaListReq.setScene(AreaListSceneEnum.sku_rule.name());
                areaListReq.setSceneRelationIds(skuRuleIdList);
                List<AreaListDto> areaLists = serviceSkuApi.getAreaLists(areaListReq);
                // key：divisionId，value：Address
                Map<Long, Address> areaMap = new HashMap<>();
                // key: skuRuleId, value: List<AreaListDto>
                Map<Long, List<AreaListDto>> areaListMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(areaLists)) {
                    Set<Long> divisionIds = areaLists.stream().map(AreaListDto::getDivisionId).collect(Collectors.toSet());
                    List<Address> addressList = addressApi.getDivisionInfoListByDivisionIds(StringUtils.join(divisionIds, ","));
                    Map<Long, Address> addressMap = addressList.stream().collect(Collectors.toMap(Address::getDivisionId, address -> address));
                    for (AreaListDto areaList : areaLists) {
                        areaMap.put(areaList.getDivisionId(), addressMap.get(areaList.getDivisionId()));
                    }
                    areaListMap = areaLists.stream().collect(Collectors.groupingBy(AreaListDto::getRelationId));
                }
                // 若地区中包含中国，则这条规则就是全国(divisionId=1)，需要将其全部删除
                if (MapUtils.isNotEmpty(areaListMap)) {
                    Set<Map.Entry<Long, List<AreaListDto>>> entrySet = areaListMap.entrySet();
                    Iterator<Map.Entry<Long, List<AreaListDto>>> iterator = entrySet.iterator();
                    while (iterator.hasNext()) {
                        Map.Entry<Long, List<AreaListDto>> entry = iterator.next();
                        List<AreaListDto> areaListDtoList = entry.getValue();
                        if (CollectionUtils.isNotEmpty(areaListDtoList)) {
                            boolean anyMatch = areaListDtoList.stream().anyMatch(e -> e.getDivisionId().equals(1L));
                            if (anyMatch) {
                                iterator.remove();
                            }
                        }
                    }
                }

                List<ServiceSkuRuleUserRelationDto> skuRuleUserRelationDtoList = serviceSkuApi.getServiceSkuRuleUserRelation(skuRuleIdList);
                Map<Long, List<ServiceSkuRuleUserRelationDto>> accountMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(skuRuleUserRelationDtoList)) {
                    accountMap = skuRuleUserRelationDtoList.stream().collect(Collectors.groupingBy(ServiceSkuRuleUserRelationDto::getSkuRuleId));
                }

                // 根据 skuRuleId 查找 地区、用户信息
                for (ServiceSkuRuleBaseResp resp : ruleBaseRespList) {

                    // TODO 一条规则对应多个地区
                    /*
                     * TODO
                     *  ①当同一个skuRuleId中的地区有中国（即divisionId=1）时，则转换成全国，关联的省市区全部忽略。
                     *  ②当为全国时，需要查找用户信息，如果有用户信息，则分别生成 全国+用户 规则，否则不生成规则。
                     *  ③当不为全国时，则根据地区 与 用户（若有）做笛卡尔积，生成规则。
                     */

                    Long skuRuleId = resp.getSkuRuleId();
                    // 暂时不支持条件表达式导入
                    String ruleEntity = resp.getRuleEntity();
                    String price = getPrice(ruleEntity);
                    if (StringUtils.isBlank(price)) {
                        log.error("价格为空, serviceId:{}, skuNo:{}, price:{}", serviceId, skuNo, price);
                        continue;
                    }
                    try {
                        BigDecimal bigDecimal = new BigDecimal(price);
                        if (BigDecimal.ZERO.compareTo(bigDecimal) >= 0) {
                            log.error("价格<=0, serviceId:{}, skuNo:{}, price:{}", serviceId, skuNo, price);
                            continue;
                        }
                    } catch (Exception e) {
                        log.error("价格转换异常, serviceId:{}, skuNo:{}, price:{}", serviceId, skuNo, price);
                        continue;
                    }

//                    Address address = areaMap.get(skuRuleId);
                    // skuRule关联用户
                    List<ServiceSkuRuleUserRelationDto> userDtoList = accountMap.get(skuRuleId);
                    List<AreaListDto> areaListDtoList = areaListMap.get(skuRuleId);

                    /*
                    三种情况：
                    1、有地区、有用户
                    2、无地区，有用户
                    3、有地区，无用户
                     */
                    if (CollectionUtils.isNotEmpty(areaListDtoList) && CollectionUtils.isNotEmpty(userDtoList)) {
                        for (AreaListDto areaListDto : areaListDtoList) {
                            Address address = areaMap.get(areaListDto.getDivisionId());
                            for (ServiceSkuRuleUserRelationDto userDto : userDtoList) {
                                ServiceSkuRuleResp skuRuleResp = getServiceSkuRuleResp(address, userDto, ruleResp);
                                if (skuRuleResp != null) {
                                    skuRuleResp.setTemplateId(templateId == null ? "" : templateId.toString());
                                    skuRuleResp.setFeeName(attributeDisplayName);
                                    skuRuleResp.setPrice(price);
                                    platform_fixed_price_user_region_resultList.add(skuRuleResp);
                                }
                            }
                        }
                    } else if (CollectionUtils.isEmpty(areaListDtoList) && CollectionUtils.isNotEmpty(userDtoList)) {
                        for (ServiceSkuRuleUserRelationDto userDto : userDtoList) {
                            ServiceSkuRuleResp skuRuleResp = getServiceSkuRuleResp(null, userDto, ruleResp);
                            if (skuRuleResp != null) {
                                skuRuleResp.setTemplateId(templateId == null ? "" : templateId.toString());
                                skuRuleResp.setFeeName(attributeDisplayName);
                                skuRuleResp.setPrice(price);
                                platform_fixed_price_user_nationwide_resultList.add(skuRuleResp);
                            }
                        }
                    } else if (CollectionUtils.isNotEmpty(areaListDtoList) && CollectionUtils.isEmpty(userDtoList)) {
                        for (AreaListDto areaListDto : areaListDtoList) {
                            Address address = areaMap.get(areaListDto.getDivisionId());
                            ServiceSkuRuleResp skuRuleResp = getServiceSkuRuleResp(address, null, ruleResp);
                            if (skuRuleResp != null) {
                                skuRuleResp.setTemplateId(templateId == null ? "" : templateId.toString());
                                skuRuleResp.setFeeName(attributeDisplayName);
                                skuRuleResp.setPrice(price);
                                platform_fixed_price_region_resultList.add(skuRuleResp);
                            }
                        }
                    }
                }
            }
        }

        // 创建字节输出流，用于存放Excel的字节数据
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try {
            if (SceneCodeEnum.ENTERPRISE_FIXED_PRICE.getCode().equals(sceneCode)) {
                // 使用EasyExcel的write方法，将数据写入到输出流中
                if (CollectionUtils.isNotEmpty(platform_fixed_price_nationwide_resultList)) {
                    EasyExcel.write(outputStream, ServiceSkuRuleResp.class)
                            .sheet(sceneName) // 设置工作表名称
                            .doWrite(platform_fixed_price_nationwide_resultList); // 写入数据列表

                    // 将输出流转换为字节数组
                    FileUploadResp fileUploadResp = FileUploadUtils.upload(outputStream.toByteArray(), "xlsx");
                    outputStream.reset();
                    resultList.add(fileUploadResp.getData().getFileUrl());
                }

                if (CollectionUtils.isNotEmpty(platform_fixed_price_region_resultList)) {
                    EasyExcel.write(outputStream, ServiceSkuRuleResp.class)
                            .sheet(sceneName) // 设置工作表名称
                            .doWrite(platform_fixed_price_region_resultList); // 写入数据列表

                    // 将输出流转换为字节数组
                    FileUploadResp fileUploadResp = FileUploadUtils.upload(outputStream.toByteArray(), "xlsx");
                    outputStream.reset();
                    resultList.add(fileUploadResp.getData().getFileUrl());
                }
            } else if (SceneCodeEnum.ENTERPRISE_FIXED_PRICE_SPECIFIED.getCode().equals(sceneCode)) {
                if (CollectionUtils.isNotEmpty(platform_fixed_price_user_nationwide_resultList)) {
                    EasyExcel.write(outputStream, ServiceSkuRuleResp.class)
                            .sheet(sceneName) // 设置工作表名称
                            .doWrite(platform_fixed_price_user_nationwide_resultList); // 写入数据列表

                    // 将输出流转换为字节数组
                    FileUploadResp fileUploadResp = FileUploadUtils.upload(outputStream.toByteArray(), "xlsx");
                    outputStream.reset();
                    resultList.add(fileUploadResp.getData().getFileUrl());
                }

                if (CollectionUtils.isNotEmpty(platform_fixed_price_user_region_resultList)) {
                    EasyExcel.write(outputStream, ServiceSkuRuleResp.class)
                            .sheet(sceneName) // 设置工作表名称
                            .doWrite(platform_fixed_price_user_region_resultList); // 写入数据列表

                    // 将输出流转换为字节数组
                    FileUploadResp fileUploadResp = FileUploadUtils.upload(outputStream.toByteArray(), "xlsx");
                    outputStream.reset();
                    resultList.add(fileUploadResp.getData().getFileUrl());
                }
            } else {
                throw new BusException("不支持的场景,sceneCode:" + sceneCode);
            }


        } finally {
            // 关闭输出流
            try {
                outputStream.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        // 获取Excel文件名称
//        String fileName = "output.xlsx"; // 默认文件名，可以根据需要修改
//        if (!fileUploadResp.isSuccess()) {
//            log.error("文件上传失败:"+ fileUploadResp.getMsg(),fileUploadResp);
//            throw new BusException("文件上传失败"+fileUploadResp.getMsg());
//        }

//        String path2 = String.format("C:%sUsers%s26069%sDownloads%s计价规则导入%s导出的计价规则(地区&用户-%s)", s, s, s, s, s, sceneName);
//        String pathName2 = path2 + DateUtils.getDateTime().replace(" ", "&").replace(":", "_") + ".xlsx";
//        EasyExcel.write(pathName2, ServiceSkuRuleResp.class).sheet(sceneName).doWrite(resultList2);
        return resultList;
    }

    @ApiOperation("根据一级商品类目Id刷计价规则数据(分页)")
    @PostMapping("flushFeeRuleByLv1GoodsCategoryId2")
    public List<String> flushFeeRuleByLv1GoodsCategoryId2(@RequestParam("lv1GoodsCategoryId") Long lv1GoodsCategoryId,
                                                          @RequestParam(value = "serviceTypeId", required = false) Long serviceTypeId,
                                                          @RequestParam("sceneCode") String sceneCode,
                                                          @RequestParam("pageSize") Integer pageSize,
                                                          @RequestParam("pageNum") Integer pageNum) {
        /**
         * 思路：
         * 1、根据一级商品类目Id查找服务SKU数据
         * 2、根据服务SKU数据查找计价模板数据
         * 3、如果SKU是简单类型（即pricingMode=1），则直接将计价模板Id更新到sku rule中，构建成一条feeRule。
         * 4、如果SKU是复杂类型（即pricingMode=2），则需要根据skuId查找sku rule，再根据skuRuleId查找地区、用户信息
         * 5、根据skuRuleId、地区、用户信息构建成一条feeRule
         * （每一条sku rule 最多对应一条地区、用户信息，所以skuRuleId+地区+用户可以生成唯一的一条feeRule）
         */
        final String platformFixedPriceUser = "商家定制一口价";
        final String platformFixedPrice = "平台一口价";
        SceneInfo sceneInfo = sceneInfoService.query(sceneCode);
        if (sceneInfo == null) {
            throw new BusException("场景不存在或已删除");
        }
        String sceneName = sceneInfo.getSceneName();
        // 平台一口价，全国
        List<ServiceSkuRuleResp> platform_fixed_price_nationwide_resultList = new ArrayList<>();
        // 平台一口价，地区
        List<ServiceSkuRuleResp> platform_fixed_price_region_resultList = new ArrayList<>();
        // 商家定制一口价，全国
        List<ServiceSkuRuleResp> platform_fixed_price_user_nationwide_resultList = new ArrayList<>();
        // 商家定制一口价，地区
        List<ServiceSkuRuleResp> platform_fixed_price_user_region_resultList = new ArrayList<>();
        // 其实返回的是ServiceSku + 费用名称
        List<ServiceSkuRuleResp> serviceSkuRuleList = serviceSkuApi.getServiceSkuRule(lv1GoodsCategoryId, serviceTypeId, "user", 4L);
        if (com.wanshifu.framework.utils.CollectionUtils.isEmpty(serviceSkuRuleList)) {
            log.error("未找到服务SKU规则数据");
            return Collections.emptyList();
        }

        int size = serviceSkuRuleList.size();
        log.error("serviceSkuRuleList数据条数:{}", size);

        List<String> resultList = new ArrayList<>();

        for (int i = (pageNum - 1) * pageSize; (i < pageNum * pageSize && i < size); i++) {
            ServiceSkuRuleResp ruleResp = serviceSkuRuleList.get(i);
//        }
//
//        for (ServiceSkuRuleResp ruleResp : serviceSkuRuleList) {
            Long skuId = ruleResp.getSkuId();
            Long serviceId = ruleResp.getServiceId();
            String skuNo = ruleResp.getSkuNo();
            Byte pricingMode = ruleResp.getPricingMode();
            List<FeeTemplate> feeTemplateList = feeTemplateService.selectByServiceIdAndSkuNo(sceneCode, serviceId.toString(), skuNo);
            if (com.wanshifu.framework.utils.CollectionUtils.isEmpty(feeTemplateList)) {
                log.error("sceneCode:{}, serviceId:{}, skuNo:{} 未找到计价模板", sceneCode, serviceId, skuNo);
                continue;
            }
            FeeTemplate feeTemplate = feeTemplateList.get(0);
            Long templateId = feeTemplate.getTemplateId();
            String attributeDisplayName = feeTemplate.getBizRule().get(CommonBizRule.Fields.attributeDisplayName);
            // 2：复杂模式-通过sku规则计价
//            if (Byte.valueOf("1").equals(pricingMode)) {
            ruleResp.setFeeName(attributeDisplayName);
            ruleResp.setTemplateId(templateId == null ? "" : templateId.toString());
            platform_fixed_price_nationwide_resultList.add(ruleResp);
//            } else

            if (Byte.valueOf("2").equals(pricingMode)) {
                List<ServiceSkuRuleBaseResp> ruleBaseRespList = serviceSkuApi.getServiceSkuRuleBySkuId(skuId);
                if (com.wanshifu.framework.utils.CollectionUtils.isEmpty(ruleBaseRespList)) {
                    log.error("未找到服务SKU规则数据, serviceId:{}, skuNo:{}", serviceId, skuNo);
                    continue;
                }
                // 根据skuRuleId 查找 地区、用户信息
                Set<Long> skuRuleIdList = ruleBaseRespList.stream().map(ServiceSkuRuleBaseResp::getSkuRuleId).collect(Collectors.toSet());
                AreaListReq areaListReq = new AreaListReq();
                areaListReq.setListType(AreaListTypeEnum.white.name());
                areaListReq.setScene(AreaListSceneEnum.sku_rule.name());
                areaListReq.setSceneRelationIds(skuRuleIdList);
                List<AreaListDto> areaLists = serviceSkuApi.getAreaLists(areaListReq);
                // key：divisionId，value：Address
                Map<Long, Address> areaMap = new HashMap<>();
                // key: skuRuleId, value: List<AreaListDto>
                Map<Long, List<AreaListDto>> areaListMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(areaLists)) {
                    Set<Long> divisionIds = areaLists.stream().map(AreaListDto::getDivisionId).collect(Collectors.toSet());
                    List<Address> addressList = addressApi.getDivisionInfoListByDivisionIds(StringUtils.join(divisionIds, ","));
                    Map<Long, Address> addressMap = addressList.stream().collect(Collectors.toMap(Address::getDivisionId, address -> address));
                    for (AreaListDto areaList : areaLists) {
                        areaMap.put(areaList.getDivisionId(), addressMap.get(areaList.getDivisionId()));
                    }
                    areaListMap = areaLists.stream().collect(Collectors.groupingBy(AreaListDto::getRelationId));
                }
                // 若地区中包含中国，则这条规则就是全国(divisionId=1)，需要将其全部删除
                if (MapUtils.isNotEmpty(areaListMap)) {
                    Set<Map.Entry<Long, List<AreaListDto>>> entrySet = areaListMap.entrySet();
                    Iterator<Map.Entry<Long, List<AreaListDto>>> iterator = entrySet.iterator();
                    while (iterator.hasNext()) {
                        Map.Entry<Long, List<AreaListDto>> entry = iterator.next();
                        List<AreaListDto> areaListDtoList = entry.getValue();
                        if (CollectionUtils.isNotEmpty(areaListDtoList)) {
                            boolean anyMatch = areaListDtoList.stream().anyMatch(e -> e.getDivisionId().equals(1L));
                            if (anyMatch) {
                                iterator.remove();
                            }
                        }
                    }
                }

                List<ServiceSkuRuleUserRelationDto> skuRuleUserRelationDtoList = serviceSkuApi.getServiceSkuRuleUserRelation(skuRuleIdList);
                Map<Long, List<ServiceSkuRuleUserRelationDto>> accountMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(skuRuleUserRelationDtoList)) {
//                    for (ServiceSkuRuleUserRelationDto dto : skuRuleUserRelationDtoList) {
//                        accountMap.put(dto.getSkuRuleId(), dto);
//                    }
                    accountMap = skuRuleUserRelationDtoList.stream().collect(Collectors.groupingBy(ServiceSkuRuleUserRelationDto::getSkuRuleId));
                }

                // 根据 skuRuleId 查找 地区、用户信息
                for (ServiceSkuRuleBaseResp resp : ruleBaseRespList) {

                    // TODO 一条规则对应多个地区
                    /*
                     * TODO
                     *  ①当同一个skuRuleId中的地区有中国（即divisionId=1）时，则转换成全国，关联的省市区全部忽略。
                     *  ②当为全国时，需要查找用户信息，如果有用户信息，则分别生成 全国+用户 规则，否则不生成规则。
                     *  ③当不为全国时，则根据地区 与 用户（若有）做笛卡尔积，生成规则。
                     */

                    Long skuRuleId = resp.getSkuRuleId();
                    // 暂时不支持条件表达式导入
                    String ruleEntity = resp.getRuleEntity();
                    String price = getPrice(ruleEntity);
//                    Address address = areaMap.get(skuRuleId);
                    // skuRule关联用户
                    List<ServiceSkuRuleUserRelationDto> userDtoList = accountMap.get(skuRuleId);
                    List<AreaListDto> areaListDtoList = areaListMap.get(skuRuleId);

                    /*
                    三种情况：
                    1、有地区、有用户
                    2、无地区，有用户
                    3、有地区，无用户
                     */
                    if (CollectionUtils.isNotEmpty(areaListDtoList) && CollectionUtils.isNotEmpty(userDtoList)) {
                        for (AreaListDto areaListDto : areaListDtoList) {
                            Address address = areaMap.get(areaListDto.getDivisionId());
                            for (ServiceSkuRuleUserRelationDto userDto : userDtoList) {
                                ServiceSkuRuleResp skuRuleResp = getServiceSkuRuleResp(address, userDto, ruleResp);
                                if (skuRuleResp != null) {
                                    skuRuleResp.setTemplateId(templateId == null ? "" : templateId.toString());
                                    skuRuleResp.setFeeName(attributeDisplayName);
                                    skuRuleResp.setPrice(price);
                                    platform_fixed_price_user_region_resultList.add(skuRuleResp);
                                }
                            }
                        }
                    } else if (CollectionUtils.isEmpty(areaListDtoList) && CollectionUtils.isNotEmpty(userDtoList)) {
                        for (ServiceSkuRuleUserRelationDto userDto : userDtoList) {
                            ServiceSkuRuleResp skuRuleResp = getServiceSkuRuleResp(null, userDto, ruleResp);
                            if (skuRuleResp != null) {
                                skuRuleResp.setTemplateId(templateId == null ? "" : templateId.toString());
                                skuRuleResp.setFeeName(attributeDisplayName);
                                skuRuleResp.setPrice(price);
                                platform_fixed_price_user_nationwide_resultList.add(skuRuleResp);
                            }
                        }
                    } else if (CollectionUtils.isNotEmpty(areaListDtoList) && CollectionUtils.isEmpty(userDtoList)) {
                        for (AreaListDto areaListDto : areaListDtoList) {
                            Address address = areaMap.get(areaListDto.getDivisionId());
                            ServiceSkuRuleResp skuRuleResp = getServiceSkuRuleResp(address, null, ruleResp);
                            if (skuRuleResp != null) {
                                skuRuleResp.setTemplateId(templateId == null ? "" : templateId.toString());
                                skuRuleResp.setFeeName(attributeDisplayName);
                                skuRuleResp.setPrice(price);
                                platform_fixed_price_region_resultList.add(skuRuleResp);
                            }
                        }
                    }
                }
            }
        }
//        String s = File.separator;
//
//        String path = String.format("C:%sUsers%s26069%sDownloads%s计价规则导入%s导出的计价规则(全国-%s)", s, s, s, s, s, sceneName);
//        String pathName = path + DateUtils.getDateTime().replace(" ", "&").replace(":", "_") + ".xlsx";
//        EasyExcel.write(pathName, ServiceSkuRuleResp.class).sheet(sceneName).doWrite(resultList1);

        // 创建字节输出流，用于存放Excel的字节数据
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try {
            if (SceneCodeEnum.PLATFORM_FIXED_PRICE.getCode().equals(sceneCode)) {
                // 使用EasyExcel的write方法，将数据写入到输出流中
                if (CollectionUtils.isNotEmpty(platform_fixed_price_nationwide_resultList)) {
                    EasyExcel.write(outputStream, ServiceSkuRuleResp.class)
                            .sheet(platformFixedPrice) // 设置工作表名称
                            .doWrite(platform_fixed_price_nationwide_resultList); // 写入数据列表

                    // 将输出流转换为字节数组
                    FileUploadResp fileUploadResp = FileUploadUtils.upload(outputStream.toByteArray(), "xlsx");
                    outputStream.reset();
                    resultList.add(fileUploadResp.getData().getFileUrl());
                }

                if (CollectionUtils.isNotEmpty(platform_fixed_price_region_resultList)) {
                    EasyExcel.write(outputStream, ServiceSkuRuleResp.class)
                            .sheet(platformFixedPrice) // 设置工作表名称
                            .doWrite(platform_fixed_price_region_resultList); // 写入数据列表

                    // 将输出流转换为字节数组
                    FileUploadResp fileUploadResp = FileUploadUtils.upload(outputStream.toByteArray(), "xlsx");
                    outputStream.reset();
                    resultList.add(fileUploadResp.getData().getFileUrl());
                }
            } else if (SceneCodeEnum.PLATFORM_FIXED_PRICE_USER.getCode().equals(sceneCode)) {
                if (CollectionUtils.isNotEmpty(platform_fixed_price_user_nationwide_resultList)) {
                    EasyExcel.write(outputStream, ServiceSkuRuleResp.class)
                            .sheet(platformFixedPriceUser) // 设置工作表名称
                            .doWrite(platform_fixed_price_user_nationwide_resultList); // 写入数据列表

                    // 将输出流转换为字节数组
                    FileUploadResp fileUploadResp = FileUploadUtils.upload(outputStream.toByteArray(), "xlsx");
                    outputStream.reset();
                    resultList.add(fileUploadResp.getData().getFileUrl());
                }

                if (CollectionUtils.isNotEmpty(platform_fixed_price_user_region_resultList)) {
                    EasyExcel.write(outputStream, ServiceSkuRuleResp.class)
                            .sheet(platformFixedPriceUser) // 设置工作表名称
                            .doWrite(platform_fixed_price_user_region_resultList); // 写入数据列表

                    // 将输出流转换为字节数组
                    FileUploadResp fileUploadResp = FileUploadUtils.upload(outputStream.toByteArray(), "xlsx");
                    outputStream.reset();
                    resultList.add(fileUploadResp.getData().getFileUrl());
                }
            } else {
                throw new BusException("不支持的场景,sceneCode:" + sceneCode);
            }


        } finally {
            // 关闭输出流
            try {
                outputStream.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        // 获取Excel文件名称
//        String fileName = "output.xlsx"; // 默认文件名，可以根据需要修改
//        if (!fileUploadResp.isSuccess()) {
//            log.error("文件上传失败:"+ fileUploadResp.getMsg(),fileUploadResp);
//            throw new BusException("文件上传失败"+fileUploadResp.getMsg());
//        }

//        String path2 = String.format("C:%sUsers%s26069%sDownloads%s计价规则导入%s导出的计价规则(地区&用户-%s)", s, s, s, s, s, sceneName);
//        String pathName2 = path2 + DateUtils.getDateTime().replace(" ", "&").replace(":", "_") + ".xlsx";
//        EasyExcel.write(pathName2, ServiceSkuRuleResp.class).sheet(sceneName).doWrite(resultList2);
        return resultList;
    }


    @ApiOperation("根据一级商品类目Id刷计价规则数据")
    @PostMapping("flushFeeRuleByLv1GoodsCategoryId")
    public List<String> flushFeeRuleByLv1GoodsCategoryId(@RequestParam("lv1GoodsCategoryId") Long lv1GoodsCategoryId,
                                                         @RequestParam(value = "serviceTypeId", required = false) Long serviceTypeId,
                                                         @RequestParam("sceneCode") String sceneCode) {
        /**
         * 思路：
         * 1、根据一级商品类目Id查找服务SKU数据
         * 2、根据服务SKU数据查找计价模板数据
         * 3、如果SKU是简单类型（即pricingMode=1），则直接将计价模板Id更新到sku rule中，构建成一条feeRule。
         * 4、如果SKU是复杂类型（即pricingMode=2），则需要根据skuId查找sku rule，再根据skuRuleId查找地区、用户信息
         * 5、根据skuRuleId、地区、用户信息构建成一条feeRule
         * （每一条sku rule 最多对应一条地区、用户信息，所以skuRuleId+地区+用户可以生成唯一的一条feeRule）
         */
        final String platformFixedPriceUser = "商家定制一口价";
        final String platformFixedPrice = "平台一口价";
        SceneInfo sceneInfo = sceneInfoService.query(sceneCode);
        if (sceneInfo == null) {
            throw new BusException("场景不存在或已删除");
        }
        String sceneName = sceneInfo.getSceneName();
        // 平台一口价，全国
        List<ServiceSkuRuleResp> platform_fixed_price_nationwide_resultList = new ArrayList<>();
        // 平台一口价，地区
        List<ServiceSkuRuleResp> platform_fixed_price_region_resultList = new ArrayList<>();
        // 商家定制一口价，全国
        List<ServiceSkuRuleResp> platform_fixed_price_user_nationwide_resultList = new ArrayList<>();
        // 商家定制一口价，地区
        List<ServiceSkuRuleResp> platform_fixed_price_user_region_resultList = new ArrayList<>();
        // 其实返回的是ServiceSku + 费用名称
        List<ServiceSkuRuleResp> serviceSkuRuleList = serviceSkuApi.getServiceSkuRule(lv1GoodsCategoryId, serviceTypeId, "user", 4L);
        if (com.wanshifu.framework.utils.CollectionUtils.isEmpty(serviceSkuRuleList)) {
            log.error("未找到服务SKU规则数据");
            return Collections.emptyList();
        }

        log.error("serviceSkuRuleList数据条数:{}", serviceSkuRuleList.size());

        List<String> resultList = new ArrayList<>();

        for (ServiceSkuRuleResp ruleResp : serviceSkuRuleList) {
            Long skuId = ruleResp.getSkuId();
            Long serviceId = ruleResp.getServiceId();
            String skuNo = ruleResp.getSkuNo();
            Byte pricingMode = ruleResp.getPricingMode();
            List<FeeTemplate> feeTemplateList = feeTemplateService.selectByServiceIdAndSkuNo(sceneCode, serviceId.toString(), skuNo);
            if (com.wanshifu.framework.utils.CollectionUtils.isEmpty(feeTemplateList)) {
                log.error("sceneCode:{}, serviceId:{}, skuNo:{} 未找到计价模板", sceneCode, serviceId, skuNo);
                continue;
            }
            FeeTemplate feeTemplate = feeTemplateList.get(0);
            Long templateId = feeTemplate.getTemplateId();
            String attributeDisplayName = feeTemplate.getBizRule().get(CommonBizRule.Fields.attributeDisplayName);
            // 2：复杂模式-通过sku规则计价
//            if (Byte.valueOf("1").equals(pricingMode)) {
            ruleResp.setFeeName(attributeDisplayName);
            ruleResp.setTemplateId(templateId == null ? "" : templateId.toString());
            platform_fixed_price_nationwide_resultList.add(ruleResp);
//            } else

            if (Byte.valueOf("2").equals(pricingMode)) {
                List<ServiceSkuRuleBaseResp> ruleBaseRespList = serviceSkuApi.getServiceSkuRuleBySkuId(skuId);
                if (com.wanshifu.framework.utils.CollectionUtils.isEmpty(ruleBaseRespList)) {
                    log.error("未找到服务SKU规则数据, serviceId:{}, skuNo:{}", serviceId, skuNo);
                    continue;
                }
                // 根据skuRuleId 查找 地区、用户信息
                Set<Long> skuRuleIdList = ruleBaseRespList.stream().map(ServiceSkuRuleBaseResp::getSkuRuleId).collect(Collectors.toSet());
                AreaListReq areaListReq = new AreaListReq();
                areaListReq.setListType(AreaListTypeEnum.white.name());
                areaListReq.setScene(AreaListSceneEnum.sku_rule.name());
                areaListReq.setSceneRelationIds(skuRuleIdList);
                List<AreaListDto> areaLists = serviceSkuApi.getAreaLists(areaListReq);
                // key：divisionId，value：Address
                Map<Long, Address> areaMap = new HashMap<>();
                // key: skuRuleId, value: List<AreaListDto>
                Map<Long, List<AreaListDto>> areaListMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(areaLists)) {
                    Set<Long> divisionIds = areaLists.stream().map(AreaListDto::getDivisionId).collect(Collectors.toSet());
                    List<Address> addressList = addressApi.getDivisionInfoListByDivisionIds(StringUtils.join(divisionIds, ","));
                    Map<Long, Address> addressMap = addressList.stream().collect(Collectors.toMap(Address::getDivisionId, address -> address));
                    for (AreaListDto areaList : areaLists) {
                        areaMap.put(areaList.getDivisionId(), addressMap.get(areaList.getDivisionId()));
                    }
                    areaListMap = areaLists.stream().collect(Collectors.groupingBy(AreaListDto::getRelationId));
                }
                // 若地区中包含中国，则这条规则就是全国(divisionId=1)，需要将其全部删除
                if (MapUtils.isNotEmpty(areaListMap)) {
                    Set<Map.Entry<Long, List<AreaListDto>>> entrySet = areaListMap.entrySet();
                    Iterator<Map.Entry<Long, List<AreaListDto>>> iterator = entrySet.iterator();
                    while (iterator.hasNext()) {
                        Map.Entry<Long, List<AreaListDto>> entry = iterator.next();
                        List<AreaListDto> areaListDtoList = entry.getValue();
                        if (CollectionUtils.isNotEmpty(areaListDtoList)) {
                            boolean anyMatch = areaListDtoList.stream().anyMatch(e -> e.getDivisionId().equals(1L));
                            if (anyMatch) {
                                iterator.remove();
                            }
                        }
                    }
                }

                List<ServiceSkuRuleUserRelationDto> skuRuleUserRelationDtoList = serviceSkuApi.getServiceSkuRuleUserRelation(skuRuleIdList);
                Map<Long, List<ServiceSkuRuleUserRelationDto>> accountMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(skuRuleUserRelationDtoList)) {
//                    for (ServiceSkuRuleUserRelationDto dto : skuRuleUserRelationDtoList) {
//                        accountMap.put(dto.getSkuRuleId(), dto);
//                    }
                    accountMap = skuRuleUserRelationDtoList.stream().collect(Collectors.groupingBy(ServiceSkuRuleUserRelationDto::getSkuRuleId));
                }

                // 根据 skuRuleId 查找 地区、用户信息
                for (ServiceSkuRuleBaseResp resp : ruleBaseRespList) {

                    // TODO 一条规则对应多个地区
                    /*
                     * TODO
                     *  ①当同一个skuRuleId中的地区有中国（即divisionId=1）时，则转换成全国，关联的省市区全部忽略。
                     *  ②当为全国时，需要查找用户信息，如果有用户信息，则分别生成 全国+用户 规则，否则不生成规则。
                     *  ③当不为全国时，则根据地区 与 用户（若有）做笛卡尔积，生成规则。
                     */

                    Long skuRuleId = resp.getSkuRuleId();
                    // 暂时不支持条件表达式导入
                    String ruleEntity = resp.getRuleEntity();
                    String price = getPrice(ruleEntity);
//                    Address address = areaMap.get(skuRuleId);
                    // skuRule关联用户
                    List<ServiceSkuRuleUserRelationDto> userDtoList = accountMap.get(skuRuleId);
                    List<AreaListDto> areaListDtoList = areaListMap.get(skuRuleId);

                    /*
                    三种情况：
                    1、有地区、有用户
                    2、无地区，有用户
                    3、有地区，无用户
                     */
                    if (CollectionUtils.isNotEmpty(areaListDtoList) && CollectionUtils.isNotEmpty(userDtoList)) {
                        for (AreaListDto areaListDto : areaListDtoList) {
                            Address address = areaMap.get(areaListDto.getDivisionId());
                            for (ServiceSkuRuleUserRelationDto userDto : userDtoList) {
                                ServiceSkuRuleResp skuRuleResp = getServiceSkuRuleResp(address, userDto, ruleResp);
                                if (skuRuleResp != null) {
                                    skuRuleResp.setTemplateId(templateId == null ? "" : templateId.toString());
                                    skuRuleResp.setFeeName(attributeDisplayName);
                                    skuRuleResp.setPrice(price);
                                    platform_fixed_price_user_region_resultList.add(skuRuleResp);
                                }
                            }
                        }
                    } else if (CollectionUtils.isEmpty(areaListDtoList) && CollectionUtils.isNotEmpty(userDtoList)) {
                        for (ServiceSkuRuleUserRelationDto userDto : userDtoList) {
                            ServiceSkuRuleResp skuRuleResp = getServiceSkuRuleResp(null, userDto, ruleResp);
                            if (skuRuleResp != null) {
                                skuRuleResp.setTemplateId(templateId == null ? "" : templateId.toString());
                                skuRuleResp.setFeeName(attributeDisplayName);
                                skuRuleResp.setPrice(price);
                                platform_fixed_price_user_nationwide_resultList.add(skuRuleResp);
                            }
                        }
                    } else if (CollectionUtils.isNotEmpty(areaListDtoList) && CollectionUtils.isEmpty(userDtoList)) {
                        for (AreaListDto areaListDto : areaListDtoList) {
                            Address address = areaMap.get(areaListDto.getDivisionId());
                            ServiceSkuRuleResp skuRuleResp = getServiceSkuRuleResp(address, null, ruleResp);
                            if (skuRuleResp != null) {
                                skuRuleResp.setTemplateId(templateId == null ? "" : templateId.toString());
                                skuRuleResp.setFeeName(attributeDisplayName);
                                skuRuleResp.setPrice(price);
                                platform_fixed_price_region_resultList.add(skuRuleResp);
                            }
                        }
                    }
                }
            }
        }
//        String s = File.separator;
//
//        String path = String.format("C:%sUsers%s26069%sDownloads%s计价规则导入%s导出的计价规则(全国-%s)", s, s, s, s, s, sceneName);
//        String pathName = path + DateUtils.getDateTime().replace(" ", "&").replace(":", "_") + ".xlsx";
//        EasyExcel.write(pathName, ServiceSkuRuleResp.class).sheet(sceneName).doWrite(resultList1);

        // 创建字节输出流，用于存放Excel的字节数据
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try {
            if (SceneCodeEnum.PLATFORM_FIXED_PRICE.getCode().equals(sceneCode)) {
                // 使用EasyExcel的write方法，将数据写入到输出流中
                if (CollectionUtils.isNotEmpty(platform_fixed_price_nationwide_resultList)) {
                    EasyExcel.write(outputStream, ServiceSkuRuleResp.class)
                            .sheet(platformFixedPrice) // 设置工作表名称
                            .doWrite(platform_fixed_price_nationwide_resultList); // 写入数据列表

                    // 将输出流转换为字节数组
                    FileUploadResp fileUploadResp = FileUploadUtils.upload(outputStream.toByteArray(), "xlsx");
                    outputStream.reset();
                    resultList.add(fileUploadResp.getData().getFileUrl());
                }

                if (CollectionUtils.isNotEmpty(platform_fixed_price_region_resultList)) {
                    EasyExcel.write(outputStream, ServiceSkuRuleResp.class)
                            .sheet(platformFixedPrice) // 设置工作表名称
                            .doWrite(platform_fixed_price_region_resultList); // 写入数据列表

                    // 将输出流转换为字节数组
                    FileUploadResp fileUploadResp = FileUploadUtils.upload(outputStream.toByteArray(), "xlsx");
                    outputStream.reset();
                    resultList.add(fileUploadResp.getData().getFileUrl());
                }
            } else if (SceneCodeEnum.PLATFORM_FIXED_PRICE_USER.getCode().equals(sceneCode)) {
                if (CollectionUtils.isNotEmpty(platform_fixed_price_user_nationwide_resultList)) {
                    EasyExcel.write(outputStream, ServiceSkuRuleResp.class)
                            .sheet(platformFixedPriceUser) // 设置工作表名称
                            .doWrite(platform_fixed_price_user_nationwide_resultList); // 写入数据列表

                    // 将输出流转换为字节数组
                    FileUploadResp fileUploadResp = FileUploadUtils.upload(outputStream.toByteArray(), "xlsx");
                    outputStream.reset();
                    resultList.add(fileUploadResp.getData().getFileUrl());
                }

                if (CollectionUtils.isNotEmpty(platform_fixed_price_user_region_resultList)) {
                    EasyExcel.write(outputStream, ServiceSkuRuleResp.class)
                            .sheet(platformFixedPriceUser) // 设置工作表名称
                            .doWrite(platform_fixed_price_user_region_resultList); // 写入数据列表

                    // 将输出流转换为字节数组
                    FileUploadResp fileUploadResp = FileUploadUtils.upload(outputStream.toByteArray(), "xlsx");
                    outputStream.reset();
                    resultList.add(fileUploadResp.getData().getFileUrl());
                }
            } else {
                throw new BusException("不支持的场景,sceneCode:" + sceneCode);
            }


        } finally {
            // 关闭输出流
            try {
                outputStream.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return resultList;
    }


    @ApiOperation("根据一级商品类目Id刷计价模板数据(总包一口价)")
    @PostMapping("flushTemplateByLv1GoodsCategoryId4Enterprise")
    public List<Long> flushTemplateByLv1GoodsCategoryId4Enterprise(@RequestParam("lv1GoodsCategoryId") Long lv1GoodsCategoryId,
                                                                   @RequestParam(value = "serviceTypeId", required = false) Long serviceTypeId,
                                                                   @RequestParam("sceneCode") String sceneCode,
                                                                   @RequestParam("isOverwrite") boolean isOverwrite,
                                                                   @RequestParam("secret") String secret) {
        if (StringUtils.isBlank(secret)) {
            throw new BusException("secret不能为空");
        }
        if (!"arq415adf456ar414246@#123#wer&tt@".equals(secret)) {
            throw new BusException("秘钥错误");
        }
        if (!(sceneCode.equals(SceneCodeEnum.ENTERPRISE_FIXED_PRICE.getCode()) || sceneCode.equals(SceneCodeEnum.ENTERPRISE_FIXED_PRICE_SPECIFIED.getCode()))) {
            throw new BusException("不允许刷【总包给师傅一口价、总包指定商家给师傅一口价】以外的场景");
        }
        SceneInfo sceneInfo = sceneInfoService.query(sceneCode);
        if (sceneInfo == null) {
            throw new BusException("场景不存在或已删除");
        }
        // 1、根据场景编码 + 服务Id + feeTypeTag 不能为空 查找出所有的计价模板
        Set<Long> serviceIds = serviceApi.getServiceIdByLv1GoodsCategoryIdAndModel(lv1GoodsCategoryId, serviceTypeId, 2L);
//        Set<Long> serviceIds = new HashSet<>();
        if (com.wanshifu.framework.utils.CollectionUtils.isEmpty(serviceIds)) {
            return Collections.emptyList();
        }

        // 如果覆盖，则需要根据 场景编码+serviceId删除现有数据
        if (isOverwrite) {
            Criteria criteria = Criteria.where(FeeTemplate.Fields.sceneCode).is(sceneCode)
                    .and(FeeTemplate.Fields.bizRule + PunConstant.DOT +
                            CommonBizRule.Fields.serviceId)
                    .in(serviceIds.stream().map(String::valueOf).collect(Collectors.toList()));
            Query query = new Query(criteria);
            int n = mongoTemplate.remove(query, FeeTemplate.class).getN();
            System.out.println("删除了" + n + "条数据");
        }
        List<Long> resultList = new ArrayList<>();

        // 如果是 商家白名单的，则跳过
        Set<String> whiteSkuNoList = getWhiteSkuNoList();

        for (Long serviceId : serviceIds) {
            ServiceSkuReq req = new ServiceSkuReq();
            req.setServiceId(serviceId);
            req.setProductLine("enterprise");
//            req.setSkuRuleScene("direct_price");
            List<ServiceSkuResp> respList = serviceSkuApi.getServiceSkuResp(req);
            if (com.wanshifu.framework.utils.CollectionUtils.isEmpty(respList)) {
                continue;
            }

            FeeTemplateConfigureReq configureReq = new FeeTemplateConfigureReq();
            List<FeeTemplateConfigureReq.FeeTemplateConfigure> configureList = new ArrayList<>();
            configureReq.setConfigureList(configureList);
            for (ServiceSkuResp resp : respList) {

                BigDecimal price = resp.getPrice();
                if (price == null || price.compareTo(BigDecimal.ZERO) <= 0) {
                    log.warn("价格为空或小于等于0，skuNo:{}, serviceId:{}, price:{}", resp.getSkuNo(), resp.getServiceId(), price);
                    continue;
                }

                String skuNo = resp.getSkuNo();

                if (whiteSkuNoList.contains(skuNo)) {
                    log.info("skuNo:{} 是商家白名单的，跳过", skuNo);
                    continue;
                }

                String skuNumberPathNo = resp.getSkuNumberPathNo();
                // 如果没有计价数量路径，则默认为1
                if (StringUtils.isBlank(skuNumberPathNo)) {
                    skuNumberPathNo = "1";
                }
                String expression = "masterInputPrice * " + skuNumberPathNo;
                FeeTemplateConfigureReq.FeeTemplateConfigure configure = new FeeTemplateConfigureReq.FeeTemplateConfigure();
                configure.setSceneCode(sceneInfo.getSceneCode());
                configure.setSceneName(sceneInfo.getSceneName());
                configure.setGroup(serviceId.toString());
                Map<String, String> bizRule = new HashMap<>();
                configure.setBizRule(bizRule);
//                bizRule.put(PlatformFixedPriceBizRule.Fields.feeName, resp.getFeeName());
                // 取属性值，用>> 分隔，取最后一截，适用 【窗帘】【健身器材】
                String skuAttributePathName = resp.getSkuAttributePathName();
                if (Constant.BASE_SERVICE_FEE_SKU_NO.equals(skuNo)) {
                    bizRule.put(PlatformFixedPriceBizRule.Fields.feeName, resp.getFeeName());
                    bizRule.put(PlatformFixedPriceBizRule.Fields.attributeDisplayName, resp.getFeeName());
                } else {
                    StringBuilder result = new StringBuilder();
                    String[] parts = skuAttributePathName.split("\\|");

                    for (String part : parts) {
                        String[] subParts = part.trim().split("\\s*>>\\s*");
                        if (subParts.length == 2) {
                            result.append(subParts[1].trim()).append(" | ");
                        } else {
                            // 有的的分割是()，
                            String[] subParts2 = part.trim().split("\\(");
                            if (subParts2.length == 2) {
                                result.append(subParts2[1].replace(")", "").trim()).append(" | ");
                            } else {
                                // 有的的分割是（），
                                String[] subParts3 = part.trim().split("（");
                                if (subParts3.length == 2) {
                                    result.append(subParts3[1].replace("）", "").trim()).append(" | ");
                                } else {
                                    log.error("属性路径分割异常，skuAttributePathName:{}", skuAttributePathName);
                                }
                            }
                        }
                    }
                    // 删除最后一个多余的" | "，包括空格是3个字符
                    if (result.length() > 0) {
                        result.delete(result.length() - 3, result.length());
                    }
                    bizRule.put(CommonBizRule.Fields.feeName, result.toString());
                    bizRule.put(CommonBizRule.Fields.attributeDisplayName, result.toString());
                }
                bizRule.put(CommonBizRule.Fields.feeUnit, resp.getFeeUnit());
                bizRule.put(CommonBizRule.Fields.goodsCategoryName, resp.getGoodsCategoryName());
                bizRule.put(CommonBizRule.Fields.serviceTypeName, resp.getServiceTypeName());
                bizRule.put(CommonBizRule.Fields.serviceId, serviceId.toString());
                bizRule.put(CommonBizRule.Fields.group, serviceId.toString());
                bizRule.put(CommonBizRule.Fields.serviceName, resp.getServiceName());
                bizRule.put(CommonBizRule.Fields.skuAttributePathName, skuAttributePathName);
                bizRule.put(CommonBizRule.Fields.skuNo, skuNo);
                bizRule.put(CommonBizRule.Fields.skuType, FeeSkuTypeEnum.STANDARD_SKU.code);
                bizRule.put(CommonBizRule.Fields.serviceCategoryId, resp.getServiceCategoryId().toString());
                bizRule.put(CommonBizRule.Fields.serviceModelId, resp.getServiceModelId().toString());
                bizRule.put(CommonBizRule.Fields.skuNumberName, resp.getSkuNumberName());
                bizRule.put(CommonBizRule.Fields.skuNumberPathNo, skuNumberPathNo);

                bizRule.put(CommonBizRule.Fields.sceneCode, sceneCode);
                bizRule.put(CommonBizRule.Fields.sceneName, sceneInfo.getSceneName());
                bizRule.put(CommonBizRule.Fields.applyFlag, ApplyFlagEnum.NO_LIMIT.code);
//                bizRule.put(PlatformFixedPriceBizRule.Fields.expression, expression);
                if (price.compareTo(BigDecimal.ZERO) > 0) {
                    if (Constant.BASE_SERVICE_FEE_SKU_NO.equals(skuNo) || checkSkuNameWithKeyWord(skuAttributePathName)) {
                        bizRule.put(CommonBizRule.Fields.feeTypeTag, FeeTypeTagEnum.SERVICE_FEE.code);
                        bizRule.put(CommonBizRule.Fields.feeTypeTagName, FeeTypeTagEnum.SERVICE_FEE.name);
                    } else if (skuAttributePathName.contains("拆旧要求")) { // 仅适用【灯具】
                        bizRule.put(CommonBizRule.Fields.feeTypeTag, FeeTypeTagEnum.STANDARD_SURCHARGE.code);
                        bizRule.put(CommonBizRule.Fields.feeTypeTagName, FeeTypeTagEnum.STANDARD_SURCHARGE.name);
                    }
//                    else if ("AP45516339".equals(skuNo)) { // FIXME 仅适用 【窗帘】【健身器材】
//                        bizRule.put(CommonBizRule.Fields.feeTypeTag, FeeTypeTagEnum.GOOD_SURCHARGE.code);
//                        bizRule.put(CommonBizRule.Fields.feeTypeTagName, FeeTypeTagEnum.GOOD_SURCHARGE.name);
//                    } else {
//                        bizRule.put(CommonBizRule.Fields.feeTypeTag, FeeTypeTagEnum.STANDARD_SURCHARGE.code);
//                        bizRule.put(CommonBizRule.Fields.feeTypeTagName, FeeTypeTagEnum.STANDARD_SURCHARGE.name);
//                    }
                    else {
                        bizRule.put(CommonBizRule.Fields.feeTypeTag, FeeTypeTagEnum.GOOD_SURCHARGE.code);
                        bizRule.put(CommonBizRule.Fields.feeTypeTagName, FeeTypeTagEnum.GOOD_SURCHARGE.name);
                    }
                } else {
                    log.warn("价格为空，skuNo:{}, serviceId:{}", skuNo, resp.getServiceId());
                    continue;
                }
                // 识别刷的数据
                bizRule.put("refreshTag", "20250521_by_cy");
                CalculateRuleData data = new CalculateRuleData();
                data.setExpress(expression);
                List<String> paramList = new ArrayList<>();
                paramList.add("masterInputPrice");
                paramList.add(skuNumberPathNo);
                data.setExpressionParamList(paramList);
                configure.setCalculateRuleData(data);
                configureList.add(configure);
            }
            if (CollectionUtils.isNotEmpty(configureList)) {
                feeTemplateController.create(configureReq);
            } else {
                resultList.add(serviceId);
            }
        }
        return resultList;
    }


    @ApiOperation("根据一级商品类目Id刷计价模板数据")
    @PostMapping("flushTemplateByLv1GoodsCategoryId")
    public List<Long> flushTemplateByLv1GoodsCategoryId(@RequestParam("lv1GoodsCategoryId") Long lv1GoodsCategoryId,
                                                        @RequestParam(value = "serviceTypeId", required = false) Long serviceTypeId,
                                                        @RequestParam("sceneCode") String sceneCode,
                                                        @RequestParam("isOverwrite") boolean isOverwrite,
                                                        @RequestParam("secret") String secret) {
        if (StringUtils.isBlank(secret)) {
            throw new BusException("secret不能为空");
        }
        if (!"arq415adf456ar414246@#123#wer&tt@".equals(secret)) {
            throw new BusException("秘钥错误");
        }
        if (!(sceneCode.equals("platform_fixed_price_user") || sceneCode.equals("platform_fixed_price"))) {
            throw new BusException("不允许刷【一口价】以外的场景");
        }
        SceneInfo sceneInfo = sceneInfoService.query(sceneCode);
        if (sceneInfo == null) {
            throw new BusException("场景不存在或已删除");
        }
        // 1、根据场景编码 + 服务Id + feeTypeTag 不能为空 查找出所有的计价模板
        Set<Long> serviceIds = serviceApi.getServiceIdByLv1GoodsCategoryId(lv1GoodsCategoryId, serviceTypeId);
        if (com.wanshifu.framework.utils.CollectionUtils.isEmpty(serviceIds)) {
            return Collections.emptyList();
        }

        // 如果覆盖，则需要根据 场景编码+serviceId删除现有数据
        if (isOverwrite) {
            Criteria criteria = Criteria.where(FeeTemplate.Fields.sceneCode).is(sceneCode)
                    .and(FeeTemplate.Fields.bizRule + PunConstant.DOT +
                            CommonBizRule.Fields.serviceId)
                    .in(serviceIds.stream().map(String::valueOf).collect(Collectors.toList()));
            Query query = new Query(criteria);
            int n = mongoTemplate.remove(query, FeeTemplate.class).getN();
            System.out.println("删除了" + n + "条数据");
        }
        List<Long> resultList = new ArrayList<>();

        // 如果是 商家白名单的，则跳过
        Set<String> whiteSkuNoList = getWhiteSkuNoList();

        for (Long serviceId : serviceIds) {
            ServiceSkuReq req = new ServiceSkuReq();
            req.setServiceId(serviceId);
            List<ServiceSkuResp> respList = serviceSkuApi.getServiceSkuResp(req);
            if (com.wanshifu.framework.utils.CollectionUtils.isEmpty(respList)) {
                continue;
            }

            FeeTemplateConfigureReq configureReq = new FeeTemplateConfigureReq();
            List<FeeTemplateConfigureReq.FeeTemplateConfigure> configureList = new ArrayList<>();
            configureReq.setConfigureList(configureList);
            for (ServiceSkuResp resp : respList) {

                BigDecimal price = resp.getPrice();
                if (price == null || price.compareTo(BigDecimal.ZERO) <= 0) {
                    log.warn("价格为空或小于等于0，skuNo:{}, serviceId:{}, price:{}", resp.getSkuNo(), resp.getServiceId(), price);
                    continue;
                }

                String skuNo = resp.getSkuNo();

                if (whiteSkuNoList.contains(skuNo)) {
                    log.info("skuNo:{} 是商家白名单的，跳过", skuNo);
                    continue;
                }

                String skuNumberPathNo = resp.getSkuNumberPathNo();
                // 如果没有计价数量路径，则默认为1
                if (StringUtils.isBlank(skuNumberPathNo)) {
                    skuNumberPathNo = "1";
                }
                String expression = "masterInputPrice * " + skuNumberPathNo;
                FeeTemplateConfigureReq.FeeTemplateConfigure configure = new FeeTemplateConfigureReq.FeeTemplateConfigure();
                configure.setSceneCode(sceneInfo.getSceneCode());
                configure.setSceneName(sceneInfo.getSceneName());
                configure.setGroup(serviceId.toString());
                Map<String, String> bizRule = new HashMap<>();
                configure.setBizRule(bizRule);
//                bizRule.put(PlatformFixedPriceBizRule.Fields.feeName, resp.getFeeName());
                // 取属性值，用>> 分隔，取最后一截，适用 【窗帘】【健身器材】
                String skuAttributePathName = resp.getSkuAttributePathName();
                if (Constant.BASE_SERVICE_FEE_SKU_NO.equals(skuNo)) {
                    bizRule.put(PlatformFixedPriceBizRule.Fields.feeName, resp.getFeeName());
                    bizRule.put(PlatformFixedPriceBizRule.Fields.attributeDisplayName, resp.getFeeName());
                } else {
                    StringBuilder result = new StringBuilder();
                    String[] parts = skuAttributePathName.split("\\|");

                    for (String part : parts) {
                        String[] subParts = part.trim().split("\\s*>>\\s*");
                        if (subParts.length == 2) {
                            result.append(subParts[1].trim()).append(" | ");
                        } else {
                            // 有的的分割是()，
                            String[] subParts2 = part.trim().split("\\(");
                            if (subParts2.length == 2) {
                                result.append(subParts2[1].replace(")", "").trim()).append(" | ");
                            } else {
                                // 有的的分割是（），
                                String[] subParts3 = part.trim().split("（");
                                if (subParts3.length == 2) {
                                    result.append(subParts3[1].replace("）", "").trim()).append(" | ");
                                } else {
                                    log.error("属性路径分割异常，skuAttributePathName:{}", skuAttributePathName);
                                }
                            }
                        }
                    }
                    // 删除最后一个多余的" | "，包括空格是3个字符
                    if (result.length() > 0) {
                        result.delete(result.length() - 3, result.length());
                    }
                    bizRule.put(PlatformFixedPriceBizRule.Fields.feeName, result.toString());
                    bizRule.put(PlatformFixedPriceBizRule.Fields.attributeDisplayName, result.toString());
                }
                bizRule.put(PlatformFixedPriceBizRule.Fields.feeUnit, resp.getFeeUnit());
                bizRule.put(PlatformFixedPriceBizRule.Fields.goodsCategoryName, resp.getGoodsCategoryName());
                bizRule.put(PlatformFixedPriceBizRule.Fields.serviceTypeName, resp.getServiceTypeName());
                bizRule.put(PlatformFixedPriceBizRule.Fields.serviceId, serviceId.toString());
                bizRule.put(PlatformFixedPriceBizRule.Fields.group, serviceId.toString());
                bizRule.put(PlatformFixedPriceBizRule.Fields.serviceName, resp.getServiceName());
                bizRule.put(PlatformFixedPriceBizRule.Fields.skuAttributePathName, skuAttributePathName);
                bizRule.put(PlatformFixedPriceBizRule.Fields.skuNo, skuNo);
                bizRule.put(PlatformFixedPriceBizRule.Fields.skuType, FeeSkuTypeEnum.STANDARD_SKU.code);
                bizRule.put(PlatformFixedPriceBizRule.Fields.serviceCategoryId, resp.getServiceCategoryId().toString());
                bizRule.put(PlatformFixedPriceBizRule.Fields.serviceModelId, resp.getServiceModelId().toString());
                bizRule.put(PlatformFixedPriceBizRule.Fields.skuNumberName, resp.getSkuNumberName());
                bizRule.put(PlatformFixedPriceBizRule.Fields.skuNumberPathNo, skuNumberPathNo);

                bizRule.put(PlatformFixedPriceBizRule.Fields.sceneCode, sceneCode);
                bizRule.put(PlatformFixedPriceBizRule.Fields.sceneName, sceneInfo.getSceneName());
                bizRule.put(PlatformFixedPriceBizRule.Fields.applyFlag, ApplyFlagEnum.NO_LIMIT.code);
                bizRule.put(PlatformFixedPriceBizRule.Fields.expression, expression);
                if (price.compareTo(BigDecimal.ZERO) > 0) {
                    if (Constant.BASE_SERVICE_FEE_SKU_NO.equals(skuNo) || checkSkuNameWithKeyWord(skuAttributePathName)) {
                        bizRule.put(PlatformFixedPriceBizRule.Fields.feeTypeTag, FeeTypeTagEnum.SERVICE_FEE.code);
                        bizRule.put(PlatformFixedPriceBizRule.Fields.feeTypeTagName, FeeTypeTagEnum.SERVICE_FEE.name);
                    } else if (skuAttributePathName.contains("拆旧要求")) { // 仅适用【灯具】
                        bizRule.put(PlatformFixedPriceBizRule.Fields.feeTypeTag, FeeTypeTagEnum.STANDARD_SURCHARGE.code);
                        bizRule.put(PlatformFixedPriceBizRule.Fields.feeTypeTagName, FeeTypeTagEnum.STANDARD_SURCHARGE.name);
                    }
//                    else if ("AP45516339".equals(skuNo)) { // FIXME 仅适用 【窗帘】【健身器材】
//                        bizRule.put(PlatformFixedPriceBizRule.Fields.feeTypeTag, FeeTypeTagEnum.GOOD_SURCHARGE.code);
//                        bizRule.put(PlatformFixedPriceBizRule.Fields.feeTypeTagName, FeeTypeTagEnum.GOOD_SURCHARGE.name);
//                    } else {
//                        bizRule.put(PlatformFixedPriceBizRule.Fields.feeTypeTag, FeeTypeTagEnum.STANDARD_SURCHARGE.code);
//                        bizRule.put(PlatformFixedPriceBizRule.Fields.feeTypeTagName, FeeTypeTagEnum.STANDARD_SURCHARGE.name);
//                    }
                    else {
                        bizRule.put(PlatformFixedPriceBizRule.Fields.feeTypeTag, FeeTypeTagEnum.GOOD_SURCHARGE.code);
                        bizRule.put(PlatformFixedPriceBizRule.Fields.feeTypeTagName, FeeTypeTagEnum.GOOD_SURCHARGE.name);
                    }
                } else {
                    log.warn("价格为空，skuNo:{}, serviceId:{}", skuNo, resp.getServiceId());
                    continue;
                }
                // 识别刷的数据
                bizRule.put("refreshTag", "2000");
                CalculateRuleData data = new CalculateRuleData();
                data.setExpress(expression);
                List<String> paramList = new ArrayList<>();
                paramList.add("masterInputPrice");
                paramList.add(skuNumberPathNo);
                data.setExpressionParamList(paramList);
                configure.setCalculateRuleData(data);
                configureList.add(configure);
            }
            if (CollectionUtils.isNotEmpty(configureList)) {
                feeTemplateController.create(configureReq);
            } else {
                resultList.add(serviceId);
            }
        }
        return resultList;
    }

    @ApiOperation("将没有区县的用街道顶替")
    @PostMapping("flushStreetToDistrict")
    public void flushStreetToDistrict() {
        CompletableFuture.runAsync(this::doFlushStreetToDistrict, executorService)
                .thenRun(() -> log.info("flushStreetToDistrict success"))
                .exceptionally(e -> {
                    log.error("flushStreetToDistrict fail");
                    return null;
                });
    }


    @ApiOperation("初始化自定义SKU的value值类型为“范围值”")
    @GetMapping("initCustomSkuValueToRange")
    public void initCustomSkuValueToRange() {
        feeTemplateService.initCustomSkuValueToRange();
    }


    @ApiOperation("存在自定义sku的，则其父sku（serviceId+skuNo与自定义的相同，且为标准sku），则应当将其状态改为LOCK")
    @GetMapping("correctCustomSkuFatherStatusToLock")
    public void correctCustomSkuFatherStatusToLock() {
        feeTemplateService.correctCustomSkuFatherStatusToLock();
    }


    private void doFlushStreetToDistrict() {
        final String sceneCode = "master_auto_offer_price";
        // 分别为 东莞、中山、嘉峪关
        List<String> level2DivisionIds = Arrays.asList("441900", "442000", "620200");
        Criteria criteria = Criteria.where(FeeRule.Fields.sceneCode).is(sceneCode)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId).in(level2DivisionIds)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code);
        final int size = 500;
        int num = 1;
        while (true) {
            Query query = new Query(criteria);
            query.skip((num - 1) * size).limit(size);
            List<FeeRule> feeRuleList = mongoTemplate.find(query, FeeRule.class);
            if (CollectionUtils.isEmpty(feeRuleList)) {
                break;
            }
            BulkOperations bulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, FeeRule.class);
            for (FeeRule feeRule : feeRuleList) {
                Map<String, String> bizRule = feeRule.getBizRule();
                String level4DivisionId = bizRule.get(CommonBizRule.Fields.level4DivisionId);
                String street = bizRule.get(CommonBizRule.Fields.street);
                // 根据id更新
                Query updateQuery = new Query(where(BaseDocument.Fields.id).is(feeRule.getId()));
                Update update = new Update();
                update.set(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level3DivisionId, level4DivisionId);
                update.set(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.district, street);
                bulkOps.updateOne(updateQuery, update);
            }
            bulkOps.execute();
            num++;
        }
    }


    private ServiceSkuRuleResp getServiceSkuRuleResp(Address address, ServiceSkuRuleUserRelationDto dto, ServiceSkuRuleResp resp) {
        ServiceSkuRuleResp skuRuleResp = null;
        if (address != null || dto != null) {
            skuRuleResp = new ServiceSkuRuleResp();
            BeanUtils.copyProperties(resp, skuRuleResp);
        }
        if (address != null) {
            // 地区
            skuRuleResp.setProvince(address.getLv2DivisionName());
            skuRuleResp.setCity(address.getLv3DivisionName());
            skuRuleResp.setDistrict(address.getLv4DivisionName());
            skuRuleResp.setStreet(address.getLv5DivisionName());
        }
        if (dto != null) {
            // 用户
            skuRuleResp.setBizId(dto.getUserId().toString());
        }
        return skuRuleResp;
    }


    private String getPrice(String ruleEntity) {
        // 解析JSON字符串
        JSONObject jsonObject = JSONObject.parseObject(ruleEntity);
        // 获取conditionEntities数组
        JSONArray conditionEntities = jsonObject.getJSONArray("conditionEntities");
        // 如果数组不为空且长度大于0，则获取第一个元素的priceExpress值
        if (CollectionUtils.isNotEmpty(conditionEntities)) {
            JSONObject firstEntity = conditionEntities.getJSONObject(0);
            return firstEntity.getString("priceExpress");
        } else {
            log.error("没有找到priceExpress值");
            return "ruleEntity中没有找到priceExpress的值";
        }
    }

    public Set<String> getWhiteSkuNoList() {
        return new HashSet<>(Arrays.asList(
                "AP48015197",
                "AP48015198",
                "AP48015199",
                "AP48015200",
                "AP48015201",
                "AP48015202",
                "AP48015203",
                "AP48015204",
                "AP48015205",
                "AP48015206",
                "AP48015207",
                "AP48015208",
                "AP48015209",
                "AP48015210",
                "AP48015211",
                "AP48015212",
                "AP48015213",
                "AP48015214",
                "AP48015215",
                "AP48015216",
                "AP48015217",
                "AP48015218",
                "AP48015219",
                "AP48015220",
                "AP48015221",
                "AP48015222",
                "AP48015223",
                "AP48015224",
                "AP48015225",
                "AP48015226",
                "AP48015227",
                "AP48015228",
                "AP48015229",
                "AP48015230",
                "AP48015231",
                "AP48015232",
                "AP48015233",
                "AP48015234",
                "AP48015235",
                "AP48015236",
                "AP48015237",
                "AP48015238",
                "AP48015239",
                "AP48015240",
                "AP48015241",
                "AP48015242",
                "AP48015243",
                "AP48015244",
                "AP48015245",
                "AP48015246",
                "AP48015247",
                "AP48015248",
                "AP48015249",
                "AP48015250",
                "AP48015251",
                "AP48015252",
                "AP48015253",
                "AP48015254",
                "AP48015255",
                "AP48015256",
                "AP48015257",
                "AP48015258",
                "AP48015259",
                "AP48015260",
                "AP48015261",
                "AP48015262",
                "AP48015263",
                "AP48015264",
                "AP48015265",
                "AP48015266",
                "AP48015267",
                "AP48015268",
                "AP48015269",
                "AP48015270",
                "AP48015271",
                "AP48015272",
                "AP48015273",
                "AP48015274",
                "AP48015275",
                "AP48015276"
        ));
    }

    private boolean checkSkuNameWithKeyWord(String skuName) {
        List<String> keyWords = Arrays.asList("商品属性", "商品尺寸", "商品规格", "包装方式", "维修类型", "窗户数量", "开门方式", "轨道类型", "是否异形", "全屋柜包含");
        return keyWords.stream().anyMatch(skuName::contains);
    }


    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static String convertJsonStructure(String inputJson) {
        try {
            JsonNode inputNode = objectMapper.readTree(inputJson);
            ObjectNode resultNode = objectMapper.createObjectNode();

            // 创建expressInfo对象
            ObjectNode expressInfo = objectMapper.createObjectNode();
            ArrayNode expressInfoUnits = objectMapper.createArrayNode();

            // 处理conditionEntities
            JsonNode conditionEntities = inputNode.get("conditionEntities");
            Set<String> allParams = new HashSet<>();
            StringBuilder expressBuilder = new StringBuilder();
            expressBuilder.append("import java.math.BigDecimal; import com.wanshifu.fee.center.domain.dto.ExpressResultInfo; ");

            if (conditionEntities != null && conditionEntities.isArray()) {
                for (JsonNode entity : conditionEntities) {
                    ObjectNode unit = objectMapper.createObjectNode();
                    ObjectNode condition = objectMapper.createObjectNode();

                    // 构建条件
                    JsonNode conditionParams = entity.get("skuRuleConditionParams");
                    if (conditionParams != null && conditionParams.isArray()) {
                        String conditionStr = buildCondition(conditionParams, allParams);

                        // 这里简化处理，取第一个参数作为主要参数
                        JsonNode firstParam = conditionParams.get(0);
                        if (firstParam != null) {
                            String paramNo = firstParam.get("paramNo").asText();
                            String term = firstParam.get("term").asText();
                            String threshold = firstParam.get("conditionThresholding").asText();

                            condition.put("param", convertParamName(paramNo));
                            condition.put("operator", convertOperator(term));
                            condition.put("value", threshold);
                            allParams.add(convertParamName(paramNo));
                        }
                    }

                    // 设置价格表达式
                    String priceExpress = entity.get("priceExpress").asText();
                    unit.set("condition", condition);
                    unit.put("priceExpress", "masterInputPrice"); // 根据业务逻辑可能需要调整

                    expressInfoUnits.add(unit);

                    // 构建执行表达式
                    expressBuilder.append(buildExecuteExpression(conditionParams, priceExpress));
                }
            }

            // 添加默认条件
            ObjectNode defaultUnit = objectMapper.createObjectNode();
            ObjectNode defaultCondition = objectMapper.createObjectNode();
            defaultCondition.put("param", "1");
            defaultCondition.put("operator", "==");
            defaultCondition.put("value", "1");
            defaultUnit.set("condition", defaultCondition);
            defaultUnit.put("priceExpress", "masterInputPrice");
            expressInfoUnits.add(defaultUnit);

            // 添加默认执行逻辑
            expressBuilder.append("if (1 == 1) { ")
                    .append("BigDecimal price = (masterInputPrice); ")
                    .append("BigDecimal num = (null); ")
                    .append("BigDecimal cost = price * num; ")
                    .append("ExpressResultInfo returnResult = new ExpressResultInfo(); ")
                    .append("returnResult.setNumber(num); ")
                    .append("returnResult.setPrice(price); ")
                    .append("returnResult.setCost(cost); ")
                    .append("return returnResult; } ");

            expressInfo.set("expressInfoUnits", expressInfoUnits);
            resultNode.set("expressInfo", expressInfo);

            // 设置表达式
            resultNode.put("express", expressBuilder.toString());

            // 设置参数列表
            allParams.add("masterInputPrice");
            ArrayNode paramList = objectMapper.createArrayNode();
            for (String param : allParams) {
                paramList.add(param);
            }
            resultNode.set("expressionParamList", paramList);

            // 设置参数映射（空对象）
            ObjectNode paramMap = objectMapper.createObjectNode();
            resultNode.set("expressionParamMap", paramMap);

            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(resultNode);

        } catch (Exception e) {
            throw new RuntimeException("JSON转换失败", e);
        }
    }

    private static String buildCondition(JsonNode conditionParams, Set<String> allParams) {
        StringBuilder conditionBuilder = new StringBuilder();

        for (int i = 0; i < conditionParams.size(); i++) {
            JsonNode param = conditionParams.get(i);
            String paramNo = param.get("paramNo").asText();
            String term = param.get("term").asText();
            String threshold = param.get("conditionThresholding").asText();

            String convertedParam = convertParamName(paramNo);
            allParams.add(convertedParam);

            conditionBuilder.append(convertedParam)
                    .append(" ")
                    .append(term)
                    .append(" ")
                    .append(threshold);

            if (i < conditionParams.size() - 1) {
                String connector = param.get("nextConditionConnectorChar").asText();
                conditionBuilder.append(" ").append(connector).append(" ");
            }
        }

        return conditionBuilder.toString();
    }

    private static String buildExecuteExpression(JsonNode conditionParams, String priceExpress) {
        StringBuilder expressBuilder = new StringBuilder();

        // 构建if条件
        expressBuilder.append("if (");
        for (int i = 0; i < conditionParams.size(); i++) {
            JsonNode param = conditionParams.get(i);
            String paramNo = param.get("paramNo").asText();
            String term = param.get("term").asText();
            String threshold = param.get("conditionThresholding").asText();

            String convertedParam = convertParamName(paramNo);
            expressBuilder.append(convertedParam)
                    .append(" ")
                    .append(term)
                    .append(" ")
                    .append(threshold);

            if (i < conditionParams.size() - 1) {
                String connector = param.get("nextConditionConnectorChar").asText();
                expressBuilder.append(" ").append("&&".equals(connector) ? "&&" : "||").append(" ");
            }
        }
        expressBuilder.append(") { ");

        // 构建执行体
        expressBuilder.append("BigDecimal price = (")
                .append(priceExpress)
                .append("); ")
                .append("BigDecimal num = (null); ")
                .append("BigDecimal cost = price * num; ")
                .append("ExpressResultInfo returnResult = new ExpressResultInfo(); ")
                .append("returnResult.setNumber(num); ")
                .append("returnResult.setPrice(price); ")
                .append("returnResult.setCost(cost); ")
                .append("return returnResult; } ");

        return expressBuilder.toString();
    }

    private static String convertParamName(String paramNo) {
        // 根据业务逻辑转换参数名
        // 这里假设AP开头的参数转换为特定格式
        if (paramNo.startsWith("AP")) {
            return "AP_SKU_PRICE"; // 简化处理，实际可能需要更复杂的映射
        }
        return paramNo;
    }

    private static String convertOperator(String term) {
        switch (term) {
            case ">":
                return ">";
            case ">=":
                return ">=";
            case "<":
                return "<";
            case "<=":
                return "<=";
            case "==":
            case "=":
                return "==";
            case "!=":
                return "!=";
            default:
                return term;
        }
    }
}
