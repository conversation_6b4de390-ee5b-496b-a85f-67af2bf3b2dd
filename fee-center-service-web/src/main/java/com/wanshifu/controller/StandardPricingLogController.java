package com.wanshifu.controller;

import com.wanshifu.domain.request.pricinglog.GetStandardPricingLogListRequest;
import com.wanshifu.fee.center.domain.document.StandardPricingLog;
import com.wanshifu.service.StandardPricingLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "标准计价日志管理")
@RestController
@RequestMapping("standardPricingLog")
public class StandardPricingLogController {

    @Resource
    private StandardPricingLogService standardPricingLogService;

    @ApiOperation("查询标准计价流程及结果日志详情")
    @PostMapping("getStandardPricingLogList")
    public List<StandardPricingLog> getStandardPricingLogList(@Validated @RequestBody GetStandardPricingLogListRequest request) {
        return standardPricingLogService.getStandardPricingLogList(request);
    }
}
