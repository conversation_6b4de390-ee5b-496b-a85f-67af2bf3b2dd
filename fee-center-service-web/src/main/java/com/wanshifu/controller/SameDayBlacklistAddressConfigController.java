package com.wanshifu.controller;

import com.wanshifu.domain.request.samedayblacklistaddress.*;
import com.wanshifu.fee.center.domain.request.common.Create;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.service.SameDayBlacklistAddressConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "当日装地址黑名单CRUD")
@RestController
@RequestMapping("sameDayBlacklistAddressConfig")
public class SameDayBlacklistAddressConfigController {

    @Resource
    private SameDayBlacklistAddressConfigService configService;

    /**
     * 新增配置
     *
     * @param request 新增配置request body
     */
    @ApiOperation("增加配置")
    @PostMapping("add")
    public void add(@RequestBody @Validated(Create.class) AddRequest request) {
        configService.add(request);
    }

    /**
     * 修改配置
     *
     * @param request 修改配置request body
     */
    @ApiOperation("修改配置")
    @PostMapping("modify")
    public void modify(@RequestBody @Validated ModifyRequest request) {
        configService.modify(request);
    }


    @ApiOperation("查询配置分页列表")
    @GetMapping("pageList")
    public SimplePageInfo<PageListResponse> pageList(@RequestParam(value = "level1GoodsCategoryId", required = false) Long level1GoodsCategoryId,
                                                     @RequestParam(value = "districtId", required = false) String districtId,
                                                     @RequestParam(value = "keyword", required = false) String keyword,
                                                     @RequestParam("pageNum") int pageNum,
                                                     @RequestParam("pageSize") int pageSize) {
        return configService.pageList(level1GoodsCategoryId, districtId, keyword, pageNum, pageSize);
    }



    /**
     * 详情
     *
     * @param configId 配置ID
     * @return 详情
     */
    @ApiOperation("详情")
    @GetMapping("detail")
    public DetailResponse detail(@RequestParam("configId") String configId) {
        return configService.detail(configId);
    }


    /**
     * 删除配置
     *
     * @param request 配置ID
     */
    @ApiOperation("删除配置")
    @PostMapping("deleteByConfigId")
    public void deleteByConfigId(@RequestBody @Validated DeleteRequest request) {
        configService.deleteByConfigId(request);
    }

}
