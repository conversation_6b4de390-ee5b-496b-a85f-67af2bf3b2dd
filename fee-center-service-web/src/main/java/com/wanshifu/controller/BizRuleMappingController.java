package com.wanshifu.controller;


import com.wanshifu.api.utils.MapUtils;
import com.wanshifu.fee.center.api.BizRuleMappingApi;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.document.BizRuleMapping;
import com.wanshifu.fee.center.domain.document.FeeTemplate;
import com.wanshifu.fee.center.domain.enums.ApplyTypeEnum;
import com.wanshifu.fee.center.domain.request.*;
import com.wanshifu.fee.center.domain.request.mapping.BizRuleMappingBatchAddReq;
import com.wanshifu.fee.center.domain.request.mapping.CopyBizRuleMappingReq;
import com.wanshifu.fee.center.domain.request.mapping.QueryPageReq;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.service.BizRuleMappingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Api(value = "模板映射")
@RestController
@RequestMapping("bizRuleMapping")
@Deprecated
public class BizRuleMappingController implements BizRuleMappingApi {
    @Resource
    private BizRuleMappingService bizRuleMappingService;

    @Override
    @PostMapping("query")
    public SimplePageInfo<BizRuleMapping> query(@RequestBody @Validated BizRuleMappingQueryReq bizRuleMappingQueryReq) {
        Page<BizRuleMapping> skuMappings = bizRuleMappingService.queryByCondition(bizRuleMappingQueryReq);
        SimplePageInfo<BizRuleMapping> skuMappingSimplePageInfo = new SimplePageInfo<>();
        skuMappingSimplePageInfo.setPages(skuMappings.getTotalPages());
        skuMappingSimplePageInfo.setPageSize(skuMappings.getSize());
        skuMappingSimplePageInfo.setTotal(skuMappings.getTotalElements());
        skuMappingSimplePageInfo.setPageNum(skuMappings.getNumber());
        skuMappingSimplePageInfo.setList(skuMappings.getContent());
        return skuMappingSimplePageInfo;
    }


    @PostMapping("queryPage")
    public SimplePageInfo<BizRuleMapping> queryPage(@RequestBody @Validated QueryPageReq req) {
        Page<BizRuleMapping> skuMappings = bizRuleMappingService.queryPage(req);
        SimplePageInfo<BizRuleMapping> skuMappingSimplePageInfo = new SimplePageInfo<>();
        skuMappingSimplePageInfo.setPages(skuMappings.getTotalPages());
        skuMappingSimplePageInfo.setPageSize(skuMappings.getSize());
        skuMappingSimplePageInfo.setTotal(skuMappings.getTotalElements());
        skuMappingSimplePageInfo.setPageNum(skuMappings.getNumber());
        skuMappingSimplePageInfo.setList(skuMappings.getContent());
        return skuMappingSimplePageInfo;
    }

    @Override
    @PostMapping("batchQuery")
    public SimplePageInfo<BizRuleMapping> batchQuery(@RequestBody @Validated BizRuleMappingBatchQueryReq bizRuleMappingBatchQueryReq) {
        Page<BizRuleMapping> skuMappings = bizRuleMappingService.batchQueryByCondition(bizRuleMappingBatchQueryReq);
        SimplePageInfo<BizRuleMapping> skuMappingSimplePageInfo = new SimplePageInfo<>();
        skuMappingSimplePageInfo.setPages(skuMappings.getTotalPages());
        skuMappingSimplePageInfo.setPageSize(skuMappings.getSize());
        skuMappingSimplePageInfo.setTotal(skuMappings.getTotalElements());
        skuMappingSimplePageInfo.setPageNum(skuMappings.getNumber());
        skuMappingSimplePageInfo.setList(skuMappings.getContent());
        return skuMappingSimplePageInfo;
    }

    @Override
    @PostMapping("del")
    public BizRuleMapping del(@RequestBody @Validated BizRuleMappingDelReq bizRuleMappingDelReq) {
        return bizRuleMappingService.delete(bizRuleMappingDelReq.getBizRuleMappingId());
    }


    @Override
    @PostMapping("configure")
    public BizRuleMapping configure(@RequestBody @Validated BizRuleMappingConfigureReq bizRuleMappingConfigureReq) {
        BizRuleMapping bizRuleMapping = new BizRuleMapping();
        BeanUtils.copyProperties(bizRuleMappingConfigureReq, bizRuleMapping);
        BizRuleMapping result;
        Long bizRuleMappingId = bizRuleMappingConfigureReq.getBizRuleMappingId();

        Map<String, String> fromBizRule = bizRuleMapping.getFromBizRule();
        String fromServiceId = fromBizRule.get(CommonBizRule.Fields.serviceId);
        if (StringUtils.isBlank(fromServiceId)) {
            throw new BusException("from的serviceId不能为空");
        }

        if (Objects.isNull(bizRuleMappingId)) {
            fromBizRule.put(FeeTemplate.Fields.sceneCode, bizRuleMappingConfigureReq.getSceneCode());
            // 外层的sceneCode是 from的sceneCode
//            bizRuleMapping.setSceneCode(null);
            result = bizRuleMappingService.save(bizRuleMapping);
        } else {
            Map<String, String> toBizRule = bizRuleMapping.getToBizRule();
            if (MapUtils.isEmpty(toBizRule)) {
                throw new BusException("映射规则不能为空");
            }
            String toServiceId = toBizRule.get(CommonBizRule.Fields.serviceId);
            if (StringUtils.isBlank(toServiceId)) {
                throw new BusException("to的serviceId不能为空");
            }

            // 产品需求原文：首次添加,在列表每条【设置】的时候将默认应用状态设为"价格同步",并将del设为false,旧数据无需处理
            BizRuleMapping oldRuleMapping = bizRuleMappingService.queryByMappingId(bizRuleMappingId);
            if (oldRuleMapping == null) {
                throw new BusException("映射规则不存在");
            }
            Map<String, String> oldRuleMappingToBizRule = oldRuleMapping.getToBizRule();
            if (oldRuleMappingToBizRule == null) {
                bizRuleMapping.setDel(false);
                bizRuleMapping.setApplyTypes(Collections.singletonList(ApplyTypeEnum.PRICE_SYNC.getCode()));
            }

            result = bizRuleMappingService.update(bizRuleMapping);
        }
        return result;
    }


    @PostMapping("batchAdd")
    @ApiOperation("批量添加")
    public void batchAdd(@Validated @RequestBody List<BizRuleMappingBatchAddReq> reqList) {
        bizRuleMappingService.batchAdd(reqList);
    }


    @ApiOperation("设置应用类型")
    @PostMapping("setApplyTypes")
    public void setApplyTypes(@RequestBody @Validated BizRuleMappingApplyTypesReq req) {
        bizRuleMappingService.setApplyTypes(req);
    }


    @ApiOperation("复制映射")
    @PostMapping("copyBizRuleMapping")
    public void copyBizRuleMapping(@RequestBody @Validated CopyBizRuleMappingReq req) {
        bizRuleMappingService.copyBizRuleMapping(req);
    }

}
