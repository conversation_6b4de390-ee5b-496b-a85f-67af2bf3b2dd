package com.wanshifu.controller;

import com.alibaba.fastjson.JSON;
import com.ql.util.express.ExpressRunner;
import com.wanshifu.fee.center.api.FeeRuleApi;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.document.CalculateRuleData;
import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.document.SceneInfo;
import com.wanshifu.fee.center.domain.dto.CalculateServiceInfo;
import com.wanshifu.fee.center.domain.dto.FeeBizRuleDTO;
import com.wanshifu.fee.center.domain.enums.DivisionTypeEnum;
import com.wanshifu.fee.center.domain.enums.ExceptionEnum;
import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.request.*;
import com.wanshifu.fee.center.domain.request.feeRule.*;
import com.wanshifu.fee.center.domain.request.feeRule.master.*;
import com.wanshifu.fee.center.domain.response.*;
import com.wanshifu.fee.center.domain.response.feerule.BatchAddRulesResponse;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.MapUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.config.ApolloConfig;
import com.wanshifu.infrastructure.utils.CollectionInnerUtil;
import com.wanshifu.service.AsyncTasksInfoService;
import com.wanshifu.service.FeeRuleService;
import com.wanshifu.service.SceneInfoService;
import com.wanshifu.strategy.express.ExpressCompiler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;

@Slf4j
@Api(tags = "费用规则管理")
@RestController
@RequestMapping("feeRule")
public class FeeRuleController implements FeeRuleApi {

    @Resource
    private FeeRuleService feeRuleService;
    @Resource
    private ExpressCompiler expressCompilerComposite;
    @Resource
    private ExpressRunner expressRunner;
    @Resource
    private SceneInfoService sceneInfoService;
    @Resource
    private AsyncTasksInfoService asyncTasksInfoService;


    @Value("${fee.rule.getBargainPriceEverydayFeeRuleByServiceIdSwitch:true}")
    private Boolean getBargainPriceEverydayFeeRuleByServiceIdSwitch;

    @Value("${spring.profiles.active}")
    private String activeProfiles;
    @Value("${fee.rule.circuitBreakerSceneCodes:}")
    private String circuitBreakerSceneCodes;



    @Qualifier("autoAdjustThreadPool")
    @Autowired
    private ExecutorService executorService;

    @Override
    @PostMapping("query")
    public List<FeeRule> query(@RequestBody @Validated FeeRuleQueryReq feeRuleQueryReq) {
        return feeRuleService.queryByCondition(feeRuleQueryReq);
    }

    @Override
    @PostMapping("queryBaseInfoForMaster")
    public FeeRuleBaseInfoForMasterResp queryBaseInfoForMaster(@RequestBody @Validated FeeRuleBaseInfoForMasterReq feeRuleBaseInfoForMasterReq) {
        String divisionType = feeRuleBaseInfoForMasterReq.getDivisionType();
        // 如果是街道维度，则只取其中一个街道
        if (DivisionTypeEnum.STREET.code.equals(divisionType)) {
            Set<String> divisionIdList = feeRuleBaseInfoForMasterReq.getDivisionIdList();
            if (divisionIdList.size() > 1) {
                log.warn("街道id个数大于1。入参：{}", JSON.toJSONString(feeRuleBaseInfoForMasterReq));
            }
//            Set<String> divisionIdListNew = new HashSet<>();
//            divisionIdListNew.add(divisionIdList.stream().findFirst().orElseThrow(() -> new BusException("至少传一个街道id")));
//            feeRuleBaseInfoForMasterReq.setDivisionIdList(divisionIdListNew);
        }
        return feeRuleService.queryBaseInfoForMaster(feeRuleBaseInfoForMasterReq);
    }

    @Override
    @PostMapping("queryMaster")
    public List<FeeRule> queryMaster(@RequestBody @Validated FeeRuleQueryReq feeRuleQueryReq) {
        return feeRuleService.queryMasterByCondition(feeRuleQueryReq);
    }

    @Override
    @PostMapping("batchQuery")
    public List<FeeRule> batchQuery(@RequestBody @Validated FeeRuleBatchQueryReq feeRuleBatchQueryReq) {
        return feeRuleService.batchQueryByCondition(feeRuleBatchQueryReq);
    }

    @Override
    @PostMapping("configure")
    public FeeRule configure(@RequestBody @Validated FeeRuleConfigureReq feeRuleConfigureReq) {
        FeeRule feeRule = new FeeRule();
        BeanUtils.copyProperties(feeRuleConfigureReq, feeRule);
        FeeRule result;
        // 编译公式
        CalculateRuleData calculateRuleData = feeRule.getCalculateRuleData();
        if (Objects.nonNull(calculateRuleData) && Objects.nonNull(calculateRuleData.getExpressInfo())) {
            String compile = expressCompilerComposite.compile(calculateRuleData.getExpressInfo());
            try {
                String[] outVarNames = expressRunner.getOutVarNames(compile);
                calculateRuleData.setExpress(compile);
                if (Objects.nonNull(outVarNames) && outVarNames.length > 0) {
                    calculateRuleData.setExpressionParamList(Arrays.asList(outVarNames));
                }
            } catch (Exception e) {
                throw new BusException(e.getMessage(), e);
            }

        }
        if (Objects.isNull(feeRuleConfigureReq.getFeeRuleId())) {
            result = feeRuleService.save(feeRule);
        } else {
            result = feeRuleService.update(feeRule);
        }
        return result;
    }


    @ApiOperation("计价（老版，逐步弃用）")
    @Override
    @Deprecated
    @PostMapping("apply")
    public ApplyCalculateResp applyCalculate(@RequestBody @Validated ApplyCalculateReq applyCalculateReq) {
        return feeRuleService.calculate(applyCalculateReq);
    }

    @ApiOperation("订单计价")
    @Override
    @PostMapping("applyOrder")
    public ApplyOrderCalculateResp applyOrderCalculate(@RequestBody @Validated ApplyOrderCalculateReq applyOrderCalculateReq) {
        if ("test".equals(activeProfiles)) {
            log.info("applyOrderCalculateReqNew:{}", JSON.toJSONString(applyOrderCalculateReq));
        }
        circuitBreakerBySceneCode(applyOrderCalculateReq.getSceneCode());
        List<CalculateServiceInfo> serviceInfos = applyOrderCalculateReq.getServiceInfos();
        if (CollectionUtils.isEmpty(serviceInfos)) {
            throw new BusException("服务信息不能为空");
        }
        return feeRuleService.calculateOrder(applyOrderCalculateReq);
    }


    @ApiOperation("批量订单计价（仅业务id批量，订单上下文相同）")
    @Override
    @PostMapping("applyOrderBatch")
    public List<ApplyOrderCalculateBatchResp> applyOrderCalculateBatch(@RequestBody @Validated ApplyOrderCalculateBatchReq applyOrderCalculateBatchReq) {
        if ("test".equals(activeProfiles)) {
            log.info("applyOrderCalculateBatchReq:{}", JSON.toJSONString(applyOrderCalculateBatchReq));
        }
        circuitBreakerBySceneCode(applyOrderCalculateBatchReq.getApplyOrderCalculateReq().getSceneCode());
        return feeRuleService.calculateOrderBatch(applyOrderCalculateBatchReq);
    }



    @ApiOperation("调算费接口前的 探测接口（企业用户前端专用），用于探测是否可以算费")
    @Override
    @PostMapping("applyOrderDetector")
    public boolean applyOrderDetector(@RequestBody @Validated ApplyOrderCalculateReq applyOrderCalculateReq) {
        if ("test".equals(activeProfiles)) {
            log.info("applyOrderDetector:{}", JSON.toJSONString(applyOrderCalculateReq));
        }
        return feeRuleService.applyOrderDetector(applyOrderCalculateReq);
    }

    @Override
    @PostMapping("queryGuidePrice")
    public List<FeeRule> queryGuidePrice(@RequestBody @Validated FeeRuleGuidePriceReq feeRuleGuidePriceReq) {
        if (CollectionUtils.isEmpty(feeRuleGuidePriceReq.getServiceId()) && CollectionUtils.isEmpty(feeRuleGuidePriceReq.getServiceCategoryId())) {
            throw new BusException("serviceId 与 serviceCategoryId不能同时为空");
        }

        if (CollectionUtils.isNotEmpty(feeRuleGuidePriceReq.getServiceCategoryId()) && StringUtils.isBlank(feeRuleGuidePriceReq.getServiceModelId())) {
            throw new BusException("serviceCategoryId不为空的时候serviceModelId不能为空");
        }

        if (StringUtils.isNotBlank(feeRuleGuidePriceReq.getServiceModelId()) && !Arrays.asList("2", "4").contains(feeRuleGuidePriceReq.getServiceModelId())) {
            throw new BusException("ServiceModelId必须为2或者4");
        }

        if (StringUtils.isNotBlank(feeRuleGuidePriceReq.getDivisionType())) {
            if (CollectionUtils.isEmpty(feeRuleGuidePriceReq.getDivisionIds())) {
                throw new BusException("DivisionIds不能为空");
            }
            DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(feeRuleGuidePriceReq.getDivisionType());
            if (Objects.isNull(divisionTypeEnum)) {
                throw new BusException("DivisionType不合法");
            }
        }

        return feeRuleService.queryGuidePrice(feeRuleGuidePriceReq);
    }

    @Override
    @PostMapping("batchAdd")
    public List<FeeRule> batchAdd(@RequestBody @Validated FeeRuleBatchAddReq feeRuleBatchAddReq) {
        log.warn("batchAdd开始...");
        List<FeeRuleBatchAddReq.FeeRuleData> feeRuleDataList = feeRuleBatchAddReq.getFeeRuleDataList();
        if (CollectionUtils.isEmpty(feeRuleDataList)) {
            throw new BusException("feeRuleDataList不能为空");
        }
        FeeRuleBatchAddReq.FeeRuleData feeRuleData = feeRuleDataList.get(0);
        String sceneCode = feeRuleData.getSceneCode();
        Map<String, String> bizRule = feeRuleData.getBizRule();
        if (MapUtils.isNotEmpty(bizRule)) {
            String masterId = bizRule.get(CommonBizRule.Fields.masterId);
            String bizTag = bizRule.get(CommonBizRule.Fields.bizTag);
            handleDirtyData(sceneCode, masterId, bizTag);
        }

        if (SceneCodeEnum.CONTRACT_MASTER.getCode().equals(sceneCode)) {
            return feeRuleService.saveFeeRuleListNew(feeRuleDataList);
        } else {
            return saveFeeRuleList(feeRuleDataList);
        }

    }


    @ApiOperation("批量添加计价规则（返回规则id列表）")
    @Override
    @PostMapping("batchAddRules")
    public List<BatchAddRulesResponse> batchAddRules(@RequestBody @Validated BatchAddRulesRequest request) {
        List<BatchAddRulesRequest.FeeRuleData> feeRuleDataList = request.getFeeRuleDataList();
        if (CollectionUtils.isEmpty(feeRuleDataList)) {
            throw new BusException("feeRuleDataList不能为空");
        }
        BatchAddRulesRequest.FeeRuleData feeRuleData = feeRuleDataList.get(0);
        String sceneCode = feeRuleData.getSceneCode();
        FeeBizRuleDTO bizRuleDto = feeRuleData.getBizRuleDto();
        if (bizRuleDto != null) {
            handleDirtyData(sceneCode, bizRuleDto.getMasterId(), bizRuleDto.getBizTag());
        }

        return feeRuleService.saveFeeRuleList(feeRuleDataList);
    }


    @Override
    @PostMapping("batchDel")
    public List<FeeRule> batchDel(@RequestBody @Validated FeeRuleBatchDelReq feeRuleBatchDelReq) {
        if (CollectionUtils.isEmpty(feeRuleBatchDelReq.getFeeRuleIds())) {
            throw new BusException("id不能为空");
        }
        return feeRuleService.batchDel(feeRuleBatchDelReq.getFeeRuleIds());
    }


    @Override
    @PostMapping("batchDelRules")
    public boolean batchDelRules(@RequestBody @Validated FeeRuleBatchDelReq feeRuleBatchDelReq) {
        if (CollectionUtils.isEmpty(feeRuleBatchDelReq.getFeeRuleIds())) {
            throw new BusException("id不能为空");
        }
        try {
            feeRuleService.batchDelRules(feeRuleBatchDelReq.getFeeRuleIds());
            return true;
        } catch (Exception e) {
            return false;
        }
    }


    @PostMapping("batchDelete")
    public void batchDelete(@Validated @RequestBody FeeRuleBatchOperationReq req) {
        feeRuleService.batchDelete(req);
    }

    @PostMapping("getFeeRulePage")
    public SimplePageInfo<FeeRulePageResp> getFeeRulePage(@Validated @RequestBody FeeRulePageReq req) {
        return feeRuleService.getFeeRulePage(req);
    }

    @PostMapping("getFeeRuleServicePage")
    @Override
    public SimplePageInfo<FeeRuleServicePageResp> getFeeRuleServicePage(@Validated(FeeRuleServicePageReq.FeeRuleGroup.class) @RequestBody FeeRuleServicePageReq req) {
        return feeRuleService.getFeeRuleServicePage(req, "feeRule");
    }

    @GetMapping("completeDivision")
    public void completeDivision() {
        // 补全地区数据
        feeRuleService.completeDivision();

    }

    @GetMapping("completeGroup")
    public void completeGroup() {
        // 补全group数据，template + FeeRule
        feeRuleService.completeGroup();
    }

    @GetMapping("completeBizId")
    public void completeBizId() {
        // FeeRule completeBizId

    }


    @ApiOperation("补全服务类目ID")
    @GetMapping("completeServiceCategoryId")
    public void completeServiceCategoryId(@RequestParam("sceneCode") String sceneCode) {
        SceneInfo sceneInfo = sceneInfoService.query(sceneCode);
        if (Objects.isNull(sceneInfo)) {
            throw new BusException("场景不存在");
        }
        feeRuleService.completeServiceCategoryId(sceneCode, "系统补全serviceCategoryId字段，不影响价格");
    }


    @ApiOperation("根据地址查询服务id")
    @Override
    @PostMapping("getServiceIdsByDivision")
    public Set<Long> getServiceIdsByDivision(@Validated @RequestBody ServiceIdByDivisionIdsReq req) {
        return feeRuleService.getServiceIdsByDivision(req);
    }


    @ApiOperation("根据业务id查询待删除列表")
    @PostMapping("getByBizIdGroupByCondition")
    public List<QueryByBizIdResp> getByBizIdGroupByCondition(@Validated @RequestBody QueryByBizIdReq req) {
        return feeRuleService.getByBizIdGroupByCondition(req);
    }

    @ApiOperation("根据条件删除计价规则")
    @PostMapping("deleteByCondition")
    public Integer deleteByCondition(@Validated @RequestBody List<DeleteByBizIdReq> reqList) {
        if (CollectionUtils.isEmpty(reqList)) {
            return 0;
        }
        int deleteCount = 0;
        for (DeleteByBizIdReq req : reqList) {
            deleteCount += feeRuleService.deleteByCondition(req);
        }
        return deleteCount;
    }


    @PostMapping("getAttributeValuePriceByServiceId")
    public Map<String, AttributeValuePriceResp> getAttributeValuePriceByServiceId(@RequestParam("serviceId") Long serviceId,
                                                                                  @RequestParam("userId") Long userId) {
        log.info("getAttributeValuePriceByServiceId serviceId:{}, userId:{}", serviceId, userId);
        return feeRuleService.getAttributeValuePriceByServiceId(serviceId, userId);
    }

    @PostMapping("getAttributeValuePriceByServiceIdBatch")
    public Map<Long, Map<String, AttributeValuePriceResp>> getAttributeValuePriceByServiceIdBatch(@RequestParam("serviceIds") Set<Long> serviceIds,
                                                                                                  @RequestParam("userId") Long userId) {
        if (CollectionUtils.isEmpty(serviceIds)) {
            return Collections.emptyMap();
        }
        Map<Long, Map<String, AttributeValuePriceResp>> resultMap = new HashMap<>();
        for (Long serviceId : serviceIds) {
            Map<String, AttributeValuePriceResp> map = feeRuleService.getAttributeValuePriceByServiceId(serviceId, userId);
            if (MapUtils.isNotEmpty(map)) {
                resultMap.put(serviceId, map);
            }
        }
        return resultMap;
    }


    @ApiOperation("根据服务id获取“天天特价”费用规则列表")
    @PostMapping("getBargainPriceEverydayFeeRuleByServiceId")
    public SimplePageInfo<BargainPriceEverydayFeeRuleResp> getBargainPriceEverydayFeeRuleByServiceId(@Validated @RequestBody BargainPriceEverydayFeeRuleReq req) {
        if (!ApolloConfig.BARGAIN_PRICE_EVERYDAY_FEE_RULE_BY_SERVICE_ID_SWITCH) {
            log.warn("获取“天天特价”费用规则列表接口关闭！");
            return new SimplePageInfo<>();
        }
        log.info("getBargainPriceEverydayFeeRuleByServiceId req:{}", JSON.toJSONString(req));
        return feeRuleService.getBargainPriceEverydayFeeRuleByServiceId(req);
    }


    @ApiOperation("获取“天天特价”地区详情")
    @PostMapping("getDistrictDetail")
    public DistrictDetailResp getDistrictDetail(@Validated @RequestBody DistrictDetailReq req) {
        return feeRuleService.getDistrictDetail(req);
    }


    @ApiOperation("根据一级商品类目id或服务id获取最低价")
    @PostMapping("getLowestPriceByLevel1GoodsCategoryIdOrServiceIds")
    public SimplePageInfo<LowestPriceResp> getLowestPriceByLevel1GoodsCategoryIdOrServiceIds(@RequestBody LowestPriceReq req) {
        return feeRuleService.getLowestPriceByLevel1GoodsCategoryIdOrServiceIds(req);
    }


    @ApiOperation("根据服务id获取规则（sku）")
    @PostMapping("getAttributeValueByServiceId")
    public List<AttributeValueResp> getAttributeValueByServiceId(@RequestParam("sceneCode") String sceneCode,
                                                                 @RequestParam("serviceId") Long serviceId) {
        log.info("getAttributeValueByServiceId sceneCode:{}, serviceId:{}", sceneCode, serviceId);
        return feeRuleService.getAttributeValueByServiceId(sceneCode, serviceId);
    }


    @ApiOperation("获取单价和费用名称")
    @PostMapping("getPriceAndFeeName")
    public List<PriceAndFeeNameResp> getPriceAndFeeName(@Validated @RequestBody PriceAndFeeNameReq req) {
        return feeRuleService.getPriceAndFeeName(req);
    }


    /******  以下为师傅端定制接口 START ********/
    @ApiOperation("创建招募活动--基础价到活动价")
    @PostMapping("createRecruitActivity")
    public void createRecruitActivity(@Validated @RequestBody CreateRecruitActivityReq req) {
        feeRuleService.createRecruitActivityAsync(req);
    }


    @ApiOperation("删除指定师傅、指定招募活动的价格规则")
    @PostMapping("deleteForMaster")
    public void deleteForMaster(@RequestBody @Validated RemoveForMasterReq req) {
        feeRuleService.deleteForMaster(req);
    }

    @Override
    @ApiOperation("删除指定师傅、指定招募活动、指定街道的价格规则-异步（APP侧使用）")
    @PostMapping("deleteRuleByMasterIdAsync")
    public void deleteRuleByMasterIdAsync(@Validated @RequestBody DeleteRuleByMasterIdRequest request) {
        feeRuleService.deleteRuleByMasterIdAsync(request);
    }

    @Deprecated
    @PostMapping("createRecruitMaster")
    public void createRecruitMaster(@Validated @RequestBody CreateRecruitMasterReq req) {
        feeRuleService.createRecruitMasterAsync(req);
    }


    @ApiOperation("创建师傅招募--ocs后台手动录入师傅价（无活动/招募，即招募id=0）")
    @PostMapping("createRecruitMasterAsync")
    public void createRecruitMasterAsync(@Validated @RequestBody CreateRecruitMasterReq req) {
        feeRuleService.createRecruitMasterAsync(req);
    }


    @ApiOperation("创建招募活动--管理后台，同步")
    @PostMapping("createRecruitActivityMaster")
    public void createRecruitActivityMaster(@Validated @RequestBody CreateRecruitActivityMasterReq req) {
        try {
            feeRuleService.createRecruitActivityMaster(req);
        } catch (Exception e) {
            log.error("createRecruitActivityMaster，创建招募活动失败", e);
            // 这里尽量删即可，万一失败也无妨
            feeRuleService.batchDeleteLogically(SceneCodeEnum.CONTRACT_MASTER.getCode(), req.getMasterId(), req.getRecruitId());
            throw e;
        }
    }

    @ApiOperation("创建招募活动--APP端录入活动价，异步")
    @PostMapping("createRecruitActivityMasterAsync")
    public void createRecruitActivityMasterAsync(@Validated @RequestBody CreateRecruitActivityMasterReq req) {
        feeRuleService.createRecruitActivityMasterAsync(req);
    }


    @ApiOperation("修改招募活动--管理后台，异步")
    @PostMapping("modifyRecruitActivityMasterAsync")
    public void modifyRecruitActivityMasterAsync(@Validated @RequestBody ModifyRecruitActivityMasterReq req) {
        feeRuleService.modifyRecruitActivityMasterAsync(req);
//        try {
//            feeRuleService.modifyRecruitActivityMaster(req);
//        } catch (Exception e) {
//            log.error("modifyRecruitActivityMaster，修改招募活动失败", e);
//            // 这里尽量删即可，万一失败也无妨，因为师傅侧会屏蔽该师傅
//            feeRuleService.batchDeleteLogically(SceneCodeEnum.CONTRACT_MASTER.getCode(), req.getMasterId(), req.getRecruitId());
//        }
    }


    @PostMapping("getRecruitActivityMasterCreateStatus")
    public List<GetRecruitActivityMasterCreateStatusResp> getRecruitActivityMasterCreateStatus(@Validated @RequestBody GetRecruitActivityMasterCreateStatusReq req) {
        // 跟业务侧确认过，一个师傅的招募id不会超过100个，并且业务已经做好了分页，所以不用分页了
        return feeRuleService.getRecruitActivityMasterCreateStatus(req);
    }


    @ApiOperation("批量删除招募师傅--异步")
    @PostMapping("deleteRecruitMasterBatchAsync")
    public void deleteRecruitMasterBatchAsync(@Validated @RequestBody List<DeleteRecruitMasterReq> reqList) {
        feeRuleService.deleteRecruitMasterBatchAsync(reqList);
    }


    @ApiOperation("修改招募师傅")
    @PostMapping("modifyRecruitMaster")
    public void modifyRecruitMaster(@Validated @RequestBody ModifyRecruitMasterReq req) {
        feeRuleService.modifyRecruitMaster(req);
    }


    /******  以上为师傅端定制接口 END ********/



    private void handleDirtyData(String sceneCode, String masterId, String bizTag) {
        if (StringUtils.isNotBlank(sceneCode) && StringUtils.isNotBlank(masterId) && StringUtils.isNotBlank(bizTag)) {
            feeRuleService.batchDeleteLogically(sceneCode, masterId, bizTag);
        }
    }


    private List<FeeRule> saveFeeRuleList(List<FeeRuleBatchAddReq.FeeRuleData> feeRuleDataList) {
        List<FeeRule> feeRuleList = new LinkedList<>();
        for (FeeRuleBatchAddReq.FeeRuleData feeRuleData : feeRuleDataList) {
            FeeRule feeRule = new FeeRule();
            // 编译公式
            CalculateRuleData calculateRuleData = feeRuleData.getCalculateRuleData();
            if (Objects.nonNull(calculateRuleData) && Objects.nonNull(calculateRuleData.getExpressInfo())) {
                String compile = expressCompilerComposite.compile(calculateRuleData.getExpressInfo());
                try {
                    String[] outVarNames = expressRunner.getOutVarNames(compile);
                    calculateRuleData.setExpress(compile);
                    if (Objects.nonNull(outVarNames) && outVarNames.length > 0) {
                        calculateRuleData.setExpressionParamList(Arrays.asList(outVarNames));
                    }
                } catch (Exception e) {
                    throw new BusException(e.getMessage(), e);
                }
            }
            BeanUtils.copyProperties(feeRuleData, feeRule);
            feeRuleList.add(feeRule);
        }

        return feeRuleService.batchSave(feeRuleList);
    }


    private void circuitBreakerBySceneCode(List<String> sceneCodes) {
        if (StringUtils.isNotBlank(circuitBreakerSceneCodes)) {
            Set<String> intersections = CollectionInnerUtil.getIntersections(Arrays.asList(circuitBreakerSceneCodes.split(",")), sceneCodes);
            if (CollectionUtils.isNotEmpty(intersections)) {
                log.warn("熔断的场景编码:{}", StringUtils.join(intersections, ","));
                throw new BusException(ExceptionEnum.SCENE_CIRCUIT_BREAKER.code,
                        ExceptionEnum.SCENE_CIRCUIT_BREAKER.msg);
            }
        }
    }



//    private List<FeeRule> saveFeeRuleListNew(List<FeeRuleBatchAddReq.FeeRuleData> feeRuleDataList) {
//        List<FeeRule> feeRuleList = new LinkedList<>();
//        for (FeeRuleBatchAddReq.FeeRuleData feeRuleData : feeRuleDataList) {
//            FeeRule feeRule = new FeeRule();
//            // 编译公式
//            CalculateRuleData calculateRuleData = feeRuleData.getCalculateRuleData();
//            if (Objects.nonNull(calculateRuleData) && Objects.nonNull(calculateRuleData.getExpressInfo())) {
//                String compile = expressCompilerComposite.compile(calculateRuleData.getExpressInfo());
//                try {
//                    String[] outVarNames = expressRunner.getOutVarNames(compile);
//                    calculateRuleData.setExpress(compile);
//                    if (Objects.nonNull(outVarNames) && outVarNames.length > 0) {
//                        calculateRuleData.setExpressionParamList(Arrays.asList(outVarNames));
//                    }
//                } catch (Exception e) {
//                    throw new BusException(e.getMessage(), e);
//                }
//            }
//            BeanUtils.copyProperties(feeRuleData, feeRule);
//            feeRuleList.add(feeRule);
//        }
//
//        return feeRuleService.batchSaveNew(feeRuleList);
//    }

}
