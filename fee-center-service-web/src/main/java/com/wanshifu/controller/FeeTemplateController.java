package com.wanshifu.controller;

import com.alibaba.fastjson.JSON;
import com.ql.util.express.DefaultContext;
import com.ql.util.express.ExpressRunner;
import com.wanshifu.domain.request.template.TemplateDelRequest;
import com.wanshifu.domain.response.template.TemplateDelResponse;
import com.wanshifu.exception.FeeException;
import com.wanshifu.fee.center.api.FeeTemplateApi;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.document.CalculateRuleData;
import com.wanshifu.fee.center.domain.document.FeeTemplate;
import com.wanshifu.fee.center.domain.dto.ExpressResultInfo;
import com.wanshifu.fee.center.domain.enums.BizRuleConditionModeEnum;
import com.wanshifu.fee.center.domain.enums.FeeTypeTagEnum;
import com.wanshifu.fee.center.domain.request.*;
import com.wanshifu.fee.center.domain.request.template.*;
import com.wanshifu.fee.center.domain.response.*;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.service.FeeTemplateService;
import com.wanshifu.strategy.express.ExpressCompiler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "计价模板管理")
@RestController
@RequestMapping("feeTemplate")
public class FeeTemplateController implements FeeTemplateApi {

    @Resource
    private FeeTemplateService feeTemplateService;

    @Resource
    private ExpressCompiler expressCompilerComposite;

    @Resource
    private ExpressRunner expressRunner;


    @Override
    @PostMapping("query")
    @Deprecated // 尽量使用queryByCondition(FeeTemplateQueryByConditionReq feeTemplateQueryReq)
    public SimplePageInfo<FeeTemplate> query(@RequestBody @Validated FeeTemplateQueryReq feeTemplateQueryReq) {
        Page<FeeTemplate> feeTemplates = feeTemplateService.queryByCondition(feeTemplateQueryReq);
        SimplePageInfo<FeeTemplate> feeTemplateSimplePageInfo = new SimplePageInfo<>();
        feeTemplateSimplePageInfo.setPages(feeTemplates.getTotalPages());
        feeTemplateSimplePageInfo.setPageSize(feeTemplates.getSize());
        feeTemplateSimplePageInfo.setTotal(feeTemplates.getTotalElements());
        feeTemplateSimplePageInfo.setPageNum(feeTemplates.getNumber());
        feeTemplateSimplePageInfo.setList(feeTemplates.getContent());
        return feeTemplateSimplePageInfo;
    }

    @PostMapping("queryService")
    public SimplePageInfo<FeeTemplate> queryService(@RequestBody @Validated FeeTemplateQueryByConditionReq feeTemplateQueryByConditionReq) {
        if (StringUtils.isEmpty(feeTemplateQueryByConditionReq.getSceneCode())) {
            throw new BusException("场景为空");
        }
        Page<FeeTemplate> feeTemplates = feeTemplateService.queryServiceByCondition(feeTemplateQueryByConditionReq);
        SimplePageInfo<FeeTemplate> feeTemplateSimplePageInfo = new SimplePageInfo<>();
        feeTemplateSimplePageInfo.setPages(feeTemplates.getTotalPages());
        feeTemplateSimplePageInfo.setPageSize(feeTemplates.getSize());
        feeTemplateSimplePageInfo.setTotal(feeTemplates.getTotalElements());
        feeTemplateSimplePageInfo.setPageNum(feeTemplates.getNumber());
        feeTemplateSimplePageInfo.setList(feeTemplates.getContent());
        return feeTemplateSimplePageInfo;
    }

    @Override
    @PostMapping("queryByCondition")
    public SimplePageInfo<FeeTemplate> queryByCondition(@RequestBody @Validated FeeTemplateQueryByConditionReq feeTemplateQueryByConditionReq) {
        Page<FeeTemplate> feeTemplates = feeTemplateService.queryByCondition(feeTemplateQueryByConditionReq);
        SimplePageInfo<FeeTemplate> feeTemplateSimplePageInfo = new SimplePageInfo<>();
        feeTemplateSimplePageInfo.setPages(feeTemplates.getTotalPages());
        feeTemplateSimplePageInfo.setPageSize(feeTemplates.getSize());
        feeTemplateSimplePageInfo.setTotal(feeTemplates.getTotalElements());
        feeTemplateSimplePageInfo.setPageNum(feeTemplates.getNumber());
        feeTemplateSimplePageInfo.setList(feeTemplates.getContent());
        return feeTemplateSimplePageInfo;
    }


    @ApiOperation("根据条件查询算费模板-智能运营、配置管理平台")
    @Override
    @PostMapping("queryTemplateByCondition")
    public SimplePageInfo<FeeTemplateResp> queryTemplateByCondition(@RequestBody @Validated FeeTemplateReq req) {
        Page<FeeTemplate> templatePage = feeTemplateService.queryTemplateByCondition(req);
        List<FeeTemplate> templates = templatePage.getContent();
        if (CollectionUtils.isEmpty(templates)) {
            return new SimplePageInfo<>();
        }
        SimplePageInfo<FeeTemplateResp> resultPage = new SimplePageInfo<>();
        resultPage.setPages(templatePage.getTotalPages());
        resultPage.setPageSize(templatePage.getSize());
        resultPage.setTotal(templatePage.getTotalElements());
        resultPage.setPageNum(templatePage.getNumber() + 1);
        resultPage.setList(templates.stream().map(t -> {
            FeeTemplateResp resp =  new FeeTemplateResp();
            Map<String, String> bizRule = t.getBizRule();
            resp.setTemplateId(t.getTemplateId());
            resp.setServiceName(bizRule.get(CommonBizRule.Fields.serviceName));
            resp.setSkuNo(bizRule.get(CommonBizRule.Fields.skuNo));
            resp.setFeeTypeTag(bizRule.get(CommonBizRule.Fields.feeTypeTag));
            resp.setAttributeDisplayName(bizRule.get(CommonBizRule.Fields.attributeDisplayName));
            return resp;
        }).collect(Collectors.toList()));
        return resultPage;
    }

    @Override
    @ApiOperation("根据条件查询算费模板-配置管理平台-场景列表->删除自定义sku的查询")
    @PostMapping("queryTemplateByCondition4DeleteCustomTemplate")
    public SimplePageInfo<FeeTemplate4DeleteCustomResp> queryTemplateByCondition4DeleteCustomTemplate(@RequestBody @Validated FeeTemplate4DeleteCustomReq req) {
        Page<FeeTemplate> templatePage = feeTemplateService.queryTemplateByCondition4DeleteCustom(req);
        List<FeeTemplate> templates = templatePage.getContent();
        if (CollectionUtils.isEmpty(templates)) {
            return new SimplePageInfo<>();
        }
        SimplePageInfo<FeeTemplate4DeleteCustomResp> resultPage = new SimplePageInfo<>();
        resultPage.setPages(templatePage.getTotalPages());
        resultPage.setPageSize(templatePage.getSize());
        resultPage.setTotal(templatePage.getTotalElements());
        resultPage.setPageNum(templatePage.getNumber() + 1);
        resultPage.setList(templates.stream().map(t -> {
            FeeTemplate4DeleteCustomResp resp =  new FeeTemplate4DeleteCustomResp();
            Map<String, String> bizRule = t.getBizRule();
            resp.setTemplateId(t.getTemplateId());
            resp.setServiceName(bizRule.get(CommonBizRule.Fields.serviceName));
            resp.setSkuNo(bizRule.get(CommonBizRule.Fields.skuNo));
            resp.setFeeTypeTag(bizRule.get(CommonBizRule.Fields.feeTypeTag));
            resp.setAttributeDisplayName(bizRule.get(CommonBizRule.Fields.attributeDisplayName));
            resp.setCustomSkuUserId(bizRule.get(CommonBizRule.Fields.customSkuUserId));
            resp.setSkuType(bizRule.get(CommonBizRule.Fields.skuType));
            return resp;
        }).collect(Collectors.toList()));
        return resultPage;
    }


    @ApiOperation("通过serviceId获取计价模板列表")
    @PostMapping("getListByServiceId")
    public List<FeeTemplate> getListByServiceId(@Validated @RequestBody GetByServiceIdRequest req) {
        return feeTemplateService.getListByServiceId(req);
    }


    @Override
    @PostMapping("batchQuery")
    public SimplePageInfo<FeeTemplate> batchQuery(@RequestBody @Validated FeeTemplateBatchQueryReq feeTemplateBatchQueryReq) {
        Page<FeeTemplate> feeTemplates = feeTemplateService.batchQueryByCondition(feeTemplateBatchQueryReq);
        SimplePageInfo<FeeTemplate> feeTemplateSimplePageInfo = new SimplePageInfo<>();
        feeTemplateSimplePageInfo.setPages(feeTemplates.getTotalPages());
        feeTemplateSimplePageInfo.setPageSize(feeTemplates.getSize());
        feeTemplateSimplePageInfo.setTotal(feeTemplates.getTotalElements());
        feeTemplateSimplePageInfo.setPageNum(feeTemplates.getNumber());
        feeTemplateSimplePageInfo.setList(feeTemplates.getContent());
        return feeTemplateSimplePageInfo;
    }


    @ApiOperation("分页查询模板列表")
    @Override
    @PostMapping("getPageList")
    public SimplePageInfo<TemplatePageListResponse> getPageList(@RequestBody @Validated FeeTemplatePageListRequest request) {
        request.validate();
        Page<FeeTemplate> pageList = feeTemplateService.getPageList(request);
        SimplePageInfo<TemplatePageListResponse> resultPage = new SimplePageInfo<>();
        resultPage.setPages(pageList.getTotalPages());
        resultPage.setPageSize(pageList.getSize());
        resultPage.setTotal(pageList.getTotalElements());
        resultPage.setPageNum(pageList.getNumber() + 1);
        resultPage.setList(
                Optional.ofNullable(pageList.getContent()).orElse(Collections.emptyList())
                        .stream()
                        .map(this::getFeeTemplatePageListResponse)
                        .collect(Collectors.toList())
        );
        return resultPage;
    }


    @ApiOperation("创建模板")
    @PostMapping("create")
    public void create(@RequestBody @Validated FeeTemplateConfigureReq feeTemplateConfigureReq) {
        List<FeeTemplate> feeTemplates = feeTemplateService.getFeeTemplates(feeTemplateConfigureReq);
        feeTemplateService.save(feeTemplates);
    }


    @ApiOperation("修改模板")
    @PostMapping("modify")
    public void modify(@RequestBody @Validated FeeTemplateConfigureReq feeTemplateConfigureReq) {
        List<FeeTemplateConfigureReq.FeeTemplateConfigure> configureList = feeTemplateConfigureReq.getConfigureList();
        configureList.stream()
                .filter(Objects::nonNull)
                .forEach(configure -> {
                    if (Objects.isNull(configure.getTemplateId())) {
                        throw new FeeException("templateId不能为空");
                    }
                });
        List<FeeTemplate> feeTemplates = feeTemplateService.getFeeTemplates(feeTemplateConfigureReq);
        feeTemplateService.update(feeTemplates);
    }


    @Override
    @PostMapping("modifySynthetically")
    public void modifySynthetically(@RequestBody @Validated FeeTemplateModifySyntheticallyReq req) {
        FeeTemplateConfigureReq createReq = req.getCreateReq();
        FeeTemplateConfigureReq modifyReq = req.getModifyReq();

        List<FeeTemplateConfigureReq.FeeTemplateConfigure> configureList = new ArrayList<>();
        List<FeeTemplateConfigureReq.FeeTemplateConfigure> createReqConfigureList = createReq.getConfigureList();
        List<FeeTemplateConfigureReq.FeeTemplateConfigure> modifyConfigureList = modifyReq.getConfigureList();
        configureList.addAll(createReqConfigureList);
        configureList.addAll(modifyConfigureList);
        if (!CollectionUtils.isEmpty(configureList)) {
            feeTemplateService.checkHasFeeTypeTag(configureList);
        }

        if (!CollectionUtils.isEmpty(createReqConfigureList)) {
            createReq.setCheckHasFeeTypeTag(false);
            create(createReq);
        }

        if (!CollectionUtils.isEmpty(modifyConfigureList)) {
            modifyReq.setCheckHasFeeTypeTag(false);
            modify(modifyReq);
        }

        List<FeeTemplateDelReq> deleteReqList = req.getDeleteReqList();
        if (!CollectionUtils.isEmpty(deleteReqList)) {
            deleteReqList.forEach(e -> feeTemplateService.delete(e.getTemplateId()));
        }
    }

    @ApiOperation("删除模板")
    @PostMapping("del")
    public void del(@RequestBody @Validated FeeTemplateDelReq feeTemplateDelReq) {
        feeTemplateService.delete(feeTemplateDelReq.getTemplateId());
    }

    @ApiOperation("锁定模板-当添加了自定义模板时，父模板需要锁定（即有自定义sku的父模板不能用来算价）")
    @Override
    @PostMapping("lock")
    public void lock(@RequestBody @Validated FeeTemplateLockReq feeTemplateLockReq) {
        feeTemplateService.lock(feeTemplateLockReq.getTemplateIds());
    }

    @PostMapping("validateExpress")
    public ExpressResultInfo validateExpress(@RequestBody @Validated CalculateRuleData calculateRuleData) {
        // 编译 , 缓存;
        DefaultContext<String, Object> params = new DefaultContext<>();
        for (Map.Entry<String, String> stringStringEntry : calculateRuleData.getExpressionParamMap().entrySet()) {
            try {
                params.put(stringStringEntry.getKey(), new BigDecimal(stringStringEntry.getValue()));
            } catch (Exception e) {
                params.put(stringStringEntry.getKey(), stringStringEntry.getValue());
            }
        }
        List<String> errorList = new ArrayList<>();
        try {
            ExpressResultInfo result = (ExpressResultInfo) expressRunner.execute(calculateRuleData.getExpress(), params, errorList, false, false, 2000);
            if (!CollectionUtils.isEmpty(errorList)) {
                throw new BusException(JSON.toJSONString(errorList));
            }
            return result;
        } catch (Exception e) {
            throw new BusException(e.getMessage(), e);
        }
    }

    @PostMapping("compileExpress")
    public String compileExpress(@RequestBody @Validated CalculateRuleData calculateRuleData) {
        // 编译
        return expressCompilerComposite.compile(calculateRuleData.getExpressInfo());
    }


    @Override
    @GetMapping("duplicateValidate")
    public boolean duplicateValidate(@RequestParam("serveId") Long serveId, @RequestParam("sceneCode") String sceneCode) {
        if (serveId == null || serveId.equals(0L)) {
            throw new BusException("服务id不能为空或0");
        }
        if (StringUtils.isEmpty(sceneCode)) {
            throw new BusException("场景编号不能为空");
        }
        return feeTemplateService.duplicateValidate(serveId, sceneCode);
    }


    @ApiOperation("复制计价属性")
    @Override
    @PostMapping("copyPricingAttribute")
    public CopyPricingAttributeResp copyPricingAttribute(@RequestBody @Valid CopyPricingAttributeReq req) {
        return feeTemplateService.copyPricingAttribute(req);
    }

    @ApiOperation("根据skuNo获取计价模板")
    @Override
    @PostMapping("getTemplateBySkuNo")
    public List<GetTemplateBySkuNoResp> getTemplateBySkuNo(@RequestBody @Valid GetTemplateBySkuNoReq req) {
        return feeTemplateService.getTemplateBySkuNo(req);
    }

    @ApiOperation("根据serviceId查询【存在】计价规则的场景列表")
    @PostMapping("getSceneListExistServiceIdAndSkuNos")
    public List<SceneBaseInfo> getSceneListExistServiceIdAndSkuNos(@RequestBody @Valid ServiceIdAndSkuNos req) {
        return feeTemplateService.getSceneListExistServiceIdAndSkuNos(req.getServiceId(), req.getSkuNos());
    }

    @ApiOperation("根据serviceId查询【不存在】计价规则的场景列表")
    @PostMapping("getSceneListNotExistServiceIdAndSkuNos")
    public List<SceneBaseInfo> getSceneListNotExistServiceIdAndSkuNos(@RequestBody @Valid ServiceIdAndSkuNos req) {
        return feeTemplateService.getSceneListNotExistServiceIdAndSkuNos(req.getServiceId(), req.getSkuNos());
    }

    @ApiOperation("创建计价模板（配置管理平台-企业用户-服务定价管理-创建到计价场景）")
    @PostMapping("createTemplate")
    public void createTemplate(@RequestBody @Valid SaveTemplateSkuNoReq req) {
        feeTemplateService.createTemplate(req);
    }


    @ApiOperation("修改计价模板（配置管理平台-企业用户-服务定价管理-更新到计价场景）")
    @PostMapping("modifyTemplate")
    public void modifyTemplate(@RequestBody @Valid SaveTemplateSkuNoReq req) {
        feeTemplateService.modifyTemplate(req);
    }


    @ApiOperation("根据场景编码获取已添加的服务id和名称")
    @PostMapping("getFeeTemplateServiceIdAndName")
    public SimplePageInfo<GetFeeTemplateServiceIdAndNameResp> getFeeRuleServiceIdAndName(@RequestBody @Validated GetFeeTemplateServiceIdAndNameReq req) {
        return feeTemplateService.getFeeRuleServiceIdAndName(req);
    }

    @ApiOperation("根据场景编码与服务名称（模糊查询）获取服务id和服务名称")
    @PostMapping("getServiceIdAndNameBySceneCodeAndServiceName")
    public SimplePageInfo<GetFeeTemplateServiceIdAndNameResp> getServiceIdAndNameBySceneCodeAndServiceName(@RequestBody @Validated GetServiceIdAndNameBySceneCodeAndServiceNameReq req) {
        FeeTemplateQueryByConditionReq request = new FeeTemplateQueryByConditionReq();
        request.setSceneCode(req.getSceneCode());
        FeeTemplateQueryByConditionReq.BizRuleCondition condition = new FeeTemplateQueryByConditionReq.BizRuleCondition();
        condition.setKey(CommonBizRule.Fields.serviceName);
        condition.setValue(req.getServiceName());
        condition.setMode(BizRuleConditionModeEnum.FUZZY_MATCH.code);
        request.setBizRule(Collections.singletonList(condition));
        request.setPageNum(req.getPageNum());
        request.setPageSize(req.getPageSize());
        Page<FeeTemplate> feeTemplatePage = feeTemplateService.queryServiceByCondition(request);

        SimplePageInfo<GetFeeTemplateServiceIdAndNameResp> resultPage = new SimplePageInfo<>();
        resultPage.setPages(feeTemplatePage.getTotalPages());
        resultPage.setPageSize(feeTemplatePage.getSize());
        resultPage.setTotal(feeTemplatePage.getTotalElements());
        resultPage.setPageNum(feeTemplatePage.getNumber() + 1);
        List<FeeTemplate> templates = feeTemplatePage.getContent();
        if (CollectionUtils.isEmpty(templates)) {
            return resultPage;
        }
        List<GetFeeTemplateServiceIdAndNameResp> resultList = templates.stream().map(template -> {
            Map<String, String> bizRule = template.getBizRule();
            String serviceId = bizRule.get(CommonBizRule.Fields.serviceId);
            String serviceName = bizRule.get(CommonBizRule.Fields.serviceName);
            return new GetFeeTemplateServiceIdAndNameResp(serviceId, serviceName);
        }).collect(Collectors.toList());
        resultPage.setList(resultList);
        return resultPage;
    }


    @ApiOperation("根据条件获取算费模板中的sku列表")
    @PostMapping("getFeeTemplateSkuList")
    public SimplePageInfo<GetFeeTemplateSkuListResp> getFeeTemplateSkuList(@RequestBody @Validated GetFeeTemplateSkuListReq req) {
        return feeTemplateService.getFeeTemplateSkuList(req);
    }

    @ApiOperation("获取模板中的服务类目id集合")
    @PostMapping("getServiceCategoryIds")
    public Set<Long> getServiceCategoryIds(@RequestBody @Valid GetServiceCategoryIdsReq req) {
        return feeTemplateService.getServiceCategoryIds(req);
    }


    @ApiOperation("批量删除模板")
    @PostMapping("delBatch")
    public TemplateDelResponse delBatch(@Validated @RequestBody TemplateDelRequest templateDelRequest) {
        Set<String> templateIds = templateDelRequest.getTemplateIds();
        if (templateIds.size() > 50) {
            throw new BusException("一次最多删除50个模板");
        }
        return feeTemplateService.delBatch(templateDelRequest);
    }



    private TemplatePageListResponse getFeeTemplatePageListResponse(FeeTemplate t) {
        TemplatePageListResponse response = new TemplatePageListResponse();
        response.setSceneCode(t.getSceneCode());
        response.setTemplateId(t.getTemplateId());
        Map<String, String> bizRule = t.getBizRule();
        response.setServiceCategoryId(bizRule.get(CommonBizRule.Fields.serviceCategoryId));
        response.setServiceId(bizRule.get(CommonBizRule.Fields.serviceId));
        response.setServiceName(bizRule.get(CommonBizRule.Fields.serviceName));
        response.setServiceModelId(bizRule.get(CommonBizRule.Fields.serviceModelId));
        response.setSkuNo(bizRule.get(CommonBizRule.Fields.skuNo));
        response.setSkuAttributePathName(bizRule.get(CommonBizRule.Fields.skuAttributePathName));
        response.setSkuNumberPathNo(bizRule.get(CommonBizRule.Fields.skuNumberPathNo));
        response.setSkuNumberName(bizRule.get(CommonBizRule.Fields.skuNumberName));
        response.setAttributeDisplayName(bizRule.get(CommonBizRule.Fields.attributeDisplayName));
        response.setFeeUnit(bizRule.get(CommonBizRule.Fields.feeUnit));
        response.setFeeName(bizRule.get(CommonBizRule.Fields.feeName));
        response.setSkuType(bizRule.get(CommonBizRule.Fields.skuType));
        response.setFeeTypeTag(bizRule.get(CommonBizRule.Fields.feeTypeTag));
        response.setCalculateRuleData(t.getCalculateRuleData());
        FeeTypeTagEnum feeTypeTagEnum = FeeTypeTagEnum.fromCode(bizRule.get(CommonBizRule.Fields.feeTypeTag));
        response.setFeeTypeTagName(feeTypeTagEnum != null ? feeTypeTagEnum.name : "费用类型标签有误：" + bizRule.get(CommonBizRule.Fields.feeTypeTag));
        return response;
    }

}
