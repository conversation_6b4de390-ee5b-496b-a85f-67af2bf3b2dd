package com.wanshifu.controller;

import com.wanshifu.fee.center.api.CalculateResultApi;
import com.wanshifu.fee.center.domain.document.CalculateResult;
import com.wanshifu.fee.center.domain.request.CalculateResultReq;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.service.CalculateResultService;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("calculateResult")
public class CalculateResultController implements CalculateResultApi {

    @Resource
    private CalculateResultService calculateResultService;

    @Override
    @PostMapping("query")
    public SimplePageInfo<CalculateResult> query(@RequestBody @Validated CalculateResultReq calculateResultReq) {
        Page<CalculateResult> calculateResults = calculateResultService.queryByCondition(calculateResultReq);
        SimplePageInfo<CalculateResult> calculateResultSimplePageInfo = new SimplePageInfo<>();
        calculateResultSimplePageInfo.setPages(calculateResults.getTotalPages());
        calculateResultSimplePageInfo.setPageSize(calculateResults.getSize());
        calculateResultSimplePageInfo.setTotal(calculateResults.getTotalElements());
        calculateResultSimplePageInfo.setPageNum(calculateResults.getNumber());
        calculateResultSimplePageInfo.setList(calculateResults.getContent());
        return calculateResultSimplePageInfo;
    }
}
