package com.wanshifu.controller;

import com.wanshifu.fee.center.api.AsyncTasksInfoApi;
import com.wanshifu.fee.center.domain.enums.AsyncTaskStatusEnum;
import com.wanshifu.fee.center.domain.enums.AsyncTaskTypeEnum;
import com.wanshifu.service.AsyncTasksInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api("异步任务信息")
@RestController
@RequestMapping("asyncTasksInfo")
public class AsyncTasksInfoController implements AsyncTasksInfoApi {

    @Resource
    private AsyncTasksInfoService asyncTasksInfoService;

    @ApiOperation(value = "根据任务id获取任务状态")
    @Override
    @GetMapping("getStatusByTaskId")
    public AsyncTaskStatusEnum getStatusByTaskId(@RequestParam("taskId") String taskId, @RequestParam("taskType") AsyncTaskTypeEnum taskType) {
        return asyncTasksInfoService.getStatusByTaskId(taskId, taskType);
    }
}
