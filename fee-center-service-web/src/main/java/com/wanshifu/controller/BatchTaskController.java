package com.wanshifu.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.wanshifu.domain.dto.table.BatchCustomSkuTemplateExcelModel;
import com.wanshifu.domain.dto.table.CustomSkuTemplateExcelModel;
import com.wanshifu.domain.request.BatchCustomSkuTemplateUploadReq;
import com.wanshifu.domain.request.BatchTaskGenerateReq;
import com.wanshifu.domain.request.BatchTaskUploadReq;
import com.wanshifu.domain.request.CustomSkuTemplateUploadReq;
import com.wanshifu.fee.center.api.BatchTaskApi;
import com.wanshifu.fee.center.domain.document.BatchTaskInfo;
import com.wanshifu.fee.center.domain.document.SceneInfo;
import com.wanshifu.fee.center.domain.enums.CustomSkuValueTypeEnum;
import com.wanshifu.fee.center.domain.enums.MatchSkuTypeEnum;
import com.wanshifu.fee.center.domain.request.BatchTaskQueryReq;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.listener.BatchCustomSkuDataListener;
import com.wanshifu.listener.CustomSkuDataListener;
import com.wanshifu.service.BatchTaskInfoService;
import com.wanshifu.service.FeeTemplateService;
import com.wanshifu.service.SceneInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("batchTask")
@Api("批量任务")
public class BatchTaskController implements BatchTaskApi {

    @Resource
    private BatchTaskInfoService batchTaskInfoService;

    @Resource
    private SceneInfoService sceneInfoService;
    @Resource
    private FeeTemplateService feeTemplateService;

    @PostMapping("query")
    @Override
    public SimplePageInfo<BatchTaskInfo> query(@RequestBody @Validated BatchTaskQueryReq batchTaskQueryReq) {
        Page<BatchTaskInfo> batchTaskInfos = batchTaskInfoService.queryByCondition(batchTaskQueryReq);
        SimplePageInfo<BatchTaskInfo> batchTaskInfoSimplePageInfo = new SimplePageInfo<>();
        batchTaskInfoSimplePageInfo.setPages(batchTaskInfos.getTotalPages());
        batchTaskInfoSimplePageInfo.setPageSize(batchTaskInfos.getSize());
        batchTaskInfoSimplePageInfo.setTotal(batchTaskInfos.getTotalElements());
        batchTaskInfoSimplePageInfo.setPageNum(batchTaskInfos.getNumber());
        batchTaskInfoSimplePageInfo.setList(batchTaskInfos.getContent());
        return batchTaskInfoSimplePageInfo;
    }

    @PostMapping(value = "upload", consumes = "multipart/form-data", produces = "application/json")
    public BatchTaskInfo upload(@Validated BatchTaskUploadReq batchTaskUploadReq) {
        return batchTaskInfoService.upload(batchTaskUploadReq);
    }


    @PostMapping("generate")
    public void generate(@RequestBody @Validated BatchTaskGenerateReq batchTaskGenerateReq, HttpServletResponse response) throws IOException {
        byte[] resource = batchTaskInfoService.generateTable(batchTaskGenerateReq);
        SceneInfo sceneInfo = sceneInfoService.query(batchTaskGenerateReq.getSceneCode());

        Assert.notNull(sceneInfo, "场景编码错误");

        // 返回文件流
        response.setCharacterEncoding(Charset.defaultCharset().displayName());
        String fileName = sceneInfo.getSceneName() + DateUtils.formatDateTime(new Date()) + ".xlsx";
        String encode = URLEncoder.encode(fileName, "UTF-8");
        response.addHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + encode + ";filename* =UTF-8''" + encode);
        response.setContentType("application/octet-stream");
        response.setStatus(HttpStatus.OK.value());

        response.getOutputStream().write(resource);

    }

    @GetMapping("resendMessage")
    public String resendMessage(String ids) {
        if (StringUtils.isBlank(ids)) {
            return "success";
        }
        String[] idAll = ids.split(",");
        Set<Long> idLongs = Arrays.stream(idAll).map(Long::parseLong).collect(Collectors.toSet());
        return batchTaskInfoService.resend(idLongs);
    }


    @ApiOperation("批量上传自定义SKU模板")
    @PostMapping(value = "uploadCustomSkuTemplate", consumes = "multipart/form-data", produces = "application/json")
    public void uploadCustomSkuTemplate(CustomSkuTemplateUploadReq req) {
        String customSkuValueType = req.getCustomSkuValueType();
        String matchSkuType = req.getMatchSkuType();
        if (CustomSkuValueTypeEnum.getByCode(customSkuValueType) == null) {
            throw new BusException(StrUtil.format("自定义sku值类型错误({})，仅支持：{}", customSkuValueType, CustomSkuValueTypeEnum.getAllCodes()));
        }

        if (MatchSkuTypeEnum.getByCode(matchSkuType) == null) {
            throw new BusException(StrUtil.format("匹配sku类型错误({})，仅支持：{}", matchSkuType, MatchSkuTypeEnum.getAllCodes()));
        }

        MultipartFile file = req.getFile();
        // 1. 文件类型校验
        if (!file.getOriginalFilename().matches(".*\\.(xls|xlsx)$")) {
            throw new BusException("文件类型错误，仅支持Excel文件");
        }

        // 2. 使用监听器解析文件
        CustomSkuDataListener listener = new CustomSkuDataListener(customSkuValueType);
        try {
            EasyExcel.read(
                    file.getInputStream(),
                    CustomSkuTemplateExcelModel.class,
                    listener
            ).sheet().headRowNumber(2).doRead();
        } catch (IOException e) {
            throw new BusException("Excel读取异常", e);
        }

        // 3. 判断数据库中已存在相同的数据
        boolean exist = feeTemplateService.checkCustomSkuTemplateExist(
                req.getTemplateId(),
                customSkuValueType,
                matchSkuType,
                listener.getDataList());
        if (exist) {
            throw new BusException("上传的文件中 与系统里存在相同的数据");
        }

        // 4. 保存有效数据
        feeTemplateService.saveCustomSkuTemplateBatch(
                req.getTemplateId(),
                customSkuValueType,
                matchSkuType,
                listener.getDataList());
    }


    @ApiOperation("按服务及sku批量上传自定义SKU模板")
    @PostMapping(value = "uploadCustomSkuTemplateBatch", consumes = "multipart/form-data", produces = "application/json")
    public void uploadCustomSkuTemplateBatch(BatchCustomSkuTemplateUploadReq req) {
        String customSkuValueType = req.getCustomSkuValueType();
        String matchSkuType = req.getMatchSkuType();
        if (CustomSkuValueTypeEnum.getByCode(customSkuValueType) == null) {
            throw new BusException(StrUtil.format("自定义sku值类型错误({})，仅支持：{}", customSkuValueType, CustomSkuValueTypeEnum.getAllCodes()));
        }

        if (MatchSkuTypeEnum.getByCode(matchSkuType) == null) {
            throw new BusException(StrUtil.format("匹配sku类型错误({})，仅支持：{}", matchSkuType, MatchSkuTypeEnum.getAllCodes()));
        }

        MultipartFile file = req.getFile();
        // 1. 文件类型校验
        if (!file.getOriginalFilename().matches(".*\\.(xls|xlsx)$")) {
            throw new BusException("文件类型错误，仅支持Excel文件");
        }

        // 2. 使用监听器解析文件
        BatchCustomSkuDataListener listener = new BatchCustomSkuDataListener(customSkuValueType);
        try {
            EasyExcel.read(
                    file.getInputStream(),
                    BatchCustomSkuTemplateExcelModel.class,
                    listener
            ).sheet().headRowNumber(2).doRead();
        } catch (IOException e) {
            throw new BusException("Excel读取异常", e);
        }

        // 3. 判断数据库中已存在相同的数据
        List<BatchCustomSkuTemplateExcelModel> dataList = listener.getDataList();
        if (CollectionUtil.isEmpty(dataList)) {
            return;
        }
        for (BatchCustomSkuTemplateExcelModel data : dataList) {
            boolean exist = feeTemplateService.checkCurrentCustomSkuTemplateExist(
                    Long.valueOf(data.getTemplateId()),
                    customSkuValueType,
                    matchSkuType,
                    data);
            if (exist) {
                throw new BusException("上传的文件中 与系统里存在相同的数据");
            }
        }

        // 4. 保存有效数据
        for (BatchCustomSkuTemplateExcelModel data : dataList) {
            feeTemplateService.saveCurrentCustomSkuTemplate(
                    Long.valueOf(data.getTemplateId()),
                    customSkuValueType,
                    matchSkuType,
                    data);
        }


    }
}
