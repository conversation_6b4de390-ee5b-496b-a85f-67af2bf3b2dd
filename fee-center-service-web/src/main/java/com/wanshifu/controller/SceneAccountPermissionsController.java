package com.wanshifu.controller;

import com.wanshifu.fee.center.domain.document.SceneAccountPermissions;
import com.wanshifu.fee.center.domain.request.permission.GetPageRequest;
import com.wanshifu.fee.center.domain.request.permission.UpdateRequest;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.infrastructure.utils.EnvUtil;
import com.wanshifu.iop.account.api.AccountApi;
import com.wanshifu.iop.account.domain.req.account.AccountListReq;
import com.wanshifu.iop.account.domain.resp.account.AccountListResp;
import com.wanshifu.service.SceneAccountPermissionsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "场景账号权限管理")
@RestController
@RequestMapping("sceneAccountPermissions")
public class SceneAccountPermissionsController {

    @Resource
    private SceneAccountPermissionsService sceneAccountPermissionsService;
    @Resource
    private AccountApi accountApi;

    @ApiOperation("获取场景账号权限列表")
    @PostMapping("getPage")
    public SimplePageInfo<SceneAccountPermissions> getPage(@Validated @RequestBody GetPageRequest request) {
        return sceneAccountPermissionsService.getPage(request);
    }


    @ApiOperation("更新场景账号权限")
    @PostMapping("update")
    public void update(@Validated @RequestBody UpdateRequest request) {
        sceneAccountPermissionsService.update(request);
    }


    @ApiOperation("根据账号模糊查询账号列表(仅做转发)")
    @PostMapping("getAccountPage")
    public SimplePageInfo<AccountListResp> getAccountPage(@RequestBody @Validated AccountListReq request) {
        int productType = 10;  // 生产环境
        if (EnvUtil.PROFILE_TEST.equals(EnvUtil.getProfile())) {
            productType = 11;   // 测试环境
        }
        request.setProductType(productType);
        return accountApi.list(request);
    }

}
