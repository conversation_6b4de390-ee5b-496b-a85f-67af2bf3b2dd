package com.wanshifu.controller;

import com.wanshifu.fee.center.domain.request.common.Create;
import com.wanshifu.fee.center.domain.request.common.Modify;
import com.wanshifu.fee.center.domain.request.common.Update;
import com.wanshifu.fee.center.domain.request.mapping.*;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.service.FeeTemplateMappingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(value = "规则模板映射")
@RestController
@RequestMapping("feeTemplateMapping")
public class FeeTemplateMappingController {

    @Resource
    private FeeTemplateMappingService mappingService;

    @ApiOperation("添加")
    @PostMapping("add")
    public void add(@RequestBody @Validated(Create.class) AddRequest addRequest) {
        mappingService.add(addRequest);
    }

    @ApiOperation("批量添加")
    @PostMapping("batchAdd")
    public void batchAdd(@RequestBody @Validated(Create.class) List<AddRequest> addRequestList) {
        mappingService.batchAdd(addRequestList);
    }


    @ApiOperation("更新")
    @PostMapping("update")
    public void update(@RequestBody @Validated(Update.class) UpdateRequest updateRequest) {
        mappingService.update(updateRequest);
    }


    @ApiOperation("删除")
    @PostMapping("delete")
    public void delete(@RequestParam("mappingId") String mappingId) {
        mappingService.delete(mappingId);
    }


    @ApiOperation("分页查询列表")
    @PostMapping("getPageList")
    public SimplePageInfo<MappingPageListResponse> getPageList(@RequestBody @Validated MappingPageListRequest request) {
        Page<MappingPageListResponse> page = mappingService.getPageList(request);
        SimplePageInfo<MappingPageListResponse> mappingPage = new SimplePageInfo<>();
        mappingPage.setPages(page.getTotalPages());
        mappingPage.setPageSize(page.getSize());
        mappingPage.setTotal(page.getTotalElements());
        mappingPage.setPageNum(page.getNumber() + 1);
        mappingPage.setList(page.getContent());
        return mappingPage;
    }


    @ApiOperation("复制映射")
    @PostMapping("copyMapping")
    public void copyMapping(@RequestBody @Validated CopyMappingRequest request) {
        mappingService.copyMapping(request);
    }

    @ApiOperation("设置应用类型")
    @PostMapping("setApplyTypes")
    public void setApplyTypes(@RequestBody @Validated UpdateMappingApplyTypesRequest request) {
        mappingService.setApplyTypes(request);
    }


}
