package com.wanshifu.controller;

import com.wanshifu.fee.center.domain.request.formula.*;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.service.FormulaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: <PERSON>
 * @create: 2024-02-27 14:16
 * @description: 公式Controller
 */
@Api(tags = "公式管理")
@RestController
@RequestMapping("formula")
public class FormulaController {

    @Resource
    private FormulaService formulaService;

    @ApiOperation("添加公式")
    @PostMapping(value = "add", consumes = MediaType.APPLICATION_JSON_VALUE)
    public void add(@RequestBody @Valid FormulaAddReq req) {
        formulaService.add(req);
    }

    @ApiOperation("修改公式")
    @PostMapping(value = "modify", consumes = MediaType.APPLICATION_JSON_VALUE)
    public void modify(@RequestBody @Valid FormulaModifyReq req) {
        formulaService.modify(req);
    }

    @ApiOperation("删除公式")
    @PostMapping("delete")
    public void delete(@RequestParam("formulaId") String formulaId) {
        formulaService.delete(Long.parseLong(formulaId));
    }

    @ApiOperation("分页查询公式列表")
    @PostMapping(value = "pageList", consumes = MediaType.APPLICATION_JSON_VALUE)
    public SimplePageInfo<FormulaListResp> pageList(@RequestBody @Valid FormulaListReq req) {
        return formulaService.pageList(req);
    }

    @ApiOperation("查询公式详情")
    @GetMapping("detail")
    public FormulaDetailResp detail(@RequestParam("formulaId") String formulaId) {
        return formulaService.detail(Long.parseLong(formulaId));
    }


    @ApiOperation("根据公式id查询公式列表")
    @PostMapping("getListByIds")
    public List<FormulaListByIdsResp> getListByIds(@RequestBody List<String> formulaIds) {
        if (CollectionUtils.isEmpty(formulaIds)) {
            return new ArrayList<>();
        }
        List<Long> formulaIdList = formulaIds.stream().map(Long::parseLong).collect(Collectors.toList());
        return formulaService.getListByFormulaIds(formulaIdList);
    }
}
