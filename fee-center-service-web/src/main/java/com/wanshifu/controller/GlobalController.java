package com.wanshifu.controller;

import com.wanshifu.domain.response.EnumInfoResp;
import com.wanshifu.fee.center.api.GlobalApi;
import com.wanshifu.fee.center.domain.document.SceneInfo;
import com.wanshifu.fee.center.domain.enums.*;
import com.wanshifu.fee.center.domain.response.SceneListResp;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.service.SceneInfoService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("global")
public class GlobalController implements GlobalApi {

    @Resource
    private SceneInfoService sceneInfoService;

    /**
     * @return
     * @see SceneInfoController
     * @deprecated
     */
    @Override
    @GetMapping("sceneList")
    public List<SceneListResp> sceneList() {
        List<SceneInfo> sceneInfos = sceneInfoService.queryAll();
        if (CollectionUtils.isEmpty(sceneInfos)) {
            return Collections.emptyList();
        }
        return sceneInfos.stream().map(sceneInfo -> {
            SceneListResp sceneListResp = new SceneListResp();
            sceneListResp.setName(sceneInfo.getSceneName());
            sceneListResp.setCode(sceneInfo.getSceneCode());
            sceneListResp.setBizIdType(sceneInfo.getBizIdType());
            sceneListResp.setPriceMaintain(sceneInfo.getPriceMaintain());
            sceneListResp.setSkuType(sceneInfo.getSkuType());
            return sceneListResp;
        }).collect(Collectors.toList());
    }

    @GetMapping("optionInfo")
    public EnumInfoResp getEnumInfo(@RequestParam(name = "needs", required = false) String needs) {

        boolean all = StringUtils.isBlank(needs);

        if ("all".equals(needs)) {
            all = true;
        }
        List<String> realNeeds = Collections.emptyList();
        if (!all) {
            realNeeds = Arrays.asList(needs.split(","));
            if (realNeeds.contains("all")) {
                all = true;
            }
        }
        EnumInfoResp enumInfoResp = new EnumInfoResp();

        // 逐个enum处理

        if (all || realNeeds.contains("amountType")) {
            List<EnumInfoResp.EnumInfoData> amountTypeList = Arrays.stream(AmountTypeEnum.values()).map(amountTypeEnum -> new EnumInfoResp.EnumInfoData(amountTypeEnum.code, amountTypeEnum.name, null)).collect(Collectors.toList());
            enumInfoResp.setAmountType(amountTypeList);
        }

        if (all || realNeeds.contains("applicableBiz")) {
            List<EnumInfoResp.EnumInfoData> applicableBizList = Arrays.stream(ApplicableBizEnum.values()).map(applicableBizEnum -> new EnumInfoResp.EnumInfoData(applicableBizEnum.code, applicableBizEnum.name, null)).collect(Collectors.toList());
            enumInfoResp.setApplicableBiz(applicableBizList);
        }

        if (all || realNeeds.contains("bizIdType")) {
            List<EnumInfoResp.EnumInfoData> bizIdTypeList = Arrays.stream(BizIdTypeEnum.values()).map(bizIdTypeEnum -> new EnumInfoResp.EnumInfoData(bizIdTypeEnum.code, bizIdTypeEnum.name, null)).collect(Collectors.toList());
            enumInfoResp.setBizIdType(bizIdTypeList);
        }

        if (all || realNeeds.contains("integrationSys")) {
            List<EnumInfoResp.EnumInfoData> integrationSysList = Arrays.stream(IntegrationSysEnum.values()).map(integrationSysEnum -> new EnumInfoResp.EnumInfoData(integrationSysEnum.code, integrationSysEnum.name, null)).collect(Collectors.toList());
            enumInfoResp.setIntegrationSys(integrationSysList);
        }
        if (all || realNeeds.contains("orderRange")) {
            List<EnumInfoResp.EnumInfoData> orderRangeList = Arrays.stream(OrderRangeEnum.values()).map(orderRangeEnum -> new EnumInfoResp.EnumInfoData(orderRangeEnum.code, orderRangeEnum.name, null)).collect(Collectors.toList());
            enumInfoResp.setOrderRange(orderRangeList);
        }
        if (all || realNeeds.contains("priceMaintain")) {
            List<EnumInfoResp.EnumInfoData> priceMaintainList = Arrays.stream(PriceMaintainEnum.values()).map(priceMaintainEnum -> new EnumInfoResp.EnumInfoData(priceMaintainEnum.code.toString(), priceMaintainEnum.name, null)).collect(Collectors.toList());
            enumInfoResp.setPriceMaintain(priceMaintainList);
        }

        if (all || realNeeds.contains("priceType")) {
            List<EnumInfoResp.EnumInfoData> priceTypeList = Arrays.stream(PriceTypeEnum.values()).map(priceTypeEnum -> new EnumInfoResp.EnumInfoData(priceTypeEnum.code, priceTypeEnum.name, null)).collect(Collectors.toList());
            enumInfoResp.setPriceType(priceTypeList);
        }

        if (all || realNeeds.contains("skuType")) {
            List<EnumInfoResp.EnumInfoData> skuTypeList = Arrays.stream(SkuTypeEnum.values()).map(skuTypeEnum -> new EnumInfoResp.EnumInfoData(skuTypeEnum.code, skuTypeEnum.name, skuTypeEnum.serviceModelId.toString())).collect(Collectors.toList());
            enumInfoResp.setSkuType(skuTypeList);
        }

        return enumInfoResp;
    }


}
