package com.wanshifu.controller;

import com.wanshifu.fee.center.api.FeeReasonRelationApi;
import com.wanshifu.fee.center.domain.document.FeeReasonRelation;
import com.wanshifu.fee.center.domain.request.FeeReasonRelationMaintainReq;
import com.wanshifu.fee.center.domain.request.FeeReasonRelationQueryReq;
import com.wanshifu.service.FeeReasonRelationService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: <PERSON>
 * @create: 2023-11-01 16:57
 * @description: 增减费用项关联平台通用SKU Controller
 */
@RestController
@RequestMapping("feeReasonRelation")
public class FeeReasonRelationController implements FeeReasonRelationApi {

    @Resource
    private FeeReasonRelationService feeReasonRelationService;

    @Override
    @PostMapping("query")
    public List<FeeReasonRelation> query(@RequestBody @Validated FeeReasonRelationQueryReq feeReasonRelationQueryReq) {
        return feeReasonRelationService.query(feeReasonRelationQueryReq);
    }

    @Override
    @PostMapping("maintain")
    public List<FeeReasonRelation> maintain(@RequestBody @Validated FeeReasonRelationMaintainReq feeReasonRelationMaintainReq) {
        return feeReasonRelationService.maintain(feeReasonRelationMaintainReq);
    }


}
