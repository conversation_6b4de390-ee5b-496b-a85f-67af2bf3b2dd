package com.wanshifu.controller;

import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.service.FeeRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("cronJob")
@Api("计价定时任务")
public class CronJobController {

    @Resource
    private FeeRuleService feeRuleService;
    @Resource
    private RedisHelper redisHelper;

    @GetMapping("synchronizeBargainPriceEveryDayFeeRule")
    public void synchronizeBargainPriceEveryDayFeeRule() {
        feeRuleService.pullFeeRuleFromBigdata(SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.getCode());
    }

    @GetMapping("pullFeeRuleFromBigdataUserOrderOfferGuidePrice")
    public void pullFeeRuleFromBigdataUserOrderOfferGuidePrice() {
        feeRuleService.pullFeeRuleFromBigdata(SceneCodeEnum.USER_ORDER_OFFER_GUIDE_PRICE.getCode());
    }

    @GetMapping("pullFeeRuleFromBigdataUserHistoricalAppointPrice")
    public void pullFeeRuleFromBigdataUserHistoricalAppointPrice() {
        feeRuleService.pullFeeRuleFromBigdata(SceneCodeEnum.USER_HISTORICAL_APPOINT_PRICE.getCode());
    }

    @GetMapping("pullCustomTemplateFromBigdata")
    public void pullCustomTemplateFromBigdata() {
        feeRuleService.pullCustomTemplateFromBigdata();
    }


    @GetMapping("preGenerateLowestPrice")
    public void preGenerateLowestPrice() {
        feeRuleService.preGenerateLowestPrice();
    }


    @GetMapping("preGenerateBargainPriceEverydayFeeRule")
    public void preGenerateBargainPriceEverydayFeeRule(@RequestParam("sceneCode") String sceneCode) {
        feeRuleService.preGenerateBargainPriceEverydayFeeRule(sceneCode);
    }

    @ApiOperation("物理删除已逻辑删除的FeeRule")
    @GetMapping("batchDeleteLogicallyDeletedFeeRules")
    public void batchDeleteLogicallyDeletedFeeRules(@RequestParam("sceneCode") String sceneCode) {
        feeRuleService.batchDeleteLogicallyDeletedFeeRules(sceneCode);
    }


    @GetMapping("getRedisValueByKey")
    public String getRedisValueByKey(@RequestParam("redisKey") String redisKey) {
        if (StringUtils.isBlank(redisKey)) {
            return null;
        }
        if (redisKey.contains("*")) {
            return null;
        }
        return redisHelper.get(redisKey);
    }

    @GetMapping("setRedisValueByKey")
    public String setRedisValueByKey(@RequestParam("redisKey") String redisKey,
                                   @RequestParam("redisValue") String redisValue,
                                   @RequestParam("expireSeconds") int expireSeconds) {
        if (StringUtils.isBlank(redisKey)) {
            return null;
        }
        if (redisKey.contains("*")) {
            return null;
        }
        return redisHelper.set(redisKey, redisValue, expireSeconds);
    }
}
