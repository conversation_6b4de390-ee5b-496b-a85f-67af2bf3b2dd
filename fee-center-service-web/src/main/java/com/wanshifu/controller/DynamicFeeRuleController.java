package com.wanshifu.controller;

import cn.hutool.core.util.StrUtil;
import com.ql.util.express.ExpressRunner;
import com.wanshifu.fee.center.domain.document.DynamicCalculateRuleData;
import com.wanshifu.fee.center.domain.document.DynamicFeeRule;
import com.wanshifu.fee.center.domain.document.DynamicPricingIndicator;
import com.wanshifu.fee.center.domain.enums.*;
import com.wanshifu.fee.center.domain.request.DynamicFeeRulePageReq;
import com.wanshifu.fee.center.domain.request.DynamicFeeRuleSaveReq;
import com.wanshifu.fee.center.domain.request.common.Create;
import com.wanshifu.fee.center.domain.request.common.Modify;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.service.DynamicFeeRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Objects;

@Api("动态价规则")
@RestController
@RequestMapping("dynamicFeeRule")
public class DynamicFeeRuleController {

    @Resource
    private DynamicFeeRuleService dynamicFeeRuleService;
    @Resource
    private ExpressRunner expressRunner;

    @GetMapping("queryDetail/{dynamicFeeRuleId}")
    public DynamicFeeRule queryDetail(@PathVariable(value = "dynamicFeeRuleId", required = true) Long dynamicFeeRuleId) {
        return dynamicFeeRuleService.queryById(dynamicFeeRuleId);
    }

    @PostMapping("query")
    public SimplePageInfo<DynamicFeeRule> query(@RequestBody @Validated DynamicFeeRulePageReq dynamicFeeRulePageReq) {
        Page<DynamicFeeRule> dynamicFeeRules = dynamicFeeRuleService.queryByCondition(dynamicFeeRulePageReq);
        SimplePageInfo<DynamicFeeRule> simplePageInfo = new SimplePageInfo<>();
        simplePageInfo.setPages(dynamicFeeRules.getTotalPages());
        simplePageInfo.setPageSize(dynamicFeeRules.getSize());
        simplePageInfo.setTotal(dynamicFeeRules.getTotalElements());
        simplePageInfo.setPageNum(dynamicFeeRules.getNumber());
        simplePageInfo.setList(dynamicFeeRules.getContent());
        return simplePageInfo;
    }

    @GetMapping("del")
    public DynamicFeeRule del(@RequestParam("dynamicFeeRuleId") Long dynamicFeeRuleId) {
        return dynamicFeeRuleService.del(dynamicFeeRuleId);
    }

    @GetMapping("audit")
    public DynamicFeeRule audit(@RequestParam("dynamicFeeRuleId") Long dynamicFeeRuleId, @RequestParam("status") Integer status) {
        if (Objects.isNull(dynamicFeeRuleId)) {
            throw new BusException("dynamicFeeRuleId is null");
        }
        if (Objects.isNull(status)) {
            throw new BusException("status is null");
        }

        DynamicFeeRuleStatusEnum dynamicFeeRuleStatusEnum = DynamicFeeRuleStatusEnum.fromCode(status);
        if (Objects.isNull(dynamicFeeRuleStatusEnum) || dynamicFeeRuleStatusEnum == DynamicFeeRuleStatusEnum.AUDIT) {
            throw new BusException("status不正确");
        }

        return dynamicFeeRuleService.audit(dynamicFeeRuleId, dynamicFeeRuleStatusEnum);
    }

    @ApiOperation("创建动态调价规则")
    @PostMapping("create")
    public DynamicFeeRule create(@RequestBody @Validated(Create.class) DynamicFeeRuleSaveReq dynamicFeeRuleSaveReq) {
        if (dynamicFeeRuleSaveReq.getStartTime().after(dynamicFeeRuleSaveReq.getEndTime())) {
            throw new BusException("开始时间不能大于结束时间");
        }

        if(CollectionUtils.isNotEmpty(dynamicFeeRuleSaveReq.getUserIds())){
            if(dynamicFeeRuleSaveReq.getUserIds().size()>200){
                throw new BusException("不能超过200个商户");
            }
        }

        validateDynamicCalculateRuleData(dynamicFeeRuleSaveReq);

        return dynamicFeeRuleService.save(dynamicFeeRuleSaveReq);
    }

    @PostMapping("modify")
    public DynamicFeeRule modify(@RequestBody @Validated(Modify.class) DynamicFeeRuleSaveReq dynamicFeeRuleSaveReq) {
        if (dynamicFeeRuleSaveReq.getStartTime().after(dynamicFeeRuleSaveReq.getEndTime())) {
            throw new BusException("开始时间不能大于结束时间");
        }
        if(CollectionUtils.isNotEmpty(dynamicFeeRuleSaveReq.getUserIds())){
            if(dynamicFeeRuleSaveReq.getUserIds().size()>200){
                throw new BusException("不能超过200个商户");
            }
        }
        validateDynamicCalculateRuleData(dynamicFeeRuleSaveReq);
        return dynamicFeeRuleService.modify(dynamicFeeRuleSaveReq);
    }

    private void validateDynamicCalculateRuleData(DynamicFeeRuleSaveReq dynamicFeeRuleSaveReq) {
        String adjustPriceUnit = dynamicFeeRuleSaveReq.getAdjustPriceUnit();
        if (StringUtils.isBlank(adjustPriceUnit)) {
            throw new BusException("调价单元不能为空");
        }
        if (AdjustPriceUnitEnum.SERVICE.getCode().equals(adjustPriceUnit)) {
            if (CollectionUtils.isEmpty(dynamicFeeRuleSaveReq.getDynamicPricingIndicators())) {
                throw new BusException("动态调价指标不能为空");
            }
            dynamicFeeRuleSaveReq.getDynamicPricingIndicators().forEach(DynamicPricingIndicator::validate);
        } else if (AdjustPriceUnitEnum.SKU.getCode().equals(adjustPriceUnit)) {
            DynamicCalculateRuleData dynamicCalculateRuleData = dynamicFeeRuleSaveReq.getDynamicCalculateRuleData();
            if (Objects.isNull(dynamicCalculateRuleData)) {
                throw new BusException("dynamicCalculateRuleData不能为空");
            }
            String dynamicType = dynamicCalculateRuleData.getDynamicType();
            DynamicTypeEnum dynamicTypeEnum = DynamicTypeEnum.fromCode(dynamicType);
            if (Objects.isNull(dynamicTypeEnum)) {
                throw new BusException("dynamicType非法");
            }
            if (dynamicTypeEnum == DynamicTypeEnum.RULE) {
                if (StringUtils.isNotBlank(dynamicCalculateRuleData.getExpress())) {
                    throw new BusException("计费表达式不能为空");
                }
                try {
                    String[] outVarNames = expressRunner.getOutVarNames(dynamicCalculateRuleData.getExpress());
                    dynamicCalculateRuleData.setExpressionParamList(Arrays.asList(outVarNames));
                } catch (Exception e) {
                    throw new BusException("表达式校验错误", e);
                }
            } else {
                String dynamicSymbol = dynamicCalculateRuleData.getDynamicSymbol();
                DynamicSymbolEnum dynamicSymbolEnum = DynamicSymbolEnum.fromCode(dynamicSymbol);
                if (Objects.isNull(dynamicSymbolEnum)) {
                    throw new BusException("dynamicSymbol非法");
                }
                if (StringUtils.isBlank(dynamicCalculateRuleData.getAdjustValue())) {
                    throw new BusException("adjustValue不能为空");
                }
                try {
                    BigDecimal adjustValue = new BigDecimal(dynamicCalculateRuleData.getAdjustValue());
                    BigDecimal _100 = BigDecimal.valueOf(100);
                    BigDecimal percent = adjustValue.divide(_100, 2, RoundingMode.HALF_UP);
                    // ORIGIN_PRICE
                    // buildExpress
                    StringBuilder expressBuilder = new StringBuilder("import java.math.BigDecimal; import com.wanshifu.fee.center.domain.dto.ExpressResultInfo; ");
                    expressBuilder.append(DynamicCalculateRuleData.ORIGIN_PRICE).append(" ").append(dynamicSymbolEnum.symbol);
                    if (DynamicTypeEnum.PERCENT == dynamicTypeEnum) {
                        expressBuilder.append(" ( ").append(DynamicCalculateRuleData.ORIGIN_PRICE).append(" * ").append(percent).append(")");
                    } else {
                        expressBuilder.append(" ").append(adjustValue);
                    }
                    // 构建表达式完成
                    dynamicCalculateRuleData.setExpress(expressBuilder.toString());
                    try {
                        String[] outVarNames = expressRunner.getOutVarNames(dynamicCalculateRuleData.getExpress());
                        dynamicCalculateRuleData.setExpressionParamList(Arrays.asList(outVarNames));
                    } catch (Exception e) {
                        throw new BusException("表达式校验错误", e);
                    }

                } catch (Exception e) {
                    throw new BusException("adjustValue数字格式错误", e);
                }
            }
        } else {
            throw new BusException(StrUtil.format("调价单元非法,非法调价单元={}", adjustPriceUnit));
        }
    }
}
