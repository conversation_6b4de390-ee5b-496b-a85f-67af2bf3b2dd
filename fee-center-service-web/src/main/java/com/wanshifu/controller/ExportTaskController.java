package com.wanshifu.controller;

import com.wanshifu.domain.request.CrossSceneExportReq;
import com.wanshifu.domain.request.ExportTaskAddReq;
import com.wanshifu.domain.request.ExportTaskQueryReq;
import com.wanshifu.fee.center.domain.document.ExportTaskInfo;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.service.ExportTaskInfoService;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 批量导出任务
 */
@RestController
@RequestMapping("exportTask")
public class ExportTaskController {


    @Resource
    private ExportTaskInfoService exportTaskInfoService;

    @PostMapping("query")
    public SimplePageInfo<ExportTaskInfo> query(@RequestBody @Validated ExportTaskQueryReq exportTaskQueryReq) {
        Page<ExportTaskInfo> exportTaskInfos = exportTaskInfoService.queryByCondition(exportTaskQueryReq);
        SimplePageInfo<ExportTaskInfo> simplePageInfo = new SimplePageInfo<>();
        simplePageInfo.setPages(exportTaskInfos.getTotalPages());
        simplePageInfo.setPageSize(exportTaskInfos.getSize());
        simplePageInfo.setTotal(exportTaskInfos.getTotalElements());
        simplePageInfo.setPageNum(exportTaskInfos.getNumber());
        simplePageInfo.setList(exportTaskInfos.getContent());
        return simplePageInfo;
    }

    @PostMapping("addExportTask")
    public ExportTaskInfo addExportTask(@RequestBody @Validated ExportTaskAddReq exportTaskAddReq) {
        return exportTaskInfoService.addTask(exportTaskAddReq);
    }

    @GetMapping("resendMessage")
    public String resendMessage(String ids) {
        if (StringUtils.isBlank(ids)) {
            return "success";
        }
        String[] idAll = ids.split(",");
        Set<Long> idLongs = Arrays.stream(idAll).map(Long::parseLong).collect(Collectors.toSet());
        return exportTaskInfoService.resend(idLongs);
    }


    @ApiOperation(value = "跨场景导出算费规则")
    @PostMapping("crossSceneExport")
    public ExportTaskInfo crossSceneExport(@Validated @RequestBody CrossSceneExportReq req) {
        return exportTaskInfoService.crossSceneExport(req);
    }

}
