package com.wanshifu.controller;

import com.wanshifu.domain.request.samedayconfig.*;
import com.wanshifu.fee.center.api.SameDayPriceConfigApi;
import com.wanshifu.fee.center.domain.request.common.Create;
import com.wanshifu.fee.center.domain.request.common.Modify;
import com.wanshifu.fee.center.domain.request.sameday.GetPriceRequest;
import com.wanshifu.fee.center.domain.response.sameday.GetPriceResponse;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.service.SameDayPriceConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "当日装CRUD")
@RestController
@RequestMapping("sameDayConfig")
public class SameDayPriceConfigController implements SameDayPriceConfigApi {

    @Resource
    private SameDayPriceConfigService sameDayPriceConfigService;

    /**
     * 新增配置
     *
     * @param request 新增配置request body
     */
    @ApiOperation("增加配置")
    @PostMapping("add")
    public void add(@RequestBody @Validated(Create.class) SaveRequest request) {
        sameDayPriceConfigService.add(request);
    }

    /**
     * 修改配置
     *
     * @param request 修改配置request body
     */
    @ApiOperation("修改配置")
    @PostMapping("modify")
    public void modify(@RequestBody @Validated(Modify.class) SaveRequest request) {
        sameDayPriceConfigService.modify(request);
    }


    @ApiOperation("查询配置分页列表")
    @GetMapping("pageList")
    public SimplePageInfo<PricePageListResponse> pageList(@RequestParam(value = "level1GoodsCategoryId", required = false) Long level1GoodsCategoryId,
                                                          @RequestParam(value = "activationState", required = false) String activationState,
                                                          @RequestParam("pageNum") int pageNum,
                                                          @RequestParam("pageSize") int pageSize) {
        return sameDayPriceConfigService.pageList(level1GoodsCategoryId, activationState, pageNum, pageSize);
    }



    /**
     * 详情
     *
     * @param sameDayPriceConfigId 当日价格配置ID
     * @return 详情
     */
    @ApiOperation("详情")
    @GetMapping("detail")
    public PriceDetailResponse detail(@RequestParam("sameDayPriceConfigId") String sameDayPriceConfigId) {
        return sameDayPriceConfigService.detail(sameDayPriceConfigId);
    }

    /**
     * 状态开关（开通/关闭）
     *
     * @param request request body
     * @see com.wanshifu.fee.center.domain.enums.SameDayPriceConfigStateEnum
     */
    @ApiOperation("状态开关")
    @PostMapping("stateSwitch")
    public void stateSwitch(@RequestBody @Validated StateSwitchRequest request) {
        sameDayPriceConfigService.stateSwitch(request);
    }

    /**
     * 快捷修改价格
     *
     * @param request 修改价格request body
     */
    @ApiOperation("快捷修改价格")
    @PostMapping("modifyPrice")
    public void modifyPrice(@RequestBody @Validated ModifyPriceRequest request) {
        sameDayPriceConfigService.modifyPrice(request);
    }


    @ApiOperation("获取一级商品类目（排除已添加的）")
    @GetMapping("level1GoodsCategory")
    public List<Level1GoodsCategoryResponse> level1GoodsCategory() {
        return sameDayPriceConfigService.level1GoodsCategory();
    }


    @Override
    @ApiOperation("获取当日装价格")
    @PostMapping("getPrice")
    public GetPriceResponse getPrice(@RequestBody @Validated GetPriceRequest request) {
        return sameDayPriceConfigService.getPrice(request);
    }
}
