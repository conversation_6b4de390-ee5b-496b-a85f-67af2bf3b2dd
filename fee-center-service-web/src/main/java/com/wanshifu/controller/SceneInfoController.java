package com.wanshifu.controller;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.domain.request.SceneInfoQueryPageReq;
import com.wanshifu.fee.center.api.SceneInfoApi;
import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.SceneInfo;
import com.wanshifu.fee.center.domain.enums.*;
import com.wanshifu.fee.center.domain.request.SceneInfoAddReq;
import com.wanshifu.fee.center.domain.request.SceneInfoModifyReq;
import com.wanshifu.fee.center.domain.request.SceneInfoSaveReq;
import com.wanshifu.fee.center.domain.request.scene.GetComparisonSceneCodeListReq;
import com.wanshifu.fee.center.domain.response.SceneListResp;
import com.wanshifu.fee.center.domain.response.scene.GetComparisonSceneCodeListResp;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.utils.CommonUtils;
import com.wanshifu.service.SceneAccountPermissionsService;
import com.wanshifu.service.SceneInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@RestController
@RequestMapping("sceneInfo")
@Api(tags = "计价场景管理")
public class SceneInfoController implements SceneInfoApi {

    @Resource
    private SceneInfoService sceneInfoService;
    // 字母开头，字母后续字符只能是英文字符数字与_-且字符串长度在1到64之间且_-不能连续出现
    public static final Pattern NAME_PATTERN = Pattern.compile("^(?!.*[-_]{2,})[a-zA-Z][a-zA-Z0-9_-]{0,62}$");


    /**
     * 查询全部场景
     */
    @Override
    @GetMapping("sceneList")
    public List<SceneListResp> sceneList() {
        return sceneInfoService.sceneList();
    }

    @PostMapping("query")
    public SimplePageInfo<SceneInfo> query(@RequestBody @Validated SceneInfoQueryPageReq sceneInfoQueryPageReq) {
        List<SceneListResp> respList = sceneInfoService.sceneList();
        if (CollectionUtils.isNotEmpty(respList)) {
            List<String> sceneCodes = respList.stream().map(SceneListResp::getCode).collect(Collectors.toList());
            sceneInfoQueryPageReq.setSceneCodes(sceneCodes);
        }
        Page<SceneInfo> sceneInfos = sceneInfoService.query(sceneInfoQueryPageReq);
        SimplePageInfo<SceneInfo> simplePageInfo = new SimplePageInfo<>();
        simplePageInfo.setPages(sceneInfos.getTotalPages());
        simplePageInfo.setPageSize(sceneInfos.getSize());
        simplePageInfo.setTotal(sceneInfos.getTotalElements());
        simplePageInfo.setPageNum(sceneInfos.getNumber());
        simplePageInfo.setList(sceneInfos.getContent());
        return simplePageInfo;
    }

    @GetMapping("queryById")
    public SceneInfo queryById(@RequestParam Long id) {
        return sceneInfoService.query(id);
    }

    /**
     * @param sceneInfoAddReq
     * @return
     */
    @ApiOperation("添加场景")
    @PostMapping("add")
    public SceneInfo add(@RequestBody @Validated SceneInfoAddReq sceneInfoAddReq) {
        //模板名称：必填，最多40个字符长度，错误：请填写正确的模板名称
        String sceneName = sceneInfoAddReq.getSceneName();
        if (StringUtils.isNotBlank(sceneName)) {
            int length = sceneName.length();
            if (length > 40 || length < 1) {
                throw new BusException("请填写正确的模板名称");
            }
        }
        // 模板编号/场景：必填，判断唯一性，提示模板编号已重复，最多64个个英文长度，错误：请填写正确的模板编号
        String sceneCode = sceneInfoAddReq.getSceneCode();
        if (!NAME_PATTERN.matcher(sceneCode).matches()) {
            throw new BusException("请填写正确的场景编码");
        }

        // 对接系统：选填，多选（可多个），选择范围如下：
        List<String> integrationSys = sceneInfoAddReq.getIntegrationSys();
        if (CollectionUtils.isNotEmpty(integrationSys)) {
            for (String integration : integrationSys) {
                IntegrationSysEnum integrationSysEnum = IntegrationSysEnum.fromCode(integration);
                if (Objects.isNull(integrationSysEnum)) {
                    throw new BusException(integration + "对接系统不存在");
                }
            }
        }
        // 价格类型（四选1，必填）：基础定价、商家、师傅、运营
        String priceType = sceneInfoAddReq.getPriceType();
        PriceTypeEnum priceTypeEnum = PriceTypeEnum.fromCode(priceType);
        if (Objects.isNull(priceTypeEnum)) {
            throw new BusException(priceType + "价格类型不存在");
        }

        // 业务ID类型（多选1，必填）：请选择、无、师傅、商家、合约师傅……（目前只能选这几个其中1个，默认选中请选择）
        String bizIdType = sceneInfoAddReq.getBizIdType();
        BizIdTypeEnum bizIdTypeEnum = BizIdTypeEnum.fromCode(bizIdType);
        if (Objects.isNull(bizIdTypeEnum)) {
            throw new BusException(bizIdType + "业务ID类型不存在");
        }

        // 计价范围（可多个，选填）：

        List<String> orderRange = sceneInfoAddReq.getOrderRange();
        if (CollectionUtils.isNotEmpty(orderRange)) {
            for (String range : orderRange) {
                OrderRangeEnum orderRangeEnum = OrderRangeEnum.fromCode(range);
                if (Objects.isNull(orderRangeEnum)) {
                    throw new BusException(range + "计价范围不存在");
                }
            }
        }

        // 中台维护价格（2选1，选填）：
        Integer priceMaintain = sceneInfoAddReq.getPriceMaintain();
        if (Objects.nonNull(priceMaintain)) {
            PriceMaintainEnum priceMaintainEnum = PriceMaintainEnum.fromCode(priceMaintain);
            if (Objects.isNull(priceMaintainEnum)) {
                throw new BusException(priceMaintain + "priceMaintain类型不存在");
            }
        }

        // 服务起步价（2选1，选填）
        // sceneInfoAddReq.getHaveBasePrice()

        // 模板标准sku类型（2选1，必填）
        String skuType = sceneInfoAddReq.getSkuType();
        SkuTypeEnum skuTypeEnum = SkuTypeEnum.fromCode(skuType);
        if (Objects.isNull(skuTypeEnum)) {
            throw new BusException(skuType + "模板标准sku类型不存在");
        }

        // 金额类型（2选1，必填）
        String amountType = sceneInfoAddReq.getAmountType();
        AmountTypeEnum amountTypeEnum = AmountTypeEnum.fromCode(amountType);
        if (Objects.isNull(amountTypeEnum)) {
            throw new BusException(amountType + "金额类型不存在");
        }


        // 模板备注（选填）
        String note = sceneInfoAddReq.getNote();
        if (StringUtils.isNotBlank(note)) {
            int length = note.trim().length();
            if (length < 1 || length > 100) {
                throw new BusException("请填写正确的场景备注");
            }
        }

        validatePriceUploadAlert(sceneInfoAddReq);

        return sceneInfoService.add(sceneInfoAddReq);
    }



    @ApiOperation("修改场景")
    @PostMapping("modify")
    public SceneInfo modify(@RequestBody @Validated SceneInfoModifyReq sceneInfoModifyReq) {
        String sceneName = sceneInfoModifyReq.getSceneName();
        if (StringUtils.isNotBlank(sceneName)) {
            int length = sceneName.length();
            if (length > 40 || length < 1) {
                throw new BusException("请填写正确的场景名称");
            }
        }
        String note = sceneInfoModifyReq.getNote();
        if (StringUtils.isNotBlank(note)) {
            int length = note.trim().length();
            if (length < 1 || length > 100) {
                throw new BusException("请填写正确的场景备注");
            }
        }
        Integer priceMaintain = sceneInfoModifyReq.getPriceMaintain();
        if (Objects.nonNull(priceMaintain)) {
            PriceMaintainEnum priceMaintainEnum = PriceMaintainEnum.fromCode(priceMaintain);
            if (Objects.isNull(priceMaintainEnum)) {
                throw new BusException(priceMaintain + "priceMaintain类型不存在");
            }
        }

        List<String> integrationSys = sceneInfoModifyReq.getIntegrationSys();
        if (CollectionUtils.isNotEmpty(integrationSys)) {
            for (String integration : integrationSys) {
                IntegrationSysEnum integrationSysEnum = IntegrationSysEnum.fromCode(integration);
                if (Objects.isNull(integrationSysEnum)) {
                    throw new BusException(integration + "对接系统不存在");
                }
            }
        }

        validatePriceUploadAlert(sceneInfoModifyReq);

        return sceneInfoService.modify(sceneInfoModifyReq);
    }

    @GetMapping("del")
    public SceneInfo del(@RequestParam(name = "id") Long id) {
        return sceneInfoService.del(id);
    }


    @ApiOperation("获取对比场景列表（上传价格告警）")
    @PostMapping("getComparisonSceneCodeList")
    public List<GetComparisonSceneCodeListResp> getComparisonSceneCodeList(@RequestBody @Validated GetComparisonSceneCodeListReq req) {
        return sceneInfoService.getComparisonSceneCodeList(req);
    }


    private void validatePriceUploadAlert(SceneInfoSaveReq req) {
        SceneInfoAddReq.PriceUploadAlert uploadAlert = req.getPriceUploadAlert();
        if (Objects.isNull(uploadAlert)) {
            throw new BusException("价格上传告警不能为空");
        }
        if (Objects.isNull(uploadAlert.getEnabled())) {
            throw new BusException("是否价格上传告警不能为空");
        }
        if (uploadAlert.getEnabled()) {
            String comparisonSceneCode = uploadAlert.getComparisonSceneCode();
            if (StringUtils.isBlank(comparisonSceneCode)) {
                throw new BusException("比较场景编码不能为空");
            }
            Integer greaterThan = uploadAlert.getGreaterThan();
            if (Objects.isNull(greaterThan) || greaterThan <= 0) {
                throw new BusException("对比“大于”百分比不能为空且应大于0");
            }
            Integer lessThan = uploadAlert.getLessThan();
            if (Objects.isNull(lessThan) || lessThan <= 0) {
                throw new BusException("对比“小于”百分比不能为空且应大于0");
            }
        }
    }

}
