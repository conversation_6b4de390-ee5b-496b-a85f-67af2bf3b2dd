package com.wanshifu.controller;

import com.wanshifu.fee.center.domain.document.SyncBlacklistStrategy;
import com.wanshifu.fee.center.domain.enums.DivisionTypeEnum;
import com.wanshifu.fee.center.domain.enums.LimitDivisionTypeEnum;
import com.wanshifu.fee.center.domain.request.common.Create;
import com.wanshifu.fee.center.domain.request.common.Modify;
import com.wanshifu.fee.center.domain.request.strategy.SyncBlacklistStrategyBatchUpdateStatusReq;
import com.wanshifu.fee.center.domain.request.strategy.SyncBlacklistStrategyPageReq;
import com.wanshifu.fee.center.domain.request.strategy.SyncBlacklistStrategyPageResp;
import com.wanshifu.fee.center.domain.request.strategy.SyncBlacklistStrategySaveReq;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.service.SyncBlacklistStrategyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Set;

@Api("同步数据黑名单策略")
@RestController
@RequestMapping("syncBlacklistStrategy")
public class SyncBlacklistStrategyController {

    @Resource
    private SyncBlacklistStrategyService syncBlacklistStrategyService;

    @ApiOperation(value = "添加同步数据黑名单策略")
    @PostMapping("add")
    public void add(@RequestBody @Validated(Create.class) SyncBlacklistStrategySaveReq req) {
        validate(req);
        syncBlacklistStrategyService.add(req);
    }


    @ApiOperation(value = "修改同步数据黑名单策略")
    @PostMapping("modify")
    public void modify(@RequestBody @Validated(Modify.class) SyncBlacklistStrategySaveReq req) {
        validate(req);
        syncBlacklistStrategyService.modify(req);
    }


    @ApiOperation(value = "删除同步数据黑名单策略")
    @PostMapping("delete")
    public void delete(@RequestParam String strategyId) {
        syncBlacklistStrategyService.delete(Long.valueOf(strategyId));
    }

    @ApiOperation(value = "同步数据黑名单策略详情")
    @PostMapping("detail")
    public SyncBlacklistStrategy detail(@RequestParam String strategyId) {
        return syncBlacklistStrategyService.detail(Long.valueOf(strategyId));
    }


    @ApiOperation(value = "同步数据黑名单策略分页查询")
    @PostMapping("findPage")
    public SimplePageInfo<SyncBlacklistStrategyPageResp> findPage(@RequestBody @Validated SyncBlacklistStrategyPageReq req) {
        return syncBlacklistStrategyService.findPage(req);
    }

    @ApiOperation(value = "批量更新同步数据黑名单策略状态")
    @PostMapping("batchUpdateStatus")
    public void batchUpdateStatus (@RequestBody @Validated SyncBlacklistStrategyBatchUpdateStatusReq req) {
        syncBlacklistStrategyService.batchUpdateStatus(req);
    }

    private void validate(SyncBlacklistStrategySaveReq req) {
        String divisionType = req.getDivisionType();
        String limitDivisionType = req.getLimitDivisionType();
        if (!DivisionTypeEnum.COUNTRY.code.equals(divisionType) && StringUtils.isEmpty(limitDivisionType)) {
            throw new BusException("非全国，限定区域不能为空");
        }
        Set<Long> divisionIds = req.getDivisionIds();
        if (StringUtils.isNotEmpty(limitDivisionType) &&
                !LimitDivisionTypeEnum.COUNTRY.code.equals(limitDivisionType) &&
                CollectionUtils.isEmpty(divisionIds)) {
            throw new BusException("限定区域不为无限制，区域id不能为空");
        }
    }

}
