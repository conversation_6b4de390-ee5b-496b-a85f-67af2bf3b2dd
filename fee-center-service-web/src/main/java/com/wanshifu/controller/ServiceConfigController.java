package com.wanshifu.controller;

import com.wanshifu.domain.request.serviceconfig.GetServiceRequest;
import com.wanshifu.domain.response.serviceconfig.ServiceBaseInfo;
import com.wanshifu.service.ServiceConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "服务配置")
@RestController
@RequestMapping("serviceConfig")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ServiceConfigController {

    private final ServiceConfigService serviceConfigService;

    @ApiOperation("获取服务基础信息")
    @PostMapping("getService")
    public ServiceBaseInfo getService(@RequestBody @Validated GetServiceRequest request) {
        return serviceConfigService.getService(request);
    }

}
