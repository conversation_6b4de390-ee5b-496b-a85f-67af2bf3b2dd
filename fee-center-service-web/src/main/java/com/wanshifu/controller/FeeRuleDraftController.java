package com.wanshifu.controller;

import com.ql.util.express.ExpressRunner;
import com.wanshifu.fee.center.domain.document.CalculateRuleData;
import com.wanshifu.fee.center.domain.document.FeeRuleDraft;
import com.wanshifu.fee.center.domain.request.FeeRuleBatchOperationReq;
import com.wanshifu.fee.center.domain.request.FeeRuleDraftModifyReq;
import com.wanshifu.fee.center.domain.request.FeeRulePageReq;
import com.wanshifu.fee.center.domain.request.FeeRuleServicePageReq;
import com.wanshifu.fee.center.domain.request.feeRule.DeleteByBizIdReq;
import com.wanshifu.fee.center.domain.request.feeRule.QueryByBizIdReq;
import com.wanshifu.fee.center.domain.request.feeRule.QueryByBizIdResp;
import com.wanshifu.fee.center.domain.request.formula.HandleBatchTaskReq;
import com.wanshifu.fee.center.domain.response.FeeRulePageResp;
import com.wanshifu.fee.center.domain.response.FeeRuleServicePageResp;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.service.FeeRuleDraftService;
import com.wanshifu.strategy.batch.BaseBatchTableHandler;
import com.wanshifu.strategy.batch.EnterpriseOrderOfferGuidePriceBatchTableHandler;
import com.wanshifu.strategy.express.ExpressCompiler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @author: Chen Yong
 * @create: 2023-09-08 17:09
 * @description: 计费规则草稿 Controller
 */
@Api(tags = "计费规则草稿")
@RestController
@RequestMapping("feeRuleDraft")
public class FeeRuleDraftController {

    @Resource
    private FeeRuleDraftService feeRuleDraftService;
    @Resource
    private ExpressCompiler expressCompilerComposite;
    @Resource
    private ExpressRunner expressRunner;
    @Resource
    private EnterpriseOrderOfferGuidePriceBatchTableHandler handler;
    @Resource
    private BaseBatchTableHandler handler2;

    @PostMapping("batchDelete")
    public void batchDelete(@Validated @RequestBody FeeRuleBatchOperationReq req) {
        feeRuleDraftService.batchDelete(req);
    }

    @PostMapping("modify")
    public FeeRuleDraft modify(@Validated @RequestBody FeeRuleDraftModifyReq req) {
        // 编译
        CalculateRuleData calculateRuleData = req.getCalculateRuleData();
        if (Objects.nonNull(calculateRuleData.getExpressInfo())) {
            String compile = expressCompilerComposite.compile(calculateRuleData.getExpressInfo());
            // 提取变量
            try {
                String[] outVarNames = expressRunner.getOutVarNames(compile);
                calculateRuleData.setExpress(compile);
                if (Objects.nonNull(outVarNames) && outVarNames.length > 0) {
                    calculateRuleData.setExpressionParamList(Arrays.asList(outVarNames));
                }
            } catch (Exception e) {
                throw new BusException(e.getMessage(), e);
            }
        }
        return feeRuleDraftService.modify(req);
    }


    @ApiOperation("批量审核计价规则草稿（一次只能审核一个服务）")
    @PostMapping("batchReview")
    public void batchReview(@Validated @RequestBody FeeRuleBatchOperationReq req) {
        feeRuleDraftService.batchReview(req);
        feeRuleDraftService.sendMessagePreGenerateMessage(req.getSceneCode());
    }

    @ApiOperation("真·批量审核计价规则草稿（一次能审核多个服务）")
    @PostMapping("realBatchReview")
    public void batchReview(@Validated @RequestBody List<FeeRuleBatchOperationReq> reqList) {
        if (CollectionUtils.isNotEmpty(reqList)) {
            reqList.forEach(req -> feeRuleDraftService.batchReview(req));
            feeRuleDraftService.sendMessagePreGenerateMessage(reqList.get(0).getSceneCode());
        }
    }

    @ApiOperation("分页查询计价规则草稿")
    @PostMapping("getFeeRuleDraftPage")
    public SimplePageInfo<FeeRulePageResp> getFeeRuleDraftPage(@Validated @RequestBody FeeRulePageReq req) {
        return feeRuleDraftService.getFeeRuleDraftPage(req);
    }

    @PostMapping("getFeeRuleDraftServicePage")
    public SimplePageInfo<FeeRuleServicePageResp> getFeeRuleDraftServicePage(@Validated(FeeRuleServicePageReq.FeeRuleDraftGroup.class) @RequestBody FeeRuleServicePageReq req) {
        return feeRuleDraftService.getFeeRuleDraftServicePage(req);
    }

    @ApiOperation("处理批量任务，用于本地调试")
    @PostMapping("handleBatchTask")
    public void handleBatchTask(@RequestBody HandleBatchTaskReq req) {
        handler2.handleBatchTask(req.getSceneInfo(), req.getBatchTaskInfo());
    }


    @ApiOperation("根据业务id查询待删除计价规则草稿列表")
    @PostMapping("getByBizIdGroupByCondition")
    public List<QueryByBizIdResp> getByBizIdGroupByCondition(@Validated @RequestBody QueryByBizIdReq req) {
        return feeRuleDraftService.getByBizIdGroupByCondition(req);
    }

    @ApiOperation("根据条件删除计价规则草稿")
    @PostMapping("deleteByCondition")
    public Integer deleteByCondition(@Validated @RequestBody List<DeleteByBizIdReq> reqList) {
        if (CollectionUtils.isEmpty(reqList)) {
            return 0;
        }
        int deleteCount = 0;
        for (DeleteByBizIdReq req : reqList) {
            deleteCount += feeRuleDraftService.deleteByCondition(req);
        }
        return deleteCount;
    }
}
