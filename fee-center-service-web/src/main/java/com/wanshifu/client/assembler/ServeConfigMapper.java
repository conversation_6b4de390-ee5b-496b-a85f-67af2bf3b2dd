package com.wanshifu.client.assembler;

import com.wanshifu.fee.center.domain.document.master2c.ServePrice2C4MasterDetail;
import com.wanshifu.order.config.domains.dto.serveConfig.ServeConfig4MasterPriceResp;
import ma.glasnost.orika.MapperFacade;
import ma.glasnost.orika.MapperFactory;
import ma.glasnost.orika.impl.DefaultMapperFactory;

import java.util.List;


public class ServeConfigMapper {
    private static final MapperFactory mapperFactory;

    static {
        mapperFactory = new DefaultMapperFactory.Builder().build();

        // 映射 ServeConfig.Option 内部嵌套结构
        mapperFactory.classMap(
                ServeConfig4MasterPriceResp.Option.class,
                ServePrice2C4MasterDetail.ServeConfig.Option.class
        ).byDefault().register();

        // 映射 ServeConfig 主体结构
        mapperFactory.classMap(
                ServeConfig4MasterPriceResp.class,
                ServePrice2C4MasterDetail.ServeConfig.class
        ).byDefault().register();
    }

    public static ServePrice2C4MasterDetail.ServeConfig mapToServeConfig(ServeConfig4MasterPriceResp src) {
        MapperFacade mapper = mapperFactory.getMapperFacade();
        return mapper.map(src, ServePrice2C4MasterDetail.ServeConfig.class);
    }

    public static List<ServePrice2C4MasterDetail.ServeConfig> mapToServeConfigList(List<ServeConfig4MasterPriceResp> srcList) {
        MapperFacade mapper = mapperFactory.getMapperFacade();
        return mapper.mapAsList(srcList, ServePrice2C4MasterDetail.ServeConfig.class);
    }
}
