package com.wanshifu.client.assembler;

import com.wanshifu.domain.request.master2c.ServePrice2C4MasterAddReq;
import com.wanshifu.fee.center.domain.document.master2c.DraftServePrice;
import com.wanshifu.fee.center.domain.document.master2c.ServePrice;
import ma.glasnost.orika.MapperFacade;
import ma.glasnost.orika.MapperFactory;
import ma.glasnost.orika.impl.DefaultMapperFactory;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/5 14:27
 */
public class DraftServePriceMapper {

    private static final MapperFactory mapperFactory;

    static {
        mapperFactory = new DefaultMapperFactory.Builder().build();

        mapperFactory.classMap(
                ServePrice2C4MasterAddReq.ServeConfig.Option.class,
                DraftServePrice.ServeConfig.Option.class
        ).byDefault().register();

        mapperFactory.classMap(
                ServePrice2C4MasterAddReq.ServeConfig.class,
                DraftServePrice.ServeConfig.class
        ).byDefault().register();

        mapperFactory.classMap(
                ServePrice2C4MasterAddReq.CityGroup.class,
                DraftServePrice.CityGroup.class
        ).byDefault().register();

        // 顶层映射：ServePrice2C4MasterAddReq → ServePrice
        mapperFactory.classMap(
                ServePrice2C4MasterAddReq.class,
                DraftServePrice.class
        ).byDefault().register();
    }


    public static DraftServePrice mapToDraftServePrice(ServePrice2C4MasterAddReq req) {
        MapperFacade mapper = mapperFactory.getMapperFacade();
        return mapper.map(req, DraftServePrice.class);
    }

}
