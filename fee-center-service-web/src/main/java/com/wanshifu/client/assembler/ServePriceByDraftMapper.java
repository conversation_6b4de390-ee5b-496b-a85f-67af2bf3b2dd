package com.wanshifu.client.assembler;

import com.wanshifu.fee.center.domain.document.master2c.DraftServePrice;
import com.wanshifu.fee.center.domain.document.master2c.ServePrice;
import ma.glasnost.orika.MapperFacade;
import ma.glasnost.orika.MapperFactory;
import ma.glasnost.orika.impl.DefaultMapperFactory;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/4 19:40
 */
public class ServePriceByDraftMapper {

    private static final MapperFactory mapperFactory;

    static {
        mapperFactory = new DefaultMapperFactory.Builder().build();

        mapperFactory.classMap(
                DraftServePrice.ServeConfig.Option.class,
                ServePrice.ServeConfig.Option.class
        ).byDefault().register();

        mapperFactory.classMap(
                DraftServePrice.ServeConfig.class,
                ServePrice.ServeConfig.class
        ).byDefault().register();

        mapperFactory.classMap(
                DraftServePrice.CityGroup.class,
                ServePrice.CityGroup.class
        ).byDefault().register();

        // 顶层映射：DraftServePrice → ServePrice
        mapperFactory.classMap(
                DraftServePrice.class,
                ServePrice.class
        ).byDefault().register();
    }

    public static ServePrice mapToServePrice(DraftServePrice draftServePrice) {
        MapperFacade mapper = mapperFactory.getMapperFacade();
        return mapper.map(draftServePrice, ServePrice.class);
    }
}
