package com.wanshifu.client.assembler;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.wanshifu.fee.center.domain.document.master2c.DraftServePrice;
import com.wanshifu.fee.center.domain.document.master2c.ServePrice2C4MasterDetail;
import com.wanshifu.fee.center.domain.document.master2c.ServePrice;
import com.wanshifu.framework.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class ServeConfigPriceMerger {

    public static void mergeSourceToTarget(List<ServePrice.ServeConfig> sourceList,
                                           List<ServePrice2C4MasterDetail.ServeConfig> targetList) {
        if (CollectionUtils.isEmpty(sourceList) || CollectionUtils.isEmpty(targetList)) {
            log.warn("Empty source or target list, skip merging");
            return;
        }

        Map<Long, BigDecimal> configPriceMap = sourceList.stream()
                .filter(e -> e.getMasterPrice() != null)
                .collect(Collectors.toMap(ServePrice.ServeConfig::getServeConfigId, ServePrice.ServeConfig::getMasterPrice));

        for (ServePrice2C4MasterDetail.ServeConfig sc : targetList) {
            Long configId = sc.getServeConfigId();
            BigDecimal price = configPriceMap.get(configId);
            if (price != null) {
                sc.setMasterPrice(price);
            }
        }

        Table<Long, String, BigDecimal> table = getConfigIdExternalIdPriceTable(sourceList);

        for (ServePrice2C4MasterDetail.ServeConfig sc : targetList) {
            Long configId = sc.getServeConfigId();
            if (configId == null) {
                continue;
            }

            mergeOptions(table, configId, sc.getOptions());
            mergeOptions(table, configId, sc.getAttaches());
        }
    }

    public static void mergeSourceToTargetByDraft(List<DraftServePrice.ServeConfig> sourceList,
                                           List<ServePrice2C4MasterDetail.ServeConfig> targetList) {
        if (CollectionUtils.isEmpty(sourceList) || CollectionUtils.isEmpty(targetList)) {
            log.warn("Empty source or target list, skip merging");
            return;
        }

        Map<Long, BigDecimal> configPriceMap = sourceList.stream()
                .filter(e -> e.getMasterPrice() != null)
                .collect(Collectors.toMap(DraftServePrice.ServeConfig::getServeConfigId, DraftServePrice.ServeConfig::getMasterPrice));

        for (ServePrice2C4MasterDetail.ServeConfig sc : targetList) {
            Long configId = sc.getServeConfigId();
            BigDecimal price = configPriceMap.get(configId);
            if (price != null) {
                sc.setMasterPrice(price);
            }
        }

        Table<Long, String, BigDecimal> table = getDraftConfigIdExternalIdPriceTable(sourceList);

        for (ServePrice2C4MasterDetail.ServeConfig sc : targetList) {
            Long configId = sc.getServeConfigId();
            if (configId == null) {
                continue;
            }

            mergeOptions(table, configId, sc.getOptions());
            mergeOptions(table, configId, sc.getAttaches());
        }
    }

    private static void mergeOptions(Table<Long, String, BigDecimal> table, Long configId,
                                     List<ServePrice2C4MasterDetail.ServeConfig.Option> options) {
        if (CollectionUtils.isEmpty(options)) {
            return;
        }

        for (ServePrice2C4MasterDetail.ServeConfig.Option option : options) {
            String extId = option.getExternalId();
            if (extId == null) {
                continue;
            }

            if (table.contains(configId, extId)) {
                BigDecimal price = table.get(configId, extId);
                option.setMasterPrice(price);
            }

            mergeRelationOptions(table, configId, option.getRelationOptionList());
        }
    }

    private static void mergeRelationOptions(Table<Long, String, BigDecimal> table, Long configId,
                                             List<ServePrice.ServeConfig.Option> relationOptions) {
        if (CollectionUtils.isEmpty(relationOptions)) {
            return;
        }

        for (ServePrice.ServeConfig.Option option : relationOptions) {
            String extId = option.getExternalId();
            if (extId != null && table.contains(configId, extId)) {
                option.setMasterPrice(table.get(configId, extId));
            }
        }
    }

    private static Table<Long, String, BigDecimal> getConfigIdExternalIdPriceTable(List<ServePrice.ServeConfig> sourceList) {
        Table<Long, String, BigDecimal> table = HashBasedTable.create();

        for (ServePrice.ServeConfig sc : sourceList) {
            Long configId = sc.getServeConfigId();
            if (configId == null) {
                continue;
            }

            // 处理options和attaches
            processOptions(table, configId, sc.getOptions());
            processOptions(table, configId, sc.getAttaches());
        }
        return table;
    }

    private static Table<Long, String, BigDecimal> getDraftConfigIdExternalIdPriceTable(List<DraftServePrice.ServeConfig> sourceList) {
        Table<Long, String, BigDecimal> table = HashBasedTable.create();

        for (DraftServePrice.ServeConfig sc : sourceList) {
            Long configId = sc.getServeConfigId();
            if (configId == null) {
                continue;
            }

            // 处理options和attaches
            processDraftOptions(table, configId, sc.getOptions());
            processDraftOptions(table, configId, sc.getAttaches());
        }
        return table;
    }

    private static void processOptions(Table<Long, String, BigDecimal> table, Long configId,
                                       List<ServePrice.ServeConfig.Option> options) {
        if (CollectionUtils.isEmpty(options)) {
            return;
        }

        for (ServePrice.ServeConfig.Option option : options) {
            String extId = option.getExternalId();
            BigDecimal price = option.getMasterPrice();
            if (extId != null && price != null) {
                table.put(configId, extId, price);
            }

            // 处理关联选项
            processRelationOptions(table, configId, option.getRelationOptionList());
        }
    }

    private static void processDraftOptions(Table<Long, String, BigDecimal> table, Long configId,
                                       List<DraftServePrice.ServeConfig.Option> options) {
        if (CollectionUtils.isEmpty(options)) {
            return;
        }

        for (DraftServePrice.ServeConfig.Option option : options) {
            String extId = option.getExternalId();
            BigDecimal price = option.getMasterPrice();
            if (extId != null && price != null) {
                table.put(configId, extId, price);
            }

            // 处理关联选项
            processDraftRelationOptions(table, configId, option.getRelationOptionList());
        }
    }

    private static void processRelationOptions(Table<Long, String, BigDecimal> table, Long configId,
                                               List<ServePrice.ServeConfig.Option> relationOptions) {
        if (CollectionUtils.isEmpty(relationOptions)) {
            return;
        }

        for (ServePrice.ServeConfig.Option option : relationOptions) {
            String extId = option.getExternalId();
            BigDecimal price = option.getMasterPrice();
            if (extId != null && price != null) {
                table.put(configId, extId, price);
            }
        }
    }

    private static void processDraftRelationOptions(Table<Long, String, BigDecimal> table, Long configId,
                                               List<DraftServePrice.ServeConfig.Option> relationOptions) {
        if (CollectionUtils.isEmpty(relationOptions)) {
            return;
        }

        for (DraftServePrice.ServeConfig.Option option : relationOptions) {
            String extId = option.getExternalId();
            BigDecimal price = option.getMasterPrice();
            if (extId != null && price != null) {
                table.put(configId, extId, price);
            }
        }
    }

//    private static Table<Long, String, BigDecimal> getLongStringBigDecimalTable(List<ServePrice.ServeConfig> sourceList) {
//        Table<Long, String, BigDecimal> table = HashBasedTable.create();
//
//        for (ServePrice.ServeConfig sc : sourceList) {
//            Long configId = sc.getServeConfigId();
//            if (configId == null) {
//                continue;
//            }
//
//            // 1.1 先把 sc.getOptions() 里的所有 Option 放到 table
//            if (sc.getOptions() != null) {
//                for (ServePrice.ServeConfig.Option opt1 : sc.getOptions()) {
//                    String extId = opt1.getExternalId();
//                    BigDecimal price = opt1.getMasterPrice();
//                    if (extId != null && price != null) {
//                        table.put(configId, extId, price);
//                    }
//
//                    List<ServePrice.ServeConfig.Option> relationOptionList = opt1.getRelationOptionList();
//                    if (CollectionUtils.isNotEmpty(relationOptionList)) {
//                        for (ServePrice.ServeConfig.Option option : relationOptionList) {
//                            String relationExtId = option.getExternalId();
//                            BigDecimal relationPrice = option.getMasterPrice();
//                            if (relationExtId != null && relationPrice != null) {
//                                table.put(configId, relationExtId, relationPrice);
//                            }
//                        }
//                    }
//                }
//            }
//
//            // 1.2 再把 sc.getAttaches() 里的所有 Option 放到同一个 table
//            if (sc.getAttaches() != null) {
//                for (ServePrice.ServeConfig.Option att1 : sc.getAttaches()) {
//                    String extId = att1.getExternalId();
//                    BigDecimal price = att1.getMasterPrice();
//                    if (extId != null && price != null) {
//                        table.put(configId, extId, price);
//                    }
//
//                    List<ServePrice.ServeConfig.Option> relationOptionList = att1.getRelationOptionList();
//                    if (CollectionUtils.isNotEmpty(relationOptionList)) {
//                        for (ServePrice.ServeConfig.Option option : relationOptionList) {
//                            String relationExtId = option.getExternalId();
//                            BigDecimal relationPrice = option.getMasterPrice();
//                            if (relationExtId != null && relationPrice != null) {
//                                table.put(configId, relationExtId, relationPrice);
//                            }
//                        }
//                    }
//                }
//            }
//        }
//        return table;
//    }


//    public static void mergeWithGuavaTable(List<ServePrice.ServeConfig> sourceList,
//                                           List<ServePrice2C4MasterDetail.ServeConfig> targetList) {
//        // 1. 构造 Table：行 key = serveConfigId, 列 key = externalId, 值 = masterPrice
//        Table<Long, String, BigDecimal> table = getLongStringBigDecimalTable(sourceList);
//
//        // 2. 遍历 targetList，如果 table 中存在对应的 (serveConfigId, externalId)，就赋值
//        for (ServePrice2C4MasterDetail.ServeConfig sc : targetList) {
//            Long configId = sc.getServeConfigId();
//            if (configId == null) {
//                continue;
//            }
//
//            // 2.1 给 sc.getOptions() 中对应的 Option 赋值
//            if (sc.getOptions() != null) {
//                for (ServePrice2C4MasterDetail.ServeConfig.Option opt2 : sc.getOptions()) {
//                    String extId = opt2.getExternalId();
//                    if (extId == null) {
//                        continue;
//                    }
//                    // 如果 table 中有值，就拿出来赋给 sc2.Option.masterPrice
//                    if (table.contains(configId, extId)) {
//                        BigDecimal priceFromSc1 = table.get(configId, extId);
//                        opt2.setMasterPrice(priceFromSc1);
//                    }
//
//                    List<ServePrice.ServeConfig.Option> relationOptionList = opt2.getRelationOptionList();
//                    if (CollectionUtils.isNotEmpty(relationOptionList)) {
//                        for (ServePrice.ServeConfig.Option option : relationOptionList) {
//                            String externalId = option.getExternalId();
//                            if (table.contains(configId, externalId)) {
//                                BigDecimal price = table.get(configId, externalId);
//                                option.setMasterPrice(price);
//                            }
//                        }
//                    }
//                }
//            }
//
//            // 2.2 给 sc.getAttaches() 中对应的 Option 赋值
//            if (sc.getAttaches() != null) {
//                for (ServePrice2C4MasterDetail.ServeConfig.Option att2 : sc.getAttaches()) {
//                    String extId = att2.getExternalId();
//                    if (extId == null) {
//                        continue;
//                    }
//                    if (table.contains(configId, extId)) {
//                        BigDecimal priceFromSc1 = table.get(configId, extId);
//                        att2.setMasterPrice(priceFromSc1);
//                    }
//
//                    List<ServePrice.ServeConfig.Option> relationOptionList = att2.getRelationOptionList();
//                    if (CollectionUtils.isNotEmpty(relationOptionList)) {
//                        for (ServePrice.ServeConfig.Option option : relationOptionList) {
//                            String externalId = option.getExternalId();
//                            if (table.contains(configId, externalId)) {
//                                BigDecimal price = table.get(configId, externalId);
//                                option.setMasterPrice(price);
//                            }
//                        }
//                    }
//                }
//            }
//        }
//    }
}
