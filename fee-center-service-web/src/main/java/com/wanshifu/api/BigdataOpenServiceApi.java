package com.wanshifu.api;

import com.wanshifu.api.resp.GetSkuTemplateInfoBySceneCodeResp;
import com.wanshifu.api.rqt.GetSkuTemplateInfoBySceneCodeRqt;
import com.wanshifu.infrastructure.utils.BigDataOpenApiDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.ext.timeout.FeignTimeout;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/14 11:40
 */
@FeignClient(value = "bigdata-open-service",
        url = "${wanshifu.bigdata-open-service.url}",
        configuration = {DefaultEncoder.class, BigDataOpenApiDecoder.class, BigDataOpenApiDecoder.ApiErrorDecoder.class}
)
public interface BigdataOpenServiceApi {

    /**
     * 大数据获取自定义SKU模板信息
     * @param rqt
     * @return
     */
    @PostMapping("/dataApi/getData/getSkuTemplateInfoBySceneCode")
    @FeignTimeout(connectTimeoutMillis = 5000, readTimeoutMillis = 5000)
    List<GetSkuTemplateInfoBySceneCodeResp> getSkuTemplateInfoBySceneCode(@RequestBody GetSkuTemplateInfoBySceneCodeRqt rqt);


}
