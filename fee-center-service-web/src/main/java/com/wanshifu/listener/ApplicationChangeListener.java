package com.wanshifu.listener;

import com.wanshifu.event.DocumentChangeEvent;
import com.wanshifu.fee.center.domain.constant.GlobalRedisKeyConstant;
import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.DynamicFeeRule;
import com.wanshifu.fee.center.domain.enums.AdjustPriceUnitEnum;
import com.wanshifu.fee.center.domain.enums.DynamicFeeRuleStatusEnum;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class ApplicationChangeListener {

    @Resource
    private RedisHelper redisHelper;
    @Resource
    private MongoTemplate mongoTemplate;

    // 在Service层的增删改操作后触发
    @EventListener
    public void handleDocumentChange(DocumentChangeEvent event) {
        if (!(event.getDocument() instanceof DynamicFeeRule)) {
            return;
        }

        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(DynamicFeeRuleStatusEnum.PASS.code)
                .and(DynamicFeeRule.Fields.adjustPriceUnit).is(AdjustPriceUnitEnum.SERVICE.getCode());

        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group(DynamicFeeRule.Fields.sceneCode),
                Aggregation.project(DynamicFeeRule.Fields.sceneCode).and("_id").as(DynamicFeeRule.Fields.sceneCode)
        );

        AggregationResults<Map> results = mongoTemplate.aggregate(aggregation, "dynamicFeeRule", Map.class);

        List<String> sceneCodes = results.getMappedResults().stream()
                .map(result -> (String) result.get(DynamicFeeRule.Fields.sceneCode))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(sceneCodes)) {
            return;
        }

        redisHelper.set(
                GlobalRedisKeyConstant.SERVICE_DYNAMIC_FEE_RULE_SCENE_CODE,
                StringUtils.join(sceneCodes, ","),
                0);

    }
}
