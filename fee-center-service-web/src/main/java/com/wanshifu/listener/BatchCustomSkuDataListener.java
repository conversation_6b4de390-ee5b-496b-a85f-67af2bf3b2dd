package com.wanshifu.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.wanshifu.domain.dto.table.BatchCustomSkuTemplateExcelModel;
import com.wanshifu.fee.center.domain.enums.CustomSkuValueTypeEnum;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.utils.StrKit;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/17 20:18
 */
public class BatchCustomSkuDataListener  extends AnalysisEventListener<BatchCustomSkuTemplateExcelModel> {

    @Getter
    private final List<BatchCustomSkuTemplateExcelModel> dataList = new ArrayList<>();
    private final Set<String> fileCombinationSet = new HashSet<>();
    private static final int MAX_ROWS = 200;

    private final String customSkuValueType;

    public BatchCustomSkuDataListener(String customSkuValueType) {
        this.customSkuValueType = customSkuValueType;
    }
    @Override
    public void invoke(BatchCustomSkuTemplateExcelModel row, AnalysisContext context) {
        if (context.readRowHolder().getRowIndex() <= 1) {
            // 跳过第一行的说明和第二行的表头
            return;
        }
        if (dataList.size() >= MAX_ROWS) {
            throw new BusException("一次最多只能上传200行数据");
        }
        // 校验 skuName
        String skuName = StrKit.trimSafely(row.getSkuName());
        if (StringUtils.isBlank(skuName)) {
            throw new BusException("第" + (context.readRowHolder().getRowIndex() + 1) + "行: 自定义sku名称必填");
        }
        if (skuName.length() > 200) {
            throw new BusException("第" + (context.readRowHolder().getRowIndex() + 1) + "行: 自定义sku名称不超过200个字");
        }
        // 校验值范围
        String valueMin = StrKit.trimSafely(row.getAttributeValueMin());
        String valueMax = StrKit.trimSafely(row.getAttributeValueMax());
        String valueFixed = StrKit.trimSafely(row.getAttributeValueFixed());

        if (CustomSkuValueTypeEnum.FIXED.getCode().equals(customSkuValueType)) {
            if (StringUtils.isNotBlank(valueMin) || StringUtils.isNotBlank(valueMax)) {
                throw new BusException("value值类型为「固定值」，第" + (context.readRowHolder().getRowIndex() + 1) + "行: 「最小值(数值)」和「最大值(数值)」不填");
            }
            if (StringUtils.isBlank(valueFixed)) {
                throw new BusException("value值类型为「固定值」，第" + (context.readRowHolder().getRowIndex() + 1) + "行: 「具体值(文本)」必填");
            }
        } else if (CustomSkuValueTypeEnum.RANGE.getCode().equals(customSkuValueType)) {
            if (StringUtils.isNotBlank(valueFixed)) {
                throw new BusException("value值类型为「范围值」，第" + (context.readRowHolder().getRowIndex() + 1) + "行: 「具体值(文本)」不填");
            }
            if (StringUtils.isBlank(valueMin) || StringUtils.isBlank(valueMax)) {
                throw new BusException("value值类型为「范围值」，第" + (context.readRowHolder().getRowIndex() + 1) + "行: 「最小值(数值)」和「最大值(数值)」必填");
            }
            try {
                double d1 = Double.parseDouble(valueMin);
                double d2 = Double.parseDouble(valueMax);
                if (d2 <= d1) {
                    throw new BusException("第" + (context.readRowHolder().getRowIndex() + 1) + "行: 最大值必须大于最小值");
                }
            } catch (NumberFormatException e) {
                throw new BusException("第" + (context.readRowHolder().getRowIndex() + 1) + "行: 最小值和最大值必须为数字");
            }
        }

        // 检查文件内部重复
        String key = generateCombinationKey(row);
        if (!fileCombinationSet.add(key)) {
            throw new BusException("文件内部存在重复的商家ID+自定义sku值组合: " + key);
        }
        dataList.add(row);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }


    private String generateCombinationKey(BatchCustomSkuTemplateExcelModel data) {
        return StrUtil.format("商家ID={}|最小值(数值)={}|最大值(数值)={}|具体值={}",
                StringUtils.isBlank(data.getCustomSkuUserId()) ? "NULL" : data.getCustomSkuUserId().trim(),
                StringUtils.isBlank(data.getAttributeValueMin()) ? "NULL" : data.getAttributeValueMin().trim(),
                StringUtils.isBlank(data.getAttributeValueMax()) ? "NULL" : data.getAttributeValueMax().trim(),
                StringUtils.isBlank(data.getAttributeValueFixed()) ? "NULL" : data.getAttributeValueFixed().trim());
    }
}
