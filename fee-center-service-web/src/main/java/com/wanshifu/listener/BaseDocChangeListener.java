package com.wanshifu.listener;

import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.infrastructure.utils.CommonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.mapping.event.AbstractMongoEventListener;
import org.springframework.data.mongodb.core.mapping.event.BeforeConvertEvent;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Date;

@Component
public class BaseDocChangeListener<T extends BaseDocument> extends AbstractMongoEventListener<T> {

    @Override
    public void onBeforeConvert(BeforeConvertEvent<T> event) {
        T entity = event.getSource();
        if (entity != null) {
            Instant nowUtc = Instant.now();
            Date now = Date.from(nowUtc);
            entity.setModifyTime(now);
            // 有些地方会从接口中获取当前登录用户，这种情况通常是无法从请求头中获取当前用户信息
            if (StringUtils.isNotBlank(CommonUtils.getCurrentLoginName())) {
                entity.setUpdateBy(CommonUtils.getCurrentLoginName());
            }
            String id = entity.getId();
            // 新增时，设置创建时间和创建人
            if (id == null) {
                entity.setCreateTime(now);
                if (StringUtils.isNotBlank(CommonUtils.getCurrentLoginName())) {
                    entity.setCreateBy(CommonUtils.getCurrentLoginName());
                }
            }
        }
    }
}
