package com.wanshifu.domain.dto.table;


import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 总包参考价表格
 */
@Data
public class EnterpriseOrderOfferGuidePriceTable {


    @ExcelProperty(value = "服务ID")
    private String serviceId;

    @ExcelProperty(value = "服务名称")
    private String serviceName;

    @ExcelProperty(value = "sku_no")
    private String skuNo;

    @ExcelProperty(value = "定价ID")
    private String templateId;

    @ExcelProperty(value = "费用展示名称")
    private String attributeDisplayName;

    @ExcelProperty(value = "省")
    private String province;

    @ExcelProperty(value = "市")
    private String city;

    @ExcelProperty(value = "区")
    private String district;

    @ExcelProperty(value = "街道")
    private String street;

    @ExcelProperty(value = "单价")
    private String masterInputPrice;

    @ExcelProperty(value = "单价max")
    private String masterInputPriceMax;

    @ExcelProperty(value = "起步价")
    private String basePrice;

    @ExcelProperty(value = "业务标识")
    private String bizTag;

    @ExcelProperty(value = "错误")
    private String error;

}
