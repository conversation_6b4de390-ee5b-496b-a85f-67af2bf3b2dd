package com.wanshifu.domain.dto;

import com.wanshifu.fee.center.domain.document.SceneInfo;
import com.wanshifu.fee.center.domain.request.ApplyOrderCalculateReq;
import com.wanshifu.fee.center.domain.response.ApplyOrderCalculateResp;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ApplyOrderCalculateDTO extends ApplyOrderCalculateReq {
    private List<SceneInfo> sceneInfoList;
    private ApplyOrderCalculateResp applyOrderCalculateResp;
    private SceneInfo currentSceneInfo;
    private BigDecimal goodsNumber;

    public ApplyOrderCalculateDTO(ApplyOrderCalculateReq applyOrderCalculateReq){
        this.setAddressInfo(applyOrderCalculateReq.getAddressInfo());
        this.setOrderBase(applyOrderCalculateReq.getOrderBase());
        this.setFrom(applyOrderCalculateReq.getFrom());
        this.setSceneCode(applyOrderCalculateReq.getSceneCode());
        this.setExtraServices(applyOrderCalculateReq.getExtraServices());
        this.setPlatformInfo(applyOrderCalculateReq.getPlatformInfo());
        this.setServiceInfos(applyOrderCalculateReq.getServiceInfos());
        this.setTo(applyOrderCalculateReq.getTo());
        this.setBizRule(applyOrderCalculateReq.getBizRule());
        this.setDynamicCalculateTime(applyOrderCalculateReq.getDynamicCalculateTime());
        this.setMergeByPriority(applyOrderCalculateReq.getMergeByPriority());
        this.setIsMappingPricing(applyOrderCalculateReq.getIsMappingPricing());
        this.setFromSceneCode(applyOrderCalculateReq.getFromSceneCode());
        this.setSubSceneCode(applyOrderCalculateReq.getSubSceneCode());
        this.setDynamicIndicatorParamList(applyOrderCalculateReq.getDynamicIndicatorParamList());
    }
}
