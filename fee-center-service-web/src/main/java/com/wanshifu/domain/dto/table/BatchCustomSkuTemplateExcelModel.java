package com.wanshifu.domain.dto.table;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/17 20:15
 */
@Data
public class BatchCustomSkuTemplateExcelModel {

    @ExcelProperty("商家ID")
    private String customSkuUserId;

    @ExcelProperty("定价ID")
    private String templateId;

    @ExcelProperty("自定sku名称")
    private String skuName;

    @ExcelProperty("最小值(数值)")
    private String attributeValueMin;

    @ExcelProperty("最大值(数值)")
    private String attributeValueMax;

    @ExcelProperty("具体值(文本)")
    private String attributeValueFixed;

}
