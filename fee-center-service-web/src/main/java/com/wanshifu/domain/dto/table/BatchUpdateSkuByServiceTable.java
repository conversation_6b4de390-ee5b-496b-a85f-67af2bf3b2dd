package com.wanshifu.domain.dto.table;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

/**
 * <AUTHOR>
 * @description 批量按服务自定义sku表格
 * @date 2025/7/17 16:15
 */
@Data
@FieldNameConstants
public class BatchUpdateSkuByServiceTable {

    @ExcelProperty("商家ID")
    private String customSkuUserId;

    @ExcelProperty(value = "服务ID")
    private String serviceId;

    @ExcelProperty(value = "服务名称")
    private String serviceName;

    @ExcelProperty(value = "sku_no")
    private String skuNo;

    @ExcelProperty(value = "定价ID")
    private String templateId;

    @ExcelProperty(value = "费用展示名称")
    private String attributeDisplayName;

    @ExcelProperty("自定sku名称")
    private String skuName;

    @ExcelProperty("最小值(数值)")
    private String attributeValueMin;

    @ExcelProperty("最大值(数值)")
    private String attributeValueMax;

    @ExcelProperty("具体值(文本)")
    private String attributeValueFixed;

    @ExcelProperty(value = "白名单属性商家ID")
    private String whiteListAttributesAccountIds;

    @ExcelProperty(value = "自定义商家ID")
    private String customizeSkuUserId;

    @ExcelProperty(value = "错误")
    private String error;
}
