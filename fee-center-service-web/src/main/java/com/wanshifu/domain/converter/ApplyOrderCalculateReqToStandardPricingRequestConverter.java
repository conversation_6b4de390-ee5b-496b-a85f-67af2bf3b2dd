package com.wanshifu.domain.converter;

import com.wanshifu.api.utils.MapUtils;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.dto.*;
import com.wanshifu.fee.center.domain.enums.AccountTypeEnum;
import com.wanshifu.fee.center.domain.request.ApplyOrderCalculateReq;
import com.wanshifu.fee.center.domain.request.calculate.StandardPricingRequest;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ApplyOrderCalculateReqToStandardPricingRequestConverter {

    public static StandardPricingRequest convert(ApplyOrderCalculateReq request) {
        if (request == null) {
            throw new IllegalArgumentException("request object cannot be null");
        }

        StandardPricingRequest target = new StandardPricingRequest();
        target.setSceneCodeList(request.getSceneCode());
        target.setNeedIntegrityValidation(request.getNeedIntegrityValidation());
        target.setMergeByPriority(request.getMergeByPriority());
        target.setIsMappingPricing(request.getIsMappingPricing());
        target.setDynamicCalculateTime(request.getDynamicCalculateTime());
        target.setTargetSceneCode(request.getFromSceneCode());
        target.setOrderBase(request.getOrderBase());
        target.setDynamicIndicatorParamList(request.getDynamicIndicatorParamList());

        // handle account info
        AccountInfo accountFrom = request.getFrom();
        AccountInfo accountTo = request.getTo();
        if (accountFrom != null) {
            String accountType = accountFrom.getAccountType();
            if (StringUtils.isBlank(accountType) || AccountTypeEnum.USER.code.equals(accountType)) {
                BizInfo bizInfo = new BizInfo();
                bizInfo.setUserId(accountFrom.getAccountId().toString());
                bizInfo.setBizId(bizInfo.getUserId());
                target.setBizInfo(bizInfo);
            }
        } else if (accountTo != null) {
            String accountType = accountTo.getAccountType();
            if (StringUtils.isBlank(accountType) || AccountTypeEnum.MASTER.code.equals(accountType)) {
                BizInfo bizInfo = new BizInfo();
                bizInfo.setMasterId(accountTo.getAccountId().toString());
                bizInfo.setBizId(bizInfo.getMasterId());
                target.setBizInfo(bizInfo);
            }
        }

        // handle address info
        AddressInfo addressInfo = request.getAddressInfo();
        if (addressInfo != null) {
            CoreAddressInfo coreAddressInfo = new CoreAddressInfo();
            coreAddressInfo.setDivisionId(addressInfo.getDivisionId());
            coreAddressInfo.setDivisionName(addressInfo.getDivisionName());
            coreAddressInfo.setNeedSearchParent(addressInfo.isNeedSearchParent());
            coreAddressInfo.setDivisionType(addressInfo.getDivisionType());
            target.setCoreAddressInfo(coreAddressInfo);
        }

        // handle biz info
        Map<String, String> bizRule = request.getBizRule();
        if (MapUtils.isNotEmpty(bizRule)) {
            BizInfo bizInfo = target.getBizInfo();
            if (bizInfo == null) {
                bizInfo = new BizInfo();
            }
            bizInfo.setBizId(bizRule.get(CommonBizRule.Fields.bizId));
            bizInfo.setBizTag(bizRule.get(CommonBizRule.Fields.bizTag));
            bizInfo.setMasterId(bizRule.get(CommonBizRule.Fields.masterId));
            bizInfo.setUserId(bizRule.get(CommonBizRule.Fields.userId));
            target.setBizInfo(bizInfo);
        }

        // handle service info
        target.setServiceDtoList(convertServiceInfosToServiceDTOs(request.getServiceInfos()));

        return target;
    }

    private static List<ServiceDTO> convertServiceInfosToServiceDTOs(List<CalculateServiceInfo> serviceInfos) {
        if (CollectionUtils.isEmpty(serviceInfos)) {
            return Collections.emptyList();
        }

        return serviceInfos.stream()
                .map(ApplyOrderCalculateReqToStandardPricingRequestConverter::convertServiceInfoToServiceDTO)
                .collect(Collectors.toList());
    }

    private static ServiceDTO convertServiceInfoToServiceDTO(CalculateServiceInfo serviceInfo) {
        if (serviceInfo == null) {
            return null;
        }
        ServiceDTO serviceDto = new ServiceDTO();
        serviceDto.setServiceId(serviceInfo.getServiceId());

        List<ServiceAttribute> rootAttributeDetailList = serviceInfo.getRootAttributeDetailList();
        if (CollectionUtils.isEmpty(rootAttributeDetailList)) {
            return serviceDto;
        }
        List<AttributeDTO> attributeDtoList = getAttributeDtoList(rootAttributeDetailList);
        serviceDto.setAttributeDtoList(attributeDtoList);
        return serviceDto;
    }

    public static void toAttributeDTO(ServiceAttribute attribute, AttributeDTO attributeDTO) {
        if (attribute == null || attributeDTO == null) {
            return;
        }
        attributeDTO.setAttributeId(attribute.getAttributeId());
        attributeDTO.setAttributeKey(attribute.getAttributeKey());
        attributeDTO.setAttributeName(attribute.getAttributeName());
        attributeDTO.setAttributeTag(attribute.getAttributeTag());
        attributeDTO.setAttributePathNo(attribute.getAttributePathNo());
        List<ServiceAttributeValue> serviceAttributeValues = attribute.getChildList();
        if (CollectionUtils.isNotEmpty(serviceAttributeValues)) {
            List<AttributeValueDTO> valueDtoList = serviceAttributeValues.stream().map(attributeValue -> {
                AttributeValueDTO value = new AttributeValueDTO();
                value.setAttributeValueId(attributeValue.getAttributeValueId());
                value.setAttributeValueKey(attributeValue.getAttributeValueKey());
                value.setAttributeValueName(attributeValue.getAttributeValueName());
                value.setAttributePathNo(attributeValue.getAttributePathNo());
                value.setValue(attributeValue.getValue() == null ? "" : attributeValue.getValue().trim());
                List<ServiceAttribute> childAttributeList = attributeValue.getChildList();
                if (CollectionUtils.isNotEmpty(childAttributeList)) {
                    List<AttributeDTO> attributeDtoList = getAttributeDtoList(childAttributeList);
                    value.setAttributeDtoList(attributeDtoList);
                }
                return value;
            }).collect(Collectors.toList());
            attributeDTO.setAttributeValueDtoList(valueDtoList);
        }
    }

    private static List<AttributeDTO> getAttributeDtoList(List<ServiceAttribute> childAttributeList) {
        List<AttributeDTO> childAttributeDtoList = new ArrayList<>();
        for (ServiceAttribute serviceAttribute : childAttributeList) {
            AttributeDTO childAttributeDto = new AttributeDTO();
            toAttributeDTO(serviceAttribute, childAttributeDto);
            childAttributeDtoList.add(childAttributeDto);
        }
        return childAttributeDtoList;
    }

}
