package com.wanshifu.domain.request.samedayblacklistaddress;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

@Data
@ApiModel("修改地址黑名单request body")
public class ModifyRequest {

    @ApiModelProperty(value = "配置id", required = true)
    @NotBlank(message = "configId不能为空")
    private String configId;

    @ApiModelProperty(value = "关键词, 多个用\n分割", required = true)
    @NotBlank(message = "关键词不能为空")
    @Length(min = 1, max = 500, message = "关键词长度在1-500之间")
    private String keywords;

}
