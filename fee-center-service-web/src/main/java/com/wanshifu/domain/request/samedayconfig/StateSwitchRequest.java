package com.wanshifu.domain.request.samedayconfig;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

@Data
@ApiModel("状态开关request body")
public class StateSwitchRequest {

    @ApiModelProperty(value = "配置Id", required = true)
    @NotBlank(message = "sameDayPriceConfigId不能为空")
    private String sameDayPriceConfigId;

    @ApiModelProperty(value = "状态", required = true)
    @NotBlank(message = "状态不能为空")
    @ValueIn("active,inactive")
    private String state;
}
