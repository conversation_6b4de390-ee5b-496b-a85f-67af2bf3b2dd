package com.wanshifu.domain.request.samedayblacklistaddress;

import com.wanshifu.fee.center.domain.request.common.Modify;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("保存当日价黑名单地区配置request body")
public class AddRequest {

    @ApiModelProperty(value = "配置id")
    @NotBlank(message = "configId不能为空", groups = Modify.class)
    private String configId;

    @ApiModelProperty(value = "区县id", required = true)
    @NotBlank(message = "区县id不能为空")
    private String districtId;

    @ApiModelProperty(value = "区县名称", required = true)
    @NotBlank(message = "区县名称不能为空")
    private String districtName;

    @ApiModelProperty(value = "关键词, 多个用\n分割", required = true)
    @Length(min = 1, max = 500, message = "关键词长度在1-500之间")
    @NotBlank(message = "关键词不能为空")
    private String keywords;

    @ApiModelProperty(value = "商品一级类目信息", required = true)
    @NotEmpty(message = "商品一级类目信息不能为空")
    private List<GoodsCategoryInfo> goodsCategoryInfos;

    @Data
    public static class GoodsCategoryInfo {
        @ApiModelProperty(value = "一级类目id", required = true)
        @NotNull(message = "一级类目id不能为空")
        private Long level1GoodsCategoryId;

        @ApiModelProperty(value = "一级类目名称", required = true)
        @NotBlank(message = "一级类目名称不能为空")
        private String level1GoodsCategoryName;
    }

}
