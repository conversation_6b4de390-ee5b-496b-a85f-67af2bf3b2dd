package com.wanshifu.domain.request.samedayconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("获取一级商品类目（排除已添加的）Response body")
public class Level1GoodsCategoryResponse {

    @ApiModelProperty(value = "一级商品类目id", required = true)
    private Long level1GoodsCategoryId;

    @ApiModelProperty(value = "一级商品类目名称", required = true)
    private String level1GoodsCategoryName;
}
