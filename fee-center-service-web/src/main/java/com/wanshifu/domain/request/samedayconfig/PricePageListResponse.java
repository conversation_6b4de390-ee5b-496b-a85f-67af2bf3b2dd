package com.wanshifu.domain.request.samedayconfig;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel("分页列表Response body")
@Data
public class PricePageListResponse {

    @ApiModelProperty(value = "配置id")
    private String sameDayPriceConfigId;

    @ApiModelProperty(value = "一级类目id")
    private Long level1GoodsCategoryId;

    @ApiModelProperty(value = "一级类目名称")
    private String level1GoodsCategoryName;

    @ApiModelProperty(value = "服务类型id")
    private Long serviceTypeId;

    @ApiModelProperty(value = "价格")
    private Integer price;

    @ApiModelProperty(value = "状态。active:开通，inactive:关闭")
    private String activationState;

    @ApiModelProperty(value = "已选省份数量")
    private Integer provinceCount;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "最后修改人")
    private String updateBy;
}
