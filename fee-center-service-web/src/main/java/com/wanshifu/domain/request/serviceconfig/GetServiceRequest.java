package com.wanshifu.domain.request.serviceconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

@Data
@ApiModel("获取服务request body")
public class GetServiceRequest {

    @NotBlank(message = "场景编码不能为空")
    @ApiModelProperty(value = "场景编码", required = true)
    private String sceneCode;

    @NotNull(message = "商品类目id不能为空")
    @ApiModelProperty(value = "商品类目id", required = true)
    private Long goodsCategoryId;

    @NotNull(message = "服务类型id不能为空")
    @ApiModelProperty(value = "服务类型id", required = true)
    private Long serviceTypeId;

}
