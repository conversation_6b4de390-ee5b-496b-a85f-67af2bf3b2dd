package com.wanshifu.domain.request;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.util.Set;

@Data
public class BatchTaskGenerateReq {
    @NotBlank
    private String sceneCode;
    private Set<String> serviceIds;

    private Boolean showWhitelistAttributesAndStatus = false;

    /**
     * batchUpdateSkuByService: 按服务自定义SKU
     */
    private String taskType;
}
