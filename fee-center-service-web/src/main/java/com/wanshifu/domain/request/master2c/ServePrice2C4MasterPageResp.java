package com.wanshifu.domain.request.master2c;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.wanshifu.fee.center.domain.request.BasePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("C端师傅服务价格分页列表")
public class ServePrice2C4MasterPageResp extends BasePageReq {

    @ApiModelProperty(value = "服务价格id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long priceId;

    @ApiModelProperty(value = "服务id")
    private Long serveId;

    @ApiModelProperty(value = "服务名称")
    private String serveName;

    @ApiModelProperty(value = "配置名称")
    private String configName;

    @ApiModelProperty(value = "配置权重")
    private Integer priority;

    @ApiModelProperty(value = "城市名称")
    private String cityNames;

    @ApiModelProperty(value = "师傅侧基础价格")
    private BigDecimal masterBasePrice;

    @ApiModelProperty(value = "影响价格参数的数量")
    private Integer priceParamCount;

    @ApiModelProperty(value = "生效时间")
    private Date startTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "状态")
    private String status;

}
