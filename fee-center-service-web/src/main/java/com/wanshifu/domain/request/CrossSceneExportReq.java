package com.wanshifu.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

@ApiModel("跨场景导出请求")
@Data
public class CrossSceneExportReq {

    @ApiModelProperty(value = "源场景", required = true)
    @NotBlank(message = "源场景不能为空")
    private String fromSceneCode;

    @ApiModelProperty(value = "价格维度", required = true)
    @NotBlank(message = "价格维度不能为空")
    private String divisionType;

    @ApiModelProperty(value = "服务ID", required = true)
    @NotEmpty(message = "服务ID不能为空")
    private List<String> serviceIds;

    @ApiModelProperty(value = "目标场景", required = true)
    @NotBlank(message = "目标场景不能为空")
    private String toSceneCode;

    @ApiModelProperty(value = "操作人", required = true)
    @NotBlank(message = "操作人不能为空")
    private String operator;

}
