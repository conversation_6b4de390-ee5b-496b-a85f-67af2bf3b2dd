package com.wanshifu.domain.request.rule;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

@Data
@ApiModel("更新活动价刷新request body")
public class UpdateActivityPriceRefreshRequest {

    @NotBlank(message = "招募/活动id不能为空")
    @ApiModelProperty("招募/活动id")
    private String bizTag;

    @NotBlank(message = "服务id不能为空")
    @ApiModelProperty("服务id")
    private String serviceId;

    @NotBlank(message = "skuNo不能为空")
    @ApiModelProperty("skuNo")
    private String skuNo;

    @ApiModelProperty("原价")
    private String originalPrice;

    @NotBlank(message = "新价不能为空")
    @ApiModelProperty("新价")
    private String newPrice;
}
