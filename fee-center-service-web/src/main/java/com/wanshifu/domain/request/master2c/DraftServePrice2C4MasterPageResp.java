package com.wanshifu.domain.request.master2c;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/3 15:10
 */
@Data
@ApiModel("C端师傅服务价格草稿分页列表")
public class DraftServePrice2C4MasterPageResp {

    @ApiModelProperty(value = "服务价格草稿id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long draftId;

    @ApiModelProperty(value = "配置id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long priceId;

    @ApiModelProperty(value = "服务id")
    private Long serveId;

    @ApiModelProperty(value = "服务名称")
    private String serveName;

    @ApiModelProperty(value = "配置名称")
    private String configName;

    @ApiModelProperty(value = "城市名称")
    private String cityNames;

    @ApiModelProperty(value = "师傅侧基础价格")
    private BigDecimal masterBasePrice;

    @ApiModelProperty(value = "提交人")
    private String createBy;

    @ApiModelProperty(value = "生效时间")
    private Date startTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "状态")
    private String status;
}
