package com.wanshifu.domain.request.master2c;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("服务价格")
public class ServePrice2C4MasterModifyReq extends ServePrice2C4MasterAddReq {

    @ApiModelProperty(value = "服务价格id", required = true)
    @NotNull(message = "服务价格id不能为空")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long priceId;
}
