package com.wanshifu.domain.request.pricinglog;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jodd.util.StringUtil;
import lombok.Data;

@Data
@ApiModel(description = "标准计价日志列表request body")
public class GetStandardPricingLogListRequest {

    @ApiModelProperty(value = "标准计价任务id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long tid;

    @ApiModelProperty(value = "订单id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long orderId;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "全局订单id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long globalOrderTraceId;

    public void validate() {
        if (tid == null && orderId == null && StringUtil.isBlank(orderNo) && globalOrderTraceId == null) {
            throw new IllegalArgumentException("tid, orderId, orderNo, globalOrderTraceId不能同时为空");
        }
    }
}
