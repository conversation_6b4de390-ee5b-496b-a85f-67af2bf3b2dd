package com.wanshifu.domain.request.samedayconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("详情查询Response body")
public class PriceDetailResponse {

    @ApiModelProperty(value = "配置id", required = true)
    private String sameDayPriceConfigId;

    @ApiModelProperty(value = "一级类目id", required = true)
    private Long level1GoodsCategoryId;

    @ApiModelProperty(value = "一级类目名称", required = true)
    private String level1GoodsCategoryName;

    @ApiModelProperty(value = "已选省份数量", required = true)
    private Integer provinceCount;

    @ApiModelProperty(value = "区县配置", required = true)
    private List<SaveRequest.AddressConfig> addressConfigs;
}
