package com.wanshifu.domain.request;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.web.multipart.MultipartFile;

@Data
public class BatchTaskUploadReq {
    @NotBlank
    private String sceneCode;
    private String bizId;
    private String divisionType;
    // 直接整正式库 不入草稿，不需要审核
    private boolean directFeeRule = false;
    @NotBlank
    private String operator;
    private MultipartFile file;
}
