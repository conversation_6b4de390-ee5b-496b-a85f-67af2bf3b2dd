package com.wanshifu.domain.request.samedayconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

@Data
@ApiModel("修改价格request body")
public class ModifyPriceRequest {

    @ApiModelProperty(value = "id", required = true)
    @NotBlank(message = "sameDayPriceConfigId不能为空")
    private String sameDayPriceConfigId;

    @ApiModelProperty(value = "价格", required = true)
    @NotNull(message = "价格不能为空")
    @Range(min = 1, max = 9999, message = "价格范围1-9999")
    private Integer price;
}
