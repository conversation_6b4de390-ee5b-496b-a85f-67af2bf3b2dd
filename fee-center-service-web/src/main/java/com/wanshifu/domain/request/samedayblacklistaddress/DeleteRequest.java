package com.wanshifu.domain.request.samedayblacklistaddress;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

@Data
@ApiModel("删除配置request body")
public class DeleteRequest {

    @ApiModelProperty(value = "配置Id", required = true)
    @NotBlank(message = "configId不能为空")
    private String configId;
}
