package com.wanshifu.domain.request.master2c;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/3 11:33
 */
@Data
@ApiModel("C端师傅服务价格角标")
public class ServePrice2C4MasterCountResp {

    /**
     * 正式数据待审核数量
     */
    @ApiModelProperty(value = "正式数据待审核数量")
    private long pendingReviewCount = 0;

    /**
     * 草稿数据待审核数量
     */
    @ApiModelProperty(value = "草稿数据待审核数量")
    private long draftPendingReviewCount = 0;
}
