package com.wanshifu.domain.request.master2c;

import com.wanshifu.fee.center.domain.document.master2c.ServePrice;
import com.wanshifu.fee.center.domain.enums.MasterPrice2CActivationMethodEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("服务价格")
public class ServePrice2C4MasterAddReq {

    @NotNull(message = "服务id不能为空")
    @ApiModelProperty(value = "服务id", required = true)
    private Long serveId;

    @NotBlank(message = "服务名称不能为空")
    @ApiModelProperty(value = "服务名称", required = true)
    private String serveName;

    @ApiModelProperty(value = "服务配置名称")
    private String configName;

    @NotNull(message = "一级服务id不能为空")
    @ApiModelProperty(value = "一级服务id", required = true)
    private Long level1ServeId;

    @NotBlank(message = "一级服务名称不能为空")
    @ApiModelProperty(value = "一级服务名称", required = true)
    private String level1ServeName;

    @NotNull(message = "二级服务id不能为空")
    @ApiModelProperty(value = "二级服务id", required = true)
    private Long level2ServeId;

    @NotBlank(message = "二级服务名称不能为空")
    @ApiModelProperty(value = "二级服务名称", required = true)
    private String level2ServeName;

    @ApiModelProperty(value = "三级服务id")
    private Long level3ServeId;

    @ApiModelProperty(value = "三级服务名称")
    private String level3ServeName;

    @NotNull(message = "优先级不能为空")
    @ApiModelProperty(value = "优先级", required = true)
    private Integer priority;

    @DecimalMin(value = "0.01", message = "师傅侧基础价格不能小于0.01")
    @ApiModelProperty(value = "师傅侧基础价格", required = true)
    private BigDecimal masterBasePrice;

    private List<ServePrice2C4MasterAddReq.ServeConfig> serveConfigs;

    private List<Long> cityIds;

    private ServePrice2C4MasterAddReq.CityGroup cityGroup;

    /**
     * 生效方式
     *
     * @see MasterPrice2CActivationMethodEnum
     */
    @NotBlank(message = "生效方式不能为空")
    @ApiModelProperty(value = "生效方式", required = true)
    private String activationMethod;

    @NotNull(message = "开始生效时间不能为空")
    @ApiModelProperty(value = "开始生效时间", required = true)
    private Date startTime;

    @Data
    public static class CityGroup {

        @NotNull(message = "城市群组分类id不能为空")
        @ApiModelProperty(value = "城市群组分类id")
        private String classifyId;

        @NotNull(message = "城市群组id不能为空")
        @ApiModelProperty(value = "城市群组id")
        private Long cityGroupId;

        @NotEmpty(message = "城市群组城市id列表不能为空")
        @ApiModelProperty(value = "城市群组城市id列表")
        private List<Long> cityIds;
    }

    @Data
    public static class ServeConfig {

        @ApiModelProperty(value = "服务配置id")
        @NotNull(message = "服务配置id不能为空")
        private Long serveConfigId;

        @DecimalMin(value = "0.01", message = "师傅价格不能小于0.01")
        @ApiModelProperty(value = "师傅价格")
        private BigDecimal masterPrice;

        @ApiModelProperty(value = "关联参数id")
        private Long relationId;

        @ApiModelProperty(value = "选项")
        private List<ServePrice2C4MasterAddReq.ServeConfig.Option> options;

        @ApiModelProperty(value = "附加选项")
        private List<ServePrice2C4MasterAddReq.ServeConfig.Option> attaches;

        @Data
        public static class Option {

            @NotNull(message = "选项id（外部）不能为空")
            @ApiModelProperty(value = "选项id（外部）")
            private String externalId;

            @DecimalMin(value = "0.01", message = "师傅价格不能小于0.01")
            @ApiModelProperty(value = "师傅价格")
            private BigDecimal masterPrice;

            private List<ServePrice.ServeConfig.Option> relationOptionList;
        }
    }
}
