package com.wanshifu.domain.request.master2c;


import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/3 16:43
 */
@Data
@ApiModel("改价修改实体")
public class DraftServePrice2C4MasterModifyReq extends ServePrice2C4MasterAddReq{

    @ApiModelProperty(value = "改价草稿id", required = true)
    @NotNull(message = "改价草稿id不能为空")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long draftId;
}
