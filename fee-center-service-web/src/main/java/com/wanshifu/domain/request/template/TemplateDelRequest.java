package com.wanshifu.domain.request.template;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Set;

@Data
@ApiModel("删除模板request body")
public class TemplateDelRequest {

    @ApiModelProperty(value = "模板id集合", required = true)
    @NotEmpty(message = "模板Id不能为空")
    private Set<String> templateIds;
}
