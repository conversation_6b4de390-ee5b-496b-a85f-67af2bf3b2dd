package com.wanshifu.domain.request.samedayblacklistaddress;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

@ApiModel("分页列表Response body")
@Data
public class PageListResponse {

    @ApiModelProperty(value = "配置id")
    private String configId;

    @ApiModelProperty(value = "一级类目id")
    private Long level1GoodsCategoryId;

    @ApiModelProperty(value = "一级类目名称")
    private String level1GoodsCategoryName;

    @ApiModelProperty(value = "区县id", required = true)
    private String districtId;

    @ApiModelProperty(value = "区县名称", required = true)
    private String districtName;

    @ApiModelProperty(value = "关键词", required = true)
    private Set<String> keywords;

}
