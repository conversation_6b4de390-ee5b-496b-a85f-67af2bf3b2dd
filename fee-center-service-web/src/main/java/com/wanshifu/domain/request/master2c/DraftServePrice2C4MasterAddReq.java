package com.wanshifu.domain.request.master2c;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/3 16:15
 */
@Data
@ApiModel("改价新增实体")
public class DraftServePrice2C4MasterAddReq extends ServePrice2C4MasterAddReq{

    @NotNull(message = "配置id不能为空")
    @ApiModelProperty(value = "配置id", required = true)
    private Long priceId;


}
