package com.wanshifu.domain.request.samedayblacklistaddress;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

@Data
@ApiModel("详情查询Response body")
public class DetailResponse {

    @ApiModelProperty(value = "配置id", required = true)
    private String configId;

    @ApiModelProperty(value = "一级类目id", required = true)
    private Long level1GoodsCategoryId;

    @ApiModelProperty(value = "一级类目名称", required = true)
    private String level1GoodsCategoryName;

    @ApiModelProperty(value = "区县id", required = true)
    private String districtId;

    @ApiModelProperty(value = "区县名称", required = true)
    private String districtName;

    @ApiModelProperty(value = "关键词", required = true)
    private Set<String> keywords;
}
