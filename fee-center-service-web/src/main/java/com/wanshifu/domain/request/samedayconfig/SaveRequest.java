package com.wanshifu.domain.request.samedayconfig;

import com.wanshifu.fee.center.domain.document.SameDayPriceConfig;
import com.wanshifu.fee.center.domain.request.common.Create;
import com.wanshifu.fee.center.domain.request.common.Modify;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("保存当日价配置request body")
public class SaveRequest {

    @ApiModelProperty(value = "配置id", required = true)
    @NotBlank(message = "sameDayPriceConfigId不能为空", groups = Modify.class)
    private String sameDayPriceConfigId;

    @ApiModelProperty(value = "一级类目id", required = true)
    @NotNull(message = "一级类目id不能为空", groups = Create.class)
    private Long level1GoodsCategoryId;

    @ApiModelProperty(value = "一级类目名称", required = true)
    @NotBlank(message = "一级类目名称不能为空", groups = Create.class)
    private String level1GoodsCategoryName;

    @ApiModelProperty(value = "已选省份数量", required = true)
    @NotNull(message = "已选省份数量不能为空", groups = {Create.class})
    private Integer provinceCount;

    @ApiModelProperty(value = "区县配置", required = true)
    @NotEmpty(message = "区县配置不能为空", groups = {Create.class, Modify.class})
    private List<SaveRequest.AddressConfig> addressConfigs;

    @Data
    public static class AddressConfig {

        @ApiModelProperty(value = "区县id", required = true)
        @NotBlank(message = "区县id不能为空")
        private String districtId;

        @ApiModelProperty(value = "区县名称", required = true)
        @NotBlank(message = "区县名称不能为空")
        private String districtName;

    }

}
