package com.wanshifu.domain.request;


import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

@Data
public class ExportTaskAddReq{

    @NotBlank(message = "sceneCode不能为空")
    private String sceneCode;

//    @NotBlank(message = "divisionType不能为空")
    private String divisionType;

    @NotEmpty(message = "serviceIds不能为空")
    private List<String> serviceIds;

    private List<String> bizIds;

//    @NotBlank(message = "操作人不能为空")
    private String operator;

}
