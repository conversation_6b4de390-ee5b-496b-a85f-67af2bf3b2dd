package com.wanshifu.domain.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;

@ApiModel("自定义SKU模板上传Request body")
@Data
public class CustomSkuTemplateUploadReq {

    @ApiModelProperty(value = "定价id", required = true)
    @NotBlank(message = "定价id不能为空")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long templateId;

    /**
     * 自定义sku值类型
     *
     * @see com.wanshifu.fee.center.domain.enums.CustomSkuValueTypeEnum
     */
    @ApiModelProperty(value = "自定义sku值类型", required = true)
    @NotBlank(message = "自定义sku值类型不能为空")
    private String customSkuValueType;

    /**
     * 匹配sku类型（计价逻辑）
     *
     * @see com.wanshifu.fee.center.domain.enums.MatchSkuTypeEnum
     */
    @ApiModelProperty(value = "匹配sku类型（计价逻辑）", required = true)
    @NotBlank(message = "匹配sku类型（计价逻辑）不能为空")
    private String matchSkuType;

    @ApiModelProperty(value = "文件", required = true)
    @NotNull(message = "文件不能为空")
    private MultipartFile file;

}
