package com.wanshifu.domain.request.master2c;

import com.wanshifu.fee.center.domain.request.BasePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("家庭师傅服务价格")
public class ServePrice2C4MasterPageReq extends BasePageReq {

    @ApiModelProperty(value = "服务名称，支持模糊搜索")
    private String serveName;

    @ApiModelProperty(value = "配置名称，支持模糊搜索")
    private String configName;

    @ApiModelProperty(value = "服务状态")
    private String status;

    @ApiModelProperty(value = "城市名称，精确搜索")
    private String cityName;
}
