package com.wanshifu.domain.request.master2c;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/5 14:36
 */
@Data
@ApiModel("C端师傅服务价格是否存在可用草稿")
public class DraftServePriceIsExistReq {

    @ApiModelProperty(value = "正式价格配置id", required = true)
    @NotNull(message = "正式价格配置id不能为空")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long priceId;
}
