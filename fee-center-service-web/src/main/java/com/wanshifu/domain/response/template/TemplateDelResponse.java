package com.wanshifu.domain.response.template;

import com.wanshifu.domain.enums.CheckReliedEnum;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel("删除模板response body")
@AllArgsConstructor
@NoArgsConstructor
public class TemplateDelResponse {

    private int successCount;

    private int failCount;

    private List<FailItem> failItems;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FailItem {

        private String templateId;

        private String skuNo;

        private String serviceName;

        /**
         * @see com.wanshifu.domain.enums.CheckReliedEnum
         */
        private List<CheckReliedEnum> checkReliedEnums;

    }
}
