package com.wanshifu.domain.response;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
public class EnumInfoResp {
    // 金额类型
    private List<EnumInfoData> amountType;
    private List<EnumInfoData> applicableBiz;
    private List<EnumInfoData> bizIdType;
    private List<EnumInfoData> integrationSys;
    private List<EnumInfoData> orderRange;
    private List<EnumInfoData> priceMaintain;
    private List<EnumInfoData> priceType;
    private List<EnumInfoData> skuType;


    @Data
    @AllArgsConstructor
    public static class EnumInfoData{
        private String code;
        private String name;
        private String ext;
    }
}
