package com.wanshifu.consumer;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.wanshifu.infrastructure.config.UserInfoInterceptor;
import com.wanshifu.fee.center.domain.document.BatchTaskInfo;
import com.wanshifu.fee.center.domain.document.SceneInfo;
import com.wanshifu.fee.center.domain.enums.BatchTaskStatusEnum;
import com.wanshifu.fee.center.domain.enums.FeeCenterTagEnum;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.framework.rocketmq.autoconfigure.model.NormalMessage;
import com.wanshifu.repository.BatchTaskInfoRepository;
import com.wanshifu.repository.SceneInfoRepository;
import com.wanshifu.strategy.batch.BatchTableInvoker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.UUID;

@Component
@Slf4j
public class ImportTaskHandler implements TaskHandler{

   @Resource
   private BatchTaskInfoRepository batchTaskInfoRepository;

   @Resource
   private SceneInfoRepository sceneInfoRepository;

   @Resource
   private BatchTableInvoker batchTableInvoker;

   @Resource
   private RocketMqSendService rocketMqSendService;

   @Value("${wanshifu.rocketMQ.fee-center-service-general-topic}")
   private String feeCenterServiceGeneralTopic;


   @Override
   public boolean support(TagEnum tagEnum) {
      return TagEnum.IMPORT_TASK == tagEnum;
   }

   @Override
   public void handle(Message message) {
      BatchTaskInfo batchTaskInfo = JSON.parseObject(message.getBody(), BatchTaskInfo.class);
      SceneInfo sceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(batchTaskInfo.getSceneCode());
      if(Objects.isNull(sceneInfo)){
         batchTaskInfo.setStatus(BatchTaskStatusEnum.FAIL.code);
         log.error("开始消费任务-场景不存在；taskinfo={}",batchTaskInfo);
         batchTaskInfoRepository.save(batchTaskInfo);
         return;
      }
      log.info("开始消费任务 task={}",batchTaskInfo);


      try{
         String updateBy = batchTaskInfo.getUpdateBy();
         UserInfoInterceptor.userInfoLocal.set(updateBy);
         batchTableInvoker.handle(sceneInfo, batchTaskInfo);
      }catch (Throwable t){
         batchTaskInfo.setStatus(BatchTaskStatusEnum.FAIL.code);
         log.error("任务消费异常；taskinfo={} ; e={}",batchTaskInfo,t);
         batchTaskInfoRepository.save(batchTaskInfo);
      }finally {
         // 发送mq
         rocketMqSendService.sendSyncTransactionMessage(new NormalMessage(batchTaskInfo.getBatchTaskId(), UUID.randomUUID().toString(),feeCenterServiceGeneralTopic, FeeCenterTagEnum.BATCH_TASK_RESULT.tag, JSON.toJSONString(batchTaskInfo)));
         log.info("任务已消费 taskiinfo={}",batchTaskInfo);
      }

   }
}
