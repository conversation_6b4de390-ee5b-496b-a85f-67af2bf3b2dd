package com.wanshifu.consumer;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.wanshifu.fee.center.domain.document.ExportTaskInfo;
import com.wanshifu.fee.center.domain.enums.ExportTaskStatusEnum;
import com.wanshifu.repository.ExportTaskInfoRepository;
import com.wanshifu.strategy.export.ExportTableHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class ExportTaskHandler implements TaskHandler{

   @Autowired
   private List<ExportTableHandler> exportTableHandlerList;
   @Resource
   private ExportTaskInfoRepository exportTaskInfoRepository;

   @Override
   public boolean support(TagEnum tagEnum) {
      return TagEnum.EXPORT_TASK == tagEnum;
   }

   @Override
   public void handle(Message message) {
      ExportTaskInfo exportTaskInfo = JSON.parseObject(message.getBody(), ExportTaskInfo.class);
      ExportTaskInfo exportTaskInfoByExportTaskIdAndDelIsFalse = exportTaskInfoRepository.findExportTaskInfoByExportTaskIdAndDelIsFalse(exportTaskInfo.getExportTaskId());
      if(Objects.isNull(exportTaskInfoByExportTaskIdAndDelIsFalse)){
         log.info("exportTaskId={},任务不存在",exportTaskInfo.getExportTaskId());
         return;
      }

      if(ExportTaskStatusEnum.EXPORT.code != exportTaskInfoByExportTaskIdAndDelIsFalse.getStatus()){
         log.info("exportTaskId={},任务已到终态",exportTaskInfo.getExportTaskId());
         return;
      }

      for (ExportTableHandler exportTableHandler : exportTableHandlerList) {
         exportTableHandler.export(exportTaskInfoByExportTaskIdAndDelIsFalse);
         // 成功则跳出
         if (ExportTaskStatusEnum.SUCCESS.code == exportTaskInfoByExportTaskIdAndDelIsFalse.getStatus()){
            break;
         }
      }
   }
}
