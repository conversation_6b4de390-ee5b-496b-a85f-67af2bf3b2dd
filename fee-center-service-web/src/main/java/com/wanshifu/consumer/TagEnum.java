package com.wanshifu.consumer;

import com.wanshifu.framework.utils.StringUtils;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum TagEnum {
    IMPORT_TASK("import_task","批量导入任务消费"),
    EXPORT_TASK("export_task","导出表格任务消费"),
    ;
    public final String tag;
    public final String desc;

    public static TagEnum fromTag(String tag){
        if(StringUtils.isBlank(tag)){
            return null;
        }
        for (TagEnum value : TagEnum.values()) {
            if(value.tag.equals(tag)){
                return value;
            }
        }
        return null;
    }

}
