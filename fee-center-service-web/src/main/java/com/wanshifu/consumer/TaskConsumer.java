package com.wanshifu.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.Consumer;
import com.aliyun.openservices.ons.api.Message;
import com.wanshifu.fee.center.domain.constant.GlobalRedisKeyConstant;
import com.wanshifu.fee.center.domain.constant.MQTagConstant;
import com.wanshifu.fee.center.domain.enums.*;
import com.wanshifu.fee.center.domain.request.feeRule.CreateRecruitActivityReq;
import com.wanshifu.fee.center.domain.request.feeRule.RecruitActivityMasterResult;
import com.wanshifu.fee.center.domain.request.feeRule.RemoveForMasterReq;
import com.wanshifu.fee.center.domain.request.feeRule.master.CreateRecruitActivityMasterReq;
import com.wanshifu.fee.center.domain.request.feeRule.master.CreateRecruitMasterReq;
import com.wanshifu.fee.center.domain.request.feeRule.master.DeleteRuleByMasterIdRequest;
import com.wanshifu.fee.center.domain.request.feeRule.master.ModifyRecruitActivityMasterReq;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.framework.rocketmq.autoconfigure.model.NormalMessage;
import com.wanshifu.repository.RecruitActivityMasterCreateStatusRepository;
import com.wanshifu.service.AsyncTasksInfoService;
import com.wanshifu.service.FeeRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@Slf4j
public class TaskConsumer implements ApplicationRunner {

    @Value("${wanshifu.rocketMQ.fee-center-service-general-topic}")
    private String feeCenterServiceGeneralTopic;

    @Resource
    private Consumer consumer;
    @Resource
    private FeeRuleService feeRuleService;
    @Resource
    private RocketMqSendService rocketMqSendService;
    @Resource
    private RecruitActivityMasterCreateStatusRepository recruitActivityMasterCreateStatusRepository;

    @Autowired
    private List<TaskHandler> taskHandlerList;
    private Map<TagEnum, TaskHandler> taskHandlerMap;
    private Map<String, java.util.function.Consumer<Message>> messageHandlers;


    @Qualifier("redisHelper")
    @Autowired
    private RedisHelper redisHelper;
    @Resource
    private AsyncTasksInfoService asyncTasksInfoService;

    @PostConstruct
    public void init() {
        taskHandlerMap = taskHandlerList.stream()
                .collect(Collectors.toMap(handler ->
                        Arrays.stream(TagEnum.values())
                                .filter(handler::support)
                                .findFirst()
                                .orElseThrow(() -> new IllegalStateException("No TagEnum found for handler " + handler.getClass().getName())),
                        handler -> handler));

        messageHandlers = new HashMap<>();
        messageHandlers.put(MQTagConstant.RECRUIT_ACTIVITY_MASTER_ASYNC_CREATE, this::handleRecruitActivityMasterCreate);
        messageHandlers.put(MQTagConstant.CREATE_RECRUIT_MASTER_ASYNC, this::handleCreateRecruitMaster);
        messageHandlers.put(MQTagConstant.MODIFY_RECRUIT_ACTIVITY_MASTER_ASYNC, this::handleModifyRecruitActivityMaster);
        messageHandlers.put(MQTagConstant.DELETE_RULE_BY_MASTER_ID_ASYNC, this::handleDeleteRuleByMasterId);
        messageHandlers.put(MQTagConstant.PRE_GENERATE, this::handlePreGenerate);
        messageHandlers.put(MQTagConstant.CREATE_RECRUIT_ACTIVITY, this::handleCreateRecruitActivity);
    }


    @Override
    public void run(ApplicationArguments args) {
        Set<String> taskTags = Arrays.stream(TagEnum.values()).map(tagEnum -> tagEnum.tag).collect(Collectors.toSet());
        String focusTag = Stream.concat(
                taskTags.stream(),
                messageHandlers.keySet().stream()
        ).collect(Collectors.joining(" || "));

        log.info("Starting subscription, topic={}, tag={}", feeCenterServiceGeneralTopic, focusTag);

        consumer.subscribe(feeCenterServiceGeneralTopic, focusTag, (message, consumeContext) -> {
            String tag = message.getTag();
            if (message.getBody() == null || message.getBody().length == 0) {
                log.warn("Received empty message body, tag={}", tag);
                return Action.CommitMessage;
            }

            log.info("Received an asynchronous task message: tag={}, message={}",
                    tag,
                    JSONObject.toJSONString(new String(message.getBody()), SerializerFeature.DisableCircularReferenceDetect));

            try {
                if (taskTags.contains(tag)) {
                    handleDynamicTask(message);
                } else {
                    java.util.function.Consumer<Message> handler = messageHandlers.get(tag);
                    if (handler != null) {
                        handler.accept(message);
                    } else {
                        log.warn("No handler found for tag: {}", tag);
                    }
                }
                return Action.CommitMessage;
            } catch (Exception e) {
                return handleException(e, message);
            }
        });

        log.info("Started subscription topic={}, tag={}", feeCenterServiceGeneralTopic, focusTag);
    }

    private <T> void processBusMessage(Message message, Class<T> requestClass, java.util.function.Consumer<T> task, BiConsumer<T, BusException> onFailure) {
        T request = JSONObject.parseObject(new String(message.getBody()), requestClass);
        try {
            task.accept(request);
        } catch (BusException e) {
            onFailure.accept(request, e);
            throw e;
        }
    }

    private void handleDynamicTask(Message message) {
        String tag = message.getTag();
        TagEnum tagEnum = TagEnum.fromTag(tag);
        if (Objects.isNull(tagEnum)) {
            throw new BusException("Unsupported tag, tag = " + tag);
        }

        TaskHandler taskHandler = taskHandlerMap.get(tagEnum);
        if (taskHandler == null) {
            throw new BusException("No handler found for tag, tag = " + tag);
        }
        taskHandler.handle(message);
    }

    private void handleRecruitActivityMasterCreate(Message message) {
        processBusMessage(message, CreateRecruitActivityMasterReq.class,
                req -> {
                    feeRuleService.createRecruitActivityMaster(req);
                    handleCreateResult(req, RecruitActivityMasterCreateStatusEnum.SUCCESS, null);
                },
                (req, e) -> handleCreateResult(req, RecruitActivityMasterCreateStatusEnum.FAIL, e.getMessage())
        );
    }

    private void handleCreateRecruitMaster(Message message) {
        processBusMessage(message, CreateRecruitMasterReq.class,
                req -> {
                    feeRuleService.createRecruitMaster(req);
                    handleCreateMasterResult(req, RecruitActivityMasterCreateStatusEnum.SUCCESS, null);
                },
                (req, e) -> handleCreateMasterResult(req, RecruitActivityMasterCreateStatusEnum.FAIL, e.getMessage())
        );
    }

    private void handleModifyRecruitActivityMaster(Message message) {
        processBusMessage(message, ModifyRecruitActivityMasterReq.class,
                req -> {
                    feeRuleService.modifyRecruitActivityMaster(req);
                    handleModifyResult(req, RecruitActivityMasterCreateStatusEnum.SUCCESS, null);
                },
                (req, e) -> handleModifyResult(req, RecruitActivityMasterCreateStatusEnum.FAIL, e.getMessage())
        );
    }

    private void handleDeleteRuleByMasterId(Message message) {
        processBusMessage(message, DeleteRuleByMasterIdRequest.class,
                req -> {
                    log.info("deleteRuleByMasterIdAsync message: masterId={}, recruitId={}", req.getMasterId(), req.getRecruitId());
                    String taskId = req.getRecruitId() + "-" + req.getMasterId();
                    RemoveForMasterReq removeReq = new RemoveForMasterReq();
                    BeanUtils.copyProperties(req, removeReq);
                    feeRuleService.deleteForMaster(removeReq);
                    asyncTasksInfoService.updateStatus(taskId, AsyncTaskTypeEnum.DELETE_RULE_BY_MASTER_ID, AsyncTaskStatusEnum.SUCCESS, "");
                },
                (req, e) -> {
                    String taskId = req.getRecruitId() + "-" + req.getMasterId();
                    asyncTasksInfoService.updateStatus(taskId, AsyncTaskTypeEnum.DELETE_RULE_BY_MASTER_ID, AsyncTaskStatusEnum.FAIL, e.getMessage());
                }
        );
    }

    private void handlePreGenerate(Message message) {
        String sceneCode = new String(message.getBody());
        log.info("preGenerate message: {}", sceneCode);
        SceneCodeEnum sceneCodeEnum = SceneCodeEnum.fromCode(sceneCode);
        if (sceneCodeEnum == null) {
            return;
        }
        String redisKey = GlobalRedisKeyConstant.PRE_GENERATE_KEY_PREFIX + sceneCode;
        switch (sceneCodeEnum) {
            case BARGAIN_PRICE_EVERYDAY:
                feeRuleService.preGenerateLowestPrice();
                feeRuleService.preGenerateBargainPriceEverydayFeeRule(sceneCode);
                redisHelper.del(redisKey);
                break;
            case PLATFORM_FIXED_PRICE:
                feeRuleService.preGenerateBargainPriceEverydayFeeRule(sceneCode);
                redisHelper.del(redisKey);
                break;
            default:
                break;
        }
    }

    private void handleCreateRecruitActivity(Message message) {
        CreateRecruitActivityReq req = JSONObject.parseObject(new String(message.getBody()), CreateRecruitActivityReq.class);
        try {
            feeRuleService.createRecruitActivity(req);
            asyncTasksInfoService.updateStatus(req.getRecruitId(), AsyncTaskTypeEnum.RECRUIT_ACTIVITY, AsyncTaskStatusEnum.SUCCESS, "");
        } catch (Exception e) {
            asyncTasksInfoService.updateStatus(req.getRecruitId(), AsyncTaskTypeEnum.RECRUIT_ACTIVITY, AsyncTaskStatusEnum.FAIL, e.getMessage());
            log.error("createRecruitActivity，创建招募活动失败", e);
            throw e;
        }
    }


    private void handleCreateMasterResult(CreateRecruitMasterReq req, RecruitActivityMasterCreateStatusEnum statusEnum, String failReason) {
        sendMessageForCreateMaster(req.getRecruitId(), req.getMasterId(), statusEnum.code);
        recruitActivityMasterCreateStatusRepository.saveStatus(req.getMasterId(), req.getRecruitId(),
                RecruitActivityMasterBuildTypeEnum.SAVE_RECRUIT_MASTER.code, statusEnum.code, failReason);
    }

    private void handleModifyResult(ModifyRecruitActivityMasterReq req, RecruitActivityMasterCreateStatusEnum statusEnum, String failReason) {
        sendMessageForModifyMaster(req.getRecruitId(), req.getMasterId(), statusEnum.code);
        recruitActivityMasterCreateStatusRepository.saveStatus(req.getMasterId(), req.getRecruitId(),
                RecruitActivityMasterBuildTypeEnum.MODIFY_RECRUIT_ACTIVITY_MASTER.code, statusEnum.code, failReason);
    }


    private void handleCreateResult(CreateRecruitActivityMasterReq req, RecruitActivityMasterCreateStatusEnum success, String failReason) {
        sendMessageForCreateActivityMaster(req.isAddStreet(), req.getRecruitId(), req.getMasterId(), req.getStreetIds(), success.code);
        recruitActivityMasterCreateStatusRepository.saveStatus(req.getMasterId(), req.getRecruitId(),
                RecruitActivityMasterBuildTypeEnum.CREATE_RECRUIT_ACTIVITY_MASTER.code, success.code, failReason);
    }


    private void sendMessageForCreateMaster(String recruitId, String masterId, String status) {
        RecruitActivityMasterResult result = new RecruitActivityMasterResult();
        result.setRecruitId(recruitId);
        result.setMasterId(masterId);
        result.setBuildType(RecruitActivityMasterBuildTypeEnum.SAVE_RECRUIT_MASTER.code);
        result.setStatus(status);
        // bizId字段业务方不需要，所以传null
        rocketMqSendService.sendSyncTransactionMessage(new NormalMessage(null, UUID.randomUUID().toString(), feeCenterServiceGeneralTopic, MQTagConstant.NOTIFY_CALLER_CREATE_RECRUIT_MASTER, JSON.toJSONString(result)));
        log.info("createRecruitMaster send message recruitId={}, masterId={}, status={}", recruitId, masterId, status);
    }

    private void sendMessageForModifyMaster(String recruitId, String masterId, String status) {
        RecruitActivityMasterResult result = new RecruitActivityMasterResult();
        result.setRecruitId(recruitId);
        result.setMasterId(masterId);
        result.setBuildType(RecruitActivityMasterBuildTypeEnum.MODIFY_RECRUIT_ACTIVITY_MASTER.code);
        result.setStatus(status);
        // bizId字段业务方不需要，所以传null
        rocketMqSendService.sendSyncTransactionMessage(new NormalMessage(null, UUID.randomUUID().toString(), feeCenterServiceGeneralTopic, MQTagConstant.NOTIFY_CALLER_MODIFY_RECRUIT_ACTIVITY_MASTER, JSON.toJSONString(result)));
        log.info("modifyRecruitActivityMaster send message recruitId={}, masterId={}, status={}", recruitId, masterId, status);
    }

    private void sendMessageForCreateActivityMaster(boolean isAddStreet, String recruitId, String masterId, Set<String> streetIds, String status) {
        RecruitActivityMasterResult result = new RecruitActivityMasterResult();
        result.setRecruitId(recruitId);
        result.setMasterId(masterId);
        result.setBuildType(RecruitActivityMasterBuildTypeEnum.CREATE_RECRUIT_ACTIVITY_MASTER.code);
        result.setAddStreetIdList(streetIds);
        result.setStatus(status);
        // bizId字段业务方不需要，所以传null
        if (isAddStreet) {
            rocketMqSendService.sendSyncTransactionMessage(new NormalMessage(null, UUID.randomUUID().toString(), feeCenterServiceGeneralTopic, MQTagConstant.NOTIFY_CALLER_RECREATE_RECRUIT_MASTER, JSON.toJSONString(result)));
        } else {
            rocketMqSendService.sendSyncTransactionMessage(new NormalMessage(null, UUID.randomUUID().toString(), feeCenterServiceGeneralTopic, MQTagConstant.NOTIFY_CALLER_CREATE_RECRUIT_ACTIVITY_MASTER, JSON.toJSONString(result)));
        }
        log.info("createRecruitActivityMasterAsync send message isAddStreet={}, recruitId={}, masterId={}, streetIds={}, status={}", isAddStreet, recruitId, masterId, streetIds, status);
    }

    private Action handleException(Exception e, Message message) {
        if (e instanceof BusException) {
            log.error("Consumption failed: business exception, failure reason: {}, messageId: {}", e.getMessage(), message.getMsgID(), e);
        } else {
            log.error("Consumption failed: system exception, failure reason: {}, messageId: {}", e.getMessage(), message.getMsgID(), e);
        }
        if (message.getReconsumeTimes() > 0) {
            return Action.CommitMessage;
        } else {
            return Action.ReconsumeLater;
        }
    }

}

