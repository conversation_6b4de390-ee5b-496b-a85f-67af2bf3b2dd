//package com.wanshifu.consumer;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.aliyun.openservices.ons.api.Action;
//import com.aliyun.openservices.ons.api.Consumer;
//import com.wanshifu.fee.center.domain.constant.MQTagConstant;
//import com.wanshifu.fee.center.domain.enums.RecruitActivityMasterCreateStatusEnum;
//import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
//import com.wanshifu.fee.center.domain.request.feeRule.CreateRecruitActivityResult;
//import com.wanshifu.fee.center.domain.request.feeRule.master.CreateRecruitActivityMasterReq;
//import com.wanshifu.framework.core.BusException;
//import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
//import com.wanshifu.framework.rocketmq.autoconfigure.model.NormalMessage;
//import com.wanshifu.repository.RecruitActivityMasterCreateStatusRepository;
//import com.wanshifu.service.FeeRuleService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.ApplicationArguments;
//import org.springframework.boot.ApplicationRunner;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.UUID;
//
//@Component
//@Slf4j
//public class RecruitActivityMasterCreateConsumer implements ApplicationRunner {
//
//    @Value("${wanshifu.rocketMQ.fee-center-service-general-topic}")
//    private String feeCenterServiceGeneralTopic;
//
//    @Resource
//    private Consumer consumer;
//    @Resource
//    private FeeRuleService feeRuleService;
//    @Resource
//    private RocketMqSendService rocketMqSendService;
//    @Resource
//    private RecruitActivityMasterCreateStatusRepository recruitActivityMasterCreateStatusRepository;
//
//
//    @Override
//    public void run(ApplicationArguments args) {
//
//        // 订阅方法
//        final String tag = MQTagConstant.RECRUIT_ACTIVITY_MASTER_ASYNC_CREATE;
//        log.info("启动订阅 topic = {},tag = {}", feeCenterServiceGeneralTopic, tag);
//
//        consumer.subscribe(feeCenterServiceGeneralTopic, tag, (message, consumeContext) -> {
//
//            log.warn("接收到异步任务消息： tag:{}， message = {}", message.getTag(), JSONObject.toJSONString(new String(message.getBody())));
//            CreateRecruitActivityMasterReq req = JSONObject.parseObject(new String(message.getBody()), CreateRecruitActivityMasterReq.class);
//            try {
//                feeRuleService.createRecruitActivityMaster(req);
//                handleResult(req, RecruitActivityMasterCreateStatusEnum.SUCCESS, null);
//                return Action.CommitMessage;
//            } catch (BusException e) {
//                log.error("BusException executing createRecruitActivityMasterAsync task", e);
//                // 业务异常不重试
//                handleResult(req, RecruitActivityMasterCreateStatusEnum.FAIL, e.getMessage());
//                return Action.CommitMessage;
//            } catch (Exception e) {
//                // 只重试一次
//                if (message.getReconsumeTimes() >= 1) {
//                    handleResult(req, RecruitActivityMasterCreateStatusEnum.FAIL, e.getMessage());
//                    return Action.CommitMessage;
//                }
//                log.error("Error executing createRecruitActivityMasterAsync task", e);
//                return Action.ReconsumeLater;
//            }
//        });
//        log.info("完成订阅topic={},tag={}", feeCenterServiceGeneralTopic, tag);
//    }
//
//    private void handleResult(CreateRecruitActivityMasterReq req, RecruitActivityMasterCreateStatusEnum success, String failReason) {
//        sendMessage(req.getRecruitId(), req.getMasterId(), success.code);
//        recruitActivityMasterCreateStatusRepository.saveStatus(req.getMasterId(), req.getRecruitId(), success.code, failReason);
//    }
//
//
//    private void sendMessage(String recruitId, String masterId, String status) {
//        CreateRecruitActivityResult result = new CreateRecruitActivityResult();
//        result.setRecruitId(recruitId);
//        result.setMasterId(masterId);
//        result.setStatus(status);
//        // bizId字段业务方不需要，所以传null
//        rocketMqSendService.sendSyncTransactionMessage(new NormalMessage(null, UUID.randomUUID().toString(),feeCenterServiceGeneralTopic, "createRecruitActivityMasterAsync", JSON.toJSONString(result)));
//        log.info("createRecruitActivityMasterAsync send message recruitId={} {}", recruitId, status);
//    }
//
//    private void handleException(CreateRecruitActivityMasterReq req) {
//        String toSceneCode = SceneCodeEnum.CONTRACT_MASTER.getCode();
//        sendMessage(req.getRecruitId(), req.getMasterId(), "fail");
//        feeRuleService.batchDeleteLogically(toSceneCode, req.getMasterId(), req.getRecruitId());
//    }
//}
