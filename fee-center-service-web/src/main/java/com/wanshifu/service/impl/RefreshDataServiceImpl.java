package com.wanshifu.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.wanshifu.domain.request.rule.UpdateActivityPriceRefreshRequest;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.document.FeeTemplate;
import com.wanshifu.fee.center.domain.document.RefreshPriceLog;
import com.wanshifu.fee.center.domain.enums.FeeSkuTypeEnum;
import com.wanshifu.fee.center.domain.enums.RuleStatusEnum;
import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.enums.TemplateStatusEnum;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.HttpBuilder;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.repository.FeeRuleRepository;
import com.wanshifu.repository.RefreshPriceLogRepository;
import com.wanshifu.service.RefreshDataService;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class RefreshDataServiceImpl implements RefreshDataService {

    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private FeeRuleRepository feeRuleRepository;
    @Resource
    private RefreshPriceLogRepository refreshPriceLogRepository;
    @Value("${updateFeeRulePriceSwitch:true}")
    private Boolean updateFeeRulePriceSwitch;


    @Override
    @Async
    public void updateFeeRulePrice(List<UpdateActivityPriceRefreshRequest> requestList) {
        if (CollectionUtils.isEmpty(requestList)) {
            return;
        }

        final String sceneCodeActive = SceneCodeEnum.MASTER_RECRUIT_ACTIVE_PRICE.getCode();
        final String sceneCodeContractMaster = SceneCodeEnum.CONTRACT_MASTER.getCode();

        // 获取当前的日期和时间
        LocalDateTime currentDateTime = LocalDateTime.now();
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 格式化日期时间
        String formattedDateTime = currentDateTime.format(formatter);
        String updateBy = formattedDateTime + "<EMAIL>";
        log.info("开始更新师傅招募活动价、合约师傅价格，更新人：{}", updateBy);

        for (UpdateActivityPriceRefreshRequest request : requestList) {
            if (!updateFeeRulePriceSwitch) {
                break;
            }
            /*
             * 1、根据sceneCode+serviceId+skuNo 查找出唯一的计价规则模板id，即templateId
             * 2、根据templateId+bizTag 查找出计价规则，把新价格填充后保存
             */
            updateFeeRulePriceBySceneCode(request, sceneCodeActive, updateBy);
            updateFeeRulePriceBySceneCode(request, sceneCodeContractMaster, updateBy);
        }
    }

    private void updateFeeRulePriceBySceneCode(UpdateActivityPriceRefreshRequest request, String sceneCode, String updateBy) {
        String serviceId = request.getServiceId();
        String skuNo = request.getSkuNo();
        String bizTag = request.getBizTag();
        String newPrice = request.getNewPrice();
        Criteria activeTemplateCriteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code)
                .and(FeeTemplate.Fields.sceneCode).is(sceneCode)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(skuNo)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuType).is(FeeSkuTypeEnum.STANDARD_SKU.code);
        List<FeeTemplate> feeTemplates = mongoTemplate.find(new Query(activeTemplateCriteria), FeeTemplate.class);
        if (CollectionUtils.isEmpty(feeTemplates)) {
            throw new BusException(StrUtil.format("更新师傅招募活动价、合约师傅价格，未找到对应的计价模板，sceneCode={}, serviceId={}, skuNo={}", sceneCode, serviceId, skuNo));
        }
        if (feeTemplates.size() > 1) {
            throw new BusException(StrUtil.format("更新师傅招募活动价、合约师傅价格，找到多条模板，sceneCode={}, serviceId={}, skuNo={}", sceneCode, serviceId, skuNo));
        }
        FeeTemplate feeTemplate = feeTemplates.get(0);
        Long templateId = feeTemplate.getTemplateId();

        Criteria activeRuleCriteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code)
                .and(FeeRule.Fields.sceneCode).is(sceneCode)
                .and(FeeRule.Fields.templateId).is(templateId)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizTag).is(bizTag);
        Query query = new Query(activeRuleCriteria);

        Sort sort = new Sort(new Sort.Order(Sort.Direction.ASC, FeeRule.Fields.feeRuleId));

        final int pageSize = 2000;
        int pageNum = 0;

        while (true) {
            PageRequest pageRequest = new PageRequest(pageNum, pageSize, sort);
            query.with(pageRequest);
            List<FeeRule> feeRules = mongoTemplate.find(query, FeeRule.class);
            if (CollectionUtils.isEmpty(feeRules)) {
                log.info("更新价格成功");
                return;
            }

            List<RefreshPriceLog> logs = new ArrayList<>();
            for (FeeRule feeRule : feeRules) {

                RefreshPriceLog log = new RefreshPriceLog();
                log.setSceneCode(sceneCode);
                log.setFeeRuleId(feeRule.getFeeRuleId());
                log.setOriginalPrice(feeRule.getBizRule().get(CommonBizRule.Fields.masterInputPrice));
                log.setNewPrice(newPrice);
                log.setUpdateBy(updateBy);
                logs.add(log);

                feeRule.getBizRule().put(CommonBizRule.Fields.masterInputPrice, newPrice);
                feeRule.setUpdateBy(updateBy);
            }

            feeRuleRepository.save(feeRules);
            refreshPriceLogRepository.save(logs);

            pageNum++;
        }

    }



    @Override
    public void refreshActivityMasterFeeRules() {
        // 根据Excel读取Excel文档
        // 下载文件
        final String fileUrl = "http://10.10.10.10/fee-center-service-web/feeRule/flushBizRuleMappingToTemplateMapping";
        final String sheetName = "Sheet1";
        InputStream content;
        try {
            content = HttpBuilder.genericGet(fileUrl).build().execute().getEntity().getContent();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        List<RecruitMasterInfo> recruitMasterInfoList = EasyExcel.read(content).sheet(sheetName).head(RecruitMasterInfo.class).doReadSync();
        if (CollectionUtils.isEmpty(recruitMasterInfoList)) {
            throw new RuntimeException("文件为空");
        }

        Date now = new Date();
        String createBy = "refresh20250326@wshifu";

        recruitMasterInfoList.forEach(recruitMasterInfo -> {
            String oldRecruitId = recruitMasterInfo.getOldRecruitId();
            String newRecruitId = recruitMasterInfo.getNewRecruitId();
            String masterIds = recruitMasterInfo.getMasterIds();
            if (StringUtils.isBlank(oldRecruitId) || StringUtils.isBlank(newRecruitId) || StringUtils.isBlank(masterIds)) {
                log.error("oldRecruitId or newRecruitId or masterIds is null, oldRecruitId={}, newRecruitId={}, masterIds={}", oldRecruitId, newRecruitId, masterIds);
                throw new RuntimeException("存在空值");
            }
            String[] masterIdArr = masterIds.split(",");
            for (String masterId : masterIdArr) {
                Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                        .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code)
                        .and(FeeRule.Fields.sceneCode).is(SceneCodeEnum.CONTRACT_MASTER.getCode())
                        .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizTag).is(oldRecruitId)
                        .orOperator(
                                Criteria.where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterId).is(masterId),
                                Criteria.where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizId).is(masterId)
                        );
                Query query = new Query(criteria);

                int pageNum = 0;
                final int pageSize = 1000;
                Sort sort = new Sort(new Sort.Order(Sort.Direction.ASC, BaseDocument.Fields.id));
                while (true) {
                    PageRequest pageRequest = new PageRequest(pageNum, pageSize, sort);
                    query.with(pageRequest);
                    List<FeeRule> feeRules = mongoTemplate.find(query, FeeRule.class);
                    if (CollectionUtils.isEmpty(feeRules)) {
                        break;
                    }
                    feeRules.forEach(feeRule -> {
                        feeRule.setId(null);
                        feeRule.setFeeRuleId(SnowFlakeGenerator.INSTANCE.generate());
                        feeRule.setDel(false);
                        feeRule.setStatus(RuleStatusEnum.ACTIVE.code);
                        feeRule.setCreateTime(now);
                        feeRule.setModifyTime(now);
                        feeRule.setCreateBy(createBy);
                        feeRule.setUpdateBy(createBy);
                        Map<String, String> bizRule = feeRule.getBizRule();
                        bizRule.put(CommonBizRule.Fields.bizTag, newRecruitId);
                        bizRule.put(CommonBizRule.Fields.masterId, masterId);
                        bizRule.put(CommonBizRule.Fields.bizId, masterId);
                    });
                    feeRuleRepository.save(feeRules);

                    pageNum++;
                }
            }
        });
    }
}




@Data
@FieldNameConstants
class RecruitMasterInfo {

    @ExcelProperty(value = "旧招募id")
    private String oldRecruitId;

    @ExcelProperty(value = "新招募id")
    private String newRecruitId;

    @ExcelProperty(value = "师傅ids")
    private String masterIds;
}