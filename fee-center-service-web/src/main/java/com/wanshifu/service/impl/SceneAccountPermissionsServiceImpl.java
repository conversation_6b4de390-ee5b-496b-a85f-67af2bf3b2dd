package com.wanshifu.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.SceneAccountPermissions;
import com.wanshifu.fee.center.domain.document.SceneInfo;
import com.wanshifu.fee.center.domain.enums.SceneStatusEnum;
import com.wanshifu.fee.center.domain.request.permission.GetPageRequest;
import com.wanshifu.fee.center.domain.request.permission.UpdateRequest;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.repository.SceneAccountPermissionsRepository;
import com.wanshifu.repository.SceneInfoRepository;
import com.wanshifu.service.SceneAccountPermissionsService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SceneAccountPermissionsServiceImpl implements SceneAccountPermissionsService {

    private final SceneAccountPermissionsRepository sceneAccountPermissionsRepository;
    private final MongoTemplate mongoTemplate;
    private final SceneInfoRepository sceneInfoRepository;


    @Override
    public SimplePageInfo<SceneAccountPermissions> getPage(GetPageRequest request) {
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(SceneStatusEnum.ACTIVE.code);
        Query query = new Query(criteria);
        long totalCount = mongoTemplate.count(query, SceneInfo.class);
        if (totalCount <= 0L) {
            return new SimplePageInfo<>(Collections.emptyList());
        }

        Pageable pageRequest = new PageRequest(request.getPageNum() - 1, request.getPageSize());
        Sort sort = new Sort(Sort.Direction.ASC, SceneInfo.Fields.sceneCode);
        query.with(pageRequest).with(sort);
        List<SceneInfo> sceneInfos = mongoTemplate.find(query, SceneInfo.class);

        List<SceneAccountPermissions> permissionList = sceneAccountPermissionsRepository.findAllByDelIsFalse();
        Map<String, SceneAccountPermissions> permissionsMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(permissionList)) {
            permissionsMap = permissionList.stream().collect(Collectors.toMap(SceneAccountPermissions::getSceneCode, Function.identity()));
        }
        return getSceneAccountPermissionsSimplePageInfo(sceneInfos, pageRequest, permissionsMap, totalCount);
    }


    @Override
    public void update(UpdateRequest request) {
        SceneInfo sceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(request.getSceneCode());
        if  (sceneInfo == null) {
            throw new BusException(StrUtil.format("场景编码【{}】有误", request.getSceneCode()));
        }
        SceneAccountPermissions permission = sceneAccountPermissionsRepository.findBySceneCodeIsAndDelIsFalse(request.getSceneCode());
        if (permission == null) {
            permission = new SceneAccountPermissions();
            permission.setSceneCode(request.getSceneCode());
        }
        permission.setAccounts(request.getAccounts());
        sceneAccountPermissionsRepository.save(permission);
    }


    @Override
    public List<String> getSceneCodesByAccount(String account) {
        if (StringUtils.isBlank(account)) {
            return Collections.emptyList();
        }
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .orOperator(new Criteria(SceneAccountPermissions.Fields.accounts).is(account),
                        new Criteria(SceneAccountPermissions.Fields.accounts).is(null),
                        new Criteria(SceneAccountPermissions.Fields.accounts).is(Collections.EMPTY_LIST));
        List<SceneAccountPermissions> permissionsList = mongoTemplate.find(new Query(criteria), SceneAccountPermissions.class);
        List<String> sceneCodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(permissionsList)) {
            sceneCodes = permissionsList.stream().map(SceneAccountPermissions::getSceneCode).collect(Collectors.toList());
        }
        List<SceneInfo> sceneInfos = sceneInfoRepository.findAllByDelIsFalse();
        List<SceneAccountPermissions> permissions = sceneAccountPermissionsRepository.findAllByDelIsFalse();
        List<String> unconfiguredPermissionSceneCodes = sceneInfos.stream()
                .map(SceneInfo::getSceneCode)
                .filter(sceneCode -> permissions.stream()
                                .noneMatch(permission -> permission.getSceneCode().equals(sceneCode)))
                .collect(Collectors.toList());
        return CollUtil.addAllIfNotContains(sceneCodes, unconfiguredPermissionSceneCodes);
    }



    private  SimplePageInfo<SceneAccountPermissions> getSceneAccountPermissionsSimplePageInfo(List<SceneInfo> sceneInfos, Pageable pageRequest, Map<String, SceneAccountPermissions> permissionsMap, long totalCount) {
        List<SceneAccountPermissions> permissionResultList = new ArrayList<>();
        for (SceneInfo sceneInfo : sceneInfos) {
            SceneAccountPermissions permission = new SceneAccountPermissions();
            permission.setSceneCode(sceneInfo.getSceneCode());
            permission.setSceneName(sceneInfo.getSceneName());
            SceneAccountPermissions permissions = permissionsMap.get(sceneInfo.getSceneCode());
            if (permissions != null) {
                permission.setAccounts(permissions.getAccounts());
            }
            permissionResultList.add(permission);
        }


        PageImpl<SceneAccountPermissions> page = new PageImpl<>(permissionResultList, pageRequest, totalCount);
        SimplePageInfo<SceneAccountPermissions> resultPage = new SimplePageInfo<>(permissionResultList);
        resultPage.setPages(page.getTotalPages());
        resultPage.setPageNum(page.getNumber());
        resultPage.setPageSize(page.getSize());
        resultPage.setTotal(totalCount);
        return resultPage;
    }
}
