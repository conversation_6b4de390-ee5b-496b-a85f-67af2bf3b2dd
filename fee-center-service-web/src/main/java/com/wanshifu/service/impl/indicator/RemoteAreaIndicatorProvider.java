package com.wanshifu.service.impl.indicator;

import com.wanshifu.fee.center.domain.document.DynamicPricingIndicator;
import com.wanshifu.fee.center.domain.enums.AdjustPriceIndicatorEnum;
import com.wanshifu.fee.center.domain.enums.IndicatorOptionalConditionEnum;
import com.wanshifu.fee.center.domain.response.bigdata.RemoteArea;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.infrastructure.gateway.BigdataGateway;
import com.wanshifu.service.impl.IndicatorContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@RequiredArgsConstructor
public class RemoteAreaIndicatorProvider implements IndicatorProvider {

    private final BigdataGateway bigdataGateway;
    private final IndicatorValueSelector selector;

    @Override
    public boolean supports(AdjustPriceIndicatorEnum indicatorEnum) {
        return AdjustPriceIndicatorEnum.REMOTE_AREA.equals(indicatorEnum);
    }

    @Override
    public List<DynamicPricingIndicator.IndicatorValue> getValues(IndicatorContext context, List<DynamicPricingIndicator> indicators) {
        if (context.getStreetId() == null) {
            return Collections.emptyList();
        }

        List<RemoteArea> remoteAreas = bigdataGateway.getRemoteAreas(context.getStreetId());
        if (CollectionUtils.isEmpty(remoteAreas)) {
            return Collections.emptyList();
        }
        String isRemoteDistrictLast90d = remoteAreas.get(0).getIsRemoteDistrictLast90d();

        // 1. 收集所有符合距离条件的候选值
        List<DynamicPricingIndicator.IndicatorValue> candidateValues = indicators.stream()
                .flatMap(indicator -> {
                    IndicatorOptionalConditionEnum condition = IndicatorOptionalConditionEnum.fromCode(indicator.getIndicatorOptionalCondition());
                    if (condition == null) {
                        return Stream.empty();
                    }
                    return indicator.getIndicatorValueList().stream()
                            .filter(value -> condition.matches(Double.parseDouble(isRemoteDistrictLast90d), value));
                })
                .collect(Collectors.toList());

        // 2. 使用选择器筛选出最优的单个值
        Optional<DynamicPricingIndicator.IndicatorValue> bestValue = selector.selectBestValue(candidateValues);
        return bestValue.map(Collections::singletonList).orElse(Collections.emptyList());
    }
}
