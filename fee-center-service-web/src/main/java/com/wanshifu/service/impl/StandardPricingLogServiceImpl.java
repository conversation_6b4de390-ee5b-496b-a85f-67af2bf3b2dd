package com.wanshifu.service.impl;

import com.wanshifu.adapter.api.backend.ServiceBackendApi;
import com.wanshifu.adapter.dto.service.ServiceDetailResp;
import com.wanshifu.domain.request.pricinglog.GetStandardPricingLogListRequest;
import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.SceneInfo;
import com.wanshifu.fee.center.domain.document.StandardPricingLog;
import com.wanshifu.fee.center.domain.dto.ServiceDTO;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.repository.SceneInfoRepository;
import com.wanshifu.repository.StandardPricingLogRepository;
import com.wanshifu.service.StandardPricingLogService;
import jodd.util.StringUtil;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class StandardPricingLogServiceImpl implements StandardPricingLogService {

    @Resource
    private StandardPricingLogRepository standardPricingLogRepository;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private SceneInfoRepository sceneInfoRepository;
    @Resource
    private ServiceBackendApi serviceBackendApi;

    @Async("logTaskExecutor")
    @Override
    public void saveAsync(StandardPricingLog standardPricingLog) {
        standardPricingLogRepository.save(standardPricingLog);
    }

    /**
     * 查询标准计价流程及结果日志详情
     * PS：由于对入参做了限制，回参数据量不会很多，故没有做分页
     * @param request request
     * @return List<StandardPricingLog>
     */
    @Override
    public List<StandardPricingLog> getStandardPricingLogList(GetStandardPricingLogListRequest request) {
        if (request == null) {
            throw new RuntimeException("GetStandardPricingLogListRequest object is null");
        }
        request.validate();
        Long tid = request.getTid();
        Long orderId = request.getOrderId();
        String orderNo = request.getOrderNo();
        Long globalOrderTraceId = request.getGlobalOrderTraceId();
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        if (tid != null) {
            criteria.and(StandardPricingLog.Fields.tid).is(tid);
        }
        if (orderId != null) {
            criteria.and(StandardPricingLog.Fields.orderId).is(orderId);
        }
        if (StringUtil.isNotBlank(orderNo)) {
            criteria.and(StandardPricingLog.Fields.orderNo).is(orderNo);
        }
        if (globalOrderTraceId != null) {
            criteria.and(StandardPricingLog.Fields.globalOrderTraceId).is(globalOrderTraceId);
        }
        Query query = new Query(criteria);
        new Sort(new Sort.Order(Sort.Direction.ASC, BaseDocument.Fields.id));

        List<StandardPricingLog> logs = mongoTemplate.find(query, StandardPricingLog.class);
        if (CollectionUtils.isEmpty(logs)) {
            return Collections.emptyList();
        }

        List<String> sceneCodes = logs.stream().map(StandardPricingLog::getSceneCodeList).flatMap(List::stream).distinct().collect(Collectors.toList());
        List<SceneInfo> sceneInfos = sceneInfoRepository.findAllBySceneCodeInAndDelIsFalse(sceneCodes);
        Map<String, String> sceneMap = sceneInfos.stream().collect(Collectors.toMap(SceneInfo::getSceneCode, SceneInfo::getSceneName));

        Set<Long> serviceIds = logs.stream()
                .flatMap(log -> {
                    List<ServiceDTO> dtoList = log.getServiceDtoList();
                    return CollectionUtils.isNotEmpty(dtoList) ? dtoList.stream() : Stream.empty();
                })
                .map(ServiceDTO::getServiceId)
                .collect(Collectors.toSet());
        List<ServiceDetailResp> serviceList = serviceBackendApi.getDetailBatch(StringUtils.join(serviceIds, ","));
        Map<Long, String> serviceMap = serviceList.stream().collect(Collectors.toMap(ServiceDetailResp::getServiceId, ServiceDetailResp::getServiceName));

        for (StandardPricingLog log : logs) {
            List<StandardPricingLog.ServiceResultLog> logList = log.getServiceResultLogList();
            if (CollectionUtils.isNotEmpty(logList)) {
                for (StandardPricingLog.ServiceResultLog serviceResultLog : logList) {
                    serviceResultLog.setServiceName(serviceMap.get(Long.valueOf(serviceResultLog.getServiceId())));
                    serviceResultLog.setSceneName(sceneMap.get(serviceResultLog.getSceneCode()));
                }
            }
        }

        // logs根据isSuccess、sceneCode正序排序
        logs = logs.stream().sorted(Comparator.comparing(BaseDocument::getCreateTime)).collect(Collectors.toList());

        logs.stream().filter(l -> CollectionUtils.isNotEmpty(l.getServiceResultLogList()))
                .forEach(log -> log.setServiceResultLogList(log.getServiceResultLogList().stream()
                        .sorted(Comparator.comparing(StandardPricingLog.ServiceResultLog::getSceneCode))
                        .sorted(Comparator.comparing(StandardPricingLog.ServiceResultLog::getIsSuccess).reversed())
                        .collect(Collectors.toList())));

        return logs;
    }
}
