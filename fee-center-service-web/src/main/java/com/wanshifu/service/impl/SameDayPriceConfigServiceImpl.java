package com.wanshifu.service.impl;

import cn.hutool.core.lang.Assert;
import com.wanshifu.adapter.api.GoodsCategoryApi;
import com.wanshifu.adapter.dto.goods.GoodsCategoryLevel1Resp;
import com.wanshifu.domain.request.samedayconfig.*;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.SameDayPriceConfig;
import com.wanshifu.fee.center.domain.enums.SameDayPriceConfigStateEnum;
import com.wanshifu.fee.center.domain.request.sameday.GetPriceRequest;
import com.wanshifu.fee.center.domain.response.sameday.GetPriceResponse;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.repository.SameDayPriceConfigRepository;
import com.wanshifu.service.SameDayBlacklistAddressConfigService;
import com.wanshifu.service.SameDayPriceConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SameDayPriceConfigServiceImpl implements SameDayPriceConfigService {

    @Resource
    private SameDayPriceConfigRepository sameDayPriceConfigRepository;
    @Resource
    private SameDayBlacklistAddressConfigService blacklistAddressConfigService;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private GoodsCategoryApi goodsCategoryApi;

    @Override
    public void stateSwitch(StateSwitchRequest request) {
        String sameDayPriceConfigId = request.getSameDayPriceConfigId();
        String state = request.getState();
        if (StringUtils.isBlank(sameDayPriceConfigId) || StringUtils.isBlank(state)) {
            throw new BusException("Id和状态不为能为空");
        }
        SameDayPriceConfigStateEnum stateEnum = SameDayPriceConfigStateEnum.fromCode(state);
        if (stateEnum == null) {
            throw new BusException("状态值只能是active或inactive");
        }
        SameDayPriceConfig config = sameDayPriceConfigRepository.findBySameDayPriceConfigId(getId(sameDayPriceConfigId));
        if (config == null) {
            throw new BusException("未找到对应的配置, sameDayPriceConfigId=" + sameDayPriceConfigId);
        }
        int price = config.getPrice();
        if (price == 0) {
            throw new BusException("未设置价格，无法开通");
        }
        config.setActivationState(state);
        sameDayPriceConfigRepository.save(config);
    }

    @Override
    public void modifyPrice(ModifyPriceRequest request) {
        Assert.notNull(request, "request is null");
        String sameDayPriceConfigId = request.getSameDayPriceConfigId();
        SameDayPriceConfig config = sameDayPriceConfigRepository.findBySameDayPriceConfigId(getId(request.getSameDayPriceConfigId()));
        if (config == null) {
            throw new BusException("未找到对应的配置, sameDayPriceConfigId=" + sameDayPriceConfigId);
        }
        config.setPrice(request.getPrice());
        sameDayPriceConfigRepository.save(config);
    }


    @Override
    public synchronized void add(SaveRequest request)  {
        Long level1GoodsCategoryId = request.getLevel1GoodsCategoryId();
        List<SameDayPriceConfig> configs = sameDayPriceConfigRepository.findAllByLevel1GoodsCategoryId(level1GoodsCategoryId);
        if (CollectionUtils.isNotEmpty(configs)) {
            throw new BusException("类目「" + request.getLevel1GoodsCategoryName() + "」已存在配置，请勿重复添加");
        }
        SameDayPriceConfig config = new SameDayPriceConfig();
        BeanUtils.copyProperties(request, config);
        config.setSameDayPriceConfigId(SnowFlakeGenerator.INSTANCE.generate());
        // 默认是【安装】（serviceTypeId=4），灯具为 【灯具安装】（serviceTypeId=18）
        config.setServiceTypeId(4L);
        if (level1GoodsCategoryId == 2L) {
            config.setServiceTypeId(18L);
        }
        config.setActivationState(SameDayPriceConfigStateEnum.INACTIVE.getCode());
        sameDayPriceConfigRepository.save(config);
    }


    @Override
    public void modify(SaveRequest request) {
        String configId = request.getSameDayPriceConfigId();
        List<SaveRequest.AddressConfig> addressConfigs = request.getAddressConfigs();
        Assert.notBlank(configId, "configId is null");
        Assert.notEmpty(addressConfigs, "address is empty");
        SameDayPriceConfig config = sameDayPriceConfigRepository.findBySameDayPriceConfigId(getId(configId));
        Assert.notNull(config, "未找到对应的配置, sameDayPriceConfigId=" + configId);
        List<SameDayPriceConfig.AddressConfig> configs = addressConfigs.stream().map(addressConfig -> {
            SameDayPriceConfig.AddressConfig address = new SameDayPriceConfig.AddressConfig();
            address.setDistrictId(addressConfig.getDistrictId());
            address.setDistrictName(addressConfig.getDistrictName());
            return address;
        }).collect(Collectors.toList());
        config.setAddressConfigs(configs);
        Integer provinceCount = request.getProvinceCount();
        if (provinceCount != null && provinceCount > 0) {
            config.setProvinceCount(provinceCount);
        }
        sameDayPriceConfigRepository.save(config);
    }


    @Override
    public SimplePageInfo<PricePageListResponse> pageList(Long level1GoodsCategoryId, String activationState, int pageNum, int pageSize) {
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        if (level1GoodsCategoryId != null) {
            criteria.and(SameDayPriceConfig.Fields.level1GoodsCategoryId).is(level1GoodsCategoryId);
        }
        if (StringUtils.isNotBlank(activationState)) {
            criteria.and(SameDayPriceConfig.Fields.activationState).is(activationState);
        }
        Query query = new Query(criteria);
        long count = mongoTemplate.count(query, SameDayPriceConfig.class);
        if (count > 0) {
            PageRequest pageRequest = new PageRequest(pageNum - 1, pageSize, Sort.Direction.DESC, BaseDocument.Fields.createTime);
            query.with(pageRequest);
            List<SameDayPriceConfig> configs = mongoTemplate.find(query, SameDayPriceConfig.class);
            List<PricePageListResponse> list = new ArrayList<>();
            configs.forEach(config -> {
                PricePageListResponse response = new PricePageListResponse();
                BeanUtils.copyProperties(config, response);
                response.setSameDayPriceConfigId(config.getSameDayPriceConfigId() + "");
                list.add(response);
            });
            PageImpl<SameDayPriceConfig> page = new PageImpl<>(configs, pageRequest, count);
            SimplePageInfo<PricePageListResponse> resultPage = new SimplePageInfo<>(list);
            resultPage.setPages(page.getTotalPages());
            resultPage.setPageNum(page.getNumber() + 1);
            resultPage.setPageSize(page.getSize());
            resultPage.setTotal(count);
            return resultPage;
        }
        return new SimplePageInfo<>();
    }

    @Override
    public PriceDetailResponse detail(String sameDayPriceConfigId) {
        Assert.notBlank(sameDayPriceConfigId, "sameDayPriceConfigId is blank");
        SameDayPriceConfig config = sameDayPriceConfigRepository.findBySameDayPriceConfigId(getId(sameDayPriceConfigId));
        if (config == null || config.isDel()) {
            return null;
        }
        PriceDetailResponse response = new PriceDetailResponse();
        BeanUtils.copyProperties(config, response);
        response.setSameDayPriceConfigId(config.getSameDayPriceConfigId() + "");
        List<SameDayPriceConfig.AddressConfig> addressConfigs = config.getAddressConfigs();
        List<SaveRequest.AddressConfig> addressConfigList = new ArrayList<>();
        addressConfigs.forEach(addressConfig -> {
            SaveRequest.AddressConfig ac = new SaveRequest.AddressConfig();
            BeanUtils.copyProperties(addressConfig, ac);
            addressConfigList.add(ac);
        });
        response.setAddressConfigs(addressConfigList);
        return response;
    }


    @Override
    public List<Level1GoodsCategoryResponse> level1GoodsCategory() {
        List<GoodsCategoryLevel1Resp> level1GoodsCategoryList = goodsCategoryApi.getAllLevel1GoodsCategoryList();
        if (CollectionUtils.isEmpty(level1GoodsCategoryList)) {
            return Collections.emptyList();
        }
        List<Level1GoodsCategoryResponse> resultList = level1GoodsCategoryList.stream().map(e -> {
            Level1GoodsCategoryResponse response = new Level1GoodsCategoryResponse();
            response.setLevel1GoodsCategoryId(e.getGoodsCategoryId());
            response.setLevel1GoodsCategoryName(e.getGoodsCategoryName());
            return response;
        }).collect(Collectors.toList());

        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        Query query = new Query(criteria);
        query.fields().include(SameDayPriceConfig.Fields.level1GoodsCategoryId);
        List<SameDayPriceConfig> sameDayPriceConfigs = mongoTemplate.find(query, SameDayPriceConfig.class);
        if (CollectionUtils.isEmpty(sameDayPriceConfigs)) {
            return resultList;
        }
        Set<Long> level1GoodsCategoryIds = sameDayPriceConfigs.stream().map(SameDayPriceConfig::getLevel1GoodsCategoryId).collect(Collectors.toSet());
        resultList.removeIf(next -> level1GoodsCategoryIds.contains(next.getLevel1GoodsCategoryId()));
        return resultList;
    }


    @Override
    public GetPriceResponse getPrice(GetPriceRequest request) {
        Long level1GoodsCategoryId = request.getLevel1GoodsCategoryId();
        String districtId = request.getDistrictId();
        String addressDetail = request.getAddressDetail();
        GetPriceResponse response = new GetPriceResponse();
        // 先判断是否为黑名单
        boolean blackAddress = blacklistAddressConfigService.isBlackAddress(level1GoodsCategoryId, districtId, addressDetail);
        if (blackAddress) {
            response.setReasonForPriceIsNull("详细地址包含黑名单关键字");
            return response;
        }

        // 再查找价格
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(SameDayPriceConfig.Fields.level1GoodsCategoryId).is(level1GoodsCategoryId)
                .and(SameDayPriceConfig.Fields.activationState).is(SameDayPriceConfigStateEnum.ACTIVE.getCode())
                .and(SameDayPriceConfig.Fields.addressConfigs + PunConstant.DOT + "districtId").is(districtId);
        SameDayPriceConfig priceConfig = mongoTemplate.findOne(new Query(criteria), SameDayPriceConfig.class);
        if (priceConfig == null) {
            log.warn("未配置当日装价格或状态未开通，level1GoodsCategoryId={}, districtId={}", level1GoodsCategoryId, districtId);
            response.setReasonForPriceIsNull("未配置当日装价格或状态未开通");
        } else {
            response.setPrice(priceConfig.getPrice());
        }
        return response;
    }

    private long getId(String id) {
        try {
            return Long.parseLong(id);
        } catch (Exception e) {
            throw new BusException("id格式不正确，无法转换为Long类型");
        }
    }
}
