package com.wanshifu.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.fee.center.domain.biz.AutoReceiveOrderGuidePriceBizRule;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.constant.GlobalRedisKeyConstant;
import com.wanshifu.fee.center.domain.constant.MQTagConstant;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.document.FeeRuleDraft;
import com.wanshifu.fee.center.domain.document.PriceUploadComparisonAlert;
import com.wanshifu.fee.center.domain.dto.ExpressInfo;
import com.wanshifu.fee.center.domain.enums.*;
import com.wanshifu.fee.center.domain.request.FeeRuleBatchOperationReq;
import com.wanshifu.fee.center.domain.request.FeeRuleDraftModifyReq;
import com.wanshifu.fee.center.domain.request.FeeRulePageReq;
import com.wanshifu.fee.center.domain.request.FeeRuleServicePageReq;
import com.wanshifu.fee.center.domain.request.feeRule.DeleteByBizIdReq;
import com.wanshifu.fee.center.domain.request.feeRule.QueryByBizIdReq;
import com.wanshifu.fee.center.domain.request.feeRule.QueryByBizIdResp;
import com.wanshifu.fee.center.domain.response.FeeRulePageResp;
import com.wanshifu.fee.center.domain.response.FeeRuleServicePageResp;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.framework.rocketmq.autoconfigure.model.DelayMessage;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.repository.FeeRuleDraftRepository;
import com.wanshifu.repository.FeeRuleRepository;
import com.wanshifu.service.FeeRuleDraftService;
import com.wanshifu.service.FeeRuleService;
import com.wanshifu.infrastructure.utils.CommonUtils;
import com.wanshifu.infrastructure.utils.PatternUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @author: Chen Yong
 * @create: 2023-09-11 14:42
 * @description: 计价规则草稿service impl
 */
@Service
@Slf4j
public class FeeRuleDraftServiceImpl implements FeeRuleDraftService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private FeeRuleDraftRepository feeRuleDraftRepository;
    @Resource
    private FeeRuleRepository feeRuleRepository;

    @Resource
    private FeeRuleService feeRuleService;
    @Resource
    private RocketMqSendService rocketMqSendService;

    @Value("${wanshifu.rocketMQ.fee-center-service-general-topic}")
    private String feeCenterServiceGeneralTopic;
    @Qualifier("redisHelper")
    @Autowired
    private RedisHelper redisHelper;


    @Override
    public void batchDelete(FeeRuleBatchOperationReq req) {
        List<FeeRuleDraft> draftList = getFeeRuleDrafts(req);
        if (CollectionUtils.isEmpty(draftList)) {
            return;
        }
        Set<String> draftIdSet = draftList.stream().map(FeeRuleDraft::getId).collect(Collectors.toSet());
        feeRuleDraftRepository.deleteAllByIdIn(draftIdSet);
    }

    @Override
    public FeeRuleDraft modify(FeeRuleDraftModifyReq req) {
        Long feeRuleDraftId = req.getFeeRuleDraftId();
        FeeRuleDraft byFeeRuleDraftId = feeRuleDraftRepository.findByFeeRuleDraftId(feeRuleDraftId);
        if(Objects.isNull(byFeeRuleDraftId)){
            throw new BusException("FeeRuleDraft不存在");
        }
        byFeeRuleDraftId.setCalculateRuleData(req.getCalculateRuleData());
        byFeeRuleDraftId.setModifyTime(new Date());
        byFeeRuleDraftId.setUpdateBy(CommonUtils.getCurrentLoginName());
        return feeRuleDraftRepository.save(byFeeRuleDraftId);
    }

    @Override
//    @Transactional
    @Async
    public void batchReview(FeeRuleBatchOperationReq req) {
        String sceneCode = req.getSceneCode();
        List<FeeRuleDraft> draftList = getFeeRuleDrafts(req);
        if (CollectionUtils.isEmpty(draftList)) {
            log.info("没有草稿");
            return;
        }
        Date now = new Date();
        String currentLoginName = CommonUtils.getCurrentLoginName();
        List<FeeRule> feeRuleList = new ArrayList<>();
        for (FeeRuleDraft draft : draftList) {
            // 重复性校验，唯一性校验规则：服务+价格维度+业务id+地区+定价id（templateId），如果唯一，则用新的覆盖旧的
            // 要加上bizId
            String divisionType = draft.getBizRule().get(AutoReceiveOrderGuidePriceBizRule.Fields.divisionType);
            // 如果价格维度为空或非法，则跳过，不入库
            if (StringUtils.isBlank(divisionType)) {
                log.info("价格维度为空，不入库");
                continue;
            }
            DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(divisionType);
            if (Objects.isNull(divisionTypeEnum)) {
                log.warn("价格维度{}非法，请检查...", divisionType);
                continue;
            }
            Criteria criteria = Criteria.where(FeeRule.Fields.bizRule + PunConstant.DOT + "serviceId").is(draft.getBizRule().get("serviceId"))
                    .and(FeeRule.Fields.bizRule + PunConstant.DOT + "divisionType").is(divisionType)
                    .and(FeeRule.Fields.sceneCode).is(sceneCode)
                    .and(FeeRule.Fields.templateId).is(draft.getTemplateId())
                    .and(BaseDocument.Fields.del).is(false);
            // 在填了id的时候才需要id
            String bizType = draft.getBizRule().get(CommonBizRule.Fields.bizType);
            String userId = draft.getBizRule().get(CommonBizRule.Fields.userId);
            String masterId = draft.getBizRule().get(CommonBizRule.Fields.masterId);
            if (StringUtils.isNotBlank(bizType)) {
                BizIdTypeEnum bizIdTypeEnum = BizIdTypeEnum.fromCode(bizType);
                if (BizIdTypeEnum.USER_ID.equals(bizIdTypeEnum)) {
                    if (StringUtils.isBlank(userId)) {
                        throw new BusException("场景需要用户Id，但草稿缺失用户Id");
                    }
                    criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.userId).is(userId);
                } else if (BizIdTypeEnum.MASTER_ID.equals(bizIdTypeEnum)) {
                    if (StringUtils.isBlank(masterId)) {
                        throw new BusException("场景需要师傅Id，但草稿缺失师傅Id");
                    }
                    criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterId).is(masterId);
                }
            }
            // 对bizTag,唯一性校验
            String bizTag = draft.getBizRule().get("bizTag");
            if (StringUtils.isNotBlank(bizTag)){
                criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "bizTag").is(bizTag);
            }
            // 如果是全国，则不需要通过divisionId检查
            switch (divisionTypeEnum) {
                case PROVINCE:
                    criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "level1DivisionId").is(draft.getBizRule().get("level1DivisionId"));
                    break;
                case CITY:
                    criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "level2DivisionId").is(draft.getBizRule().get("level2DivisionId"));
                    break;
                case DISTRICT:
                    criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "level3DivisionId").is(draft.getBizRule().get("level3DivisionId"));
                    break;
                case STREET:
                    criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "level4DivisionId").is(draft.getBizRule().get("level4DivisionId"));
                    break;
            }
            Query query = Query.query(criteria);

            mongoTemplate.remove(query, FeeRule.class);

//            List<FeeRule> drafts = mongoTemplate.find(query, FeeRule.class);
//            if (CollectionUtils.isNotEmpty(drafts)) {
////                feeRuleRepository.deleteAllByIdIn(drafts.stream().map(FeeRule::getId).collect(Collectors.toSet()));
//                feeRuleRepository.delete(drafts);
//            }
            FeeRule feeRule = new FeeRule();
            BeanUtils.copyProperties(draft, feeRule);
            feeRule.setId(null);
            feeRule.setFeeRuleId(SnowFlakeGenerator.INSTANCE.generate());
            feeRule.setStatus(RuleStatusEnum.ACTIVE.code);
            feeRule.setCreateTime(now);
            feeRule.setModifyTime(now);
            feeRule.setCreateBy(currentLoginName);
            feeRule.setUpdateBy(currentLoginName);
            feeRule.setMasterInputPriceDouble(draft.getMasterInputPriceDouble());
            ExpressInfo expressInfo = draft.getCalculateRuleData().getExpressInfo();
            if (expressInfo != null && CollectionUtils.isNotEmpty(expressInfo.getExpressInfoUnits())) {
                String numberExpress = expressInfo.getExpressInfoUnits().get(0).getNumberExpress();
                if (StringUtils.isBlank(numberExpress)) {
                    throw new BusException("计价规则中的计价公式中的数量表达式不能为空");
                }
                feeRule.getBizRule().put(ExpressParamEnum.AP_SKU_NUMBER.bizKey, numberExpress.trim());
            }
            feeRuleList.add(feeRule);
//            feeRuleRepository.save(feeRule);
//            feeRuleDraftRepository.delete(draft);
        }
        // 批量操作
        feeRuleRepository.save(feeRuleList);
        feeRuleDraftRepository.delete(draftList);
    }


    @Override
    public SimplePageInfo<FeeRulePageResp> getFeeRuleDraftPage(FeeRulePageReq req) {
        Long level2DivisionId = req.getLevel2DivisionId();
        Long level3DivisionId = req.getLevel3DivisionId();
        Long level4DivisionId = req.getLevel4DivisionId();
        String bizId = req.getBizId();
        String bizTag = req.getBizTag();
        String priceComparisonStatus = req.getPriceComparisonStatus();
        Criteria criteria = Criteria.where(FeeRuleDraft.Fields.sceneCode).is(req.getSceneCode())
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.createTime).gte(req.getStartTime()).lte(req.getEndTime());
        if (StringUtils.isNotBlank(req.getServiceId())) {
            criteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).regex(PatternUtils.toEscapeStr(req.getServiceId()));
        }
        if (req.getGoodsCategoryId() != null) {
            criteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "goodsCategoryId").is(req.getGoodsCategoryId());
        }
        if (req.getBatchTaskId() != null) {
            criteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "batchTaskId").is(req.getBatchTaskId());
        }
        if (StringUtils.isNotBlank(req.getFeeTypeTag())) {
            criteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "feeTypeTag").is(req.getFeeTypeTag());
        }
        if (level2DivisionId != null) {
            criteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId).is(level2DivisionId);
        }
        if (level3DivisionId != null) {
            criteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level3DivisionId).is(level3DivisionId);
        }
        if (level4DivisionId != null) {
            criteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level4DivisionId).is(level4DivisionId);
        }
        if (com.wanshifu.framework.utils.StringUtils.isNotBlank(bizId)) {
            criteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizId).is(bizId);
        }
        if (com.wanshifu.framework.utils.StringUtils.isNotBlank(bizTag)) {
            criteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizTag).is(bizTag);
        }
        if (StringUtils.isNotBlank(priceComparisonStatus)) {
            criteria.and(FeeRuleDraft.Fields.priceUploadComparisonAlert + PunConstant.DOT + PriceUploadComparisonAlert.Fields.priceComparisonStatus).is(priceComparisonStatus);
        }

        Query query = Query.query(criteria);
        Pageable pageable = new PageRequest(req.getPageNum() - 1, req.getPageSize());
        long totalCount = mongoTemplate.count(query, FeeRuleDraft.class);
        query.with(pageable);
        List<FeeRuleDraft> draftList = mongoTemplate.find(query, FeeRuleDraft.class);
        if (CollectionUtils.isEmpty(draftList)) {
            return null;
        }
        List<FeeRulePageResp> respList = new ArrayList<>();
        for (FeeRuleDraft draft : draftList) {
            FeeRulePageResp resp = JSONObject.parseObject(JSON.toJSONString(draft.getBizRule()), FeeRulePageResp.class);
            resp.setId(draft.getId());
            resp.setFeeRuleDraftId(draft.getFeeRuleDraftId());
            resp.setCalculateRuleData(draft.getCalculateRuleData());
            resp.setTemplateId(draft.getTemplateId());
            resp.setModifyTime(draft.getModifyTime());
            resp.setUpdateBy(draft.getUpdateBy());
            resp.setPriceUploadComparisonAlert(draft.getPriceUploadComparisonAlert());
            respList.add(resp);
        }
        PageImpl<FeeRulePageResp> page = new PageImpl<>(respList, pageable, totalCount);
        SimplePageInfo<FeeRulePageResp> resultPage = new SimplePageInfo<>(respList);
        resultPage.setPages(page.getTotalPages());
        resultPage.setPageNum(page.getNumber() + 1);
        resultPage.setPageSize(page.getSize());
        resultPage.setTotal(totalCount);
        return resultPage;
    }


    @Override
    public SimplePageInfo<FeeRuleServicePageResp> getFeeRuleDraftServicePage(FeeRuleServicePageReq req) {
//        return feeRuleService.getFeeRuleServicePage(req, "feeRuleDraft");
        Criteria criteria = Criteria.where(FeeRuleDraft.Fields.sceneCode).is(req.getSceneCode())
                .and(BaseDocument.Fields.del).is(false);
        String serviceName = req.getServiceName();
        if (StringUtils.isNotBlank(serviceName)) {
            criteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.serviceName).regex(Pattern.compile(".*" + Pattern.quote(req.getServiceName()) + ".*"));
        }
        String goodsCategoryId = req.getGoodsCategoryId();
        if (StringUtils.isNotBlank(goodsCategoryId)) {
            criteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.goodsCategoryId).is(goodsCategoryId);
        }
        String bizId = req.getBizId();
        if (StringUtils.isNotBlank(bizId)) {
            criteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.bizId).is(bizId);
        }
//        Query query = Query.query(criteria);
        Aggregation feeRuleDraftAggregation = Aggregation.newAggregation(
                Aggregation.match(criteria), // 查询条件
                Aggregation.group(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.serviceId,
                        FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.divisionType).first("$$ROOT").as("info"),
                Aggregation.replaceRoot("$info"),
                Aggregation.count().as("count")
        );
        Pageable pageable = new PageRequest(req.getPageNum() - 1, req.getPageSize());
//        Long count = mongoTemplate.aggregate(feeRuleDraftAggregation, "feeRuleDraft", JSONObject.class).getUniqueMappedResult().getLong("count");
        JSONObject feeRuleDraft = mongoTemplate.aggregate(feeRuleDraftAggregation, "feeRuleDraft", JSONObject.class).getUniqueMappedResult();
        if (feeRuleDraft == null) {
            return null;
        }
        Long count = feeRuleDraft.getLong("count");
        if (count > 0) {
            Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.createTime));
            feeRuleDraftAggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria), // 查询条件
                    Aggregation.group(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.serviceId,
                            FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.divisionType).first("$$ROOT").as("info"),
                    Aggregation.replaceRoot("$info"),
                    Aggregation.sort(sort),
                    Aggregation.skip((req.getPageNum() - 1) * req.getPageSize()),
                    Aggregation.limit(req.getPageSize())
            );
        }

//        long totalCount = mongoTemplate.count(query, FeeRuleDraft.class);
//        query.with(pageable);
        AggregationResults<FeeRuleDraft> aggregate = mongoTemplate.aggregate(feeRuleDraftAggregation, "feeRuleDraft", FeeRuleDraft.class);
//        List<FeeRuleDraft> draftList = mongoTemplate.find(query, FeeRuleDraft.class);
        if (Objects.isNull(aggregate)) {
            return null;
        }
        List<FeeRuleDraft> draftList = aggregate.getMappedResults();
        if (CollectionUtils.isEmpty(draftList)) {
            return null;
        }
        List<FeeRuleServicePageResp> respList = new ArrayList<>();
        for (FeeRuleDraft draft : draftList) {
            FeeRuleServicePageResp resp = JSONObject.parseObject(JSON.toJSONString(draft.getBizRule()), FeeRuleServicePageResp.class);
            resp.setId(draft.getId());
            respList.add(resp);
        }
        PageImpl<FeeRuleServicePageResp> page = new PageImpl<>(respList, pageable, count);
        SimplePageInfo<FeeRuleServicePageResp> resultPage = new SimplePageInfo<>(respList);
        resultPage.setPages(page.getTotalPages());
        resultPage.setPageNum(resultPage.getPageNum());
        resultPage.setPageSize(resultPage.getPageSize());
        resultPage.setTotal(count);
        return resultPage;
    }


    @Override
    public int deleteByCondition(DeleteByBizIdReq req) {
//        String sceneCode = req.getSceneCode();
//        String bizId = req.getBizId();
//        String bizTag = req.getBizTag();
//        String divisionType = req.getDivisionType();
//        String serviceId = req.getServiceId();
//        if (StringUtils.isBlank(serviceId) || com.wanshifu.framework.utils.StringUtils.isBlank(sceneCode) || com.wanshifu.framework.utils.StringUtils.isBlank(bizId) || com.wanshifu.framework.utils.StringUtils.isBlank(divisionType)) {
//            throw new BusException("场景编码、业务id、地区类型、服务id不能为空");
//        }
//        Criteria criteria = Criteria.where(FeeRuleDraft.Fields.sceneCode).is(sceneCode)
////                .and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizId).is(bizId)
//                .and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(divisionType);
//        if (com.wanshifu.framework.utils.StringUtils.isNotBlank(bizTag)) {
//            criteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizTag).is(bizTag);
//        }
//        List<Criteria> bizIdCriteriaList = new ArrayList<>();
//        bizIdCriteriaList.add(Criteria.where(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizId).is(bizId));
//        bizIdCriteriaList.add(Criteria.where(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.userId).is(bizId));
//        bizIdCriteriaList.add(Criteria.where(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterId).is(bizId));
//        criteria.orOperator(bizIdCriteriaList.toArray(new Criteria[0]));
        Query query = feeRuleService.getDeleteQueryByBizId(req);
        return mongoTemplate.remove(query, FeeRuleDraft.class).getN();
    }


    @Override
    public List<QueryByBizIdResp> getByBizIdGroupByCondition(QueryByBizIdReq req) {
        Query query = feeRuleService.getQueryByBizIdReq(req);
        List<FeeRuleDraft> feeRuleDraftList = mongoTemplate.find(query, FeeRuleDraft.class);
        if (CollectionUtils.isEmpty(feeRuleDraftList)) {
            return Collections.emptyList();
        }
        return feeRuleDraftList.stream().map(
                feeRuleDraft -> feeRuleService.getQueryByBizIdResp(feeRuleDraft.getSceneCode(), feeRuleDraft.getBizRule())
        ).distinct().collect(Collectors.toList());
    }

    private List<FeeRuleDraft> getFeeRuleDrafts(FeeRuleBatchOperationReq req) {
        Criteria criteria = Criteria.where(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "serviceId").is(req.getServiceId())
                .and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "divisionType").is(req.getDivisionType())
                .and(FeeRuleDraft.Fields.sceneCode).is(req.getSceneCode())
                .and(BaseDocument.Fields.del).is(false);
        String bizId = req.getBizId();
        if (StringUtils.isNotBlank(bizId)) {
            List<Criteria> criteriaList = new ArrayList<>();
            Criteria criteria1 = criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.userId).is(bizId);
            Criteria criteria2 = criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterId).is(bizId);
            Criteria criteria3 = criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizId).is(bizId);
            criteriaList.add(criteria1);
            criteriaList.add(criteria2);
            criteriaList.add(criteria3);
            criteria.orOperator(criteriaList.toArray(new Criteria[0]));
        }
        Query query = Query.query(criteria);
        List<FeeRuleDraft> draftList = mongoTemplate.find(query, FeeRuleDraft.class);
        return draftList;
    }


    /**
     * 在6点到23点之间返回当前时间到23点之间的毫秒数，否则返回0，
     * @return long 毫秒数
     */
    private long getIdleTimeMs() {
        // 使用Asia/Shanghai标识北京时间
        ZoneId beijingZone = ZoneId.of("Asia/Shanghai");
        ZonedDateTime now = ZonedDateTime.now(beijingZone);
        LocalTime currentTime = now.toLocalTime();

        LocalTime nightTime = LocalTime.of(23, 0);
        LocalTime morningTime = LocalTime.of(6, 0);

        if (currentTime.isBefore(morningTime)) {
            return 0;
        }

        if (currentTime.isBefore(nightTime)) {
            return ChronoUnit.MILLIS.between(currentTime, nightTime);
        }

        return 0;
    }


    @Override
    public void sendMessagePreGenerateMessage(String sceneCode) {
        String preGenerateRedisKey = GlobalRedisKeyConstant.PRE_GENERATE_KEY_PREFIX + sceneCode;
        String preGenerateRedisValue = redisHelper.get(preGenerateRedisKey);
        if (StringUtils.isNotBlank(preGenerateRedisValue)) {
            return;
        }

        DelayMessage delayMessage = new DelayMessage();
        delayMessage.setDelayTime(getIdleTimeMs());
        delayMessage.setTopic(feeCenterServiceGeneralTopic);
        delayMessage.setBody(sceneCode);
        delayMessage.setKey(UUID.randomUUID().toString());
        delayMessage.setTag(MQTagConstant.PRE_GENERATE);
        rocketMqSendService.sendDelayMessage(delayMessage);

        redisHelper.set(preGenerateRedisKey, "preGenerating", 24 * 60 * 60);
    }


}
