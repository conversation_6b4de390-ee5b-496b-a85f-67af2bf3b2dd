package com.wanshifu.service.impl;

import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.ServiceData;
import com.wanshifu.fee.center.domain.document.SyncBlacklistStrategy;
import com.wanshifu.fee.center.domain.enums.SyncBlacklistStrategyStatusEnum;
import com.wanshifu.fee.center.domain.request.strategy.SyncBlacklistStrategyBatchUpdateStatusReq;
import com.wanshifu.fee.center.domain.request.strategy.SyncBlacklistStrategyPageReq;
import com.wanshifu.fee.center.domain.request.strategy.SyncBlacklistStrategyPageResp;
import com.wanshifu.fee.center.domain.request.strategy.SyncBlacklistStrategySaveReq;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.repository.SyncBlacklistStrategyRepository;
import com.wanshifu.service.SyncBlacklistStrategyService;
import com.wanshifu.infrastructure.utils.PatternUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static org.springframework.data.mongodb.core.query.Criteria.where;

@Service
public class SyncBlacklistStrategyServiceImpl implements SyncBlacklistStrategyService {

    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private SyncBlacklistStrategyRepository syncBlacklistStrategyRepository;

    @Override
    public void add(SyncBlacklistStrategySaveReq req) {
        SyncBlacklistStrategy strategy = new SyncBlacklistStrategy();
        BeanUtils.copyProperties(req, strategy);
        strategy.setStrategyId(SnowFlakeGenerator.INSTANCE.generate());
        Date now = new Date();
        strategy.setCreateTime(now);
        strategy.setModifyTime(now);
        strategy.setStatus(SyncBlacklistStrategyStatusEnum.ENABLE.code);
        syncBlacklistStrategyRepository.save(strategy);
    }

    @Override
    public void modify(SyncBlacklistStrategySaveReq req) {
        String strategyId = req.getStrategyId();
        String strategyName = req.getStrategyName();
        Criteria criteria = Criteria.where(SyncBlacklistStrategy.Fields.strategyId).ne(Long.valueOf(strategyId))
                .and(SyncBlacklistStrategy.Fields.strategyName).is(strategyName);
        SyncBlacklistStrategy syncBlacklistStrategy = mongoTemplate.findOne(new Query(criteria), SyncBlacklistStrategy.class);
        if (syncBlacklistStrategy != null) {
            throw new BusException("策略名称重复");
        }

        SyncBlacklistStrategy strategy = syncBlacklistStrategyRepository.findByStrategyId(Long.valueOf(strategyId));
        if (strategy == null) {
            throw new BusException("策略id有误");
        }

        BeanUtils.copyProperties(req, strategy);
        strategy.setModifyTime(new Date());
        syncBlacklistStrategyRepository.save(strategy);
    }

    @Override
    public void delete(Long strategyId) {
        syncBlacklistStrategyRepository.deleteByStrategyId(strategyId);
    }


    @Override
    public void batchUpdateStatus(SyncBlacklistStrategyBatchUpdateStatusReq req) {
        Criteria criteria = Criteria.where(SyncBlacklistStrategy.Fields.strategyId)
                .in(req.getStrategyIds().stream().map(Long::valueOf).collect(Collectors.toSet()));
        Update update = new Update();
        update.set(BaseDocument.Fields.status, req.getStatus());
        update.set(SyncBlacklistStrategy.Fields.operator, req.getOperator());
        mongoTemplate.updateMulti(new Query(criteria), update, SyncBlacklistStrategy.class);
    }


    @Override
    public SimplePageInfo<SyncBlacklistStrategyPageResp> findPage(SyncBlacklistStrategyPageReq req) {
        String sceneCode = req.getSceneCode();
        Integer status = req.getStatus();
        String strategyName = req.getStrategyName();
        Criteria criteria = new Criteria();
        if (StringUtils.isNotBlank(sceneCode)) {
            criteria.and(SyncBlacklistStrategy.Fields.sceneCode).is(sceneCode);
        }
        if (status != null) {
            criteria.and(BaseDocument.Fields.status).is(status);
        }
        if (StringUtils.isNotBlank(strategyName)) {
            criteria.and(SyncBlacklistStrategy.Fields.strategyName).regex(PatternUtils.toFuzzySearch(strategyName));
        }
        // 分页查询
        Integer pageNum = req.getPageNum();
        Integer pageSize = req.getPageSize();
        Query query = new Query(criteria);
        Pageable pageable = new PageRequest(pageNum - 1, pageSize);
        query.with(pageable);
        query.with(new Sort(Sort.Direction.DESC, BaseDocument.Fields.modifyTime));
        List<SyncBlacklistStrategy> strategyList = mongoTemplate.find(query, SyncBlacklistStrategy.class);

        if (CollectionUtils.isEmpty(strategyList)) {
            return new SimplePageInfo<>();
        }

        long count = mongoTemplate.count(query, SyncBlacklistStrategy.class);

        List<SyncBlacklistStrategyPageResp> respList = strategyList.stream().map(strategy -> {
            SyncBlacklistStrategyPageResp resp = new SyncBlacklistStrategyPageResp();
            BeanUtils.copyProperties(strategy, resp);
            resp.setStrategyId(String.valueOf(strategy.getStrategyId()));
            return resp;
        }).collect(Collectors.toList());

        PageImpl<SyncBlacklistStrategyPageResp> page = new PageImpl<>(respList, pageable, count);
        SimplePageInfo<SyncBlacklistStrategyPageResp> resultPage = new SimplePageInfo<>(respList);
        resultPage.setPages(page.getTotalPages());
        resultPage.setPageNum(resultPage.getPageNum());
        resultPage.setPageSize(resultPage.getPageSize());
        resultPage.setTotal(count);
        return resultPage;
    }


    @Override
    public List<SyncBlacklistStrategy> selectListBySceneCode(String sceneCode) {
        if (StringUtils.isBlank(sceneCode)) {
            return Collections.emptyList();
        }
        return syncBlacklistStrategyRepository.findAllBySceneCode(sceneCode);
    }

    @Override
    public SyncBlacklistStrategy detail(Long strategyId) {
        return syncBlacklistStrategyRepository.findByStrategyId(strategyId);
    }

    @Override
    public List<SyncBlacklistStrategy> selectBySceneCodeAndServiceIdAndUserId(String sceneCode, Long serviceId, String userId) {
        Criteria criteria = where(SyncBlacklistStrategy.Fields.sceneCode).is(sceneCode)
                .and(SyncBlacklistStrategy.Fields.serviceDataSet + PunConstant.DOT + ServiceData.Fields.serviceId).is(serviceId)
                .and(SyncBlacklistStrategy.Fields.bizIds).regex("(^|,)" + userId + "(,|$)")
                .and(BaseDocument.Fields.status).is(SyncBlacklistStrategyStatusEnum.ENABLE.code)
                .and(BaseDocument.Fields.del).is(false);


        return mongoTemplate.find(Query.query(criteria), SyncBlacklistStrategy.class);
    }
}
