package com.wanshifu.service.impl.indicator;

import com.wanshifu.fee.center.domain.document.DynamicPricingIndicator;
import com.wanshifu.fee.center.domain.enums.AdjustPriceIndicatorEnum;
import com.wanshifu.fee.center.domain.enums.IndicatorOptionalConditionEnum;
import com.wanshifu.fee.center.domain.response.bigdata.BigDataResp;
import com.wanshifu.fee.center.domain.response.bigdata.OrderAvgPushDistance;
import com.wanshifu.infrastructure.gateway.BigdataGateway;
import com.wanshifu.service.impl.IndicatorContext;
import com.wanshifu.framework.utils.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
@RequiredArgsConstructor
public class AveragePushDistanceIndicatorProvider implements IndicatorProvider {

    private final BigdataGateway bigdataGateway;
    private final IndicatorValueSelector selector;

    @Override
    public boolean supports(AdjustPriceIndicatorEnum indicatorEnum) {
        return AdjustPriceIndicatorEnum.AVERAGE_PUSH_DISTANCE.equals(indicatorEnum);
    }

    @Override
    public List<DynamicPricingIndicator.IndicatorValue> getValues(IndicatorContext context, List<DynamicPricingIndicator> indicators) {
        if (context.getOrderNo() == null) {
            return Collections.emptyList();
        }

        OrderAvgPushDistance orderAvgPushDistance = bigdataGateway.getOrderAvgPushDistance(context.getOrderNo());
        Double avgPushDistance = orderAvgPushDistance.getAvgPushDistance();

        // 1. 收集所有符合距离条件的候选值
        List<DynamicPricingIndicator.IndicatorValue> candidateValues = indicators.stream()
                .flatMap(indicator -> {
                    IndicatorOptionalConditionEnum condition = IndicatorOptionalConditionEnum.fromCode(indicator.getIndicatorOptionalCondition());
                    if (condition == null) {
                        return Stream.empty();
                    }
                    return indicator.getIndicatorValueList().stream()
                            .filter(value -> condition.matches(avgPushDistance, value));
                })
                .collect(Collectors.toList());

        // 2. 使用选择器筛选出最优的单个值
        Optional<DynamicPricingIndicator.IndicatorValue> bestValue = selector.selectBestValue(candidateValues);
        return bestValue.map(Collections::singletonList).orElse(Collections.emptyList());
    }
}

