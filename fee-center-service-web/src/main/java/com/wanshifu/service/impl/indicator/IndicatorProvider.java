package com.wanshifu.service.impl.indicator;

import com.wanshifu.fee.center.domain.document.DynamicPricingIndicator;
import com.wanshifu.fee.center.domain.enums.AdjustPriceIndicatorEnum;
import com.wanshifu.service.impl.IndicatorContext;

import java.util.List;

/**
 * 指标值提供者策略接口
 */
public interface IndicatorProvider {

    /**
     * 判断此策略是否支持给定的指标类型
     * @param indicatorEnum 指标枚举
     * @return 如果支持则返回 true
     */
    boolean supports(AdjustPriceIndicatorEnum indicatorEnum);

    /**
     * 获取指标值
     * @param context 计价上下文
     * @param indicators 适用于此策略的指标定义
     * @return 计算或获取到的指标值列表
     */
    List<DynamicPricingIndicator.IndicatorValue> getValues(IndicatorContext context, List<DynamicPricingIndicator> indicators);
}
