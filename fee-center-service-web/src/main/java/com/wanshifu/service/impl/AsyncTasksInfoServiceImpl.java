package com.wanshifu.service.impl;

import com.wanshifu.fee.center.domain.document.AsyncTasksInfo;
import com.wanshifu.fee.center.domain.enums.AsyncTaskStatusEnum;
import com.wanshifu.fee.center.domain.enums.AsyncTaskTypeEnum;
import com.wanshifu.repository.AsyncTasksInfoRepository;
import com.wanshifu.service.AsyncTasksInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
public class AsyncTasksInfoServiceImpl implements AsyncTasksInfoService {

    @Resource
    private AsyncTasksInfoRepository asyncTasksInfoRepository;

    @Override
    public AsyncTaskStatusEnum getStatusByTaskId(String taskId, AsyncTaskTypeEnum taskType) {
        AsyncTasksInfo asyncTasksInfo = asyncTasksInfoRepository.findByTaskIdAndTaskType(taskId, taskType.getCode());
        if (asyncTasksInfo != null) {
            return AsyncTaskStatusEnum.getByCode(asyncTasksInfo.getTaskStatus());
        }
        return null;
    }

    @Override
    public void createTask(String taskId, AsyncTaskTypeEnum taskType) {
        AsyncTasksInfo asyncTasksInfo = asyncTasksInfoRepository.findByTaskIdAndTaskType(taskId, taskType.getCode());
        if (asyncTasksInfo == null) {
            asyncTasksInfo = new AsyncTasksInfo();
        }
        asyncTasksInfo.setTaskId(taskId);
        asyncTasksInfo.setTaskType(taskType.getCode());
        asyncTasksInfo.setTaskStatus(AsyncTaskStatusEnum.RUNNING.getCode());
        asyncTasksInfo.setCreateTime(new Date());
        asyncTasksInfoRepository.save(asyncTasksInfo);
    }

    @Override
    public void updateStatus(String taskId, AsyncTaskTypeEnum taskType, AsyncTaskStatusEnum status, String failReason) {
        AsyncTasksInfo asyncTasksInfo = asyncTasksInfoRepository.findByTaskIdAndTaskType(taskId, taskType.getCode());
        if (asyncTasksInfo == null) {
            return;
        }
        asyncTasksInfo.setTaskStatus(status.getCode());
        asyncTasksInfo.setModifyTime(new Date());
        asyncTasksInfo.setFailReason(failReason);
        asyncTasksInfoRepository.save(asyncTasksInfo);
    }

}
