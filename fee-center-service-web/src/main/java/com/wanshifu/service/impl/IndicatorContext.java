package com.wanshifu.service.impl;

import com.wanshifu.fee.center.domain.document.DynamicFeeRule;
import com.wanshifu.fee.center.domain.request.calculate.CalculateServiceDynamicPriceRequest;
import com.wanshifu.infrastructure.utils.DynamicParamParser;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.List;

@Getter
public class IndicatorContext {

    private final CalculateServiceDynamicPriceRequest request;
    private final List<DynamicFeeRule> rules;
    private final DynamicParamParser paramParser;
    private final String orderNo;
    private final LocalDateTime orderSubmitTime;
    private final String streetId;

    public IndicatorContext(CalculateServiceDynamicPriceRequest request, List<DynamicFeeRule> rules) {
        this.request = request;
        this.rules = rules;
        this.paramParser = new DynamicParamParser(request.getIndicatorParamList());
        this.orderNo = this.paramParser.getValue(com.wanshifu.fee.center.domain.enums.DynamicParamKeyEnum.ORDER_NO.getCode());
        this.orderSubmitTime = this.paramParser.getLocalDateTimeValue(com.wanshifu.fee.center.domain.enums.DynamicParamKeyEnum.ORDER_SUBMIT_TIME.getCode());
        this.streetId = this.paramParser.getValue(com.wanshifu.fee.center.domain.enums.DynamicParamKeyEnum.STREET_ID.getCode());
    }
}
