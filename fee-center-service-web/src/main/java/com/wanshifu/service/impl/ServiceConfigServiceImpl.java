package com.wanshifu.service.impl;

import cn.hutool.core.util.StrUtil;
import com.wanshifu.adapter.api.ServiceApi;
import com.wanshifu.adapter.dto.service.ServiceQueryByGoodsCategoryReq;
import com.wanshifu.adapter.dto.service.ServiceQueryResp;
import com.wanshifu.domain.request.serviceconfig.GetServiceRequest;
import com.wanshifu.domain.response.serviceconfig.ServiceBaseInfo;
import com.wanshifu.fee.center.domain.document.SceneInfo;
import com.wanshifu.fee.center.domain.enums.SkuTypeEnum;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.repository.SceneInfoRepository;
import com.wanshifu.service.ServiceConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ServiceConfigServiceImpl implements ServiceConfigService {

    private final SceneInfoRepository sceneInfoRepository;
    private final ServiceApi serviceApi;

    @Override
    public ServiceBaseInfo getService(GetServiceRequest request) {
        String sceneCode = request.getSceneCode();
        SceneInfo sceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(sceneCode);
        if (sceneInfo == null) {
            throw new BusException(StrUtil.format("场景编码不存在:{}", sceneCode));
        }

        String skuType = sceneInfo.getSkuType();
        SkuTypeEnum skuTypeEnum = SkuTypeEnum.fromCode(skuType);
        if (skuTypeEnum == null) {
            throw new BusException(StrUtil.format("场景的【标准sku类型】异常:{}", skuType));
        }
        ServiceQueryByGoodsCategoryReq req = new ServiceQueryByGoodsCategoryReq();
        req.setGoodsCategoryId(request.getGoodsCategoryId());
        req.setServiceTypeId(request.getServiceTypeId());
        req.setServiceModelId(Long.valueOf(skuTypeEnum.serviceModelId));
        ServiceQueryResp resp = serviceApi.serviceQueryByGoodsCategory(req);
        if (resp == null) {
            return null;
        }
        ServiceBaseInfo serviceBaseInfo = new ServiceBaseInfo();
        serviceBaseInfo.setServiceId(resp.getServiceId());
        serviceBaseInfo.setServiceName(resp.getServiceName());
        return serviceBaseInfo;
    }
}
