package com.wanshifu.service.impl.indicator;

import com.alibaba.fastjson.JSON;
import com.wanshifu.fee.center.domain.document.DynamicPricingIndicator;
import com.wanshifu.fee.center.domain.enums.IndicatorOptionalConditionEnum;
import com.wanshifu.fee.center.domain.response.bigdata.GetIndicatorValueRequest;
import com.wanshifu.fee.center.domain.response.bigdata.GetIndicatorValueResponse;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.infrastructure.gateway.BigdataGateway;
import com.wanshifu.service.impl.IndicatorContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 自动计算指标获取器
 * <p>
 * 职责：仅负责从大数据平台获取需要自动计算的指标定义列表，不进行任何业务逻辑处理。
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AutoCalculatedIndicatorProvider {

    private final BigdataGateway bigdataGateway;

    public List<DynamicPricingIndicator> fetchAutoCalculatedIndicators(IndicatorContext context) {
        Map<Long, List<DynamicPricingIndicator>> indicatorsByServiceId = groupAutoCalculatedIndicatorsByServiceId(context);
        if (indicatorsByServiceId.isEmpty()) {
            return Collections.emptyList();
        }

        List<GetIndicatorValueRequest.ServiceIndicator> serviceIndicators = indicatorsByServiceId.entrySet().stream()
                .map(entry -> {
                    GetIndicatorValueRequest.ServiceIndicator si = new GetIndicatorValueRequest.ServiceIndicator();
                    si.setServiceId(entry.getKey());
                    si.setIndicatorCodes(entry.getValue().stream().map(DynamicPricingIndicator::getIndicatorCode).distinct().collect(Collectors.toList()));
                    return si;
                })
                .collect(Collectors.toList());

        GetIndicatorValueRequest request = buildRequest(context, serviceIndicators);
        log.info("Fetching auto-calculated indicators from BigData. Request: {}", JSON.toJSONString(request));

        GetIndicatorValueResponse response = bigdataGateway.getIndicatorValue(request);

        if (response == null || CollectionUtils.isEmpty(response.getServiceIndicators())) {
            log.warn("Failed to fetch auto-calculated indicators from BigData. Response: {}", JSON.toJSONString(response));
            return Collections.emptyList();
        }

        // 原封不动地返回从大数据获取的指标定义列表
        return response.getServiceIndicators().stream()
                .filter(Objects::nonNull)
                .flatMap(serviceResult -> Optional.ofNullable(serviceResult.getIndicators()).orElse(Collections.emptyList()).stream())
                .collect(Collectors.toList());
    }

    private Map<Long, List<DynamicPricingIndicator>> groupAutoCalculatedIndicatorsByServiceId(IndicatorContext context) {
        return context.getRules().stream()
                .filter(rule -> rule.getServiceDataList() != null && rule.getDynamicPricingIndicators() != null)
                .flatMap(rule -> rule.getServiceDataList().stream()
                        .flatMap(serviceData -> rule.getDynamicPricingIndicators().stream()
                                .filter(indicator -> IndicatorOptionalConditionEnum.AUTO_CALCULATE.getCode().equals(indicator.getIndicatorOptionalCondition()))
                                .map(indicator -> new java.util.AbstractMap.SimpleEntry<>(serviceData.getServiceId(), indicator))))
                .collect(Collectors.groupingBy(
                        java.util.AbstractMap.SimpleEntry::getKey,
                        Collectors.mapping(java.util.AbstractMap.SimpleEntry::getValue, Collectors.toList())
                ));
    }

    private GetIndicatorValueRequest buildRequest(IndicatorContext context, List<GetIndicatorValueRequest.ServiceIndicator> serviceIndicators) {
        GetIndicatorValueRequest request = new GetIndicatorValueRequest();
        request.setSceneCode(context.getRequest().getSceneCode());
        request.setSubSceneCode(context.getRequest().getSubSceneCode());
        request.setUserId(context.getRequest().getUserId());
        request.setServiceIndicators(serviceIndicators);
        return request;
    }
}

