package com.wanshifu.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.wanshifu.adapter.api.ServiceApi;
import com.wanshifu.adapter.api.ServiceCategoryApi;
import com.wanshifu.adapter.dto.service.ServiceDetail;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.*;
import com.wanshifu.fee.center.domain.enums.ApplyTypeEnum;
import com.wanshifu.fee.center.domain.enums.MappingStatusEnum;
import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.enums.TemplateStatusEnum;
import com.wanshifu.fee.center.domain.request.mapping.*;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.utils.CommonUtils;
import com.wanshifu.infrastructure.utils.PatternUtils;
import com.wanshifu.repository.FeeTemplateMappingRepository;
import com.wanshifu.repository.FeeTemplateRepository;
import com.wanshifu.repository.SceneInfoRepository;
import com.wanshifu.service.FeeTemplateMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.PatternSyntaxException;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FeeTemplateMappingServiceImpl implements FeeTemplateMappingService {

    @Resource
    private FeeTemplateMappingRepository mappingRepository;
    @Resource
    private SceneInfoRepository sceneInfoRepository;
    @Resource
    private FeeTemplateRepository feeTemplateRepository;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private ServiceApi serviceApi;
    @Resource
    private ServiceCategoryApi serviceCategoryApi;

    @Override
    public void add(AddRequest addRequest) {
        FeeTemplateMapping mapping = initFeeTemplateMapping(addRequest);
        mappingRepository.save(mapping);
    }

    @Override
    public void batchAdd(List<AddRequest> addRequestList) {
        List<FeeTemplateMapping> mappingList = addRequestList.stream().map(this::initFeeTemplateMapping).collect(Collectors.toList());
        mappingRepository.save(mappingList);
    }


    @Override
    public void update(UpdateRequest updateRequest) {

        String sceneCode = updateRequest.getSceneCode();
        Long mappingId = updateRequest.getMappingId();
        FeeTemplateMapping mapping = mappingRepository.findOneByMappingId(mappingId);
        if (mapping == null) {
            throw new BusException("mappingId有误");
        }
        FeeTemplateMapping.TemplateInfo target = mapping.getTarget();
        checkMappingDuplication(updateRequest, target.getSceneCode());

        FeeTemplateMapping.TemplateInfo source = new FeeTemplateMapping.TemplateInfo();
        BeanUtil.copyProperties(updateRequest, source);
        // 当设置映射 源场景为 “基础服务计价属性” 时，源场景采用 采用目标场景的信息
        if ("base_service_price".equals(sceneCode)) {
            sceneCode = target.getSceneCode();
            source.setMappingType("基础服务计价属性");
        }
        SceneInfo sceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(sceneCode);
        if (sceneInfo == null) {
            throw new BusException(StrUtil.format("场景【{}】有误", sceneCode));
        }
        source.setSceneCode(sceneCode);
        source.setSceneName(sceneInfo.getSceneName());
        source.setSceneSkuType(sceneInfo.getSkuType());
        mapping.setStatus(MappingStatusEnum.PASS.code);

        Long templateId = updateRequest.getTemplateId();
        if (templateId != null && templateId > 0) {
            FeeTemplate template = feeTemplateRepository.findByTemplateId(templateId);
            if (template == null) {
                throw new BusException(StrUtil.format("源模板【templateId={}】有误", templateId));
            }
            source.setTemplateId(templateId);
            Map<String, String> bizRule = template.getBizRule();
            source.setTemplateSkuType(bizRule.get(CommonBizRule.Fields.skuType));
            source.setServiceId(bizRule.get(CommonBizRule.Fields.serviceId));
            source.setServiceName(bizRule.get(CommonBizRule.Fields.serviceName));
            source.setSkuNo(bizRule.get(CommonBizRule.Fields.skuNo));
            source.setSkuAttributePathName(bizRule.get(CommonBizRule.Fields.skuAttributePathName));
            source.setAttributeDisplayName(bizRule.get(CommonBizRule.Fields.attributeDisplayName));
            source.setSkuNumberPathNo(bizRule.get(CommonBizRule.Fields.skuNumberPathNo));
            source.setSkuNumberName(bizRule.get(CommonBizRule.Fields.skuNumberName));
            source.setFeeUnit(bizRule.get(CommonBizRule.Fields.feeUnit));
        }

        mapping.setSource(source);
        List<String> applyTypes = mapping.getApplyTypes();
        if (CollectionUtil.isEmpty(applyTypes)) {
            mapping.setApplyTypes(Collections.singletonList(ApplyTypeEnum.PRICE_SYNC.getCode()));
        }

        mappingRepository.save(mapping);
    }


    @Override
    public void delete(String mappingId) {
        Criteria criteria = Criteria.where(FeeTemplateMapping.Fields.mappingId).is(Long.valueOf(mappingId));
        Update update = new Update();
        update.set(BaseDocument.Fields.del, true);
        update.set(BaseDocument.Fields.modifyTime, new Date());
        update.set(BaseDocument.Fields.updateBy, CommonUtils.getCurrentLoginName());
        mongoTemplate.updateFirst(new Query(criteria), update, FeeTemplateMapping.class);
    }


    @Override
    public Page<MappingPageListResponse> getPageList(MappingPageListRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("Request cannot be null");
        }

        validatePageParameters(request.getPageNum(), request.getPageSize());

        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);

        handleApplyTypeCode(criteria, request.getApplyTypeCode());

        processTemplateInfo(criteria, request.getTarget(), FeeTemplateMapping.Fields.target);
        processTemplateInfo(criteria, request.getSource(), FeeTemplateMapping.Fields.source);

        Sort sort = new Sort(Sort.Direction.DESC, BaseDocument.Fields.modifyTime);
        PageRequest pageRequest = new PageRequest(request.getPageNum() - 1, request.getPageSize(), sort);
        Query query = Query.query(criteria);

        try {
            long count = mongoTemplate.count(query, FeeTemplateMapping.class);
            if (count > 0) {
                query.with(pageRequest);
                List<FeeTemplateMapping> mappings = mongoTemplate.find(query, FeeTemplateMapping.class);
                if (CollectionUtils.isEmpty(mappings)) {
                    return new PageImpl<>(Collections.emptyList(), pageRequest, count);
                }
                List<MappingPageListResponse> responses = convertToResponses(mappings);
                return new PageImpl<>(responses, pageRequest, count);
            } else {
                return new PageImpl<>(Collections.emptyList(), pageRequest, count);
            }
        } catch (Exception e) {
            // Log the exception and handle it appropriately
            throw new RuntimeException("Database operation failed", e);
        }
    }


    @Override
    public void copyMapping(CopyMappingRequest request) {
        /*
        2025-02-18 改动，具体代码体现在getServiceId方法中
        1、target部分复制
        1.1 所选内容的target的场景sku类型，与复制到的target的场景sku相同，则将所选内容的服务ID对应写入新对象的服务ID
        1.2 所选内容的target的场景sku类型，与复制到的target的场景sku不相同，则将所选内容的服务ID转化后（即 一口价转报价招标 或 反过来），对应写入新对象的服务ID

        2、source部分复制
        2.1 source的sceneCode的sku类型，与复制到的source的sceneCode的sku类型相同，则将选中的source的sceneCode的serviceId写入 新对象的serviceId
        2.2 source的sceneCode的sku类型，与复制到的source的sceneCode的sku类型不相同，则将选中的source的sceneCode的serviceId转化后（即 一口价转报价招标 或 反过来），对应写入新对象的serviceId
        2.3 如果复制到的source的场景为“基础服务计价属性”，维持现状逻辑
         */
        Set<Long> mappingIds = request.getMappingIds();
        String targetSceneCode = request.getTargetSceneCode();
        SceneInfo targetSceneInfo = getSceneInfo(targetSceneCode, "目标场景不合法，targetSceneCode=");
        String sourceSceneCode = request.getSourceSceneCode();
        SceneInfo sourceSceneInfo = getSceneInfo(sourceSceneCode, "源场景不合法，sourceSceneCode=");

        Criteria criteria = Criteria.where(FeeTemplateMapping.Fields.mappingId).in(mappingIds)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(MappingStatusEnum.PASS.code);
        List<FeeTemplateMapping> mappingList = mongoTemplate.find(Query.query(criteria), FeeTemplateMapping.class);
        if (CollectionUtils.isEmpty(mappingList)) {
            throw new BusException("已选映射不存在或状态 不为「审核通过」");
        }

        List<FeeTemplateMapping> newMappings = new ArrayList<>();
        AtomicInteger failCount = new AtomicInteger();
        StringBuilder failMessage = new StringBuilder();
        for (FeeTemplateMapping mapping : mappingList) {
            FeeTemplateMapping newMapping = new FeeTemplateMapping();
            BeanUtils.copyProperties(mapping, newMapping);
            newMapping.setMappingId(SnowFlakeGenerator.INSTANCE.generate());
            newMapping.setId(null);

            // 处理target部分
            FeeTemplateMapping.TemplateInfo target = mapping.getTarget();
            FeeTemplateMapping.TemplateInfo newTarget = new FeeTemplateMapping.TemplateInfo();
            BeanUtils.copyProperties(target, newTarget);
            String targetServiceId;
            try {
                targetServiceId = getServiceId(mapping, newTarget, targetSceneInfo.getSkuType(), "target");
            } catch (BusException e) {
                if (failCount.get() <= 10) {
                    failMessage.append(e.getMessage()).append("\n");
                }
                continue;
            }
            newMapping.setTarget(newTarget);
            newTarget.setSceneCode(targetSceneCode);
            newTarget.setSceneName(targetSceneInfo.getSceneName());
            String targetSkuNo = newTarget.getSkuNo();
            if (replaceTemplateId(targetSceneCode, targetServiceId, targetSkuNo, failCount, newTarget, "target", failMessage)) {
                continue;
            }

            // source部分，如果已选场景的是“基础服务计价属性”，则不进行替换。
            if (SceneCodeEnum.BASE_SERVICE_PRICE.getCode().equals(sourceSceneCode)) {
                FeeTemplateMapping.TemplateInfo source = newMapping.getSource();
                // 如果source的场景为“基础服务计价属性”，则用目标场景替换源场景。
                source.setSceneCode(newTarget.getSceneCode());
                source.setSceneName(newTarget.getSceneName());
                // 由于场景不再是原有的映射源的，所以需要清空源场景和模板相关的信息
                source.setTemplateId(null);
                source.setTemplateSkuType(null);
                source.setSceneSkuType(source.getSceneSkuType());
                source.setAttributeDisplayName(null);
                newMappings.add(newMapping);
                continue;
            }

            /*
              1. 场景编码替换：如果已选的是“基础服务计价属性”，则不进行替换。否则，已选的source场景编码，替换成复制弹窗的source场景编码。
              2. templateId替换：如果已选的是“基础服务计价属性”，则不进行替换。否则，用目标映射的source_sku_no+target场景编码 查出对应的templateId，
              替换已选的source_templateId，如果查不出来，或者查出来的templateId有多个则不进行替换，并丢弃已选的映射，加入失败数量当中，提示复制映射成功X条，失败X条
             */
            // 处理source
            FeeTemplateMapping.TemplateInfo source = mapping.getSource();
            FeeTemplateMapping.TemplateInfo newSource = new FeeTemplateMapping.TemplateInfo();
            BeanUtils.copyProperties(source, newSource);
            newMapping.setSource(newSource);
            newSource.setSceneCode(sourceSceneCode);
            newSource.setSceneName(sourceSceneInfo.getSceneName());
            String sourceSkuNo = newSource.getSkuNo();
            String sourceServiceId;
            try {
                sourceServiceId = getServiceId(mapping, source, sourceSceneInfo.getSkuType(), "source");
            } catch (BusException e) {
                if (failCount.get() <= 10) {
                    failMessage.append(e.getMessage()).append("\n");
                }
                continue;
            }
            if (replaceTemplateId(sourceSceneCode, sourceServiceId, sourceSkuNo, failCount, newSource, "source", failMessage)) {
                continue;
            }

            newMappings.add(newMapping);
        }

        // 重复性校验
        if (CollectionUtils.isNotEmpty(newMappings)) {
            Iterator<FeeTemplateMapping> iterator = newMappings.iterator();
            while (iterator.hasNext()) {
                FeeTemplateMapping mapping = iterator.next();
                try {
                    FeeTemplateMapping.TemplateInfo source = mapping.getSource();
                    if (source == null) {
                        continue;
                    }
                    SaveBaseRequest saveBaseRequest = new SaveBaseRequest();
                    BeanUtils.copyProperties(source, saveBaseRequest);
                    checkMappingDuplication(saveBaseRequest, mapping.getTarget().getSceneCode());
                } catch (BusException e) {
                    // 收集前10条错误信息返回给前端展示
                    if (failCount.get() <= 10) {
                        failMessage.append(e.getMessage()).append("\n");
                    }
                    failCount.getAndIncrement();
                    log.warn("校验重复性失败，mapping={}, exceptionMessage={}", mapping, e.getMessage());
                    iterator.remove();
                }
            }
        }

        if (CollectionUtils.isNotEmpty(newMappings)) {
            mappingRepository.save(newMappings);
        }

        if (failCount.get() > 0) {
            throw new BusException("复制映射成功" + newMappings.size() + "条，失败" + failCount.get() + "条;\n" + failMessage);
        }
    }


    @Override
    public void setApplyTypes(UpdateMappingApplyTypesRequest request) {
        List<FeeTemplateMapping> mappingList = mappingRepository.findAllByMappingIdIn(
                request.getMappingIdList().stream().map(Long::valueOf).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(mappingList)) {
            throw new BusException("映射不存在");
        }
        mappingList.forEach(
                bizRuleMapping -> {
                    bizRuleMapping.setApplyTypes(request.getApplyTypeList());
//                    bizRuleMapping.setUpdateBy(CommonUtils.getCurrentLoginName());
//                    bizRuleMapping.setModifyTime(new Date());
                });
        mappingRepository.save(mappingList);
    }


    @Override
    public FeeTemplateMapping findOneBySourceTemplateId(Long sourceTemplateId) {
        if (sourceTemplateId == null || sourceTemplateId <= 0) {
            return null;
        }
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(FeeTemplateMapping.Fields.source + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.templateId).is(sourceTemplateId);
        return mongoTemplate.findOne(Query.query(criteria), FeeTemplateMapping.class);
    }


    @Override
    public Page<FeeTemplateMapping> queryPage(MappingQueryPageRequest request) {
        String targetSceneCode = request.getTargetSceneCode();
        List<Long> targetTemplateIdList = request.getTargetTemplateIdList();
        if (CollectionUtils.isEmpty(targetTemplateIdList) || StringUtils.isBlank(targetSceneCode)) {
            return new PageImpl<>(Collections.emptyList());
        }

        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(MappingStatusEnum.PASS.code)
                .and(FeeTemplateMapping.Fields.target + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.sceneCode).is(targetSceneCode)
                .and(FeeTemplateMapping.Fields.target + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.templateId).in(targetTemplateIdList);
        Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.modifyTime));
        PageRequest pageRequest = new PageRequest(request.getPageNum() - 1, request.getPageSize(), sort);
        Query query = Query.query(criteria);
        long count = mongoTemplate.count(query, FeeTemplateMapping.class);

        if (count > 0) {
            query.with(pageRequest);
            List<FeeTemplateMapping> mappings = mongoTemplate.find(query, FeeTemplateMapping.class);
            return new PageImpl<>(mappings, pageRequest, count);
        } else {
            return new PageImpl<>(Collections.emptyList(), pageRequest, count);
        }
    }


    private void validatePageParameters(int pageNum, int pageSize) {
        if (pageNum <= 0 || pageSize <= 0) {
            throw new IllegalArgumentException("Invalid page parameters");
        }
    }

    private void handleApplyTypeCode(Criteria criteria, String applyTypeCode) {
        if (StringUtils.isNotBlank(applyTypeCode)) {
            switch (applyTypeCode) {
                case "none":
                    criteria.and(FeeTemplateMapping.Fields.applyTypes).exists(false);
                    break;
                case "all":
                    // No action needed for "all"
                    break;
                default:
                    criteria.and(FeeTemplateMapping.Fields.applyTypes).is(applyTypeCode);
            }
        }
    }

    private List<MappingPageListResponse> convertToResponses(List<FeeTemplateMapping> mappings) {
        List<MappingPageListResponse> responses = new ArrayList<>(mappings.size());
        for (FeeTemplateMapping mapping : mappings) {
            MappingPageListResponse response = new MappingPageListResponse();
            copyProperties(mapping, response);
            responses.add(response);
        }
        return responses;
    }

    private void copyProperties(FeeTemplateMapping mapping, MappingPageListResponse response) {
        BeanUtils.copyProperties(mapping, response);
        response.setApplyTypes(mapping.getApplyTypes());

        // source不一定存在
        if (mapping.getSource() != null) {
            MappingPageListResponse.TemplateInfo source = new MappingPageListResponse.TemplateInfo();
            BeanUtils.copyProperties(mapping.getSource(), source);
            response.setSource(source);
        }

        MappingPageListResponse.TemplateInfo target = new MappingPageListResponse.TemplateInfo();
        BeanUtils.copyProperties(mapping.getTarget(), target);
        response.setTarget(target);
    }


    private FeeTemplateMapping initFeeTemplateMapping(AddRequest addRequest) {
        FeeTemplateMapping mapping = new FeeTemplateMapping();
        mapping.setMappingId(SnowFlakeGenerator.INSTANCE.generate());
        mapping.setDel(false);
        mapping.setStatus(MappingStatusEnum.AUDIT.code);

        String targetSceneCode = addRequest.getSceneCode();
        Long templateId = addRequest.getTemplateId();
        FeeTemplate template = feeTemplateRepository.findByTemplateId(templateId);
        if (template == null) {
            throw new BusException(StrUtil.format("模板【templateId={}】有误", templateId));
        }

        FeeTemplateMapping.TemplateInfo target = new FeeTemplateMapping.TemplateInfo();
//        BeanUtils.copyProperties(addRequest, target);
        // 场景信息
        SceneInfo sceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(targetSceneCode);
        if (sceneInfo == null) {
            throw new BusException(StrUtil.format("目标场景【{}】有误", targetSceneCode));
        }
        target.setSceneCode(targetSceneCode);
        target.setSceneName(sceneInfo.getSceneName());
        target.setSceneSkuType(sceneInfo.getSkuType());

        // 模板信息
        target.setTemplateId(templateId);
        Map<String, String> bizRule = template.getBizRule();
        target.setTemplateSkuType(bizRule.get(CommonBizRule.Fields.skuType));
        target.setServiceId(bizRule.get(CommonBizRule.Fields.serviceId));
        target.setServiceName(bizRule.get(CommonBizRule.Fields.serviceName));
        target.setSkuNo(bizRule.get(CommonBizRule.Fields.skuNo));
        target.setSkuAttributePathName(bizRule.get(CommonBizRule.Fields.skuAttributePathName));
        target.setAttributeDisplayName(bizRule.get(CommonBizRule.Fields.attributeDisplayName));
        target.setSkuNumberPathNo(bizRule.get(CommonBizRule.Fields.skuNumberPathNo));
        target.setSkuNumberName(bizRule.get(CommonBizRule.Fields.skuNumberName));
        target.setFeeUnit(bizRule.get(CommonBizRule.Fields.feeUnit));

        // 添加的时候保存的是“映射目标”，配置（即更新）的时候保存“映射源”，映射源→映射目标之间 为1对1的关系
        mapping.setTarget(target);
        return mapping;
    }

    public void checkMappingDuplication(SaveBaseRequest saveBaseRequest, String targetSceneCode) {
        if (StringUtils.isBlank(targetSceneCode)) {
            return;
        }
        Long templateId = saveBaseRequest.getTemplateId();
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(MappingStatusEnum.PASS.code)
                // 重复性/唯一性校验 需要加上targetSceneCode！！！
                .and(FeeTemplateMapping.Fields.target + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.sceneCode).is(targetSceneCode);
        if (templateId != null) {
            // 若有templateId，则只需根据templateId判断重复性即可（如果是自定义SKU，则一定有templateId）
            criteria.and(FeeTemplateMapping.Fields.source + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.templateId).is(templateId);
        } else {
            // 若没有templateId，则根据source的 sceneCode+serviceId+skuNo组合唯一查询（因为这种情况一定不存在于自定义SKU）
            criteria.and(FeeTemplateMapping.Fields.source + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.sceneCode).is(saveBaseRequest.getSceneCode())
                    .and(FeeTemplateMapping.Fields.source + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.serviceId).is(saveBaseRequest.getServiceId())
                    .and(FeeTemplateMapping.Fields.source + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.skuNo).is(saveBaseRequest.getSkuNo());
        }
        List<FeeTemplateMapping> mappingList = mongoTemplate.find(Query.query(criteria), FeeTemplateMapping.class);
        if (CollectionUtils.isNotEmpty(mappingList)) {
            FeeTemplateMapping mapping = mappingList.get(0);
            FeeTemplateMapping.TemplateInfo source = mapping.getSource();
            FeeTemplateMapping.TemplateInfo target = mapping.getTarget();
            String message = StrUtil.format("「【映射源】场景：{}，serviceId：{}，skuNo：{}」已存在->【映射目标】「场景：{}，serviceId：{}，skuNo：{}」",
                    source.getSceneName(),
                    source.getServiceId(),
                    source.getSkuNo(),
                    target.getSceneName(),
                    target.getServiceId(),
                    target.getSkuNo());
            throw new BusException(message);
        }
    }


    private void processTemplateInfo(Criteria criteria, MappingPageListRequest.TemplateInfo templateInfo, String prefix) {
        if (templateInfo == null) {
            return;
        }

        if (StringUtils.isNotBlank(templateInfo.getSceneCode())) {
            criteria.and(prefix + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.sceneCode)
                    .is(templateInfo.getSceneCode());
        }

        if (StringUtils.isNotBlank(templateInfo.getServiceName())) {
            try {
                criteria.and(prefix + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.serviceName)
                        .regex(PatternUtils.toFuzzySearch(PatternUtils.toEscapeStr(templateInfo.getServiceName())));
            } catch (PatternSyntaxException e) {
                // 处理正则表达式解析错误
                log.error("Invalid regex pattern for serviceName: {}", templateInfo.getServiceName(), e);
            }
        }

        if (StringUtils.isNotBlank(templateInfo.getSkuNo())) {
            criteria.and(prefix + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.skuNo)
                    .is(templateInfo.getSkuNo());
        }
    }


    private SceneInfo getSceneInfo(String targetToSceneCode, String exceptionMessage) {
        if (SceneCodeEnum.BASE_SERVICE_PRICE.getCode().equals(targetToSceneCode)) {
            SceneInfo sceneInfo = new SceneInfo();
            sceneInfo.setSceneCode(SceneCodeEnum.BASE_SERVICE_PRICE.getCode());
            sceneInfo.setSceneName(SceneCodeEnum.BASE_SERVICE_PRICE.getName());
            return sceneInfo;
        } else {
            SceneInfo targetToSceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(targetToSceneCode);
            if (targetToSceneInfo == null) {
                throw new BusException(exceptionMessage + targetToSceneCode);
            }
            return targetToSceneInfo;
        }
    }


    private String getServiceId(FeeTemplateMapping mapping, FeeTemplateMapping.TemplateInfo templateInfo, String mappingSkuType, String sourceOrTarget) {
        String sceneCode;
        if ("source".equals(sourceOrTarget)) {
            sceneCode = mapping.getSource().getSceneCode();
        } else if ("target".equals(sourceOrTarget)) {
            sceneCode = mapping.getTarget().getSceneCode();
        } else {
            throw new BusException("sourceOrTarget参数错误");
        }

        if (StringUtils.isBlank(sceneCode)) {
            throw new BusException("选中的FeeTemplateMapping" + sourceOrTarget + "的sceneCode为空，历史数据问题，请联系开发");
        }
        SceneInfo sceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(sceneCode);
        if (sceneInfo == null) {
            throw new BusException("选中的FeeTemplateMapping的" + sourceOrTarget + "的sceneCode错误（sceneCode=" + sceneCode + "），历史数据问题，请联系开发");
        }
        String skuType = sceneInfo.getSkuType();
        if (StringUtils.isBlank(skuType)) {
            throw new BusException("选中的FeeTemplateMapping的" + sourceOrTarget + "的sceneCode对应的skuType为空，历史数据问题，请联系开发");
        }
        String serviceId = templateInfo.getServiceId();
        if (StringUtils.isBlank(serviceId)) {
            throw new BusException("选中的FeeTemplateMapping的" + sourceOrTarget + "的serviceId为空，历史数据问题，请联系开发");
        }
        if (!mappingSkuType.equals(skuType)) {
            ServiceDetail serviceDetail = serviceApi.getServiceDetailById(Long.valueOf(serviceId));
            if (serviceDetail == null) {
                throw new BusException("选中的FeeTemplateMapping的" + sourceOrTarget + "的serviceId已失效（serviceId=" + serviceId + "）");
            }
            Long serviceCategoryId = serviceDetail.getServiceCategoryId();
            List<ServiceDetail> serviceDetailList = serviceCategoryApi.getServiceDetailListByServiceCategoryId(serviceCategoryId);
            Long detailServiceId = serviceDetailList.stream()
                    .map(ServiceDetail::getServiceId)
                    .filter(e -> !e.equals(serviceDetail.getServiceId())).findFirst().orElse(null);
            if (detailServiceId == null) {
                throw new BusException("选中的FeeTemplateMapping的" + sourceOrTarget + "的serviceId没有对应的兄弟服务(一口价与报价招标)（serviceId=" + serviceId + "）");
            }
            serviceId = detailServiceId.toString();
        }
        return serviceId;
    }

    private boolean replaceTemplateId(String sceneCode, String serviceId, String skuNo, AtomicInteger failCount,
                                      FeeTemplateMapping.TemplateInfo source, String sourceOrTarget, StringBuilder message) {
        Criteria templateCriteria = Criteria.where(FeeTemplate.Fields.sceneCode).is(sceneCode)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(skuNo)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);
        List<FeeTemplate> feeTemplates = mongoTemplate.find(new Query(templateCriteria), FeeTemplate.class);
        if (CollectionUtils.isEmpty(feeTemplates)) {
            log.warn("找不到匹配的模板，{}SceneCode={},{}ServiceId={},{}SkuNo={}", sourceOrTarget, sceneCode, sourceOrTarget, serviceId, sourceOrTarget, skuNo);
            message.append("找不到匹配的模板，")
                    .append(sourceOrTarget).append("SceneCode=").append(sceneCode)
                    .append(",").append(sourceOrTarget).append("ServiceId=").append(serviceId)
                    .append(",").append(sourceOrTarget).append("SkuNo=").append(skuNo).append(";\n");
            failCount.getAndIncrement();
            return true;
        } else {
            int size = feeTemplates.size();
            if (size > 1) {
                log.warn("找到{}条匹配的模板，{}SceneCode={},{}ServiceId={},{}SkuNo={}", size, sourceOrTarget, sceneCode, sourceOrTarget, serviceId, sourceOrTarget, skuNo);
                message.append("找到").append(size).append("条匹配的模板，")
                        .append(sourceOrTarget).append("SceneCode=").append(sceneCode)
                        .append(",").append(sourceOrTarget).append("ServiceId=").append(serviceId)
                        .append(",").append(sourceOrTarget).append("SkuNo=").append(skuNo).append(";\n");
                failCount.getAndIncrement();
                return true;
            }
            FeeTemplate feeTemplate = feeTemplates.get(0);
            source.setTemplateId(feeTemplate.getTemplateId());
        }
        return false;
    }
}
