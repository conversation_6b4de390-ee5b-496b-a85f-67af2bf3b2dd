package com.wanshifu.service.impl.indicator;

import com.wanshifu.fee.center.domain.document.DynamicPricingIndicator;
import com.wanshifu.fee.center.domain.enums.AdjustPriceIndicatorEnum;
import com.wanshifu.service.impl.IndicatorContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class FixedIndicatorProvider implements IndicatorProvider {

    private final IndicatorValueSelector selector;

    @Override
    public boolean supports(AdjustPriceIndicatorEnum indicatorEnum) {
        return AdjustPriceIndicatorEnum.FIXED.equals(indicatorEnum);
    }

    @Override
    public List<DynamicPricingIndicator.IndicatorValue> getValues(IndicatorContext context, List<DynamicPricingIndicator> indicators) {
        // 1. 收集所有固定调价的候选值
        List<DynamicPricingIndicator.IndicatorValue> candidateValues = indicators.stream()
                .flatMap(indicator -> indicator.getIndicatorValueList().stream())
                .collect(Collectors.toList());

        // 2. 使用选择器筛选出最优的单个值
        Optional<DynamicPricingIndicator.IndicatorValue> bestValue = selector.selectBestValue(candidateValues);
        return bestValue.map(Collections::singletonList).orElse(Collections.emptyList());
    }
}
