package com.wanshifu.service.impl;

import com.wanshifu.fee.center.domain.request.calculate.CalculateAndComparePriceResponse;
import com.wanshifu.repository.CalculateAndComparePriceResponseRepository;
import com.wanshifu.service.CalculateAndComparePriceResponseService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CalculateAndComparePriceResponseServiceImpl implements CalculateAndComparePriceResponseService {

    private final CalculateAndComparePriceResponseRepository calculateAndComparePriceResponseRepository;

    @Async("logTaskExecutor")
    @Override
    public void saveAsync(CalculateAndComparePriceResponse comparePriceResponse) {
        calculateAndComparePriceResponseRepository.save(comparePriceResponse);
    }

}
