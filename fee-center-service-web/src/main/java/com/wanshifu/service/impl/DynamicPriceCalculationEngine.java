package com.wanshifu.service.impl;

import com.wanshifu.fee.center.domain.document.DynamicFeeRule;
import com.wanshifu.fee.center.domain.document.DynamicPricingIndicator;
import com.wanshifu.fee.center.domain.enums.AdjustPriceIndicatorEnum;
import com.wanshifu.fee.center.domain.enums.AdjustPriceTypeEnum;
import com.wanshifu.fee.center.domain.enums.DynamicSymbolEnum;
import com.wanshifu.fee.center.domain.enums.IndicatorOptionalConditionEnum;
import com.wanshifu.fee.center.domain.request.calculate.CalculateServiceDynamicPriceRequest;
import com.wanshifu.fee.center.domain.request.calculate.CalculateServiceDynamicPriceResponse;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.service.impl.indicator.AutoCalculatedIndicatorProvider;
import com.wanshifu.service.impl.indicator.IndicatorProvider;
import com.wanshifu.strategy.apply.PriceAdjuster;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
@RequiredArgsConstructor
public class DynamicPriceCalculationEngine {

    private final List<IndicatorProvider> indicatorProviders;
    private final AutoCalculatedIndicatorProvider autoCalculatedIndicatorProvider;

    public BigDecimal calculate(CalculateServiceDynamicPriceRequest request, List<DynamicFeeRule> rules) {
        IndicatorContext context = new IndicatorContext(request, rules);

        // 1. 获取所有需要处理的指标定义（包括自动计算和非自动计算的）
        List<DynamicPricingIndicator> allIndicators = getAllIndicators(context);

        // 2. 按指标代码分组
        Map<AdjustPriceIndicatorEnum, List<DynamicPricingIndicator>> indicatorsByEnum = allIndicators.stream()
                .map(indicator -> {
                    try {
                        return new AbstractMap.SimpleEntry<>(AdjustPriceIndicatorEnum.fromCode(indicator.getIndicatorCode()), indicator);
                    } catch (Exception e) {
                        log.warn("Invalid indicator code found: {}", indicator.getIndicatorCode());
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(Map.Entry::getKey, Collectors.mapping(Map.Entry::getValue, Collectors.toList())));

        // 3. 将每个分组分发给对应的策略进行处理，并收集所有最终生效的调价项
        List<DynamicPricingIndicator.IndicatorValue> finalValues = indicatorsByEnum.entrySet().stream()
                .flatMap(entry -> {
                    IndicatorProvider provider = findProvider(entry.getKey());
                    return provider.getValues(context, entry.getValue()).stream();
                })
                .collect(Collectors.toList());

        // 4. 如果没有生效的调价项，直接返回原始价格
        if (CollectionUtils.isEmpty(finalValues)) {
            log.warn("No applicable dynamic pricing rule found for request: {}", request);
//            CalculateServiceDynamicPriceResponse response = new CalculateServiceDynamicPriceResponse();
//            response.setDynamicFeeCost(request.getOriginalCost());
            return request.getOriginalCost();
        }

        // 5. 计算总调价金额
        BigDecimal totalAdjustment = calculateTotalAdjustment(finalValues, request.getOriginalCost());

        // 6. 构造响应
//        CalculateServiceDynamicPriceResponse response = new CalculateServiceDynamicPriceResponse();
//        response.setDynamicFeeCost(request.getOriginalCost().add(totalAdjustment));
        return request.getOriginalCost().add(totalAdjustment);
    }

    private List<DynamicPricingIndicator> getAllIndicators(IndicatorContext context) {
        // 获取非自动计算的指标
        List<DynamicPricingIndicator> nonAutoIndicators = context.getRules().stream()
                .flatMap(rule -> rule.getDynamicPricingIndicators().stream())
                .filter(indicator -> !IndicatorOptionalConditionEnum.AUTO_CALCULATE.getCode().equals(indicator.getIndicatorOptionalCondition()))
                .collect(Collectors.toList());

        // 获取自动计算的指标
        List<DynamicPricingIndicator> autoIndicators = autoCalculatedIndicatorProvider.fetchAutoCalculatedIndicators(context);

        // 合并两者
        return Stream.concat(nonAutoIndicators.stream(), autoIndicators.stream()).collect(Collectors.toList());
    }

    private IndicatorProvider findProvider(AdjustPriceIndicatorEnum indicatorEnum) {
        return indicatorProviders.stream()
                .filter(p -> p.supports(indicatorEnum))
                .findFirst()
                .orElseThrow(() -> new IllegalStateException("No provider found for indicator: " + indicatorEnum));
    }

    private BigDecimal calculateTotalAdjustment(List<DynamicPricingIndicator.IndicatorValue> values, BigDecimal originalCost) {
        return values.stream()
                .map(value -> calculateAdjustmentForValue(value, originalCost))
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);
    }

    private BigDecimal calculateAdjustmentForValue(DynamicPricingIndicator.IndicatorValue indicatorValue, BigDecimal cost) {
        DynamicSymbolEnum symbolEnum = DynamicSymbolEnum.fromCode(indicatorValue.getAdjustPriceAction());
        AdjustPriceTypeEnum priceTypeEnum = AdjustPriceTypeEnum.fromCode(indicatorValue.getAdjustPriceType());
        double adjustValue = Optional.ofNullable(indicatorValue.getAdjustValue()).orElse(0.0);

        if (symbolEnum == null || priceTypeEnum == null) {
            log.warn("Invalid adjustment action or type for value: {}", indicatorValue);
            return BigDecimal.ZERO;
        }

        PriceAdjuster adjuster = new PriceAdjuster(priceTypeEnum, symbolEnum, adjustValue, cost.doubleValue());
        return BigDecimal.valueOf(adjuster.getAdjustPrice());
    }
}
