package com.wanshifu.service.impl.indicator;

import com.wanshifu.fee.center.domain.document.DynamicPricingIndicator;
import com.wanshifu.fee.center.domain.enums.AdjustPriceTypeEnum;
import com.wanshifu.framework.utils.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 动态调价指标值筛选器
 * <p>
 * 负责根据核心业务规则从多个候选项中筛选出唯一一个最终生效的调价项。
 */
@Component
public class IndicatorValueSelector {

    /**
     * 筛选逻辑：
     * 1. 如果候选项中同时存在“固定金额”和“百分比”两种类型，则优先选择“百分比”类型。
     * 2. 在同类型中，选择调整值（adjustValue）最小的一个。
     *
     * @param candidateValues 候选的调价指标值列表
     * @return 经过筛选后唯一胜出的调价指标值，如果无有效值则返回 Optional.empty()
     */
    public Optional<DynamicPricingIndicator.IndicatorValue> selectBestValue(List<DynamicPricingIndicator.IndicatorValue> candidateValues) {
        if (CollectionUtils.isEmpty(candidateValues)) {
            return Optional.empty();
        }

        // 过滤掉 null 和 adjustValue 为 null 的项
        List<DynamicPricingIndicator.IndicatorValue> validValues = candidateValues.stream()
                .filter(Objects::nonNull)
                .filter(v -> v.getAdjustValue() != null)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validValues)) {
            return Optional.empty();
        }

        // 按调价类型分组
        Map<String, List<DynamicPricingIndicator.IndicatorValue>> groupedByType = validValues.stream()
                .collect(Collectors.groupingBy(DynamicPricingIndicator.IndicatorValue::getAdjustPriceType));

        // 优先选择百分比类型中最小的
        List<DynamicPricingIndicator.IndicatorValue> percentValues = groupedByType.get(AdjustPriceTypeEnum.PERCENT.getCode());
        if (CollectionUtils.isNotEmpty(percentValues)) {
            return percentValues.stream().min(Comparator.comparing(DynamicPricingIndicator.IndicatorValue::getAdjustValue));
        }

        // 如果没有百分比类型，则选择固定金额类型中最小的
        List<DynamicPricingIndicator.IndicatorValue> fixedValues = groupedByType.get(AdjustPriceTypeEnum.FIXED.getCode());
        if (CollectionUtils.isNotEmpty(fixedValues)) {
            return fixedValues.stream().min(Comparator.comparing(DynamicPricingIndicator.IndicatorValue::getAdjustValue));
        }

        return Optional.empty();
    }
}
