package com.wanshifu.service.impl.indicator;

import com.wanshifu.fee.center.domain.document.DynamicPricingIndicator;
import com.wanshifu.fee.center.domain.enums.AdjustPriceIndicatorEnum;
import com.wanshifu.fee.center.domain.enums.IndicatorOptionalConditionEnum;
import com.wanshifu.service.impl.IndicatorContext;
import com.wanshifu.framework.utils.CollectionUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class OrderTimeElapsedIndicatorProvider implements IndicatorProvider {

    private final IndicatorValueSelector selector;

    @Override
    public boolean supports(AdjustPriceIndicatorEnum indicatorEnum) {
        return AdjustPriceIndicatorEnum.ORDER_TIME_ELAPSED.equals(indicatorEnum);
    }

    @Override
    public List<DynamicPricingIndicator.IndicatorValue> getValues(IndicatorContext context, List<DynamicPricingIndicator> indicators) {
        if (context.getOrderSubmitTime() == null) {
            return Collections.emptyList();
        }

        double effectiveHours = calculateEffectiveHours(context.getOrderSubmitTime(), LocalDateTime.now());

        // 1. 收集所有符合时间条件的候选值
        List<DynamicPricingIndicator.IndicatorValue> candidateValues = indicators.stream()
                .flatMap(indicator -> filterValuesByCondition(indicator, effectiveHours).stream())
                .collect(Collectors.toList());

        // 2. 使用选择器筛选出最优的单个值
        Optional<DynamicPricingIndicator.IndicatorValue> bestValue = selector.selectBestValue(candidateValues);
        return bestValue.map(Collections::singletonList).orElse(Collections.emptyList());
    }

    private List<DynamicPricingIndicator.IndicatorValue> filterValuesByCondition(DynamicPricingIndicator indicator, double targetValue) {
        if (CollectionUtils.isEmpty(indicator.getIndicatorValueList())) {
            return Collections.emptyList();
        }
        IndicatorOptionalConditionEnum condition = IndicatorOptionalConditionEnum.fromCode(indicator.getIndicatorOptionalCondition());
        if (condition == null) {
            return Collections.emptyList();
        }
        return indicator.getIndicatorValueList().stream()
                .filter(value -> condition.matches(targetValue, value))
                .collect(Collectors.toList());
    }

    private double calculateEffectiveHours(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null || end.isBefore(start)) {
            return 0.0;
        }
        long totalMinutes = Duration.between(start, end).toMinutes();
        long blockedMinutes = 0;
        LocalDate date = start.toLocalDate();
        while (!date.isAfter(end.toLocalDate())) {
            LocalDateTime blockStart = LocalDateTime.of(date, LocalTime.of(23, 0));
            LocalDateTime blockEnd = LocalDateTime.of(date.plusDays(1), LocalTime.of(8, 0));
            LocalDateTime overlapStart = start.isAfter(blockStart) ? start : blockStart;
            LocalDateTime overlapEnd = end.isBefore(blockEnd) ? end : blockEnd;
            if (overlapEnd.isAfter(overlapStart)) {
                blockedMinutes += Duration.between(overlapStart, overlapEnd).toMinutes();
            }
            date = date.plusDays(1);
        }
        long effectiveMinutes = totalMinutes - blockedMinutes;
        return BigDecimal.valueOf(effectiveMinutes)
                .divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP)
                .doubleValue();
    }
}
