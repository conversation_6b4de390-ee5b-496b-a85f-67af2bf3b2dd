package com.wanshifu.service.impl;

import cn.hutool.core.lang.Assert;
import com.wanshifu.domain.request.samedayblacklistaddress.*;
import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.SameDayBlacklistAddressConfig;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.utils.PatternUtils;
import com.wanshifu.repository.SameDayBlacklistAddressConfigRepository;
import com.wanshifu.service.SameDayBlacklistAddressConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SameDayBlacklistAddressConfigServiceImpl implements SameDayBlacklistAddressConfigService {

    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private SameDayBlacklistAddressConfigRepository configRepository;


    @Override
    public void add(AddRequest request) {
        List<AddRequest.GoodsCategoryInfo> goodsCategoryInfos = request.getGoodsCategoryInfos();
        Assert.notEmpty(goodsCategoryInfos, "商品一级类目信息不能为空");
        for (AddRequest.GoodsCategoryInfo goodsCategoryInfo : goodsCategoryInfos) {
            Long level1GoodsCategoryId = goodsCategoryInfo.getLevel1GoodsCategoryId();
            String districtId = request.getDistrictId();
            String keywords = request.getKeywords();
            Assert.notBlank(districtId, "districtId is blank");
            Assert.notNull(level1GoodsCategoryId, "level1GoodsCategoryId is null");
            Assert.notBlank(keywords, "keywords is blank");

            Set<String> keywordSet = Arrays.stream(StringUtils.split(keywords, "\n")).map(String::trim).collect(Collectors.toSet());
            SameDayBlacklistAddressConfig config = configRepository.findByLevel1GoodsCategoryIdAndDistrictId(level1GoodsCategoryId, districtId);
            if (config == null) {
                config = new SameDayBlacklistAddressConfig();
                BeanUtils.copyProperties(request, config);
                config.setConfigId(SnowFlakeGenerator.INSTANCE.generate());
                // 默认是【安装】，灯具为 【灯具安装】
                config.setServiceTypeId(4L);
                if (level1GoodsCategoryId == 2L) {
                    config.setServiceTypeId(18L);
                }
                config.setKeywords(keywordSet);
                config.setLevel1GoodsCategoryId(goodsCategoryInfo.getLevel1GoodsCategoryId());
                config.setLevel1GoodsCategoryName(goodsCategoryInfo.getLevel1GoodsCategoryName());
            } else {
                Set<String> oldKeywords = config.getKeywords();
                BeanUtils.copyProperties(request, config);
                oldKeywords.addAll(keywordSet);
                config.setKeywords(oldKeywords);
            }
            configRepository.save(config);
        }

    }

    @Override
    public void modify(ModifyRequest request) {
        String configId = request.getConfigId();
        Assert.notBlank(configId, "configId is null");
        SameDayBlacklistAddressConfig config = configRepository.findByConfigId(getId(configId));
        Assert.notNull(config, "未找到对应的配置, configId=" + configId);
        Set<String> keywordSet = Arrays.stream(StringUtils.split(request.getKeywords(), "\n")).map(String::trim).collect(Collectors.toSet());
        config.setKeywords(keywordSet);
        configRepository.save(config);
    }

    @Override
    public SimplePageInfo<PageListResponse> pageList(Long level1GoodsCategoryId, String districtId, String keyword, int pageNum, int pageSize) {
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        if (level1GoodsCategoryId != null) {
            criteria.and(SameDayBlacklistAddressConfig.Fields.level1GoodsCategoryId).is(level1GoodsCategoryId);
        }
        if (StringUtils.isNotBlank(districtId)) {
            criteria.and(SameDayBlacklistAddressConfig.Fields.districtId).is(districtId);
        }
        if (StringUtils.isNotBlank(keyword)) {
            criteria.and(SameDayBlacklistAddressConfig.Fields.keywords).regex(PatternUtils.toFuzzySearch(keyword));
        }

        Query query = new Query(criteria);
        long count = mongoTemplate.count(query, SameDayBlacklistAddressConfig.class);
        if (count > 0) {
            PageRequest pageRequest = new PageRequest(pageNum - 1, pageSize, Sort.Direction.DESC, BaseDocument.Fields.createTime);
            query.with(pageRequest);
            List<SameDayBlacklistAddressConfig> configs = mongoTemplate.find(query, SameDayBlacklistAddressConfig.class);
            List<PageListResponse> list = new ArrayList<>();
            configs.forEach(config -> {
                PageListResponse response = new PageListResponse();
                BeanUtils.copyProperties(config, response);
                response.setConfigId(config.getConfigId() + "");
                list.add(response);
            });
            PageImpl<SameDayBlacklistAddressConfig> page = new PageImpl<>(configs, pageRequest, count);
            SimplePageInfo<PageListResponse> resultPage = new SimplePageInfo<>(list);
            resultPage.setPages(page.getTotalPages());
            resultPage.setPageNum(page.getNumber() + 1);
            resultPage.setPageSize(page.getSize());
            resultPage.setTotal(count);
            return resultPage;
        }
        return new SimplePageInfo<>();
    }


    @Override
    public DetailResponse detail(String configId) {
        Assert.notBlank(configId, "configId is blank");
        SameDayBlacklistAddressConfig config = configRepository.findByConfigId(getId(configId));
        Assert.notNull(config, "未找到对应的配置, configId=" + configId);
        DetailResponse response = new DetailResponse();
        BeanUtils.copyProperties(config, response);
        response.setConfigId(config.getConfigId() + "");
        return response;
    }


    @Override
    public void deleteByConfigId(DeleteRequest request) {
        configRepository.deleteByConfigId(getId(request.getConfigId()));
    }


    @Override
    public boolean isBlackAddress(Long level1GoodsCategoryId, String districtId, String addressDetail) {
        SameDayBlacklistAddressConfig config = configRepository.findByLevel1GoodsCategoryIdAndDistrictId(level1GoodsCategoryId, districtId);
        if (config == null) {
            return false;
        }
        Set<String> keywords = config.getKeywords();
        for (String keyword : keywords) {
            if (StringUtils.containsIgnoreCase(addressDetail, keyword)) {
                log.warn("地址【{}】包含黑名单关键字【{}】", addressDetail, keyword);
                return true;
            }
        }
        return false;
    }


    private long getId(String id) {
        try {
            return Long.parseLong(id);
        } catch (Exception e) {
            throw new BusException("id格式不正确，无法转换为Long类型");
        }
    }
}
