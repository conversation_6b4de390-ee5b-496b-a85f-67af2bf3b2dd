package com.wanshifu.service.impl;

import cn.hutool.core.util.StrUtil;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.document.CalculateResult;
import com.wanshifu.fee.center.domain.document.SceneInfo;
import com.wanshifu.fee.center.domain.dto.ServiceDTO;
import com.wanshifu.fee.center.domain.dto.ServicePriceDto;
import com.wanshifu.fee.center.domain.request.calculate.*;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.repository.SceneInfoRepository;
import com.wanshifu.service.CalculateAndComparePriceResponseService;
import com.wanshifu.service.DynamicFeeRuleService;
import com.wanshifu.service.PriceCalculatorService;
import com.wanshifu.strategy.apply.PriceCalculator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PriceCalculatorServiceImpl implements PriceCalculatorService {

    private final PriceCalculator priceCalculator;
    private final SceneInfoRepository sceneInfoRepository;
    private final CalculateAndComparePriceResponseService calculateAndComparePriceResponseService;
    private final MongoTemplate mongoTemplate;
    private final DynamicFeeRuleService dynamicFeeRuleService;

    @Override
    public StandardPricingResponse standardPricing(StandardPricingRequest request) {
        return priceCalculator.standardPricing(request);
    }

    /**
     * 不比价原因：
     * 1、无销售价
     * 2、无底价
     * 3、计价sku数量不一致
     * 4、计价sku数量一致，但计价skuNo不一致
     */
    @Override
    public CalculateAndComparePriceResponse calculateAndComparePrice(CalculateAndComparePriceRequest request) {
        String salePriceSceneCode = request.getSalePriceSceneCode();
        SceneInfo saleSceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(salePriceSceneCode);
        if (saleSceneInfo == null) {
            throw new BusException(StrUtil.format("销售价场景编码({})有误", salePriceSceneCode));
        }

        CalculateAndComparePriceResponse comparePriceResponse = new CalculateAndComparePriceResponse();
        comparePriceResponse.setCalculateAndCompareTaskId(SnowFlakeGenerator.INSTANCE.generate());
        List<String> priceSceneCodeList = request.getPriceSceneCodeList();
        List<ServiceDTO> serviceDtoList = request.getServiceDtoList();

        // 计算销售价
        StandardPricingRequest pricingRequest = new StandardPricingRequest();
        BeanUtils.copyProperties(request, pricingRequest);
        pricingRequest.setSceneCodeList(priceSceneCodeList);
        StandardPricingResponse salePriceResult;
        salePriceResult = priceCalculator.standardPricing(pricingRequest);
        comparePriceResponse.setSalePriceResult(salePriceResult);
        BigDecimal saleCost = salePriceResult.getCost();

        try {
            // 如果销售价为null或0，则表明计价失败，直接返回
            if (saleCost == null || !salePriceResult.isSuccess() || BigDecimal.ZERO.compareTo(saleCost) == 0) {
                if (CollectionUtils.isEmpty(salePriceResult.getServiceResultList())) {
                    // FIXME 这里是因为保存到MongoDB时会报唯一键冲突（salePriceResult.serviceResultList.calculateResultList.calculateResultId:null）
                    // 也就是说 哪怕serviceResultList或calculateResultList为空，也会出现唯一键冲突
                    // 以下为临时解决方案，需要找到长期优雅的解决方案
                    List<StandardPricingResponse.ServiceResult> serviceResultList = new ArrayList<>();
                    StandardPricingResponse.ServiceResult serviceResult = new StandardPricingResponse.ServiceResult();
                    List<CalculateResult> calculateResultList = new ArrayList<>();
                    CalculateResult calculateResult = new CalculateResult();
                    calculateResult.setCalculateResultId(SnowFlakeGenerator.INSTANCE.generate());
                    calculateResultList.add(calculateResult);
                    serviceResult.setCalculateResultList(calculateResultList);
                    serviceResultList.add(serviceResult);
                    salePriceResult.setServiceResultList(serviceResultList);
                }
                calculateAndComparePriceResponseService.saveAsync(comparePriceResponse);
                return comparePriceResponse;
            }

            final String reasonIsSalePriceAbsence = "无销售价";
            final String reasonIsFloorPriceAbsence = "无底价";
            final String reasonIsFloorPriceSceneInvalid = "底价场景编码无效";
            final String reasonIsFloorPricePaused = "底价场景已暂停";
            // 计算底价

            List<CalculateAndComparePriceResponse.ServicePriceDifference> servicePriceDifferenceList = new ArrayList<>();
            List<StandardPricingResponse.ServiceResult> saleServiceResultList = salePriceResult.getServiceResultList();
            for (int i = 0; i < saleServiceResultList.size(); i++) {
                StandardPricingResponse.ServiceResult saleServiceResult = saleServiceResultList.get(i);
                String sceneCode = saleServiceResult.getSceneCode();
                if (!salePriceSceneCode.equals(sceneCode)) {
                    CalculateAndComparePriceResponse.ServicePriceDifference priceDifference = getServicePriceDifferenceIsZero(saleServiceResult.getServiceId(), reasonIsSalePriceAbsence);
                    servicePriceDifferenceList.add(priceDifference);
                    continue;
                }

                String floorPriceSceneCode = request.getFloorPriceSceneCode();
                SceneInfo floorSceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(floorPriceSceneCode);
                if (floorSceneInfo == null) {
                    log.error("底价场景编码({})有误", floorPriceSceneCode);
                    CalculateAndComparePriceResponse.ServicePriceDifference priceDifference = getServicePriceDifferenceIsZero(saleServiceResult.getServiceId(), reasonIsFloorPriceSceneInvalid);
                    servicePriceDifferenceList.add(priceDifference);
                    continue;
                }

                if (floorSceneInfo.getEnable() == null || !floorSceneInfo.getEnable()) {
                    CalculateAndComparePriceResponse.ServicePriceDifference priceDifference = getServicePriceDifferenceIsZero(saleServiceResult.getServiceId(), reasonIsFloorPricePaused);
                    servicePriceDifferenceList.add(priceDifference);
                    continue;
                }

                try {
                    // 如果有销售价，则计算底价
                    ServiceDTO serviceDto = serviceDtoList.get(i);
                    serviceDto.setIsCalculated(false);
                    StandardPricingRequest floorPricingRequest = new StandardPricingRequest();
                    BeanUtils.copyProperties(request, floorPricingRequest);
                    floorPricingRequest.setSceneCodeList(Collections.singletonList(floorPriceSceneCode));
                    floorPricingRequest.setServiceDtoList(Collections.singletonList(serviceDto));
                    floorPricingRequest.setBizInfo(null);
                    StandardPricingResponse floorPriceResult = priceCalculator.standardPricing(floorPricingRequest);
                    if (floorPriceResult == null || !floorPriceResult.isSuccess()) {
                        CalculateAndComparePriceResponse.ServicePriceDifference priceDifference = getServicePriceDifferenceIsZero(saleServiceResult.getServiceId(), reasonIsFloorPriceAbsence);
                        servicePriceDifferenceList.add(priceDifference);
                        continue;
                    }
                    ServicePriceDto saleServicePriceDto = getServicePriceDto(saleServiceResult);
                    ServicePriceDto floorServicePriceDto = getServicePriceDto(floorPriceResult.getServiceResultList().get(0));
                    compareServiceSku(saleServicePriceDto, floorServicePriceDto, servicePriceDifferenceList);
                } catch (Exception e) {
                    CalculateAndComparePriceResponse.ServicePriceDifference priceDifference = getServicePriceDifferenceIsZero(saleServiceResult.getServiceId(), reasonIsFloorPriceAbsence);
                    servicePriceDifferenceList.add(priceDifference);
                }
            }
            comparePriceResponse.setServicePriceDifferenceList(servicePriceDifferenceList);
            calculateAndComparePriceResponseService.saveAsync(comparePriceResponse);
        } catch (Exception e) {
            log.error("计算并比价异常", e);
            comparePriceResponse.setErrorInfo(e.getMessage());
        }
        return comparePriceResponse;
    }


    @Override
    public CalculateServiceDynamicPriceResponse calculateServiceDynamicPrice(CalculateServiceDynamicPriceRequest request) {
        return dynamicFeeRuleService.calculateServiceDynamicPrice(request);
    }


    private void compareServiceSku(ServicePriceDto salePriceDto, ServicePriceDto floorPriceDto, List<CalculateAndComparePriceResponse.ServicePriceDifference> servicePriceDifferenceList) {
        // 比较sku数量
        String serviceId = salePriceDto.getServiceId();
        if (salePriceDto.getSkuPriceListSize() != floorPriceDto.getSkuPriceListSize()) {
            servicePriceDifferenceList.add(getServicePriceDifferenceIsZero(serviceId, "sku数量不相等"));
        } else {
            // sku数量相同，比较各个skuNo是否相同
            Set<String> saleSkuNoSet = salePriceDto.getSkuPriceList().stream().map(ServicePriceDto.SkuPriceDto::getSkuNo).collect(Collectors.toSet());
            Set<String> floorSkuNoSet = floorPriceDto.getSkuPriceList().stream().map(ServicePriceDto.SkuPriceDto::getSkuNo).collect(Collectors.toSet());
            CalculateAndComparePriceResponse.ServicePriceDifference servicePriceDifference;
            if (!saleSkuNoSet.equals(floorSkuNoSet)) {
                servicePriceDifference = getServicePriceDifferenceIsZero(serviceId, "skuNo不相同");
            } else {
                // 开始计算差价
                BigDecimal salePrice = salePriceDto.getPrice();
                BigDecimal floorPrice = floorPriceDto.getPrice();
                if (salePrice.compareTo(floorPrice) >= 0) {
                    servicePriceDifference = getServicePriceDifferenceIsZero(serviceId, "销售价大于等于底价");
                } else {
                    servicePriceDifference = new CalculateAndComparePriceResponse.ServicePriceDifference();
                    servicePriceDifference.setServiceId(serviceId);
                    servicePriceDifference.setPriceDifference(floorPrice.subtract(salePrice));
                }
            }
            servicePriceDifferenceList.add(servicePriceDifference);
        }
    }


    private List<CalculateAndComparePriceResponse.ServicePriceDifference> getServicePriceDifferenceIsZeroList(List<ServiceDTO> serviceDtoList, String reason) {
        return serviceDtoList.stream()
                .map(e -> getServicePriceDifferenceIsZero(e.getServiceId().toString(), reason))
                .collect(Collectors.toList());
    }

    private CalculateAndComparePriceResponse.ServicePriceDifference getServicePriceDifferenceIsZero(String serviceId, String reason) {
        CalculateAndComparePriceResponse.ServicePriceDifference servicePriceDifference = new CalculateAndComparePriceResponse.ServicePriceDifference();
        servicePriceDifference.setServiceId(serviceId);
        servicePriceDifference.setPriceDifference(BigDecimal.ZERO);
        servicePriceDifference.setPriceDifferenceAbsenceReason(reason);
        return servicePriceDifference;
    }


    private ServicePriceDto getServicePriceDto(StandardPricingResponse.ServiceResult serviceResult) {
        if (serviceResult == null) {
            return null;
        }
        ServicePriceDto servicePriceDto = new ServicePriceDto();
        servicePriceDto.setServiceId(serviceResult.getServiceId());
        servicePriceDto.setPrice(serviceResult.getDynamicFeeCost());
        List<ServicePriceDto.SkuPriceDto> skuPriceDtoList = new ArrayList<>();
        servicePriceDto.setSkuPriceList(skuPriceDtoList);
        for (CalculateResult calculateResult : serviceResult.getCalculateResultList()) {
            ServicePriceDto.SkuPriceDto skuPriceDto = new ServicePriceDto.SkuPriceDto();
            skuPriceDto.setSkuNo(calculateResult.getBizRule().get(CommonBizRule.Fields.skuNo));
            skuPriceDtoList.add(skuPriceDto);
        }
        return servicePriceDto;
    }
}
