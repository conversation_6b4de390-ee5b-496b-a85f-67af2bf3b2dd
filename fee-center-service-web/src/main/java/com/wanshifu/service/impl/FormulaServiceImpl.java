package com.wanshifu.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.ql.util.express.ExpressRunner;
import com.wanshifu.fee.center.domain.document.*;
import com.wanshifu.fee.center.domain.dto.ExpressInfo;
import com.wanshifu.fee.center.domain.enums.DivisionTypeEnum;
import com.wanshifu.fee.center.domain.enums.TemplateStatusEnum;
import com.wanshifu.fee.center.domain.request.formula.*;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.repository.FormulaRepository;
import com.wanshifu.service.FormulaService;
import com.wanshifu.strategy.express.ExpressCompiler;
import com.wanshifu.infrastructure.utils.PatternUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: Chen Yong
 * @create: 2024-02-27 14:35
 * @description: 公式serviceImpl
 */
@Slf4j
@Service
public class FormulaServiceImpl implements FormulaService {

    @Resource
    private FormulaRepository formulaRepository;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private ExpressCompiler expressCompilerComposite;
    @Resource
    private ExpressRunner expressRunner;

    @Override
    public void add(FormulaAddReq req) {
        String formulaName = req.getFormulaName();
        Formula formula = formulaRepository.findByFormulaNameAndDelIsFalse(formulaName);
        if (formula != null) {
            throw new BusException("公式名称已存在");
        }
        formula = new Formula();
        parseFormulaContent(req);
        BeanUtils.copyProperties(req, formula);
        formula.setFormulaId(SnowFlakeGenerator.INSTANCE.generate());
        formula.setDel(false);
        formula.setStatus(TemplateStatusEnum.ACTIVE.code);
        Date now = new Date();
        formula.setCreateTime(now);
        formula.setModifyTime(now);
        formulaRepository.save(formula);
    }



    @Override
    public void modify(FormulaModifyReq req) {
        long formulaId = Long.parseLong(req.getFormulaId());
        Formula formula = formulaRepository.findByFormulaNameAndDelIsFalseAndFormulaIdNot(req.getFormulaName(), formulaId);
        if (formula != null) {
            throw new BusException("公式名称已存在");
        }
        formula = formulaRepository.findByFormulaId(formulaId);
        if (Objects.isNull(formula)) {
            throw new BusException("公式不存在");
        }
        parseFormulaContent(req);
        BeanUtils.copyProperties(req, formula);
        formula.setModifyTime(new Date());
        formulaRepository.save(formula);
    }

    @Override
    public void delete(Long formulaId) {
        Formula formula = formulaRepository.findByFormulaId(formulaId);
        if (Objects.isNull(formula)) {
            throw new BusException("公式不存在");
        }
        formula.setDel(true);
        formulaRepository.save(formula);
    }


    @Override
    public SimplePageInfo<FormulaListResp> pageList(FormulaListReq req) {
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        if (StringUtils.isNotBlank(req.getFormulaName())) {
            criteria.and(Formula.Fields.formulaName).regex(PatternUtils.toEscapeStr(req.getFormulaName()));
        }
        Query query = Query.query(criteria);
        Pageable pageable = new PageRequest(req.getPageNum() - 1, req.getPageSize());
        long totalCount = mongoTemplate.count(query, Formula.class);
        query.with(pageable);
        query.with(new Sort(Sort.Direction.DESC, BaseDocument.Fields.modifyTime));
        List<Formula> formulaList = mongoTemplate.find(query, Formula.class);
        if (CollectionUtils.isEmpty(formulaList)) {
            return new SimplePageInfo<>();
        }
        List<FormulaListResp> resultList = new ArrayList<>();
        formulaList.forEach(e -> {
            FormulaListResp resp = new FormulaListResp();
            BeanUtils.copyProperties(e, resp);
            resp.setFormulaId(e.getFormulaId().toString());
            if (StringUtils.isNotBlank(e.getBizIds())) {
                resp.setBizIdCount(e.getBizIds().split(",").length);
            }
            if (StringUtils.isNotBlank(e.getDivisionIds())) {
                resp.setDivisionIdCount(e.getDivisionIds().split(",").length);
            }
            DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(e.getDivisionType());
            if (Objects.nonNull(divisionTypeEnum)) {
                resp.setDivisionType(divisionTypeEnum.name);
            }
            resultList.add(resp);
        });
        PageImpl<FormulaListResp> page = new PageImpl<>(resultList, pageable, totalCount);
        SimplePageInfo<FormulaListResp> resultPage = new SimplePageInfo<>(resultList);
        resultPage.setPages(page.getTotalPages());
        resultPage.setPageNum(page.getNumber());
        resultPage.setPageSize(page.getSize());
        resultPage.setTotal(totalCount);
        return resultPage;
    }


    @Override
    public FormulaDetailResp detail(Long formulaId) {
        Formula formula = formulaRepository.findByFormulaId(formulaId);
        if (Objects.isNull(formula)) {
            throw new BusException("公式不存在");
        }
        FormulaDetailResp resp = new FormulaDetailResp();
        BeanUtils.copyProperties(formula, resp);
        if (StringUtils.isNotBlank(formula.getDivisionIds())) {
            resp.setDivisionIdCount(formula.getDivisionIds().split(",").length);
        }
        return resp;
    }


    @Override
    public List<FormulaListByIdsResp> getListByFormulaIds(List<Long> formulaIds) {
        if (CollectionUtils.isEmpty(formulaIds)) {
            return Collections.emptyList();
        }
        List<Formula> formulaList = formulaRepository.findAllByDelIsFalseAndFormulaIdIn(formulaIds);
        if (CollectionUtils.isEmpty(formulaList)) {
            return Collections.emptyList();
        }
        List<FormulaListByIdsResp> resultList = new ArrayList<>();
        formulaList.forEach(e -> {
            FormulaListByIdsResp resp = new FormulaListByIdsResp();
            resp.setFormulaName(e.getFormulaName());
            resp.setFormulaId(e.getFormulaId().toString());
            resultList.add(resp);
        });
        return resultList;
    }


    @Override
    public CalculateRuleData handleFormulas(CalculateRuleData calculateRuleData, String divisionType, String divisionId, String bizId) {
        log.info("开始处理公式，handleFormulas calculateRuleData:{}", calculateRuleData);
        if (calculateRuleData != null && calculateRuleData.hasFormula()) {
            CalculateRuleData calculateRule = new Gson().fromJson(JSON.toJSONString(calculateRuleData), CalculateRuleData.class);
            String express = calculateRule.getExpress();
            String numberExpress = getNumberExpress(express);
            ExpressInfo expressInfo = calculateRule.getExpressInfo();
            List<Formula> formulaList = calculateRule.getFormulaIds().stream()
                    .map(Long::parseLong)
                    .map(formulaRepository::findByFormulaId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(formulaList)) {
                // 根据地区id和业务id过滤公式，过滤条件为当前地区id在公式的地区id列表中，且当前业务id在公式的业务id列表中
                formulaList = formulaList.stream().filter(e -> {
                    String bizIds = e.getBizIds();
                    boolean bizMatched = isMatchedBiz(bizId, bizIds);
                    boolean divisionMatched = isMatchedDivision(divisionType, divisionId, e);
                    return bizMatched && divisionMatched;
                }).collect(Collectors.toList());

                List<List<ExpressInfo.ExpressInfoUnit>> expressInfoUnits = formulaList.stream()
                        .map(e -> e.getFormulaContent().getExpressInfo().getExpressInfoUnits())
                        .filter(CollectionUtils::isNotEmpty)
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(expressInfoUnits)) {
                    if (expressInfo == null) {
                        expressInfo = new ExpressInfo();
                        expressInfo.setExpressInfoUnits(new ArrayList<>());
                    }
                    ExpressInfo finalExpressInfo = expressInfo;
                    expressInfoUnits.forEach(e -> {
                        // 设置计价数量
                        e.forEach(ex -> ex.setNumberExpress(numberExpress));
                        finalExpressInfo.getExpressInfoUnits().addAll(e);
                    });
                    String compile = expressCompilerComposite.compile(finalExpressInfo);
                    log.info("公式编译结果：{}", compile);

                    try {
                        String[] outVarNames = expressRunner.getOutVarNames(compile);
                        calculateRule.setExpress(compile);
                        calculateRule.setExpressInfo(finalExpressInfo);
                        if (Objects.nonNull(outVarNames) && outVarNames.length > 0) {
                            calculateRule.setExpressionParamList(Arrays.asList(outVarNames));
                        }
                    } catch (Exception e) {
                        throw new BusException(e.getMessage(), e);
                    }
                }
            }
            return calculateRule;
        }
        return calculateRuleData;
    }


    @Override
    public DynamicFeeRuleCalculateRuleData handleFormulas(CalculateRuleData calculateRuleData, DynamicCalculateRuleData dynamicCalculateRuleData, String divisionType, String divisionId, String bizId) {
        log.info("开始处理公式，handleFormulas dynamicCalculateRuleData:{}", dynamicCalculateRuleData);
        if (dynamicCalculateRuleData == null || !dynamicCalculateRuleData.hasFormula()) {
            return null;
        }

        DynamicFeeRuleCalculateRuleData newDynamicCalculateRuleData = new DynamicFeeRuleCalculateRuleData();
        String express = calculateRuleData.getExpress();
        String numberExpress = getNumberExpress(express);
        ExpressInfo expressInfo = calculateRuleData.getExpressInfo();

        List<Formula> formulaList = dynamicCalculateRuleData.getFormulaIds().stream().map(formulaId ->
                formulaRepository.findByFormulaId(Long.parseLong(formulaId))
        ).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(formulaList)) {
            return null;
        }
        // 根据地区id和业务id过滤公式，过滤条件为当前地区id在公式的地区id列表中，且当前业务id在公式的业务id列表中
        formulaList = formulaList.stream().filter(e -> {
            String bizIds = e.getBizIds();
            boolean bizMatched = isMatchedBiz(bizId, bizIds);
            boolean divisionTypeMatched = e.getDivisionType().equals(divisionType);
            boolean divisionMatched = isMatchedDivision(divisionType, divisionId, e);
            return bizMatched && divisionMatched && divisionTypeMatched;
        }).collect(Collectors.toList());

        List<List<ExpressInfo.ExpressInfoUnit>> expressInfoUnits = formulaList.stream()
                .map(e -> e.getFormulaContent().getExpressInfo().getExpressInfoUnits())
                .filter(CollectionUtils::isNotEmpty)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(expressInfoUnits)) {
            return null;
        }

        ExpressInfo finalExpressInfo;
        if (expressInfo == null) {
            finalExpressInfo = new ExpressInfo();
            finalExpressInfo.setExpressInfoUnits(new ArrayList<>());
        } else {
            finalExpressInfo = new Gson().fromJson(JSON.toJSONString(expressInfo), ExpressInfo.class);
            if (finalExpressInfo.getExpressInfoUnits() == null) {
                finalExpressInfo.setExpressInfoUnits(new ArrayList<>());
            }
        }
        expressInfoUnits.forEach(e -> {
            // 设置计价数量
            e.forEach(ex -> ex.setNumberExpress(numberExpress));
            finalExpressInfo.getExpressInfoUnits().addAll(e);
        });
        String compile = expressCompilerComposite.compile(finalExpressInfo);
        log.info("公式编译结果：{}", compile);

        try {
            String[] outVarNames = expressRunner.getOutVarNames(compile);
            DynamicFeeRuleCalculateRuleData.CalculateData skuPrice = new DynamicFeeRuleCalculateRuleData.CalculateData();
            skuPrice.setExpress(compile);
            if (Objects.nonNull(outVarNames) && outVarNames.length > 0) {
                skuPrice.setExpressionParamList(Arrays.asList(outVarNames));
            }
            newDynamicCalculateRuleData.setSkuPrice(skuPrice);
        } catch (Exception e) {
            throw new BusException(e.getMessage(), e);
        }
        return newDynamicCalculateRuleData;
    }

    private String getNumberExpress(String express) {
        String numberExpress;
        if (StringUtils.isNotBlank(express)) {
            int startIndex = express.indexOf("AP");
            // 检查是否找到了以"AP"开头的部分
            if (startIndex != -1) {
                numberExpress = express.substring(startIndex);
            } else {
//                throw new BusException("未找到以\"AP\"开头的计价数量");
                numberExpress = "1";
            }
        } else {
            numberExpress = "";
        }
        return numberExpress;
    }

    private boolean isMatchedDivision(String divisionType, String divisionId, Formula e) {
        String type = e.getDivisionType();
        if (DivisionTypeEnum.COUNTRY.code.equals(type) && DivisionTypeEnum.COUNTRY.code.equals(divisionType)) {
            return true;
        } else {
            if (StringUtils.isNotBlank(divisionType)) {
                if (divisionType.trim().equals(type)) {
                    String[] divisionIdArr = e.getDivisionIds().split(",");
                    return Arrays.stream(divisionIdArr).map(String::trim).collect(Collectors.toList()).contains(divisionId.trim());
                }
            }
        }
        return false;
    }

    private boolean isMatchedBiz(String bizId, String bizIds) {
        if (StringUtils.isBlank(bizIds) && StringUtils.isBlank(bizId)) {
            return true;
        }
        if (StringUtils.isNotBlank(bizIds) && StringUtils.isNotBlank(bizId) ) {
            String[] bizIdArr = bizIds.split(",");
            return Arrays.stream(bizIdArr).map(String::trim).collect(Collectors.toList()).contains(bizId.trim());
        }
        return false;
    }

    private void parseFormulaContent(FormulaBaseReq req) {
        CalculateRuleData formulaContent = req.getFormulaContent();
        formulaContent.parseExpressionParam();
        if (Objects.nonNull(formulaContent.getExpressInfo())) {
            String compile = expressCompilerComposite.compile(formulaContent.getExpressInfo());
            try {
                String[] outVarNames = expressRunner.getOutVarNames(compile);
                formulaContent.setExpress(compile);
                if (Objects.nonNull(outVarNames) && outVarNames.length > 0) {
                    formulaContent.setExpressionParamList(Arrays.asList(outVarNames));
                }
            } catch (Exception e) {
                throw new BusException(e.getMessage(), e);
            }
        }
    }
}
