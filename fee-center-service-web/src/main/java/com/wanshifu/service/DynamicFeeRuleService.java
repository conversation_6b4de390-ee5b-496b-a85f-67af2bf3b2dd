package com.wanshifu.service;

import com.wanshifu.fee.center.domain.document.DynamicFeeRule;
import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.dto.ServiceCalculateResult;
import com.wanshifu.fee.center.domain.enums.DynamicFeeRuleStatusEnum;
import com.wanshifu.fee.center.domain.request.*;
import com.wanshifu.fee.center.domain.request.calculate.CalculateServiceDynamicPriceRequest;
import com.wanshifu.fee.center.domain.request.calculate.CalculateServiceDynamicPriceResponse;
import com.wanshifu.fee.center.domain.request.calculate.DynamicIndicatorParam;
import com.wanshifu.fee.center.domain.response.ApplyCalculateResp;
import com.wanshifu.fee.center.domain.response.ApplyOrderCalculateResp;
import com.wanshifu.fee.center.domain.response.FeeRulePageResp;
import com.wanshifu.fee.center.domain.response.FeeRuleServicePageResp;
import com.wanshifu.framework.core.page.SimplePageInfo;
import org.springframework.data.domain.Page;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public interface DynamicFeeRuleService {

    Page<DynamicFeeRule> queryByCondition(DynamicFeeRulePageReq dynamicFeeRulePageReq);

    DynamicFeeRule queryById(Long dynamicFeeRuleId);

    DynamicFeeRule save(DynamicFeeRuleSaveReq dynamicFeeRuleSaveReq);

    DynamicFeeRule modify(DynamicFeeRuleSaveReq dynamicFeeRuleSaveReq);

    DynamicFeeRule del(Long dynamicFeeRuleId);

    DynamicFeeRule audit(Long dynamicFeeRuleId, DynamicFeeRuleStatusEnum dynamicFeeRuleStatus);

    BigDecimal getServiceDynamicFeeCost(String sceneCode,
                                        String subSceneCode,
                                        String serviceId,
                                        String userId,
                                        Long cityId,
                                        BigDecimal cost,
                                        BigDecimal dynamicFeeCost,
                                        List<DynamicIndicatorParam> indicatorParamList);

    CalculateServiceDynamicPriceResponse calculateServiceDynamicPrice(CalculateServiceDynamicPriceRequest request);
}
