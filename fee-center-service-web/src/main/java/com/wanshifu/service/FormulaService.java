package com.wanshifu.service;

import com.wanshifu.fee.center.domain.document.CalculateRuleData;
import com.wanshifu.fee.center.domain.document.DynamicCalculateRuleData;
import com.wanshifu.fee.center.domain.document.DynamicFeeRuleCalculateRuleData;
import com.wanshifu.fee.center.domain.request.formula.*;
import com.wanshifu.framework.core.page.SimplePageInfo;

import java.util.List;

/**
 * @author: <PERSON>
 * @create: 2024-02-27 14:34
 * @description: 公式service
 */
public interface FormulaService {

    void add(FormulaAddReq req);

    void modify(FormulaModifyReq req);

    void delete(Long formulaId);

    SimplePageInfo<FormulaListResp> pageList(FormulaListReq req);

    FormulaDetailResp detail(Long formulaId);

    List<FormulaListByIdsResp> getListByFormulaIds(List<Long> formulaIds);

    CalculateRuleData handleFormulas(CalculateRuleData calculateRuleData, String divisionType, String divisionId, String bizId);

    DynamicFeeRuleCalculateRuleData handleFormulas(CalculateRuleData calculateRuleData, DynamicCalculateRuleData dynamicCalculateRuleData, String divisionType, String divisionId, String bizId);
}
