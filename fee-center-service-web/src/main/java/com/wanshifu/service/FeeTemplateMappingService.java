package com.wanshifu.service;

import com.wanshifu.fee.center.domain.document.FeeTemplateMapping;
import com.wanshifu.fee.center.domain.request.mapping.*;
import org.springframework.data.domain.Page;

import java.util.List;

public interface FeeTemplateMappingService {

    void add(AddRequest addRequest);

    void batchAdd(List<AddRequest> addRequestList);

    void update(UpdateRequest updateRequest);

    void delete(String mappingId);

    Page<MappingPageListResponse> getPageList(MappingPageListRequest request);

    void copyMapping(CopyMappingRequest request);

    void setApplyTypes(UpdateMappingApplyTypesRequest request);

    FeeTemplateMapping findOneBySourceTemplateId(Long sourceTemplateId);

    Page<FeeTemplateMapping> queryPage(MappingQueryPageRequest request);
}
