package com.wanshifu.service;

import com.wanshifu.domain.request.samedayblacklistaddress.*;
import com.wanshifu.framework.core.page.SimplePageInfo;

public interface SameDayBlacklistAddressConfigService {


    void add(AddRequest request);

    void modify(ModifyRequest request);

    SimplePageInfo<PageListResponse> pageList(Long level1GoodsCategoryId, String districtId, String keyword, int pageNum, int pageSize);

    DetailResponse detail(String configId);

    void deleteByConfigId(DeleteRequest request);

    boolean isBlackAddress(Long level1GoodsCategoryId, String districtId, String addressDetail);
}
