package com.wanshifu.service;

import com.wanshifu.fee.center.domain.request.calculate.*;

public interface PriceCalculatorService {

    StandardPricingResponse standardPricing(StandardPricingRequest request);

    CalculateAndComparePriceResponse calculateAndComparePrice(CalculateAndComparePriceRequest request);

    CalculateServiceDynamicPriceResponse calculateServiceDynamicPrice(CalculateServiceDynamicPriceRequest request);
}
