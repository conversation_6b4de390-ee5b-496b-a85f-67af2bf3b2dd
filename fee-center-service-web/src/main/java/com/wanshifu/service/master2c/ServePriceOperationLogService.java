package com.wanshifu.service.master2c;

import com.wanshifu.fee.center.domain.document.master2c.ServePriceOperationLog;
import com.wanshifu.fee.center.domain.document.master2c.ServePrice2C4MasterDetail;

public interface ServePriceOperationLogService {

    ServePriceOperationLog getServePriceOperationLog(Long priceId);

    void add(Long priceId, ServePriceOperationLog.OperationLogDetail operationLogDetail);

    void update(Long priceId, ServePriceOperationLog.OperationLogDetail operationLogDetail, ServePrice2C4MasterDetail detail);

    ServePrice2C4MasterDetail getPriceDetailSnapshot(Long logId);
}
