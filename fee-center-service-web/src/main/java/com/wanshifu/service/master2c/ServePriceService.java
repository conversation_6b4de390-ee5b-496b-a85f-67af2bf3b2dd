package com.wanshifu.service.master2c;

import com.wanshifu.domain.request.master2c.*;
import com.wanshifu.fee.center.domain.document.master2c.DraftServePrice;
import com.wanshifu.fee.center.domain.document.master2c.ServePrice;
import com.wanshifu.fee.center.domain.document.master2c.ServePrice2C4MasterDetail;
import com.wanshifu.framework.core.page.SimplePageInfo;

public interface ServePriceService {

    void scheduleActive(ServePrice servePrice);

    void scheduleReject(ServePrice servePrice);

    void add(ServePrice2C4MasterAddReq req);

    void modify(ServePrice2C4MasterModifyReq req);

    ServePrice2C4MasterDetail getDetail(Long priceId);

    void reject(Long priceId, String updateBy, String reason);

    void approve(Long priceId);

    void inactive(Long priceId);

    void active(Long priceId, String updateBy);

    SimplePageInfo<ServePrice2C4MasterPageResp> getPageList(ServePrice2C4MasterPageReq req);

    ServePrice2C4MasterCountResp getCount();

    SimplePageInfo<DraftServePrice2C4MasterPageResp> getDraftPageList(ServePrice2C4MasterPageReq req);

    DraftServePriceIsExistResp getIsExistDraftByPriceId(DraftServePriceIsExistReq req);

    void addDraft(DraftServePrice2C4MasterAddReq req);

    void modifyDraft(DraftServePrice2C4MasterModifyReq req);

    DraftServePrice2C4MasterDetail getDraftDetail(Long draftId);

    void approveDraft(Long draftId);

    void rejectDraft(Long draftId, String updateBy, String reason);

    void cancelDraft(Long draftId, String remark);

    void scheduleCancelDraft(DraftServePrice draftServePrice);

    void scheduleOnlineDraft(DraftServePrice draftServePrice);

    void onlineDraft(Long draftId, String updateBy);
}
