package com.wanshifu.service.master2c;

//public interface ServePrice2C4MasterService {
//
//    void add(ServePrice2C4MasterAddReq req);
//
//    void modify(ServePrice2C4MasterModifyReq req);
//
//    ServePrice2C4MasterDetail getDetail(Long priceId);
//
//    void reject(Long priceId, String updateBy, String reason);
//
//    void approve(Long priceId);
//
//    void inactive(Long priceId);
//
//    void active(Long priceId, String updateBy);
//
//    SimplePageInfo<ServePrice2C4MasterPageResp> getPageList(ServePrice2C4MasterPageReq req);
//}
