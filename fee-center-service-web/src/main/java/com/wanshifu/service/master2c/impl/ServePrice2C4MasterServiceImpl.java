//package com.wanshifu.service.master2c.impl;
//
//import cn.hutool.core.collection.CollectionUtil;
//import com.wanshifu.base.address.api.AddressApi;
//import com.wanshifu.base.address.domain.po.Address;
//import com.wanshifu.client.assembler.ServeConfigMapper;
//import com.wanshifu.client.assembler.ServeConfigPriceMerger;
//import com.wanshifu.client.assembler.ServePriceMapper;
//import com.wanshifu.domain.request.master2c.*;
//import com.wanshifu.fee.center.domain.document.master2c.ServePrice;
//import com.wanshifu.fee.center.domain.document.master2c.ServePriceOperationLog;
//import com.wanshifu.fee.center.domain.document.master2c.ServePrice2C4MasterDetail;
//import com.wanshifu.fee.center.domain.enums.master2c.ServePriceOperationTypeEnum;
//import com.wanshifu.fee.center.domain.enums.master2c.ServePriceStatusEnum;
//import com.wanshifu.framework.core.BusException;
//import com.wanshifu.framework.core.page.SimplePageInfo;
//import com.wanshifu.framework.utils.CollectionUtils;
//import com.wanshifu.framework.utils.SnowFlakeGenerator;
//import com.wanshifu.framework.utils.StringUtils;
//import com.wanshifu.infrastructure.utils.CommonUtils;
//import com.wanshifu.order.config.api.ServeConfigServiceApi;
//import com.wanshifu.order.config.domains.dto.serveConfig.ServeConfig4MasterPriceResp;
//import com.wanshifu.repository.ServePriceRepository;
//import com.wanshifu.repository.dao.ServePriceDao;
//import com.wanshifu.service.master2c.ServePriceService;
//import com.wanshifu.service.master2c.ServePrice2C4MasterService;
//import com.wanshifu.service.master2c.ServePriceOperationLogService;
//import lombok.RequiredArgsConstructor;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.*;
//import java.util.stream.Collectors;
//
//@Service
//@RequiredArgsConstructor(onConstructor = @__(@Autowired))
//public class ServePrice2C4MasterServiceImpl implements ServePrice2C4MasterService {
//
//    private final ServePriceRepository servePriceRepository;
//    private final ServeConfigServiceApi serveConfigServiceApi;
//    private final ServePriceService servePriceService;
//    private final ServePriceDao servePriceDao;
//    private final AddressApi addressApi;
//    private final ServePriceOperationLogService servePriceOperationLogService;
//
//    @Override
//    public void add(ServePrice2C4MasterAddReq req) {
//        ServePrice servePrice = ServePriceMapper.mapToServePrice(req);
//        servePrice.setPriceId(SnowFlakeGenerator.INSTANCE.generate());
//        servePrice.setStatus(ServePriceStatusEnum.PENDING_REVIEW.getStatus());
//        ServePrice save = servePriceRepository.save(servePrice);
//
//        servePriceOperationLogService.add(save.getPriceId(), ServePriceOperationLog.OperationLogDetail.builder()
//                .operator(CommonUtils.getCurrentLoginName())
//                .operationType(ServePriceOperationTypeEnum.ADD.getOperationType())
//                .build());
//    }
//
//    @Override
//    public void modify(ServePrice2C4MasterModifyReq req) {
//        Long priceId = req.getPriceId();
//        ServePrice2C4MasterDetail detail = getDetail(priceId);
//        // TODO 根据需求文档中描述的关键字段 需要判断是否需要修改
//        /*
//          1. 需要校验的变更数据：服务基础价格、权重、参数价格、生效城市
//          2. 已生效状态下的编辑，不允许编辑服务以及生效时间
//         */
//        ServePrice servePrice = servePriceRepository.findOneByPriceId(priceId);
//        if (servePrice == null) {
//            throw new BusException("服务价格id有误");
//        }
//        String id = servePrice.getId();
//        servePrice = ServePriceMapper.mapToServePrice(req);
//        servePrice.setId(id);
//        servePrice.setPriceId(priceId);
//        servePrice.setStatus(ServePriceStatusEnum.PENDING_REVIEW.getStatus());
//        servePriceRepository.save(servePrice);
//
//        servePriceOperationLogService.update(priceId, ServePriceOperationLog.OperationLogDetail.builder()
//                .operator(CommonUtils.getCurrentLoginName())
//                .operationType(ServePriceOperationTypeEnum.MODIFY.getOperationType())
//                .servePrice2C4MasterDetail(detail)
//                .build());
//    }
//
//    @Override
//    public ServePrice2C4MasterDetail getDetail(Long priceId) {
//        ServePrice servePrice = servePriceRepository.findOneByPriceId(priceId);
//        if (servePrice == null) {
//            return null;
//        }
//        ServePrice2C4MasterDetail detail = new ServePrice2C4MasterDetail();
//        BeanUtils.copyProperties(servePrice, detail);
//
//        Long serveId = servePrice.getServeId();
//        List<ServeConfig4MasterPriceResp> serveConfigList = serveConfigServiceApi.getServeConfigListByServeId(serveId);
//        if (CollectionUtils.isEmpty(serveConfigList)) {
//            detail.setServeConfigs(null);
//            return detail;
//        }
//
//        List<ServePrice2C4MasterDetail.ServeConfig> detailServeConfigs = ServeConfigMapper.mapToServeConfigList(serveConfigList);
//        List<ServePrice.ServeConfig> serveConfigs = servePrice.getServeConfigs();
//        if  (CollectionUtil.isNotEmpty(serveConfigs)) {
//            detailServeConfigs.forEach(e -> {
//                Long relationId = e.getRelationId();
//                if  (relationId != null) {
//                    String attributeFullName = serveConfigServiceApi.getAttributeFullNameById(relationId);
//                    e.setRelationConfigName(attributeFullName);
//                }
//            });
//
//        }
//        ServeConfigPriceMerger.mergeWithGuavaTable(serveConfigs, detailServeConfigs);
//        detail.setServeConfigs(detailServeConfigs);
//
//        return detail;
//    }
//
//
//    @Override
//    public void reject(Long priceId, String updateBy, String reason) {
//        ServePrice servePrice = servePriceRepository.findOneByPriceId(priceId);
//        if (servePrice == null) {
//            throw new BusException("服务价格id有误");
//        }
//        servePrice.setStatus(ServePriceStatusEnum.REJECTED.getStatus());
//        servePriceRepository.save(servePrice);
//
//        servePriceOperationLogService.update(priceId, ServePriceOperationLog.OperationLogDetail.builder()
//                .operator(updateBy)
//                .operationType(ServePriceOperationTypeEnum.REJECT.getOperationType())
//                .remark(reason)
//                .build());
//    }
//
//
//    @Override
//    public void approve(Long priceId) {
//        ServePrice servePrice = servePriceRepository.findOneByPriceId(priceId);
//        if (servePrice == null) {
//            throw new BusException("服务价格id有误");
//        }
//        servePrice.setStatus(ServePriceStatusEnum.PENDING_ACTIVATION.getStatus());
//        servePriceRepository.save(servePrice);
//        servePriceService.scheduleStatusChange(servePrice);
//
//        servePriceOperationLogService.update(priceId, ServePriceOperationLog.OperationLogDetail.builder()
//                .operator(CommonUtils.getCurrentLoginName())
//                .operationType(ServePriceOperationTypeEnum.APPROVE.getOperationType())
//                .build());
//    }
//
//    @Override
//    public void inactive(Long priceId) {
//        ServePrice servePrice = servePriceRepository.findOneByPriceId(priceId);
//        if (servePrice == null) {
//            throw new BusException("服务价格id有误");
//        }
//        servePrice.setStatus(ServePriceStatusEnum.INACTIVE.getStatus());
//        servePriceRepository.save(servePrice);
//
//        servePriceOperationLogService.update(priceId, ServePriceOperationLog.OperationLogDetail.builder()
//                .operator(CommonUtils.getCurrentLoginName())
//                .operationType(ServePriceOperationTypeEnum.INACTIVE.getOperationType())
//                .build());
//    }
//
//
//    @Override
//    public void active(Long priceId, String updateBy) {
//        servePriceDao.updateStatusByPriceId(priceId, ServePriceStatusEnum.ACTIVE.getStatus(), updateBy);
//
//        servePriceOperationLogService.update(priceId, ServePriceOperationLog.OperationLogDetail.builder()
//                .operator(updateBy)
//                .operationType(ServePriceOperationTypeEnum.ACTIVE_SYSTEM.getOperationType())
//                .build());
//    }
//
//    @Override
//    public SimplePageInfo<ServePrice2C4MasterPageResp> getPageList(ServePrice2C4MasterPageReq req) {
//        String cityName = req.getCityName();
//        Long cityId = null;
//        if (StringUtils.isNotBlank(cityName)) {
//            final String finalCityName = cityName.trim();
//            List<Address> addressList = addressApi.getByLevelAndNameLike(3, finalCityName);
//            if (CollectionUtils.isNotEmpty(addressList)) {
//                Address address = addressList.stream().filter(e -> e.getDivisionName().equals(finalCityName)).findFirst().get();
//                cityId = address.getDivisionId();
//            }
//        }
//        SimplePageInfo<ServePrice> servePage = servePriceDao.findPageByServeNameAndCityIdAndStatus(req.getServeName(), cityId, req.getStatus(), req.getPageNum(), req.getPageSize());
//        List<ServePrice> servePriceList = servePage.getList();
//        if  (CollectionUtils.isEmpty(servePriceList)) {
//            return new SimplePageInfo<>();
//        }
//        SimplePageInfo<ServePrice2C4MasterPageResp> resultPage = new SimplePageInfo<>(servePriceList.stream().map(e -> {
//            ServePrice2C4MasterPageResp resp = new ServePrice2C4MasterPageResp();
//            BeanUtils.copyProperties(e, resp);
//            List<ServePrice.ServeConfig> serveConfigs = e.getServeConfigs();
//            resp.setPriceParamCount(CollectionUtils.isEmpty(serveConfigs) ? 0 : serveConfigs.size());
//            List<Long> cityIds = Optional.ofNullable(e.getCityIds()).orElseGet(Collections::emptyList);
//            List<Long> groupCityIds = Optional.ofNullable(e.getCityGroup()).map(ServePrice.CityGroup::getCityIds).orElseGet(Collections::emptyList);
//            Set<Long> cityIdSet = new HashSet<>();
//            cityIdSet.addAll(cityIds);
//            cityIdSet.addAll(groupCityIds);
//            if  (CollectionUtils.isNotEmpty(cityIdSet)) {
//                List<Address> addressList = addressApi.getDivisionInfoListByDivisionIds(StringUtils.join(cityIdSet, ","));
//                if (CollectionUtils.isNotEmpty(addressList)) {
//                    String cityNames = addressList.stream().map(Address::getDivisionName).collect(Collectors.joining("、"));
//                    resp.setCityNames(cityNames);
//                }
//            }
//            return resp;
//        }).collect(Collectors.toList()));
//        resultPage.setTotal(servePage.getTotal());
//        resultPage.setPageNum(servePage.getPageNum());
//        resultPage.setPageSize(servePage.getPageSize());
//        return resultPage;
//    }
//}
