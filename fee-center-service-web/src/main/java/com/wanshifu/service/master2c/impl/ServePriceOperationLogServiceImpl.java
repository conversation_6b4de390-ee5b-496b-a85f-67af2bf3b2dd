package com.wanshifu.service.master2c.impl;

import cn.hutool.core.util.StrUtil;
import com.wanshifu.fee.center.domain.document.master2c.ServePriceOperationLog;
import com.wanshifu.fee.center.domain.document.master2c.ServePrice2C4MasterDetail;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.repository.ServePrice2C4MasterDetailRepository;
import com.wanshifu.repository.ServePriceOperationLogRepository;
import com.wanshifu.service.master2c.ServePriceOperationLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ServePriceOperationLogServiceImpl implements ServePriceOperationLogService {

    private final ServePriceOperationLogRepository servePriceOperationLogRepository;
    private final ServePrice2C4MasterDetailRepository servePrice2C4MasterDetailRepository;

    @Override
    public ServePriceOperationLog getServePriceOperationLog(Long priceId) {
        ServePriceOperationLog log = servePriceOperationLogRepository.findOneByPriceId(priceId);
        if  (log == null) {
            return null;
        }
        List<ServePriceOperationLog.OperationLogDetail> logDetailList = log.getOperationLogDetailList().stream().sorted(Comparator.comparing(ServePriceOperationLog.OperationLogDetail::getLogId).reversed()).collect(Collectors.toList());
        log.setOperationLogDetailList(logDetailList);
        return log;
    }

    @Override
    public ServePrice2C4MasterDetail getPriceDetailSnapshot(Long logId) {
        return servePrice2C4MasterDetailRepository.findOneByLogId(logId);
    }

    @Override
    @Async("logTaskExecutor")
    public void add(Long priceId, ServePriceOperationLog.OperationLogDetail operationLogDetail) {
        ServePriceOperationLog log = new ServePriceOperationLog();
        log.setPriceId(priceId);
        List<ServePriceOperationLog.OperationLogDetail> detailList = new ArrayList<>();
        operationLogDetail.setLogId(SnowFlakeGenerator.INSTANCE.generate());
        operationLogDetail.setOperationTime(new Date());
        detailList.add(operationLogDetail);
        log.setOperationLogDetailList(detailList);
        servePriceOperationLogRepository.save(log);
    }


    @Override
    @Async("logTaskExecutor")
    public void update(Long priceId, ServePriceOperationLog.OperationLogDetail operationLogDetail, ServePrice2C4MasterDetail detail) {
        ServePriceOperationLog log = servePriceOperationLogRepository.findOneByPriceId(priceId);
        if  (log == null) {
            throw new BusException(StrUtil.format("服务价格id有误，priceId={}", priceId));
        }
        operationLogDetail.setLogId(SnowFlakeGenerator.INSTANCE.generate());
        operationLogDetail.setOperationTime(new Date());
        log.getOperationLogDetailList().add(operationLogDetail);
        servePriceOperationLogRepository.save(log);
        if (detail != null) {
            detail.setLogId(operationLogDetail.getLogId());
            servePrice2C4MasterDetailRepository.save(detail);
        }
    }

}
