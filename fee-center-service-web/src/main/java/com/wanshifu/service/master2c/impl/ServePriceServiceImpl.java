package com.wanshifu.service.master2c.impl;

import cn.hutool.core.collection.CollectionUtil;

import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.client.assembler.*;
import com.wanshifu.domain.request.master2c.*;
import com.wanshifu.fee.center.domain.document.master2c.DraftServePrice;
import com.wanshifu.fee.center.domain.document.master2c.ServePrice;
import com.wanshifu.fee.center.domain.document.master2c.ServePriceOperationLog;
import com.wanshifu.fee.center.domain.document.master2c.ServePrice2C4MasterDetail;
import com.wanshifu.fee.center.domain.enums.master2c.DraftServePriceStatusEnum;
import com.wanshifu.fee.center.domain.enums.master2c.ServePriceOperationTypeEnum;
import com.wanshifu.fee.center.domain.enums.master2c.ServePriceStatusEnum;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.utils.CommonUtils;
import com.wanshifu.order.config.api.ServeConfigServiceApi;
import com.wanshifu.order.config.domains.dto.serveConfig.ServeConfig4MasterPriceResp;
import com.wanshifu.repository.DraftServePriceRepository;
import com.wanshifu.repository.ServePriceRepository;
import com.wanshifu.repository.dao.DraftServePriceDao;
import com.wanshifu.repository.dao.ServePriceDao;
import com.wanshifu.service.master2c.ServePriceOperationLogService;
import com.wanshifu.service.master2c.ServePriceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ServePriceServiceImpl implements ServePriceService {

    private final TaskScheduler scheduler;
    private final ServePriceRepository servePriceRepository;
    private final ServeConfigServiceApi serveConfigServiceApi;
    private final ServePriceDao servePriceDao;
    private final AddressApi addressApi;
    private final ServePriceOperationLogService servePriceOperationLogService;

    private final DraftServePriceDao draftServePriceDao;
    private final DraftServePriceRepository draftServePriceRepository;

    @Override
    public void add(ServePrice2C4MasterAddReq req) {

        isPriorityDuplicate(req.getServeId(), null, req.getPriority());
        isConfigNameDuplicate(null,null, req.getConfigName());

        ServePrice servePrice = ServePriceMapper.mapToServePrice(req);
        servePrice.setPriceId(SnowFlakeGenerator.INSTANCE.generate());
        servePrice.setStatus(ServePriceStatusEnum.PENDING_REVIEW.getStatus());
        ServePrice save = servePriceRepository.save(servePrice);

        scheduleReject(servePrice);

        servePriceOperationLogService.add(save.getPriceId(), ServePriceOperationLog.OperationLogDetail.builder()
                .operator(CommonUtils.getCurrentLoginName())
                .operationType(ServePriceOperationTypeEnum.ADD.getOperationType())
                .build());
    }

    @Override
    public void modify(ServePrice2C4MasterModifyReq req) {
        Long priceId = req.getPriceId();
        // 先查出当前的服务价格详情，为后面记录日志用
        ServePrice2C4MasterDetail detail = getDetail(priceId);

        ServePrice servePriceExisting  = servePriceRepository.findOneByPriceId(priceId);
        if (servePriceExisting == null) {
            throw new BusException("服务价格id有误");
        }

        isPriorityDuplicate(servePriceExisting.getServeId(), priceId, req.getPriority());
        isConfigNameDuplicate(priceId, null, req.getConfigName());

        ServePrice servePriceIncoming = ServePriceMapper.mapToServePrice(req);

        // 如果提交的参数未变更，则不需要保存
        boolean same = EqualsBuilder.reflectionEquals(
                servePriceExisting,
                servePriceIncoming,
                false,
                ServePrice.class ,
                // 排除字段
                "id", "priceId", "createTime", "modifyTime", "createBy", "updateBy", "status", "del");
        if (same) {
            log.info("[服务价格] 服务价格相关数据未发生改变，无需更新");
            return;
        }

        String id = servePriceExisting.getId();
        servePriceIncoming.setId(id);
        servePriceIncoming.setPriceId(priceId);
        servePriceIncoming.setStatus(ServePriceStatusEnum.PENDING_REVIEW.getStatus());
        servePriceRepository.save(servePriceIncoming);

        scheduleReject(servePriceIncoming);

        servePriceOperationLogService.update(priceId, ServePriceOperationLog.OperationLogDetail.builder()
                        .operator(CommonUtils.getCurrentLoginName())
                        .operationType(ServePriceOperationTypeEnum.MODIFY.getOperationType())
                        .build(),
                detail);
    }

    @Override
    public ServePrice2C4MasterDetail getDetail(Long priceId) {
        ServePrice servePrice = servePriceRepository.findOneByPriceId(priceId);
        if (servePrice == null) {
            return null;
        }
        ServePrice2C4MasterDetail detail = new ServePrice2C4MasterDetail();
        BeanUtils.copyProperties(servePrice, detail);
        detail.setId(null);
        if (servePrice.getCityGroup() != null) {
            ServePrice2C4MasterDetail.CityGroup cityGroup = new ServePrice2C4MasterDetail.CityGroup();
            BeanUtils.copyProperties(servePrice.getCityGroup(), cityGroup);
            detail.setCityGroup(cityGroup);
        }

        Long serveId = servePrice.getServeId();
        List<ServeConfig4MasterPriceResp> serveConfigList = serveConfigServiceApi.getServeConfigListByServeId(serveId);
        if (CollectionUtils.isEmpty(serveConfigList)) {
            detail.setServeConfigs(null);
            return detail;
        }

        List<ServePrice2C4MasterDetail.ServeConfig> detailServeConfigs = ServeConfigMapper.mapToServeConfigList(serveConfigList);
        List<ServePrice.ServeConfig> serveConfigs = servePrice.getServeConfigs();
        if (CollectionUtil.isNotEmpty(serveConfigs)) {
            Set<Long> serveConfigIds = serveConfigs.stream().map(ServePrice.ServeConfig::getServeConfigId).collect(Collectors.toSet());
            detailServeConfigs = detailServeConfigs.stream().filter(e -> serveConfigIds.contains(e.getServeConfigId())).collect(Collectors.toList());
            ServeConfigPriceMerger.mergeSourceToTarget(serveConfigs, detailServeConfigs);
            detail.setServeConfigs(detailServeConfigs);
        }
        detail.setCreateTime(new Date());
        return detail;
    }


    @Override
    public void reject(Long priceId, String updateBy, String reason) {
        ServePrice servePrice = servePriceRepository.findOneByPriceId(priceId);
        if (servePrice == null) {
            throw new BusException("服务价格id有误");
        }
        // 只有处于到审核状态的才允许驳回
        if (!ServePriceStatusEnum.PENDING_REVIEW.getStatus().equals(servePrice.getStatus())) {
            return;
        }
        servePrice.setStatus(ServePriceStatusEnum.REJECTED.getStatus());
        servePriceRepository.save(servePrice);

        servePriceOperationLogService.update(priceId, ServePriceOperationLog.OperationLogDetail.builder()
                        .operator(updateBy)
                        .operationType(ServePriceOperationTypeEnum.REJECT.getOperationType())
                        .remark(reason)
                        .build(),
                null);
    }


    @Override
    public void approve(Long priceId) {
        ServePrice servePrice = servePriceRepository.findOneByPriceId(priceId);
        if (servePrice == null) {
            throw new BusException("服务价格id有误");
        }
        servePrice.setStatus(ServePriceStatusEnum.PENDING_ACTIVATION.getStatus());
        servePriceRepository.save(servePrice);

        this.scheduleActive(servePrice);

        servePriceOperationLogService.update(priceId, ServePriceOperationLog.OperationLogDetail.builder()
                        .operator(CommonUtils.getCurrentLoginName())
                        .operationType(ServePriceOperationTypeEnum.APPROVE.getOperationType())
                        .build(),
                null);
    }

    @Override
    public void inactive(Long priceId) {
        ServePrice servePrice = servePriceRepository.findOneByPriceId(priceId);
        if (servePrice == null) {
            throw new BusException("服务价格id有误");
        }
        servePrice.setStatus(ServePriceStatusEnum.INACTIVE.getStatus());
        servePriceRepository.save(servePrice);

        servePriceOperationLogService.update(priceId, ServePriceOperationLog.OperationLogDetail.builder()
                        .operator(CommonUtils.getCurrentLoginName())
                        .operationType(ServePriceOperationTypeEnum.INACTIVE.getOperationType())
                        .build(),
                null);

        //如果当前存在“待审核”“待生效”的草稿记录，价格配置点击“下线”时，均变更为“取消”
        Collection<String> statusList = Lists.newArrayList();
        statusList.add(DraftServePriceStatusEnum.PENDING_REVIEW.getStatus());
        statusList.add(DraftServePriceStatusEnum.PENDING_ACTIVATION.getStatus());

        DraftServePrice draftServePrice = draftServePriceRepository.findDraftServePriceByPriceIdAndDelAndStatusIn(priceId, false, statusList);
        if (Objects.nonNull(draftServePrice)) {
            draftServePriceDao.updateStatusByDraftId(draftServePrice.getDraftId(), DraftServePriceStatusEnum.CANCEL.getStatus(), null);
        }
    }


    @Override
    public void active(Long priceId, String updateBy) {
        servePriceDao.updateStatusByPriceId(priceId, ServePriceStatusEnum.ACTIVE.getStatus(), updateBy);

        servePriceOperationLogService.update(priceId, ServePriceOperationLog.OperationLogDetail.builder()
                        .operator(updateBy)
                        .operationType(ServePriceOperationTypeEnum.ACTIVE_SYSTEM.getOperationType())
                        .remark("首次上线时间")
                        .build(),
                null);
    }

    @Override
    public SimplePageInfo<ServePrice2C4MasterPageResp> getPageList(ServePrice2C4MasterPageReq req) {
        String cityName = req.getCityName();
        Long cityId = null;
        if (StringUtils.isNotBlank(cityName)) {
            final String finalCityName = cityName.trim();
            List<Address> addressList = addressApi.getByLevelAndNameLike(3, finalCityName);
            if (CollectionUtils.isNotEmpty(addressList)) {
                Address address = addressList.stream().filter(e -> e.getDivisionName().equals(finalCityName)).findFirst().orElse(null);
                if (address != null) {
                    cityId = address.getDivisionId();
                } else {
                    cityId = 0L;
                }
            } else {
                cityId = 0L;
            }
        }
        SimplePageInfo<ServePrice> servePage = servePriceDao.findPageByServeNameAndConfigNameAndCityIdAndStatus(req.getServeName(),
                req.getConfigName(), cityId, req.getStatus(), req.getPageNum(), req.getPageSize());

        List<ServePrice> servePriceList = servePage.getList();
        if (CollectionUtils.isEmpty(servePriceList)) {
            return new SimplePageInfo<>();
        }
        SimplePageInfo<ServePrice2C4MasterPageResp> resultPage = new SimplePageInfo<>(servePriceList.stream().map(e -> {
            ServePrice2C4MasterPageResp resp = new ServePrice2C4MasterPageResp();
            BeanUtils.copyProperties(e, resp);
            List<ServePrice.ServeConfig> serveConfigs = e.getServeConfigs();
            resp.setPriceParamCount(CollectionUtils.isEmpty(serveConfigs) ? 0 : serveConfigs.size());
            List<Long> cityIds = Optional.ofNullable(e.getCityIds()).orElseGet(Collections::emptyList);
            List<Long> groupCityIds = Optional.ofNullable(e.getCityGroup()).map(ServePrice.CityGroup::getCityIds).orElseGet(Collections::emptyList);
            Set<Long> cityIdSet = new HashSet<>();
            cityIdSet.addAll(cityIds);
            cityIdSet.addAll(groupCityIds);
            if (CollectionUtils.isNotEmpty(cityIdSet)) {
                List<Address> addressList = addressApi.getDivisionInfoListByDivisionIds(StringUtils.join(cityIdSet, ","));
                if (CollectionUtils.isNotEmpty(addressList)) {
                    String cityNames = addressList.stream().map(Address::getDivisionName).collect(Collectors.joining("、"));
                    resp.setCityNames(cityNames);
                }
            }
            return resp;
        }).collect(Collectors.toList()));
        resultPage.setTotal(servePage.getTotal());
        resultPage.setPageNum(servePage.getPageNum());
        resultPage.setPageSize(servePage.getPageSize());
        return resultPage;
    }

    @Override
    public ServePrice2C4MasterCountResp getCount() {
        ServePrice2C4MasterCountResp resp = new ServePrice2C4MasterCountResp();

        //正式数据待审核数量
        long servePricePendingReviewCount = servePriceDao.countByStatus(ServePriceStatusEnum.PENDING_REVIEW.getStatus());

        //草稿数据待审核数量
        long draftServePricePendingReviewCount = draftServePriceRepository.countByStatusAndDelIsFalse(DraftServePriceStatusEnum.PENDING_REVIEW.getStatus());

        resp.setPendingReviewCount(servePricePendingReviewCount);
        resp.setDraftPendingReviewCount(draftServePricePendingReviewCount);
        return resp;
    }

    @Override
    public SimplePageInfo<DraftServePrice2C4MasterPageResp> getDraftPageList(ServePrice2C4MasterPageReq req) {
        String cityName = req.getCityName();
        Long cityId = null;
        if (StringUtils.isNotBlank(cityName)) {
            final String finalCityName = cityName.trim();
            List<Address> addressList = addressApi.getByLevelAndNameLike(3, finalCityName);
            if (CollectionUtils.isNotEmpty(addressList)) {
                Address address = addressList.stream().filter(e -> e.getDivisionName().equals(finalCityName)).findFirst().orElse(null);
                if (address != null) {
                    cityId = address.getDivisionId();
                } else {
                    cityId = 0L;
                }
            } else {
                cityId = 0L;
            }
        }
        SimplePageInfo<DraftServePrice> servePage = draftServePriceDao.findPageByServeNameAndConfigNameAndCityIdAndStatus(req.getServeName(),
                req.getConfigName(), cityId, req.getStatus(), req.getPageNum(), req.getPageSize());

        List<DraftServePrice> servePriceList = servePage.getList();
        if (CollectionUtils.isEmpty(servePriceList)) {
            return new SimplePageInfo<>();
        }
        SimplePageInfo<DraftServePrice2C4MasterPageResp> resultPage = new SimplePageInfo<>(servePriceList.stream().map(e -> {
            DraftServePrice2C4MasterPageResp resp = new DraftServePrice2C4MasterPageResp();
            BeanUtils.copyProperties(e, resp);

            List<Long> cityIds = Optional.ofNullable(e.getCityIds()).orElseGet(Collections::emptyList);
            List<Long> groupCityIds = Optional.ofNullable(e.getCityGroup()).map(ServePrice.CityGroup::getCityIds).orElseGet(Collections::emptyList);
            Set<Long> cityIdSet = new HashSet<>();
            cityIdSet.addAll(cityIds);
            cityIdSet.addAll(groupCityIds);
            if (CollectionUtils.isNotEmpty(cityIdSet)) {
                List<Address> addressList = addressApi.getDivisionInfoListByDivisionIds(StringUtils.join(cityIdSet, ","));
                if (CollectionUtils.isNotEmpty(addressList)) {
                    String cityNames = addressList.stream().map(Address::getDivisionName).collect(Collectors.joining("、"));
                    resp.setCityNames(cityNames);
                }
            }
            return resp;
        }).collect(Collectors.toList()));
        resultPage.setTotal(servePage.getTotal());
        resultPage.setPageNum(servePage.getPageNum());
        resultPage.setPageSize(servePage.getPageSize());
        return resultPage;
    }

    @Override
    public DraftServePriceIsExistResp getIsExistDraftByPriceId(DraftServePriceIsExistReq req) {
        DraftServePriceIsExistResp resp = new DraftServePriceIsExistResp();
        resp.setExist(isExistDraft(req.getPriceId()));
        return resp;
    }

    @Override
    public void addDraft(DraftServePrice2C4MasterAddReq req) {
        isPriorityDuplicate(req.getServeId(), req.getPriceId(), req.getPriority());

        isConfigNameDuplicate(req.getPriceId(), null, req.getConfigName());

        //校验是否存在待审核、待生效状态草稿
        boolean exist = isExistDraft(req.getPriceId());
        if (exist) {
            throw new BusException("当前有流程中的改价，请结束该改价流程后再加调整");
        }

        ServePrice servePrice  = servePriceRepository.findOneByPriceId(req.getPriceId());

        if (servePrice == null) {
            throw new BusException("该草稿没有关联到正式数据");
        }

        DraftServePrice draftServePrice = DraftServePriceMapper.mapToDraftServePrice(req);
        draftServePrice.setDraftId(SnowFlakeGenerator.INSTANCE.generate());
        draftServePrice.setPriceId(req.getPriceId());
        draftServePrice.setCreateBy(servePrice.getCreateBy());
        draftServePrice.setStatus(DraftServePriceStatusEnum.PENDING_REVIEW.getStatus());
        draftServePriceRepository.save(draftServePrice);

        scheduleCancelDraft(draftServePrice);
    }



    @Override
    public void modifyDraft(DraftServePrice2C4MasterModifyReq req) {
        Long draftId = req.getDraftId();

        DraftServePrice draftServePriceExisting = draftServePriceRepository.findOneByDraftId(draftId);
        if (draftServePriceExisting == null) {
            throw new BusException("改价草稿id有误");
        }

        // 只有处于待审核状态的才允许编辑
        if (!DraftServePriceStatusEnum.PENDING_REVIEW.getStatus().equals(draftServePriceExisting.getStatus())) {
            return;
        }


        Long priceId = draftServePriceExisting.getPriceId();

        ServePrice servePrice  = servePriceRepository.findOneByPriceId(priceId);
        if (servePrice == null) {
            throw new BusException("该草稿没有关联到正式数据");
        }

        //校验已存在的服务是否有权重重复
        isPriorityDuplicate(servePrice.getServeId(), priceId, req.getPriority());

        //校验编辑后的服务id和权重是否重复
        isPriorityDuplicate(req.getServeId(), priceId, req.getPriority());

        //校验已存在配置名称是否重复
        isConfigNameDuplicate(priceId, draftId, req.getConfigName());

        DraftServePrice draftServePrice = DraftServePriceMapper.mapToDraftServePrice(req);

        // 如果提交的参数未变更，则不需要保存
        boolean same = EqualsBuilder.reflectionEquals(
                draftServePriceExisting,
                draftServePrice,
                false,
                DraftServePrice.class,
                // 排除字段
                "id", "priceId", "createTime", "modifyTime", "createBy", "updateBy", "status", "del", "draftId", "remark");
        if (same) {
            log.info("[服务价格草稿] 服务价格草稿相关数据未发生改变，无需更新");
            return;
        }

        String id = draftServePriceExisting.getId();
        draftServePrice.setId(id);
        draftServePrice.setPriceId(priceId);
        draftServePrice.setDraftId(draftId);
        draftServePrice.setCreateBy(draftServePriceExisting.getCreateBy());
        draftServePrice.setStatus(DraftServePriceStatusEnum.PENDING_REVIEW.getStatus());
        draftServePriceRepository.save(draftServePrice);

        //可能存在修改前的定时任务没有关闭的问题
        scheduleCancelDraft(draftServePrice);

    }

    @Override
    public DraftServePrice2C4MasterDetail getDraftDetail(Long draftId) {
        DraftServePrice draftServePrice = draftServePriceRepository.findOneByDraftId(draftId);
        if (draftServePrice == null) {
            return null;
        }
        DraftServePrice2C4MasterDetail detail = new DraftServePrice2C4MasterDetail();
        BeanUtils.copyProperties(draftServePrice, detail);
        if (draftServePrice.getCityGroup() != null) {
            DraftServePrice2C4MasterDetail.CityGroup cityGroup = new DraftServePrice2C4MasterDetail.CityGroup();
            BeanUtils.copyProperties(draftServePrice.getCityGroup(), cityGroup);
            detail.setCityGroup(cityGroup);
        }

        Long serveId = draftServePrice.getServeId();
        List<ServeConfig4MasterPriceResp> serveConfigList = serveConfigServiceApi.getServeConfigListByServeId(serveId);
        if (CollectionUtils.isEmpty(serveConfigList)) {
            detail.setServeConfigs(null);
            return detail;
        }

        List<ServePrice2C4MasterDetail.ServeConfig> detailServeConfigs = ServeConfigMapper.mapToServeConfigList(serveConfigList);
        List<DraftServePrice.ServeConfig> serveConfigs = draftServePrice.getServeConfigs();
        if (CollectionUtil.isNotEmpty(serveConfigs)) {
            Set<Long> serveConfigIds = serveConfigs.stream().map(DraftServePrice.ServeConfig::getServeConfigId).collect(Collectors.toSet());
            detailServeConfigs = detailServeConfigs.stream().filter(e -> serveConfigIds.contains(e.getServeConfigId())).collect(Collectors.toList());
            ServeConfigPriceMerger.mergeSourceToTargetByDraft(serveConfigs, detailServeConfigs);
            detail.setServeConfigs(detailServeConfigs);
        }
        detail.setCreateTime(new Date());
        return detail;
    }

    @Override
    public void approveDraft(Long draftId) {
        DraftServePrice draftServePrice = draftServePriceRepository.findOneByDraftId(draftId);
        if (draftServePrice == null) {
            throw new BusException("改价草稿id有误");
        }
        draftServePrice.setStatus(DraftServePriceStatusEnum.PENDING_ACTIVATION.getStatus());
        draftServePriceRepository.save(draftServePrice);

        this.scheduleOnlineDraft(draftServePrice);
    }

    @Override
    public void rejectDraft(Long draftId, String updateBy, String reason) {
        DraftServePrice draftServePrice = draftServePriceRepository.findOneByDraftId(draftId);
        if (draftServePrice == null) {
            throw new BusException("改价草稿id有误");
        }
        // 只有处于待审核状态的才允许驳回
        if (!DraftServePriceStatusEnum.PENDING_REVIEW.getStatus().equals(draftServePrice.getStatus())) {
            return;
        }
        draftServePrice.setStatus(DraftServePriceStatusEnum.REJECTED.getStatus());
        draftServePrice.setRemark("驳回原因：".concat(reason));
        draftServePriceRepository.save(draftServePrice);
    }

    @Override
    public void cancelDraft(Long draftId, String remark) {
        DraftServePrice draftServePrice = draftServePriceRepository.findOneByDraftId(draftId);
        if (draftServePrice == null) {
            throw new BusException("改价草稿id有误");
        }

        draftServePrice.setStatus(DraftServePriceStatusEnum.CANCEL.getStatus());
        draftServePrice.setRemark(remark);
        draftServePriceRepository.save(draftServePrice);
    }

    @Override
    public void onlineDraft(Long draftId, String updateBy) {
        draftServePriceDao.updateStatusByDraftId(draftId, DraftServePriceStatusEnum.ONLINE.getStatus(), null);

        DraftServePrice draftServePrice = draftServePriceRepository.findOneByDraftId(draftId);
        Long priceId = draftServePrice.getPriceId();


        // 获取当前的日期和时间
        LocalDateTime currentDateTime = LocalDateTime.now();
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 格式化日期时间
        String formattedDateTime = currentDateTime.format(formatter);

        //记录操作日志并保存改价上线前数据
        ServePrice2C4MasterDetail detail = getDetail(priceId);

        servePriceOperationLogService.update(priceId, ServePriceOperationLog.OperationLogDetail.builder()
                        .operator(updateBy)
                        .operationType(ServePriceOperationTypeEnum.DRAFT_ONLINE.getOperationType())
                        .remark("本配置生效时间：".concat(formattedDateTime))
                        .build(),
                detail);


        //覆盖正式数据
        ServePrice servePrice = servePriceRepository.findOneByPriceId(priceId);
        String id = servePrice.getId();
        String status = servePrice.getStatus();

        servePrice = ServePriceByDraftMapper.mapToServePrice(draftServePrice);
        servePrice.setId(id);
        //草稿状态与正式数据状态不一致，这里不能覆盖
        servePrice.setStatus(status);
        servePriceRepository.save(servePrice);

    }

    @Override
    public void scheduleCancelDraft(DraftServePrice draftServePrice) {
        Instant nowUtc = Instant.now();
        Date now = Date.from(nowUtc);
        //改价草稿待生效时间到了还是待审核状态，则算超时自动取消
        if (draftServePrice.getStartTime().after(now)) {
            scheduler.schedule(() -> {
                        Long draftId = draftServePrice.getDraftId();
                        DraftServePrice price = draftServePriceRepository.findOneByDraftId(draftId);
                        if (price != null && DraftServePriceStatusEnum.PENDING_REVIEW.getStatus().equals(price.getStatus())) {
                            this.cancelDraft(draftId, "超时自动取消");
                        }
                    },
                    draftServePrice.getStartTime());
        }
    }

    @Override
    public void scheduleOnlineDraft(DraftServePrice draftServePrice) {
        Instant nowUtc = Instant.now();
        Date now = Date.from(nowUtc);
        //改价草稿待生效时间到了是待生效状态,则变成已上线
        if (draftServePrice.getStartTime().after(now)) {
            scheduler.schedule(() -> {
                        Long draftId = draftServePrice.getDraftId();
                        DraftServePrice price = draftServePriceRepository.findOneByDraftId(draftId);
                        if (price != null && DraftServePriceStatusEnum.PENDING_ACTIVATION.getStatus().equals(price.getStatus())) {
                            this.onlineDraft(draftId, draftServePrice.getCreateBy());
                        }
                    },
                    draftServePrice.getStartTime());
        }
    }


    @Override
    public void scheduleActive(ServePrice servePrice) {
        Instant nowUtc = Instant.now();
        Date now = Date.from(nowUtc);
        // 生效任务，当前时间在“开始生效时间”之前，则需要创建待生效任务
        if (servePrice.getStartTime().after(now)) {
            scheduler.schedule(() -> {
                        Long priceId = servePrice.getPriceId();
                        ServePrice price = servePriceRepository.findOneByPriceId(priceId);
                        if (price != null && ServePriceStatusEnum.PENDING_ACTIVATION.getStatus().equals(price.getStatus())) {
                            this.active(servePrice.getPriceId(), "系统");
                        }
                    },
                    servePrice.getStartTime());
        }
    }

    @Override
    public void scheduleReject(ServePrice servePrice) {
        Instant nowUtc = Instant.now();
        Date now = Date.from(nowUtc);
        // 生效任务，当前时间在“开始生效时间”之前，则需要创建待生效任务
        if (servePrice.getStartTime().after(now)) {
            scheduler.schedule(() -> {
                        Long priceId = servePrice.getPriceId();
                        ServePrice price = servePriceRepository.findOneByPriceId(priceId);
                        if (price != null && ServePriceStatusEnum.PENDING_REVIEW.getStatus().equals(price.getStatus())) {
                            this.reject(servePrice.getPriceId(), "系统", "超时未审核，系统自动驳回");
                        }
                    },
                    servePrice.getStartTime());
        }
    }

    private void isPriorityDuplicate(Long serveId, Long priceId, Integer priority) {
        // 修改
        if (priceId != null) {
            ServePrice price = servePriceDao.findOneByServeIdAndPriorityExcludeByPriceId(serveId, priority, priceId);
            if (price != null) {
                throw new BusException("当前服务的权重值已存在，请修正后再提交");
            }
        } else { // 新增
            ServePrice price = servePriceDao.findOneByServeIdAndPriority(serveId, priority);
            if (price != null) {
                throw new BusException("当前服务的权重值已存在，请修正后再提交");
            }
        }
    }

    private void isConfigNameDuplicate(Long priceId, Long draftId, String configName) {

        if (StringUtils.isEmpty(configName)) {
            return;
        }
        configName = configName.trim();
        // 修改
        if (priceId != null) {
            ServePrice price = servePriceDao.findOneByConfigNameExcludeByPriceId(configName, priceId);
            if (price != null) {
                throw new BusException("当前服务的配置名称已存在，请修正后再提交");
            }
        } else { // 新增
            ServePrice price = servePriceDao.findOneByConfigName(configName);
            if (price != null) {
                throw new BusException("当前服务的配置名称已存在，请修正后再提交");
            }
        }

        // 修改
        if (draftId != null) {
            DraftServePrice draftServePrice = draftServePriceDao.findOneByConfigNameExcludeByDraftId(configName, draftId);
            if (draftServePrice != null) {
                throw new BusException("当前服务的配置名称已存在，请修正后再提交");
            }
        } else { // 新增
            DraftServePrice draftServePrice = draftServePriceDao.findOneByConfigName(configName);
            if (draftServePrice != null) {
                throw new BusException("当前服务的配置名称已存在，请修正后再提交");
            }
        }
    }

    /**
     * 判断是否存在待审核、待生效状态草稿
     * @param priceId
     */
    private boolean isExistDraft(Long priceId) {
        Collection<String> statusList = Lists.newArrayList();
        statusList.add(DraftServePriceStatusEnum.PENDING_REVIEW.getStatus());
        statusList.add(DraftServePriceStatusEnum.PENDING_ACTIVATION.getStatus());

        DraftServePrice draftServePrice = draftServePriceRepository.findDraftServePriceByPriceIdAndDelAndStatusIn(priceId, false, statusList);
        if (Objects.nonNull(draftServePrice)) {
            return true;
        } else {
            return false;
        }

    }
}
