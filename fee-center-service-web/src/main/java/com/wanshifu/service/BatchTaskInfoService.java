package com.wanshifu.service;

import com.wanshifu.domain.request.BatchTaskGenerateReq;
import com.wanshifu.domain.request.BatchTaskUploadReq;
import com.wanshifu.domain.request.CustomSkuTemplateUploadReq;
import com.wanshifu.fee.center.domain.document.BatchTaskInfo;
import com.wanshifu.fee.center.domain.request.BatchTaskQueryReq;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;

import java.io.OutputStream;
import java.util.Set;

public interface BatchTaskInfoService {
    Page<BatchTaskInfo> queryByCondition(BatchTaskQueryReq batchTaskQueryReq);

    BatchTaskInfo upload(BatchTaskUploadReq batchTaskUploadReq);

    byte[] generateTable(BatchTaskGenerateReq batchTaskGenerateReq);

    String resend(Set<Long> ids);

    void uploadCustomSkuTemplate(CustomSkuTemplateUploadReq req);
}
