package com.wanshifu.service;

import com.wanshifu.domain.request.CrossSceneExportReq;
import com.wanshifu.domain.request.ExportTaskAddReq;
import com.wanshifu.domain.request.ExportTaskQueryReq;
import com.wanshifu.fee.center.domain.document.ExportTaskInfo;
import org.springframework.boot.web.support.SpringBootServletInitializer;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Set;

public interface ExportTaskInfoService {

    Page<ExportTaskInfo> queryByCondition(ExportTaskQueryReq exportTaskQueryReq);

    ExportTaskInfo addTask(ExportTaskAddReq exportTaskAddReq);

    String resend(Set<Long> ids);

    ExportTaskInfo crossSceneExport(CrossSceneExportReq req);

//    void doCrossSceneExport(CrossSceneExportReq req, ExportTaskInfo exportTaskInfo);
}
