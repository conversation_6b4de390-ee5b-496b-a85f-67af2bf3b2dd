package com.wanshifu.service;

import com.wanshifu.domain.request.samedayconfig.*;
import com.wanshifu.fee.center.domain.request.sameday.GetPriceRequest;
import com.wanshifu.fee.center.domain.response.sameday.GetPriceResponse;
import com.wanshifu.framework.core.page.SimplePageInfo;

import java.util.List;

public interface SameDayPriceConfigService {

    /**
     * 状态开关（开通/关闭）
     *
     * @param request request body
     * @see com.wanshifu.fee.center.domain.enums.SameDayPriceConfigStateEnum
     */
    void stateSwitch(StateSwitchRequest request);

    /**
     * 修改价格
     *
     * @param request 修改价格request body
     */
    void modifyPrice(ModifyPriceRequest request);

    void add(SaveRequest request);

    void modify(SaveRequest request);

    PriceDetailResponse detail(String sameDayPriceConfigId);

    SimplePageInfo<PricePageListResponse> pageList(Long level1GoodsCategoryId, String activationState, int pageNum, int pageSize);

    List<Level1GoodsCategoryResponse> level1GoodsCategory();

    GetPriceResponse getPrice(GetPriceRequest request);
}
