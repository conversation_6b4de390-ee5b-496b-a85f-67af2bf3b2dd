package com.wanshifu.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Sets;
import com.mongodb.BasicDBObject;
import com.mongodb.MongoClient;
import com.mongodb.client.ClientSession;
import com.ql.util.express.DefaultContext;
import com.ql.util.express.ExpressRunner;
import com.wanshifu.adapter.api.ServiceApi;
import com.wanshifu.adapter.dto.service.GoodsAndServiceType;
import com.wanshifu.api.BigdataOpenServiceApi;
import com.wanshifu.api.resp.GetSkuTemplateInfoBySceneCodeResp;
import com.wanshifu.api.rqt.GetSkuTemplateInfoBySceneCodeRqt;
import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.domain.converter.ApplyOrderCalculateReqToStandardPricingRequestConverter;
import com.wanshifu.domain.dto.ApplyOrderCalculateDTO;
import com.wanshifu.fee.center.domain.biz.AutoReceiveOrderGuidePriceBizRule;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.constant.MQTagConstant;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.*;
import com.wanshifu.fee.center.domain.dto.BargainPriceAggregationResult;
import com.wanshifu.fee.center.domain.dto.ExpressResultInfo;
import com.wanshifu.fee.center.domain.dto.FeeBizRuleDTO;
import com.wanshifu.fee.center.domain.dto.FeeRuleAggregationResult;
import com.wanshifu.fee.center.domain.enums.*;
import com.wanshifu.fee.center.domain.request.*;
import com.wanshifu.fee.center.domain.request.calculate.StandardPricingRequest;
import com.wanshifu.fee.center.domain.request.calculate.StandardPricingResponse;
import com.wanshifu.fee.center.domain.request.common.RangeReq;
import com.wanshifu.fee.center.domain.request.feeRule.*;
import com.wanshifu.fee.center.domain.request.feeRule.master.*;
import com.wanshifu.fee.center.domain.request.mapping.MappingQueryPageRequest;
import com.wanshifu.fee.center.domain.response.*;
import com.wanshifu.fee.center.domain.response.bigdata.*;
import com.wanshifu.fee.center.domain.response.feerule.BatchAddRulesResponse;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.framework.redission.autoconfigure.component.RedissonHelper;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.framework.rocketmq.autoconfigure.model.NormalMessage;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.MapUtils;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.config.ApolloConfig;
import com.wanshifu.infrastructure.gateway.BigdataGateway;
import com.wanshifu.infrastructure.utils.CommonUtils;
import com.wanshifu.infrastructure.utils.EnvUtil;
import com.wanshifu.infrastructure.utils.NoticeUtil;
import com.wanshifu.infrastructure.utils.PatternUtils;
import com.wanshifu.order.config.api.ServeServiceApi;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.repository.*;
import com.wanshifu.service.bigdata.BigdataSyncCoordinator;
import com.wanshifu.strategy.apply.CommonSceneFeeRuleStrategy;
import com.wanshifu.strategy.apply.PriceCalculator;
import com.wanshifu.strategy.apply.SceneFeeRuleStrategy;
import com.wanshifu.strategy.express.ExpressCompilerComposite;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;
import static org.springframework.data.mongodb.core.query.Criteria.where;

@Service
@Slf4j
public class FeeRuleServiceImpl implements FeeRuleService {
    @Resource
    private FeeRuleRepository feeRuleRepository;

    @Resource
    private FeeTemplateRepository feeTemplateRepository;

    @Resource
    private CalculateResultRepository calculateResultRepository;
    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private SceneInfoRepository sceneInfoRepository;

    @Resource
    private SceneFeeRuleStrategy sceneFeeRuleStrategyComposite;

    @Resource
    private AddressApi addressApi;

    @Resource
    private RedissonHelper redissonHelper;

    @Resource
    private ExpressRunner expressRunner;
    @Resource
    private FormulaService formulaService;
    @Resource
    private ServiceApi serviceApi;
    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private CommonSceneFeeRuleStrategy commonSceneFeeRuleStrategy;
    @Resource
    private RecruitActivityMasterCreateStatusRepository recruitActivityMasterCreateStatusRepository;

    @Resource
    private BizRuleMappingService bizRuleMappingService;
    @Resource
    private FeeTemplateService feeTemplateService;
    @Resource
    private ExpressCompilerComposite expressCompilerComposite;
    @Resource
    private ServeServiceApi serveServiceApi;
    @Resource
    private RocketMqSendService rocketMqSendService;
    @Resource
    private BigdataGateway bigdataGateway;
    @Resource
    private AsyncTasksInfoService asyncTasksInfoService;
    @Resource
    private TaskDurationRepository taskDurationRepository;
    @Resource
    private FeeTemplateMappingService feeTemplateMappingService;
    @Resource
    private StandardPricingRequestBodyLogRepository standardPricingRequestBodyLogRepository;
    @Resource
    private MongoClient mongoClient;
    @Resource
    private PriceCalculator priceCalculator;

    @Resource
    private BigdataOpenServiceApi bigdataOpenServiceApi;

    @Resource
    private SyncBlacklistStrategyService syncBlacklistStrategyService;

    @Resource
    private SceneInfoService sceneInfoService;

    @Value("${fee.rule.maxCount:1500}")
    private Integer feeRuleMaxCount;
    @Value("${fee.rule.bargain_price_everyday_new:true}")
    private Boolean bargainPriceEveryDayNew;
    @Value("${wanshifu.rocketMQ.fee-center-service-general-topic}")
    private String feeCenterServiceGeneralTopic;
    @Value("${pull-feeRule-from-bigdata-pageSize:1000}")
    private Integer pageSizeInner;

    @Value("${feeRule.batchDeleteLogicallyDeletedFlag:true}")
    private Boolean batchDeleteLogicallyDeletedFlag;
    @Value("${feeRule.pullFeeRuleFromBigdataFlag:true}")
    private Boolean pullFeeRuleFromBigdataFlag;

    @Value("${delete-feeRule-pageSize:1000}")
    private Integer deleteFeeRulePageSize;
    @Value("${spring.profiles.active}")
    private String activeProfiles;
    @Value("${stand-pricing-scene-codes:auto_receive_order_guide_price,template10}")
    private String sceneCodes;
    @Value("${stand-pricing-switch:true}")
    private Boolean standPricingSwitch;
    @Value("${preGenerateLowestPrice:true}")
    private Boolean preGenerateLowestPrice;

    @Resource
    private RedisHelper redisHelper;

    private final static String lowestPricePrefix = FeeRuleServiceImpl.class.getName() + ":getLowestPriceByLevel1GoodsCategoryIdOrServiceIds";

    private final static String preGenerateBargainPriceEverydayFeeRuleRedisKey = FeeRuleServiceImpl.class.getName() + ":preGenerateBargainPriceEverydayFeeRule:versionNo";
    private final static String preGeneratePlatformFixedPriceFeeRuleRedisKey = FeeRuleServiceImpl.class.getName() + ":preGeneratePlatformFixedPriceFeeRule:versionNo";
    private final static String pullFeeRuleFromBigdataLockRedisKey = "feeCenterService:" + FeeRuleServiceImpl.class.getName() + ":pullFeeRuleFromBigdataLock";
    private final static String batchDeleteLogicallyDeletedFeeRuleLockRedisKey = "feeCenterService:" + FeeRuleServiceImpl.class.getName() + ":batchDeleteLogicallyDeletedFeeRuleLock";

    private volatile FeeRuleServiceImpl thisProxy;

    @PostConstruct
    private void initProxy() {
        if (thisProxy == null) {
            synchronized (this) {
                if (thisProxy == null) {
                    thisProxy = applicationContext.getBean(FeeRuleServiceImpl.class);
                }
            }
        }
    }

    @Override
    public List<FeeRule> queryByCondition(FeeRuleQueryReq feeRuleQueryReq) {
        Criteria criteria = where(BaseDocument.Fields.del).is(false);
        if (Objects.nonNull(feeRuleQueryReq.getTemplateId())) {
            criteria.and(FeeRule.Fields.templateId).is(feeRuleQueryReq.getTemplateId());
        }
        if (StringUtils.isNotBlank(feeRuleQueryReq.getSceneCode())) {
            criteria.and(FeeRule.Fields.sceneCode).is(feeRuleQueryReq.getSceneCode());
        }
        if (StringUtils.isNotBlank(feeRuleQueryReq.getGroup())) {
            criteria.and(FeeRule.Fields.group).is(feeRuleQueryReq.getGroup());
        }
        if (StringUtils.isNotBlank(feeRuleQueryReq.getSceneName())) {
            criteria.and(FeeRule.Fields.sceneName).regex(PatternUtils.toEscapeStr(feeRuleQueryReq.getSceneName()));
        }
        if (StringUtils.isNotBlank(feeRuleQueryReq.getFeeName())) {
            criteria.and(FeeRule.Fields.feeName).regex(PatternUtils.toEscapeStr(feeRuleQueryReq.getFeeName()));
        }
        if (Objects.nonNull(feeRuleQueryReq.getFeeRuleId())) {
            criteria.and(FeeRule.Fields.feeRuleId).is(feeRuleQueryReq.getFeeRuleId());
        }
        if (MapUtils.isNotEmpty(feeRuleQueryReq.getBizRule())) {
            feeRuleQueryReq.getBizRule().forEach((key, value) ->
                    criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + key).is(value)
            );
        }

        Query query = Query.query(criteria);

        long count = mongoTemplate.count(query, FeeRule.class);
        // 超过阈值返回空集合
        if (count > feeRuleMaxCount) {
            return new ArrayList<>();
        }

        Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.sort));
        query.with(sort);

        return mongoTemplate.find(query, FeeRule.class);
    }

    @Override
    public List<FeeRule> queryMasterByCondition(FeeRuleQueryReq feeRuleQueryReq) {
        Map<String, String> bizRule = feeRuleQueryReq.getBizRule();
        String batchTaskId = bizRule.get("batchTaskId");
        if (StringUtils.isBlank(batchTaskId)) {
            throw new BusException("批量任务id不能为空");
        }

        Criteria criteria = where(BaseDocument.Fields.del).is(false);
        if (Objects.nonNull(feeRuleQueryReq.getTemplateId())) {
            criteria.and(FeeRule.Fields.templateId).is(feeRuleQueryReq.getTemplateId());
        }
        if (StringUtils.isNotBlank(feeRuleQueryReq.getSceneCode())) {
            criteria.and(FeeRule.Fields.sceneCode).is(feeRuleQueryReq.getSceneCode());
        }
        if (StringUtils.isNotBlank(feeRuleQueryReq.getGroup())) {
            criteria.and(FeeRule.Fields.group).is(feeRuleQueryReq.getGroup());
        }
        if (StringUtils.isNotBlank(feeRuleQueryReq.getSceneName())) {
            criteria.and(FeeRule.Fields.sceneName).regex(PatternUtils.toEscapeStr(feeRuleQueryReq.getSceneName()));
        }
        if (StringUtils.isNotBlank(feeRuleQueryReq.getFeeName())) {
            criteria.and(FeeRule.Fields.feeName).regex(PatternUtils.toEscapeStr(feeRuleQueryReq.getFeeName()));
        }
        if (Objects.nonNull(feeRuleQueryReq.getFeeRuleId())) {
            criteria.and(FeeRule.Fields.feeRuleId).is(feeRuleQueryReq.getFeeRuleId());
        }
        if (MapUtils.isNotEmpty(bizRule)) {
            bizRule.forEach((key, value) ->
                    criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + key).is(value)
            );
        }

        Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.sort));

        Aggregation feeRuleDraftAggregation = newAggregation(
                match(criteria), // 查询条件
                group(FeeRule.Fields.bizRule + PunConstant.DOT + "masterId",
                        FeeRule.Fields.bizRule + PunConstant.DOT + "serviceId",
                        FeeRule.Fields.bizRule + PunConstant.DOT + "divisionType",
                        FeeRule.Fields.bizRule + PunConstant.DOT + "level1DivisionId",
                        FeeRule.Fields.bizRule + PunConstant.DOT + "level2DivisionId",
                        FeeRule.Fields.bizRule + PunConstant.DOT + "level3DivisionId",
                        FeeRule.Fields.bizRule + PunConstant.DOT + "level4DivisionId"
                ).first("$$ROOT").as("info"),
                Aggregation.replaceRoot("$info"),
                Aggregation.sort(sort)
        );
        AggregationResults<FeeRule> aggregate = mongoTemplate.aggregate(feeRuleDraftAggregation, "feeRule", FeeRule.class);
        if (Objects.isNull(aggregate)) {
            return Collections.emptyList();
        }
        return aggregate.getMappedResults();
    }

    @Override
    public List<FeeRule> batchQueryByCondition(FeeRuleBatchQueryReq feeRuleBatchQueryReq) {
        Criteria criteria = where(BaseDocument.Fields.del).is(false);
        if (Objects.nonNull(feeRuleBatchQueryReq.getTemplateId())) {
            criteria.and(FeeRule.Fields.templateId).is(feeRuleBatchQueryReq.getTemplateId());
        }
        if (StringUtils.isNotBlank(feeRuleBatchQueryReq.getSceneCode())) {
            criteria.and(FeeRule.Fields.sceneCode).is(feeRuleBatchQueryReq.getSceneCode());
        }
        if (StringUtils.isNotBlank(feeRuleBatchQueryReq.getGroup())) {
            criteria.and(FeeRule.Fields.group).is(feeRuleBatchQueryReq.getGroup());
        }
        if (StringUtils.isNotBlank(feeRuleBatchQueryReq.getSceneName())) {
            criteria.and(FeeRule.Fields.sceneName).regex(PatternUtils.toEscapeStr(feeRuleBatchQueryReq.getSceneName()));
        }
        if (StringUtils.isNotBlank(feeRuleBatchQueryReq.getFeeName())) {
            criteria.and(FeeRule.Fields.feeName).regex(PatternUtils.toEscapeStr(feeRuleBatchQueryReq.getFeeName()));
        }
        if (Objects.nonNull(feeRuleBatchQueryReq.getFeeRuleId())) {
            criteria.and(FeeRule.Fields.feeRuleId).is(feeRuleBatchQueryReq.getFeeRuleId());
        }
        if (MapUtils.isNotEmpty(feeRuleBatchQueryReq.getBizRule())) {
            feeRuleBatchQueryReq.getBizRule().forEach((key, value) -> {
                if (StringUtils.isNotBlank(key) && CollectionUtils.isNotEmpty(value)) {
                    criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + key).in(value);
                }
            });
        }
        Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.sort));

        Query query = Query.query(criteria);
        query.with(sort);
        return mongoTemplate.find(query, FeeRule.class);
    }

    @Override
    public List<FeeRule> queryGuidePrice(FeeRuleGuidePriceReq feeRuleGuidePriceReq) {
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code);

        criteria.and(FeeRule.Fields.sceneCode).is(feeRuleGuidePriceReq.getSceneCode());
        // 服务id
        if (CollectionUtils.isNotEmpty(feeRuleGuidePriceReq.getServiceId())) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + BizFieldEnum.SERVICE_ID.name).in(feeRuleGuidePriceReq.getServiceId());
        } else {
            List<String> serviceCategoryId = feeRuleGuidePriceReq.getServiceCategoryId();
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + BizFieldEnum.SERVICE_CATEGORY_ID.name).in(serviceCategoryId);
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + BizFieldEnum.SERVICE_MODEL_ID.name).is(feeRuleGuidePriceReq.getServiceModelId());
        }

        // 地区信息
        DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(feeRuleGuidePriceReq.getDivisionType());
        if (Objects.nonNull(divisionTypeEnum)) {
            Criteria baseDivision = where(FeeRule.Fields.bizRule + PunConstant.DOT + BizFieldEnum.DIVISION_TYPE.name).is(divisionTypeEnum.code);
            baseDivision.and(FeeRule.Fields.bizRule + PunConstant.DOT + divisionTypeEnum.divisionKey).in(feeRuleGuidePriceReq.getDivisionIds());
            List<FeeRuleGuidePriceReq.ParentDivisionUnit> parentDivisionUnits = feeRuleGuidePriceReq.getParentDivisionUnits();

            List<Criteria> criteriaList = new ArrayList<>();
            criteriaList.add(baseDivision);
            if (CollectionUtils.isNotEmpty(parentDivisionUnits)) {
                for (FeeRuleGuidePriceReq.ParentDivisionUnit parentDivisionUnit : parentDivisionUnits) {
                    DivisionTypeEnum parent = DivisionTypeEnum.fromCode(parentDivisionUnit.getDivisionType());
                    if (Objects.isNull(parent)) {
                        throw new BusException(parentDivisionUnit.getDivisionType() + "父级区域类型不合法");
                    }
                    Criteria parentDivision = where(FeeRule.Fields.bizRule + PunConstant.DOT + BizFieldEnum.DIVISION_TYPE.name).is(parent.code);
                    if (DivisionTypeEnum.COUNTRY != parent) {
                        parentDivision.and(FeeRule.Fields.bizRule + PunConstant.DOT + parent.divisionKey).is(parentDivisionUnit.getDivisionId());
                    }
                    criteriaList.add(parentDivision);
                }
            }

            criteria.orOperator(criteriaList.toArray(new Criteria[0]));

        }

        Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.sort));

        Query query = Query.query(criteria);
        query.with(sort);
        return mongoTemplate.find(query, FeeRule.class);
    }

    @Override
    public FeeRule save(FeeRule feeRule) {
        if (StringUtils.isEmpty(feeRule.getSceneCode())) {
            throw new BusException("场景为空");
        }

        if (Objects.isNull(feeRule.getTemplateId())) {
            throw new BusException("来源模板为空");
        }

//        if (StringUtils.isEmpty(feeRule.getFeeName())) {
//            throw new BusException("算费名称为空");
//        }
        if (Objects.isNull(feeRule.getCalculateRuleData()) || StringUtils.isEmpty(feeRule.getCalculateRuleData().getExpress())) {
            throw new BusException("算费规则为空");
        }
        if (MapUtils.isEmpty(feeRule.getBizRule())) {
            throw new BusException("业务规则为空");
        }

        FeeTemplate feeTemplate = feeTemplateRepository.findByTemplateId(feeRule.getTemplateId());
        if (Objects.isNull(feeTemplate)) {
            throw new BusException("对应模板不存在，templateId=" + feeRule.getTemplateId());
        }

        Date now = new Date();
        String currentLoginName = CommonUtils.getCurrentLoginName();
        feeRule.setFeeRuleId(SnowFlakeGenerator.INSTANCE.generate());
        feeRule.setDel(false);
        feeRule.setStatus(RuleStatusEnum.ACTIVE.code);
        feeRule.setCreateTime(now);
        feeRule.setModifyTime(now);
        feeRule.setCreateBy(currentLoginName);
        feeRule.setUpdateBy(currentLoginName);

        if (Objects.isNull(feeRule.getCalculateRuleData())) {
            feeRule.setCalculateRuleData(feeTemplate.getCalculateRuleData());
        }

        feeRule.setSort(feeTemplate.getSort());
        FeeRule save = feeRuleRepository.save(feeRule);
//        applicationEventPublisher.publishEvent(new BuildIndexEvent(save));
        return save;
    }

    @Override
    public FeeRule update(FeeRule feeRule) {
        if (Objects.isNull(feeRule.getFeeRuleId())) {
            throw new BusException("FeeRuleId不能为空");
        }
        FeeRule byFeeRuleId = feeRuleRepository.findByFeeRuleId(feeRule.getFeeRuleId());
        if (Objects.isNull(byFeeRuleId)) {
            throw new BusException("计费规则不存在,id=" + feeRule.getFeeRuleId());
        }
        Date now = new Date();
        feeRule.setModifyTime(now);
        feeRule.setUpdateBy(CommonUtils.getCurrentLoginName());

        if (Objects.isNull(feeRule.getCalculateRuleData())) {
            feeRule.setCalculateRuleData(byFeeRuleId.getCalculateRuleData());
        }

//        byFeeRuleId.setCalculateRuleData(feeRule.getCalculateRuleData());
//        byFeeRuleId.setBizRule(feeRule.getBizRule());

        FeeRule save = feeRuleRepository.save(feeRule);
//        applicationEventPublisher.publishEvent(new BuildIndexEvent(save));
        return save;
    }

    @Override
    public ApplyCalculateResp calculate(ApplyCalculateReq applyCalculateReq) {
        // 规则组校验
        Set<String> groups = applyCalculateReq.getCalculateRuleDataList().stream().map(ApplyCalculateReq.CalculateRuleDataUnit::getGroup).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(groups)) {
            List<FeeTemplate> allBySceneCodeIsAndGroupIn = feeTemplateRepository.findAllBySceneCodeIsAndGroupInAndDelIsFalse(applyCalculateReq.getSceneCode(), groups);
            List<Long> shouldNotCheckTemplateIds = applyCalculateReq.getShouldNotCheckTemplateIds();
            if (CollectionUtils.isNotEmpty(shouldNotCheckTemplateIds)) {
                // 去除不需要检查完整性的模板
                allBySceneCodeIsAndGroupIn.removeIf(feeTemplate -> shouldNotCheckTemplateIds.contains(feeTemplate.getTemplateId()));
            }
            // 去除后为空
            if (CollectionUtils.isEmpty(allBySceneCodeIsAndGroupIn)) {
                throw new BusException("规则组不存在,group=" + JSON.toJSONString(groups) + "; sceneCode=" + applyCalculateReq.getSceneCode());
            }
            // 完整性校验
            Map<String, List<FeeTemplate>> feeTemplateMap = allBySceneCodeIsAndGroupIn.stream().collect(Collectors.groupingBy(feeTemplate -> feeTemplate.getSceneCode() + feeTemplate.getGroup()));
            Map<String, List<ApplyCalculateReq.CalculateRuleDataUnit>> calculateRuleDataUnitMap = applyCalculateReq.getCalculateRuleDataList().stream().filter(calculateRuleDataUnit -> StringUtils.isNotBlank(calculateRuleDataUnit.getGroup())).collect(Collectors.groupingBy(calculateRuleDataUnit -> applyCalculateReq.getSceneCode() + calculateRuleDataUnit.getGroup()));
            // 校验组完整性
            if (feeTemplateMap.keySet().size() < calculateRuleDataUnitMap.keySet().size()) {
                throw new BusException("规则组不存在,group=" + JSON.toJSONString(groups) + "; sceneCode=" + applyCalculateReq.getSceneCode());
            }
            List<ApplyCalculateReq.CalculateRuleDataUnit> shouldRemoveUnit = new ArrayList<>();
            for (Map.Entry<String, List<FeeTemplate>> stringListEntry : feeTemplateMap.entrySet()) {
                String key = stringListEntry.getKey();
                List<FeeTemplate> value = stringListEntry.getValue();
                List<ApplyCalculateReq.CalculateRuleDataUnit> calculateRuleDataUnits = calculateRuleDataUnitMap.get(key);
                // 如果提交规则少于组规则，则运算失败
                if (calculateRuleDataUnits.size() < value.size()) {
                    throw new BusException("提交运算规则组数据不完整");
                } else {
                    // 如果提交规则多与组规则，则去除多余运算
                    Set<Long> templateIds = value.stream().map(FeeTemplate::getTemplateId).collect(Collectors.toSet());
                    Set<Long> groupTemplateIds = calculateRuleDataUnits.stream().map(ApplyCalculateReq.CalculateRuleDataUnit::getTemplateId).collect(Collectors.toSet());
                    // 取交集
                    Collection<?> intersection = CollectionUtils.intersection(templateIds, groupTemplateIds);
                    if (intersection.size() != templateIds.size()) {
                        throw new BusException("提交运算规则组数据不完整");
                    }

                    List<ApplyCalculateReq.CalculateRuleDataUnit> notInCollection = calculateRuleDataUnits.stream().filter(calculateRuleDataUnit -> !templateIds.contains(calculateRuleDataUnit.getTemplateId())).collect(Collectors.toList());
                    shouldRemoveUnit.addAll(notInCollection);
                }
            }
            // 去除多余的运算
            applyCalculateReq.getCalculateRuleDataList().removeAll(shouldRemoveUnit);

        }

        // 批量查出计算规则
        List<Long> ids = applyCalculateReq.getCalculateRuleDataList().stream().map(ApplyCalculateReq.CalculateRuleDataUnit::getFeeRuleId).collect(Collectors.toList());
        List<FeeRule> allByFeeRuleIdIn = feeRuleRepository.findAllByFeeRuleIdIn(ids);
        Map<Long, FeeRule> feeRuleMap = allByFeeRuleIdIn.stream().collect(Collectors.toMap(FeeRule::getFeeRuleId, Function.identity(), (i, j) -> i));

        List<CalculateResult> calculateResultList = new ArrayList<>(applyCalculateReq.getCalculateRuleDataList().size());
        // 应用规则
        Long tid = SnowFlakeGenerator.INSTANCE.generate();
        Date now = new Date();
        for (ApplyCalculateReq.CalculateRuleDataUnit calculateRuleDataUnit : applyCalculateReq.getCalculateRuleDataList()) {
            CalculateResult calculateResult = new CalculateResult();
            Long calculateResultId = SnowFlakeGenerator.INSTANCE.generate();
            calculateResult.setCalculateResultId(calculateResultId);
            calculateResult.setSceneCode(applyCalculateReq.getSceneCode());

            Map<String, String> bizRule = new HashMap<>();
            if (MapUtils.isNotEmpty(applyCalculateReq.getBizRule())) {
                bizRule.putAll(applyCalculateReq.getBizRule());
            }
            if (MapUtils.isNotEmpty(calculateRuleDataUnit.getBizRule())) {
                bizRule.putAll(calculateRuleDataUnit.getBizRule());
            }
            calculateResult.setBizRule(bizRule);

            calculateResult.setFeeRuleId(calculateRuleDataUnit.getFeeRuleId());
            calculateResult.setTemplateId(calculateRuleDataUnit.getTemplateId());
            calculateResult.setDel(false);
            calculateResult.setStatus(ResultStatusEnum.ACTIVE.code);
            calculateResult.setCreateTime(now);
            calculateResult.setModifyTime(now);
            calculateResult.setTid(tid);
            // 算费规则
            FeeRule feeRule = feeRuleMap.get(calculateResult.getFeeRuleId());
            if (Objects.isNull(feeRule)) {
                throw new BusException("规则不存在，id=" + calculateResult.getFeeRuleId());
            }
            CalculateRuleData calculateRuleData = new CalculateRuleData();
            CalculateRuleData feeRuleCalculateRuleData = feeRule.getCalculateRuleData();
            BeanUtils.copyProperties(feeRuleCalculateRuleData, calculateRuleData);
            calculateRuleData.setExpressionParamMap(calculateRuleDataUnit.getExpressionParamMap());
            calculateResult.setCalculateRuleData(calculateRuleData);
            // 算费
            BigDecimal cost = BigDecimal.ZERO;
            // 编译 , 缓存;
            DefaultContext<String, Object> params = new DefaultContext<>();
            for (Map.Entry<String, String> stringStringEntry : calculateRuleDataUnit.getExpressionParamMap().entrySet()) {
                try {
                    params.put(stringStringEntry.getKey(), new BigDecimal(stringStringEntry.getValue()));
                } catch (Exception e) {
                    params.put(stringStringEntry.getKey(), stringStringEntry.getValue());
                }
            }
            List<String> errorList = new ArrayList<>();
            try {
                // 缓存，不需要全量日志，超时1.5s = 防止死循环
                // 如果是简单公式则只直接返回BigDecimal
                if (Objects.isNull(feeRuleCalculateRuleData.getExpressInfo())) {
                    String str = expressRunner.execute(feeRuleCalculateRuleData.getExpress(), params, errorList, true, false, 1500).toString();
                    cost = new BigDecimal(str);
                } else {
                    // 如果是复杂公式则只直接返回
                    ExpressResultInfo resultInfo = (ExpressResultInfo) expressRunner.execute(feeRuleCalculateRuleData.getExpress(), params, errorList, true, false, 1500);
                    cost = resultInfo.getCost();
                    if (Objects.nonNull(calculateResult.getBizRule())) {
                        calculateResult.getBizRule().putIfAbsent("expressNumber", resultInfo.getNumber().toString());
                        calculateResult.getBizRule().putIfAbsent("expressPrice", resultInfo.getPrice().toString());
                    }
                }

            } catch (Exception e) {
                log.error("计算异常: tid={}, sceneCode={}, feeRuleId={} , log={} , express={}, params={}, ",
                        tid, feeRule.getSceneCode(), feeRule.getFeeRuleId(), e.getMessage(), feeRuleCalculateRuleData.getExpress(), params);
                throw new BusException("计算异常", e);
            }

            if (CollectionUtils.isNotEmpty(errorList)) {
                log.error("计算错误: tid:{}, sceneCode={}, feeRuleId={}  , errorList={}",
                        tid, feeRule.getSceneCode(), feeRule.getFeeRuleId(),  errorList);
                throw new BusException("计算错误, errorList not empty");
            }

            calculateResult.setCost(cost);

            calculateResultList.add(calculateResult);
        }
        // 存储结果
        List<CalculateResult> save = calculateResultRepository.save(calculateResultList);
//        applicationEventPublisher.publishEvent(new BuildIndexEvent(save));
        // 计算总价
        BigDecimal sumCost = BigDecimal.ZERO;
        for (CalculateResult calculateResult : save) {
            sumCost = sumCost.add(calculateResult.getCost());
        }
        ApplyCalculateResp applyCalculateResp = new ApplyCalculateResp();
        applyCalculateResp.setCalculateResultList(save);
        applyCalculateResp.setCost(sumCost);
        applyCalculateResp.setTid(tid);
        return applyCalculateResp;
    }


    @Override
    public ApplyOrderCalculateResp calculateOrder(ApplyOrderCalculateReq applyOrderCalculateReq) {
        // 分解算费元素
        List<String> sceneCodeList = applyOrderCalculateReq.getSceneCode();
        List<SceneInfo> sceneInfos = sceneInfoRepository.findAllBySceneCodeInAndDelIsFalse(sceneCodeList);
        if (CollectionUtils.isEmpty(sceneInfos)) {
            throw new BusException("无有效场景：" + JSON.toJSONString(sceneCodeList, SerializerFeature.DisableCircularReferenceDetect));
        }
        List<String> realSceneCode = sceneInfos.stream().map(SceneInfo::getSceneCode).collect(Collectors.toList());
        Collection<?> subtract = CollectionUtils.subtract(sceneCodeList, realSceneCode);
        if (CollectionUtils.isNotEmpty(subtract)) {
            throw new BusException("有场景不存在" + JSON.toJSONString(sceneCodeList, SerializerFeature.DisableCircularReferenceDetect));
        }
        ApplyOrderCalculateDTO applyOrderCalculateDTO = new ApplyOrderCalculateDTO(applyOrderCalculateReq);
        applyOrderCalculateDTO.setSceneInfoList(sceneInfos);

        // 应用算费
        ApplyOrderCalculateResp calculateResp = sceneFeeRuleStrategyComposite.apply(applyOrderCalculateDTO);

        if (calculateResp.getCost() == null) {
            return calculateResp;
        }

        // 临时收集日志的代码，仅测试环境和UAT有效 start
        if (EnvUtil.PROFILE_TEST.equals(EnvUtil.getProfile()) || EnvUtil.PROFILE_RELEASE.equals(EnvUtil.getProfileName())) {
            List<String> sceneCode = applyOrderCalculateReq.getSceneCode();
            final String redisKey = "collectStandardPricingRequestBody:" + sceneCode + ":" + activeProfiles;
            String redisValue = redisHelper.get(redisKey);
            if (StringUtils.isBlank(redisValue)) {
                // 保存
                StandardPricingRequestBodyLog requestBodyLog = new StandardPricingRequestBodyLog();
                requestBodyLog.setRequestBody(JSON.toJSONString(applyOrderCalculateReq, SerializerFeature.WriteMapNullValue));
                standardPricingRequestBodyLogRepository.save(requestBodyLog);
                redisHelper.set(redisKey, "1", 60 * 60 * 24);
            } else {
                // 删除
                int integer = Integer.parseInt(redisValue);
                if (integer < 100) {
                    StandardPricingRequestBodyLog requestBodyLog = new StandardPricingRequestBodyLog();
                    requestBodyLog.setRequestBody(JSON.toJSONString(applyOrderCalculateReq, SerializerFeature.WriteMapNullValue));
                    standardPricingRequestBodyLogRepository.save(requestBodyLog);
                }
                redisHelper.set(redisKey, String.valueOf(++integer), 60 * 60 * 24);
            }
        }
        // 临时收集日志的代码 end

        if (Arrays.asList(sceneCodes.split(",")).contains(sceneCodeList.get(0))) {
            StandardPricingRequest request = ApplyOrderCalculateReqToStandardPricingRequestConverter.convert(applyOrderCalculateReq);
            if (EnvUtil.PROFILE_TEST.equals(EnvUtil.getProfile()) || EnvUtil.PROFILE_RELEASE.equals(EnvUtil.getProfile())) {
                log.error("standardPriceRequest(inner):{}", JSON.toJSONString(request));
            }
            StandardPricingResponse response = priceCalculator.standardPricing(request);

            BigDecimal cost = calculateResp.getCost();
            BigDecimal newCost = response.getCost();
            if (cost.compareTo(newCost) != 0) {
                StandardPricingRequestBodyLog requestBodyLog = new StandardPricingRequestBodyLog();
                requestBodyLog.setRequestBody(JSON.toJSONString(applyOrderCalculateReq, SerializerFeature.WriteMapNullValue));
                requestBodyLog.setType("failure");
                standardPricingRequestBodyLogRepository.save(requestBodyLog);
                log.warn("新旧标准计价接口 返回的价格不一致，tid={}, sceneCode={}, cost={}, newCost={}",
                        calculateResp.getTid(), sceneCodeList.get(0), cost, newCost);
                NoticeUtil.setNotice(sceneCodeList.get(0),
                        request.getServiceDtoList().get(0).getServiceId().toString(),
                        "服务名称",
                        StrUtil.format("新旧标准计价接口 返回的价格不一致，cost={}, newCost={}", cost, newCost));
            }

        }

        return calculateResp;
    }


    @Override
    public List<ApplyOrderCalculateBatchResp> calculateOrderBatch(ApplyOrderCalculateBatchReq applyOrderCalculateBatchReq) {
        List<BizRuleBatchReq> bizRuleBatchReqList = applyOrderCalculateBatchReq.getBizRuleBatchReqList();
        ApplyOrderCalculateReq applyOrderCalculateReq = applyOrderCalculateBatchReq.getApplyOrderCalculateReq();
        Map<String, String> bizRule = applyOrderCalculateReq.getBizRule();
        if (bizRule == null) {
            bizRule = new HashMap<>();
            applyOrderCalculateReq.setBizRule(bizRule);
        }
        List<ApplyOrderCalculateBatchResp> applyOrderCalculateRespList = new ArrayList<>(bizRuleBatchReqList.size());
        for (BizRuleBatchReq req : bizRuleBatchReqList) {
            String bizId = req.getBizId();
            String bizTag = req.getBizTag();
//            bizRule.put(CommonBizRule.Fields.bizId, bizId);
            bizRule.put(CommonBizRule.Fields.masterId, bizId);
            bizRule.put(CommonBizRule.Fields.bizTag, bizTag);
            ApplyOrderCalculateResp applyOrderCalculateResp = calculateOrder(applyOrderCalculateReq);
            ApplyOrderCalculateBatchResp applyOrderCalculateBatchResp = new ApplyOrderCalculateBatchResp(bizId, bizTag, applyOrderCalculateResp);
            applyOrderCalculateRespList.add(applyOrderCalculateBatchResp);
        }
        return applyOrderCalculateRespList;
    }

    @Override
    public List<FeeRule> batchSave(List<FeeRule> feeRules) {
        Date now = new Date();
        Set<Long> templateIds = feeRules.stream().map(FeeRule::getTemplateId).collect(Collectors.toSet());
        Criteria criteria = where(FeeTemplate.Fields.templateId).in(templateIds);
        Query query = new Query(criteria);

        query.fields().include(FeeTemplate.Fields.templateId);
        query.fields().include(FeeTemplate.Fields.calculateRuleData);
        query.fields().include(BaseDocument.Fields.sort);

        List<FeeTemplate> feeTemplateList = mongoTemplate.find(query, FeeTemplate.class);

//        List<FeeTemplate> allByTemplateIdIn = feeTemplateRepository.findAllByTemplateIdIn(templateIds);
        if (CollectionUtils.isEmpty(feeTemplateList)) {
            throw new BusException("来源模板不存在，templateIds=" + templateIds);
        }
        Map<Long, FeeTemplate> feeTemplateMap = feeTemplateList.stream().collect(Collectors.toMap(FeeTemplate::getTemplateId, Function.identity(), (i, j) -> i));

        for (FeeRule feeRule : feeRules) {
            if (StringUtils.isEmpty(feeRule.getSceneCode())) {
                throw new BusException("场景为空");
            }

            if (Objects.isNull(feeRule.getTemplateId())) {
                throw new BusException("来源模板为空");
            }

//            if (StringUtils.isEmpty(feeRule.getFeeName())) {
//                throw new BusException("算费名称为空");
//            }
            if (Objects.isNull(feeRule.getCalculateRuleData()) || StringUtils.isEmpty(feeRule.getCalculateRuleData().getExpress())) {
                throw new BusException("算费规则为空");
            }
            Map<String, String> bizRule = feeRule.getBizRule();
            if (MapUtils.isEmpty(bizRule)) {
                throw new BusException("业务规则为空");
            }
            FeeTemplate feeTemplate = feeTemplateMap.get(feeRule.getTemplateId());
            if (Objects.isNull(feeTemplate)) {
                throw new BusException("来源模板不存在,templateId=" + feeRule.getTemplateId());
            }

            feeRule.setFeeRuleId(SnowFlakeGenerator.INSTANCE.generate());
            feeRule.setDel(false);

            feeRule.setStatus(RuleStatusEnum.ACTIVE.code);
            feeRule.setCreateTime(now);
            feeRule.setModifyTime(now);

            if (Objects.isNull(feeRule.getCalculateRuleData())) {
                feeRule.setCalculateRuleData(feeTemplate.getCalculateRuleData());
            }

            feeRule.setSort(feeTemplate.getSort());

//            handleFormula(feeRule);
        }


        List<FeeRule> save = feeRuleRepository.save(feeRules);
//        applicationEventPublisher.publishEvent(new BuildIndexEvent(save));
        return save;
    }


    @Override
    public List<FeeRule> batchSaveNew(List<FeeRule> feeRules) {
        Date now = new Date();
        Set<Long> templateIds = feeRules.stream().map(FeeRule::getTemplateId).collect(Collectors.toSet());
        Criteria criteria = where(FeeTemplate.Fields.templateId).in(templateIds);
        Query query = new Query(criteria);

        query.fields().include(FeeTemplate.Fields.templateId);
        query.fields().include(FeeTemplate.Fields.calculateRuleData);
        query.fields().include(BaseDocument.Fields.sort);

        List<FeeTemplate> feeTemplateList = mongoTemplate.find(query, FeeTemplate.class);

//        List<FeeTemplate> allByTemplateIdIn = feeTemplateRepository.findAllByTemplateIdIn(templateIds);
        if (CollectionUtils.isEmpty(feeTemplateList)) {
            Set<Long> feeRuleIds = feeRules.stream().map(FeeRule::getFeeRuleId).collect(Collectors.toSet());
            throw new BusException("来源模板不存在，templateIds=" + templateIds + ",feeRuleIds=" + feeRuleIds);
        }
        Map<Long, FeeTemplate> feeTemplateMap = feeTemplateList.stream().collect(Collectors.toMap(FeeTemplate::getTemplateId, Function.identity(), (i, j) -> i));

        for (FeeRule feeRule : feeRules) {
            if (StringUtils.isEmpty(feeRule.getSceneCode())) {
                throw new BusException("场景为空");
            }

            if (Objects.isNull(feeRule.getTemplateId())) {
                throw new BusException("来源模板为空");
            }

//            if (StringUtils.isEmpty(feeRule.getFeeName())) {
//                throw new BusException("算费名称为空");
//            }
            if (Objects.isNull(feeRule.getCalculateRuleData()) || StringUtils.isEmpty(feeRule.getCalculateRuleData().getExpress())) {
                throw new BusException("算费规则为空");
            }
            Map<String, String> bizRule = feeRule.getBizRule();
            if (MapUtils.isEmpty(bizRule)) {
                throw new BusException("业务规则为空");
            }
            FeeTemplate feeTemplate = feeTemplateMap.get(feeRule.getTemplateId());
            if (Objects.isNull(feeTemplate)) {
                throw new BusException("来源模板不存在,templateId=" + feeRule.getTemplateId());
            }

            feeRule.setFeeRuleId(SnowFlakeGenerator.INSTANCE.generate());
            feeRule.setDel(false);

            feeRule.setStatus(RuleStatusEnum.ACTIVE.code);
            feeRule.setCreateTime(now);
            feeRule.setModifyTime(now);
            feeRule.setCreateBy(CommonUtils.getCurrentLoginName());
            feeRule.setUpdateBy(CommonUtils.getCurrentLoginName());

            if (Objects.isNull(feeRule.getCalculateRuleData())) {
                feeRule.setCalculateRuleData(feeTemplate.getCalculateRuleData());
            }

            feeRule.setSort(feeTemplate.getSort());

//            handleFormula(feeRule);
        }
        feeRuleRepository.save(feeRules);
//        applicationEventPublisher.publishEvent(new BuildIndexEvent(save));
        return feeRules.subList(0, 1);
    }

    @Override
    public List<BatchAddRulesResponse> batchSaveRules(List<FeeRule> feeRules) {
        Date now = new Date();
        Set<Long> templateIds = feeRules.stream().map(FeeRule::getTemplateId).collect(Collectors.toSet());
        Criteria criteria = where(FeeTemplate.Fields.templateId).in(templateIds);
        Query query = new Query(criteria);

        query.fields().include(FeeTemplate.Fields.templateId);
        query.fields().include(FeeTemplate.Fields.calculateRuleData);
        query.fields().include(BaseDocument.Fields.sort);

        List<FeeTemplate> feeTemplateList = mongoTemplate.find(query, FeeTemplate.class);

        if (CollectionUtils.isEmpty(feeTemplateList)) {
            Set<Long> feeRuleIds = feeRules.stream().map(FeeRule::getFeeRuleId).collect(Collectors.toSet());
            throw new BusException("来源模板不存在，templateIds=" + templateIds + ",feeRuleIds=" + feeRuleIds);
        }
        Map<Long, FeeTemplate> feeTemplateMap = feeTemplateList.stream().collect(Collectors.toMap(FeeTemplate::getTemplateId, Function.identity(), (i, j) -> i));

        for (FeeRule feeRule : feeRules) {
            if (StringUtils.isEmpty(feeRule.getSceneCode())) {
                throw new BusException("场景为空");
            }

            if (Objects.isNull(feeRule.getTemplateId())) {
                throw new BusException("来源模板为空");
            }

//            if (StringUtils.isEmpty(feeRule.getFeeName())) {
//                throw new BusException("算费名称为空");
//            }
            if (Objects.isNull(feeRule.getCalculateRuleData()) || StringUtils.isEmpty(feeRule.getCalculateRuleData().getExpress())) {
                throw new BusException("算费规则为空");
            }
            Map<String, String> bizRule = feeRule.getBizRule();
            if (MapUtils.isEmpty(bizRule)) {
                throw new BusException("业务规则为空");
            }
            FeeTemplate feeTemplate = feeTemplateMap.get(feeRule.getTemplateId());
            if (Objects.isNull(feeTemplate)) {
                throw new BusException("来源模板不存在,templateId=" + feeRule.getTemplateId());
            }

            feeRule.setFeeRuleId(SnowFlakeGenerator.INSTANCE.generate());
            feeRule.setDel(false);

            feeRule.setStatus(RuleStatusEnum.ACTIVE.code);
            feeRule.setCreateTime(now);
            feeRule.setModifyTime(now);
            feeRule.setCreateBy(CommonUtils.getCurrentLoginName());
            feeRule.setUpdateBy(CommonUtils.getCurrentLoginName());

            if (Objects.isNull(feeRule.getCalculateRuleData())) {
                feeRule.setCalculateRuleData(feeTemplate.getCalculateRuleData());
            }

            feeRule.setSort(feeTemplate.getSort());

        }
        feeRuleRepository.save(feeRules);

        return feeRules.stream().map(feeRule -> {
            BatchAddRulesResponse response = new BatchAddRulesResponse();
            Map<String, String> bizRule = feeRule.getBizRule();
            response.setFeeRuleId(feeRule.getFeeRuleId());
            response.setMasterId(bizRule.get(CommonBizRule.Fields.masterId));
            response.setLevel4DivisionId(bizRule.get(CommonBizRule.Fields.level4DivisionId));
            response.setServiceCategoryId(bizRule.get(CommonBizRule.Fields.serviceCategoryId));
            return response;
        }).collect(Collectors.toList());
    }


    @Override
    public List<FeeRule> batchDel(List<Long> feeRuleIds) {
        // 直接物理删除，逻辑删除太费劲了
//        List<FeeRule> allByFeeRuleIdIn = feeRuleRepository.findAllByFeeRuleIdIn(feeRuleIds);
//        Date now = new Date();
//        for (FeeRule feeRule : allByFeeRuleIdIn) {
//            feeRule.setModifyTime(now);
//            feeRule.setDel(true);
//        }

        feeRuleRepository.deleteAllByFeeRuleIdIn(feeRuleIds);
//        return allByFeeRuleIdIn;
        // 上游仅用返回值是否为空判断是否删除成功
        return Collections.singletonList(new FeeRule());
    }


    @Override
    public void batchDelRules(List<Long> feeRuleIds) {
        feeRuleRepository.deleteAllByFeeRuleIdIn(feeRuleIds);
    }

    @Override
    public void batchDelete(FeeRuleBatchOperationReq req) {
        // TODO 这里应该直接根据条件删除，而不是先查后删
        List<FeeRule> feeRuleList = getFeeRules(req);
        if (CollectionUtils.isEmpty(feeRuleList)) {
            return;
        }
        Set<String> ids = feeRuleList.stream().map(FeeRule::getId).collect(Collectors.toSet());
        feeRuleRepository.deleteAllByIdIn(ids);
    }


    @Override
    public SimplePageInfo<FeeRulePageResp> getFeeRulePage(FeeRulePageReq req) {
        String serviceId = req.getServiceId();
        String bizId = req.getBizId();
        if (StringUtils.isBlank(serviceId) && StringUtils.isBlank(bizId)) {
            throw new BusException("「服务名称」和「业务ID标识」不能同时为空");
        }
        Long level2DivisionId = req.getLevel2DivisionId();
        Long level3DivisionId = req.getLevel3DivisionId();
        Long level4DivisionId = req.getLevel4DivisionId();
        String bizTag = req.getBizTag();
        Criteria criteria = where(FeeRule.Fields.sceneCode).is(req.getSceneCode())
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code)
                .and(BaseDocument.Fields.createTime).gte(req.getStartTime()).lte(req.getEndTime());
        if (org.apache.commons.lang.StringUtils.isNotBlank(req.getServiceId())) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId);
        }
        if (StringUtils.isNotBlank(req.getFeeTypeTag())) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).is(req.getFeeTypeTag());
        }
        if (req.getGoodsCategoryId() != null) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.goodsCategoryId).is(req.getGoodsCategoryId());
        }
        if (level2DivisionId != null) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId).is(String.valueOf(level2DivisionId));
        }
        if (level3DivisionId != null) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level3DivisionId).is(String.valueOf(level3DivisionId));
        }
        if (level4DivisionId != null) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level4DivisionId).is(String.valueOf(level4DivisionId));
        }
        if (StringUtils.isNotBlank(req.getDivisionType())) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(req.getDivisionType());
        }
        if (StringUtils.isNotBlank(req.getSkuNo())) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(req.getSkuNo());
        }
        if (StringUtils.isNotBlank(bizId)) {
            Criteria c1 = where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizId).is(bizId);
            Criteria c2 = where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterId).is(bizId);
            Criteria c3 = where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.userId).is(bizId);
            List<Criteria> criteriaList = new ArrayList<>();
            criteriaList.add(c1);
            criteriaList.add(c2);
            criteriaList.add(c3);
            criteria.orOperator(criteriaList.toArray(new Criteria[0]));
        }
        if (StringUtils.isNotBlank(bizTag)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.bizTag).is(bizTag);
        }
        Query query = Query.query(criteria);
        Pageable pageable = new PageRequest(req.getPageNum() - 1, req.getPageSize());
        query.with(pageable);
        long totalCount = mongoTemplate.count(query, FeeRule.class);
        List<FeeRule> feeRuleList = mongoTemplate.find(query, FeeRule.class);
        if (CollectionUtils.isEmpty(feeRuleList)) {
            return null;
        }
        feeRuleList.forEach(feeRule -> {
            Map<String, String> bizRule = feeRule.getBizRule();
            String divisionType = bizRule.get(CommonBizRule.Fields.divisionType);
            if (StringUtils.isNotBlank(divisionType)) {
                DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(divisionType);
                if (divisionTypeEnum != null) {
                    bizRule.put(CommonBizRule.Fields.divisionTypeName, Objects.requireNonNull(divisionTypeEnum).name);
                }
            }
        });
        List<FeeRulePageResp> respList = new ArrayList<>();
        for (FeeRule feeRule : feeRuleList) {
            FeeRulePageResp resp = JSONObject.parseObject(JSON.toJSONString(feeRule.getBizRule()), FeeRulePageResp.class);
            resp.setTemplateId(feeRule.getTemplateId());
            resp.setId(feeRule.getId());
            resp.setModifyTime(feeRule.getModifyTime());
            resp.setUpdateBy(feeRule.getUpdateBy());
            resp.setCalculateRuleData(feeRule.getCalculateRuleData());
            respList.add(resp);
        }
        PageImpl<FeeRulePageResp> page = new PageImpl<>(respList, pageable, totalCount);
        SimplePageInfo<FeeRulePageResp> resultPage = new SimplePageInfo<>(respList);
        resultPage.setPages(page.getTotalPages());
        resultPage.setPageNum(resultPage.getPageNum());
        resultPage.setPageSize(resultPage.getPageSize());
        resultPage.setTotal(totalCount);
        return resultPage;
    }


    @Override
    public SimplePageInfo<FeeRuleServicePageResp> getFeeRuleServicePage(FeeRuleServicePageReq req, String collectionName) {
        Criteria criteria = where(FeeRule.Fields.sceneCode).is(req.getSceneCode())
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(req.getServiceId())
                .and(BaseDocument.Fields.del).is(false);
//        String serviceName = req.getServiceName();
//        if (StringUtils.isNotBlank(serviceName)) {
//            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.serviceName).regex(Pattern.compile(".*" + Pattern.quote(req.getServiceName()) + ".*"));
//        }
        String goodsCategoryId = req.getGoodsCategoryId();
        if (StringUtils.isNotBlank(goodsCategoryId)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.goodsCategoryId).is(goodsCategoryId);
        }
        String bizId = req.getBizId();
        if (StringUtils.isNotBlank(bizId)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.bizId).is(bizId);
        }
        Aggregation feeRuleAggregation = newAggregation(
                match(criteria), // 查询条件
                group(FeeRule.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.serviceId,
                        FeeRule.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.divisionType).first("$$ROOT").as("info"),
                Aggregation.replaceRoot("$info"),
                Aggregation.count().as("count")
        );
        Pageable pageable = new PageRequest(req.getPageNum() - 1, req.getPageSize());
        JSONObject rule = mongoTemplate.aggregate(feeRuleAggregation, collectionName, JSONObject.class).getUniqueMappedResult();
        if (rule == null) {
            return null;
        }
        Long count = rule.getLong("count");
        if (count > 0) {
            Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.createTime));
            feeRuleAggregation = newAggregation(
                    match(criteria), // 查询条件
                    group(FeeRule.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.serviceId,
                            FeeRule.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.divisionType).first("$$ROOT").as("info"),
                    Aggregation.replaceRoot("$info"),
                    Aggregation.sort(sort),
                    Aggregation.skip((req.getPageNum() - 1) * req.getPageSize()),
                    Aggregation.limit(req.getPageSize())
            );
        }

        AggregationResults<FeeRule> aggregate = mongoTemplate.aggregate(feeRuleAggregation, collectionName, FeeRule.class);
        if (Objects.isNull(aggregate)) {
            return null;
        }
        List<FeeRule> feeRuleList = aggregate.getMappedResults();
        if (CollectionUtils.isEmpty(feeRuleList)) {
            return null;
        }
        List<FeeRuleServicePageResp> respList = new ArrayList<>();
        for (FeeRule feeRule : feeRuleList) {
            FeeRuleServicePageResp resp = JSONObject.parseObject(JSON.toJSONString(feeRule.getBizRule()), FeeRuleServicePageResp.class);
            resp.setId(feeRule.getId());
            respList.add(resp);
        }
        PageImpl<FeeRuleServicePageResp> page = new PageImpl<>(respList, pageable, count);
        SimplePageInfo<FeeRuleServicePageResp> resultPage = new SimplePageInfo<>(respList);
        resultPage.setPages(page.getTotalPages());
        resultPage.setPageNum(page.getNumber());
        resultPage.setPageSize(page.getSize());
        resultPage.setTotal(count);
        return resultPage;
    }


    private List<FeeRule> getFeeRules(FeeRuleBatchOperationReq req) {
        Criteria criteria = where(FeeRule.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.serviceId).is(req.getServiceId())
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.divisionType).is(req.getDivisionType())
                .and(FeeRule.Fields.sceneCode).is(req.getSceneCode())
                .and(BaseDocument.Fields.del).is(false);
        if (org.apache.commons.lang.StringUtils.isNotBlank(req.getBizId())) {
            criteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.bizId).is(req.getBizId());
        }
        Query query = Query.query(criteria);
        List<FeeRule> draftList = mongoTemplate.find(query, FeeRule.class);
        return draftList;
    }

    @Override
    public void completeDivision() {

        //  获取锁
        log.info("开始消费任务-尝试获取锁");
        if (!redissonHelper.tryLock("completeDivision", TimeUnit.SECONDS, 30, 300)) {
            log.info("开始消费任务-获取锁失败，先返回");
            return;
        }

        // 查出divisionType为空的数据， 已删除的数据不用处理
        Criteria noDivision = where(BaseDocument.Fields.del).is(false);
        noDivision.and(FeeRule.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.divisionType).is(null);
        // 升序，先插入先消费,一次消费256个任务
        Sort sort = new Sort(new Sort.Order(Sort.Direction.ASC, BaseDocument.Fields.createTime));
        PageRequest pageRequest = new PageRequest(0, 256, sort);
        Query query = Query.query(noDivision).with(pageRequest);

        while (true) {
            List<FeeRule> feeRuleList = mongoTemplate.find(query, FeeRule.class);
            if (CollectionUtils.isEmpty(feeRuleList)) {
                // 全部已经消费完成
                log.info("completeDivision完成");
                break;
            }
            // 根据level补全数据
            Set<String> divisionIds = new HashSet<>();
            for (FeeRule feeRule : feeRuleList) {
                String divisionId = feeRule.getBizRule().get(DivisionTypeEnum.STREET.divisionKey);
                if (StringUtils.isNotBlank(divisionId)) {
                    feeRule.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.divisionType, DivisionTypeEnum.STREET.code);
                    feeRule.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.divisionTypeName, DivisionTypeEnum.STREET.name);
                    divisionIds.add(divisionId);
                    continue;
                }
                divisionId = feeRule.getBizRule().get(DivisionTypeEnum.DISTRICT.divisionKey);
                if (StringUtils.isNotBlank(divisionId)) {
                    feeRule.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.divisionType, DivisionTypeEnum.DISTRICT.code);
                    feeRule.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.divisionTypeName, DivisionTypeEnum.DISTRICT.name);
                    divisionIds.add(divisionId);
                    continue;
                }
                divisionId = feeRule.getBizRule().get(DivisionTypeEnum.CITY.divisionKey);
                if (StringUtils.isNotBlank(divisionId)) {
                    feeRule.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.divisionType, DivisionTypeEnum.CITY.code);
                    feeRule.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.divisionTypeName, DivisionTypeEnum.CITY.name);
                    divisionIds.add(divisionId);
                    continue;
                }
                divisionId = feeRule.getBizRule().get(DivisionTypeEnum.PROVINCE.divisionKey);
                if (StringUtils.isNotBlank(divisionId)) {
                    feeRule.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.divisionType, DivisionTypeEnum.PROVINCE.code);
                    feeRule.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.divisionTypeName, DivisionTypeEnum.PROVINCE.name);
                    divisionIds.add(divisionId);
                    continue;
                }
                feeRule.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.divisionType, DivisionTypeEnum.COUNTRY.code);
                feeRule.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.divisionTypeName, DivisionTypeEnum.COUNTRY.name);
            }

            // 存入省市区
            if (CollectionUtils.isNotEmpty(divisionIds)) {
                List<Address> divisionInfoListByDivisionIds = addressApi.getDivisionInfoListByDivisionIds(String.join(",", divisionIds));
                Map<Long, Address> addressMap = divisionInfoListByDivisionIds.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity(), (a, b) -> a));
                for (FeeRule feeRule : feeRuleList) {
                    String divisionId = feeRule.getBizRule().get(DivisionTypeEnum.STREET.divisionKey);
                    if (StringUtils.isNotBlank(divisionId)) {
                        Address address = addressMap.get(Long.valueOf(divisionId));
                        if (Objects.nonNull(address)) {
                            feeRule.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.street, address.getLv5DivisionName());
                            feeRule.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.district, address.getLv4DivisionName());
                            feeRule.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.city, address.getLv3DivisionName());
                            feeRule.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.province, address.getLv2DivisionName());
                        }
                        continue;
                    }
                    divisionId = feeRule.getBizRule().get(DivisionTypeEnum.DISTRICT.divisionKey);
                    if (StringUtils.isNotBlank(divisionId)) {
                        Address address = addressMap.get(Long.valueOf(divisionId));
                        if (Objects.nonNull(address)) {
                            feeRule.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.district, address.getLv4DivisionName());
                            feeRule.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.city, address.getLv3DivisionName());
                            feeRule.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.province, address.getLv2DivisionName());
                        }
                        continue;
                    }
                    divisionId = feeRule.getBizRule().get(DivisionTypeEnum.CITY.divisionKey);
                    if (StringUtils.isNotBlank(divisionId)) {
                        Address address = addressMap.get(Long.valueOf(divisionId));
                        if (Objects.nonNull(address)) {
                            feeRule.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.city, address.getLv3DivisionName());
                            feeRule.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.province, address.getLv2DivisionName());
                        }
                        continue;
                    }
                    divisionId = feeRule.getBizRule().get(DivisionTypeEnum.PROVINCE.divisionKey);
                    if (StringUtils.isNotBlank(divisionId)) {
                        Address address = addressMap.get(Long.valueOf(divisionId));
                        if (Objects.nonNull(address)) {
                            feeRule.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.province, address.getLv2DivisionName());
                        }
                    }
                }
            }

            // 入库
            feeRuleRepository.save(feeRuleList);
        }
        log.info("completeDivision全部完成");
        redissonHelper.unlock("completeDivision");
    }

    @Override
    public void completeGroup() {
        // 获取锁
        log.info("开始消费任务-尝试获取锁");
        if (!redissonHelper.tryLock("completeGroup", TimeUnit.SECONDS, 30, 300)) {
            log.info("开始消费任务-获取锁失败，先返回");
            return;
        }

        // 查出group为空的数据
        // 查出divisionType为空的数据， 已删除的数据不用处理
        Criteria noGroup = where(BaseDocument.Fields.del).is(false);
        noGroup.and(FeeRule.Fields.group).is(null).and(FeeRule.Fields.bizRule + PunConstant.DOT + "serviceId").ne(null);
        // 升序，先插入先消费,一次消费256个任务
        Sort sort = new Sort(new Sort.Order(Sort.Direction.ASC, BaseDocument.Fields.createTime));
        PageRequest pageRequest = new PageRequest(0, 2048, sort);
        Query query = Query.query(noGroup).with(pageRequest);

        // template
        while (true) {
            List<FeeTemplate> feeTemplates = mongoTemplate.find(query, FeeTemplate.class);
            if (CollectionUtils.isEmpty(feeTemplates)) {
                // 全部已经消费完成
                log.info("completeGroup -- FeeTemplate完成");
                break;
            }
            // 取出serviceId存入group
            for (FeeTemplate feeTemplate : feeTemplates) {
                String serviceId = feeTemplate.getBizRule().get(AutoReceiveOrderGuidePriceBizRule.Fields.serviceId);
                if (StringUtils.isNotBlank(serviceId)) {
                    feeTemplate.setGroup(serviceId);
                }
            }

            // 存库
            feeTemplateRepository.save(feeTemplates);
        }

        // feeRule
        while (true) {
            List<FeeRule> feeRuleList = mongoTemplate.find(query, FeeRule.class);
            if (CollectionUtils.isEmpty(feeRuleList)) {
                // 全部已经消费完成
                log.info("completeGroup -- feeRule完成");
                break;
            }
            // 取出serviceId存入group
            for (FeeRule feeRule : feeRuleList) {
                String serviceId = feeRule.getBizRule().get(AutoReceiveOrderGuidePriceBizRule.Fields.serviceId);
                if (StringUtils.isNotBlank(serviceId)) {
                    feeRule.setGroup(serviceId);
                }
            }

            // 存库
            feeRuleRepository.save(feeRuleList);
        }

        log.info("completeGroup全部完成");
        redissonHelper.unlock("completeGroup");
    }


    /**
     * 这个接口的实现会造成大量的数据库查询，禁止使用
     *
     * @param req
     * @return
     */
    @Override
    public Set<Long> getServiceIdsByDivision(ServiceIdByDivisionIdsReq req) {
        String divisionCode = req.getDivisionCode();
        Criteria criteria;
        if (DivisionTypeEnum.COUNTRY.code.equals(divisionCode)) {
            criteria = getCriteria(divisionCode, null);
            Query query = new Query(criteria);
            query.fields().include(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId);
            List<FeeRule> feeRuleList = mongoTemplate.find(query, FeeRule.class);
            return feeRuleList.stream()
                    .filter(feeRule -> feeRule.getBizRule().containsKey(AutoReceiveOrderGuidePriceBizRule.Fields.serviceId))
                    .map(feeRule -> Long.valueOf(feeRule.getBizRule().get(AutoReceiveOrderGuidePriceBizRule.Fields.serviceId)))
                    .collect(Collectors.toSet());
        } else {
            Set<String> divisionIds = req.getDivisionIdSet();
            if (CollectionUtils.isEmpty(divisionIds)) {
                return null;
            }
            criteria = getCriteria(divisionCode, divisionIds);

            MatchOperation match = Aggregation.match(criteria);

            GroupOperation group = getGroupOperation(divisionCode)
                    .addToSet(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId)
                    .as(CommonBizRule.Fields.serviceId);

            ProjectionOperation projection = Aggregation.project(CommonBizRule.Fields.serviceId).andExclude("_id");

            Aggregation aggregation = newAggregation(match, group, projection);
            AggregationResults<FeeRuleServiceIdAggregationResult> aggregate = mongoTemplate.aggregate(aggregation, "feeRule", FeeRuleServiceIdAggregationResult.class);
            List<FeeRuleServiceIdAggregationResult> mappedResults = aggregate.getMappedResults();
            if (CollectionUtils.isEmpty(mappedResults)) {
                return Collections.emptySet();
            }

            if (mappedResults.size() == 1) {
                return mappedResults.get(0).getServiceId().stream().map(Long::valueOf).collect(Collectors.toSet());
            }

            return mappedResults.stream().map(FeeRuleServiceIdAggregationResult::getServiceId)
                    .collect(Collectors.toSet()).stream().reduce((s1, s2) -> {
                        s1.retainAll(s2); // 保留两个集合中的交集元素
                        return s1; // 返回交集后的第一个集合
                    }).orElse(Collections.emptySet()).stream().map(Long::valueOf).collect(Collectors.toSet()); // 如果没有交集则返回空集合
        }
    }

    @Override
    public void batchDeleteLogically(String sceneCode, String masterId, String bizTag) {
        if (StringUtils.isNotBlank(sceneCode) &&
                StringUtils.isNotBlank(masterId) &&
                StringUtils.isNotBlank(bizTag)) {
            Criteria criteria = where("sceneCode").is(sceneCode)
                    .and("bizRule.masterId").is(masterId)
                    .and("bizRule.bizTag").is(bizTag);
            Update update = new Update()
                    .set(BaseDocument.Fields.del, true)
                    .set(BaseDocument.Fields.modifyTime, new Date())
                    .set(BaseDocument.Fields.updateBy, CommonUtils.getCurrentLoginName());
            mongoTemplate.updateMulti(new Query(criteria), update, FeeRule.class);
        }
    }


    @Override
    @Async
    public void correctTemplateId(String sceneCode, Set<String> bizTagSet) {
        Criteria criteria1 = where("sceneCode").is(sceneCode)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuType).is("standardSku");
        List<FeeTemplate> feeTemplateList = mongoTemplate.find(new Query(criteria1), FeeTemplate.class);
        if (CollectionUtils.isEmpty(feeTemplateList)) {
            throw new BusException("没有找到模板数据");
        }
        Map<String, Long> templateIdMap = new HashMap<>();
        for (FeeTemplate feeTemplate : feeTemplateList) {
            String serviceId = feeTemplate.getBizRule().get(CommonBizRule.Fields.serviceId);
            String skuNo = feeTemplate.getBizRule().get(CommonBizRule.Fields.skuNo);
            String key = serviceId + "_" + skuNo;
            Long templateId = templateIdMap.get(key);
            if (templateId == null) {
                templateIdMap.put(key, feeTemplate.getTemplateId());
            } else {
                throw new BusException(String.format("重复的serviceId=%s和skuNo=%s", serviceId, skuNo));
            }
        }


        // 分页查询，每页100条
        int pageSize = 100;
        int startPage = 0;
        int updatedCount = 0;

        Criteria criteria = where("sceneCode").is(sceneCode)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizTag).in(bizTagSet)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuType).is("standardSku");

        while (true) {
            PageRequest pageRequest = new PageRequest(startPage, pageSize);
            Query query = Query.query(criteria).with(pageRequest);
            // 由于后面会更新数据，所以每次都从头重新查询
            List<FeeRule> feeRuleList = mongoTemplate.find(query, FeeRule.class);
            if (CollectionUtils.isEmpty(feeRuleList)) {
                log.info("修正templateId完成");
                break;
            }
            List<FeeRule> needUpdateList = new ArrayList<>();
            for (FeeRule feeRule : feeRuleList) {
                Map<String, String> bizRule = feeRule.getBizRule();
                String serviceId = bizRule.get(CommonBizRule.Fields.serviceId);
                String skuNo = bizRule.get(CommonBizRule.Fields.skuNo);
                String key = serviceId + "_" + skuNo;
                Long templateId = templateIdMap.get(key);
                if (templateId == null) {
                    continue;
                }
                Long feeRuleTemplateId = feeRule.getTemplateId();
                if (templateId.equals(feeRuleTemplateId)) {
                    continue;
                }
                feeRule.setTemplateId(templateId);
                bizRule.put(CommonBizRule.Fields.templateId, String.valueOf(templateId));
//                feeRuleRepository.save(feeRule);
                needUpdateList.add(feeRule);
                updatedCount++;
            }
            feeRuleRepository.save(needUpdateList);
            startPage++;
        }
        log.info("修正templateId完成，共更新{}条数据", updatedCount);
    }


    @Override
    @Async
    public void correctStatus() {
        Criteria criteria = where(FeeRule.Fields.sceneCode).is(SceneCodeEnum.CONTRACT_MASTER.getCode())
                .and(BaseDocument.Fields.status).is(DraftStatusEnum.AUDIT.code)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizTag).is("0");

        // 分页查询，每页100条
        int pageSize = 100;
        int startPage = 0;
        int updatedCount = 0;
        while (true) {
            PageRequest pageRequest = new PageRequest(startPage, pageSize);
            Query query = Query.query(criteria).with(pageRequest);
            // 由于后面会更新数据，所以每次都从头重新查询
            List<FeeRule> feeRuleList = mongoTemplate.find(query, FeeRule.class);
            if (CollectionUtils.isEmpty(feeRuleList)) {
                log.info("完成status 1 -> 0");
                break;
            }
            List<FeeRule> needUpdateList = new ArrayList<>();
            for (FeeRule feeRule : feeRuleList) {
                feeRule.setStatus(DraftStatusEnum.PASS.code);
                Map<String, String> bizRule = feeRule.getBizRule();
                bizRule.put("refreshFlag", "1To0");
                feeRule.setModifyTime(new Date());
                needUpdateList.add(feeRule);
                updatedCount++;
            }
            feeRuleRepository.save(needUpdateList);
            startPage++;
        }
        log.info("完成status 1 -> 0，共更新{}条数据", updatedCount);
    }


    @Override
    @Async
    public void completeServiceCategoryId(String sceneCode, String updateBy) {
        // 分页查询，每页100条
        final int pageSize = 100;
        final int startPage = 0;
        int updatedCount = 0;

        Criteria criteria = where(BaseDocument.Fields.del).is(false);
        criteria.and(FeeRule.Fields.sceneCode).is(sceneCode);
        criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceCategoryId).is(null);
        PageRequest pageRequest = new PageRequest(startPage, pageSize);
        Query query = Query.query(criteria).with(pageRequest);

        Map<Long, Long> mapping = serviceApi.getServiceIdWithServiceCategoryIdMapping(null);

        while (true) {
            // 由于后面会更新数据，所以每次都从头重新查询
            List<FeeRule> feeRuleList = mongoTemplate.find(query, FeeRule.class);
            if (CollectionUtils.isEmpty(feeRuleList)) {
                log.info("补全服务类目ID完成");
                break;
            }
            List<FeeRule> needUpdateList = new ArrayList<>();
            for (FeeRule feeRule : feeRuleList) {
                String serviceId = feeRule.getBizRule().get(AutoReceiveOrderGuidePriceBizRule.Fields.serviceId);
                if (StringUtils.isBlank(serviceId)) {
                    continue;
                }
                Long serviceCategoryId = mapping.get(Long.valueOf(serviceId));
                if (serviceCategoryId != null) {
                    feeRule.getBizRule().put(CommonBizRule.Fields.serviceCategoryId, String.valueOf(serviceCategoryId));
                    feeRule.setUpdateBy(updateBy);
                    needUpdateList.add(feeRule);
                    updatedCount++;
                }
            }
            feeRuleRepository.save(needUpdateList);
        }
        log.info("补全服务类目ID完成，共更新{}条数据", updatedCount);

    }


    @Override
    public List<QueryByBizIdResp> getByBizIdGroupByCondition(QueryByBizIdReq req) {
        Query query = getQueryByBizIdReq(req);
        List<FeeRule> feeRuleList = mongoTemplate.find(query, FeeRule.class);
        if (CollectionUtils.isEmpty(feeRuleList)) {
            return Collections.emptyList();
        }
        return feeRuleList.stream().map(
                feeRule -> getQueryByBizIdResp(feeRule.getSceneCode(), feeRule.getBizRule())
        ).distinct().collect(Collectors.toList());
    }

    @Override
    public QueryByBizIdResp getQueryByBizIdResp(String sceneCode, Map<String, String> bizRule) {
        String bizId = bizRule.get(CommonBizRule.Fields.bizId);
        String userId = bizRule.get(CommonBizRule.Fields.userId);
        String masterId = bizRule.get(CommonBizRule.Fields.masterId);
        String realBizId = StringUtils.isBlank(bizId) ? (StringUtils.isBlank(userId) ? masterId : userId) : bizId;
        String divisionType = bizRule.get(CommonBizRule.Fields.divisionType);
        String bizTag = bizRule.get(CommonBizRule.Fields.bizTag);
        String serviceId = bizRule.get(CommonBizRule.Fields.serviceId);
        String serviceName = bizRule.get(CommonBizRule.Fields.serviceName);
        return new QueryByBizIdResp(
                String.format("%s_%s_%s_%s_%s", sceneCode, realBizId, bizTag, divisionType, serviceId),
                sceneCode,
                realBizId,
                bizTag,
                divisionType,
                Objects.requireNonNull(DivisionTypeEnum.fromCode(divisionType)).name,
                serviceId,
                serviceName
        );
    }

    @Override
    public Query getQueryByBizIdReq(QueryByBizIdReq req) {
        String sceneCode = req.getSceneCode();
        String bizId = req.getBizId();
        String bizTag = req.getBizTag();
        String divisionType = req.getDivisionType();
        String serviceName = req.getServiceName();

        if (StringUtils.isBlank(sceneCode)) {
            throw new BusException("场景编码不能为空");
        }
        if (StringUtils.isBlank(bizId)) {
            throw new BusException("业务Id不能为空");
        }
        Criteria criteria = where(BaseDocument.Fields.del).is(false)
                .and(FeeRule.Fields.sceneCode).is(sceneCode);
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizId).is(bizId));
        criteriaList.add(where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterId).is(bizId));
        criteriaList.add(where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.userId).is(bizId));
        criteria.orOperator(criteriaList.toArray(new Criteria[0]));

        if (StringUtils.isNotBlank(bizTag)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizTag).is(bizTag);
        }
        if (StringUtils.isNotBlank(divisionType)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(divisionType);
        }
        if (StringUtils.isNotBlank(serviceName)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceName).regex(PatternUtils.toFuzzySearch(PatternUtils.toEscapeStr(serviceName)));
        }
        return Query.query(criteria);
    }

    private Criteria getCriteria(String divisionCode, Set<String> divisionIds) {
        Criteria criteria = where(BaseDocument.Fields.del).is(false);
        // 只查询场景为 合约师傅 sceneCode = master_recruit_base_price
        criteria.and(FeeRule.Fields.sceneCode).is(SceneCodeEnum.MASTER_RECRUIT_BASE_PRICE.getCode());
        if (DivisionTypeEnum.COUNTRY.code.equals(divisionCode)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(DivisionTypeEnum.COUNTRY.code);
        } else if (DivisionTypeEnum.PROVINCE.code.equals(divisionCode)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(DivisionTypeEnum.PROVINCE.code);
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level1DivisionId).in(divisionIds);
        } else if (DivisionTypeEnum.CITY.code.equals(divisionCode)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(DivisionTypeEnum.CITY.code);
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId).in(divisionIds);
        } else if (DivisionTypeEnum.DISTRICT.code.equals(divisionCode)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(DivisionTypeEnum.DISTRICT.code);
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level3DivisionId).in(divisionIds);
        } else if (DivisionTypeEnum.STREET.code.equals(divisionCode)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(DivisionTypeEnum.STREET.code);
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level4DivisionId).in(divisionIds);
        } else {
            throw new BusException("地区维度不合法, divisionType=" + divisionCode);
        }
        return criteria;
    }

    private GroupOperation getGroupOperation(String divisionCode) {
        GroupOperation groupOperation;
        if (DivisionTypeEnum.PROVINCE.code.equals(divisionCode)) {
            groupOperation = Aggregation.group(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level1DivisionId);
        } else if (DivisionTypeEnum.CITY.code.equals(divisionCode)) {
            groupOperation = Aggregation.group(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId);
        } else if (DivisionTypeEnum.DISTRICT.code.equals(divisionCode)) {
            groupOperation = Aggregation.group(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level3DivisionId);
        } else if (DivisionTypeEnum.STREET.code.equals(divisionCode)) {
            groupOperation = Aggregation.group(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level4DivisionId);
        } else {
            throw new BusException("地区维度不合法, divisionType=" + divisionCode);
        }
        return groupOperation;
    }


    @Override
    public int deleteByCondition(DeleteByBizIdReq req) {
        Query query = getDeleteQueryByBizId(req);
        return mongoTemplate.remove(query, FeeRule.class).getN();
    }

    @Override
    public Query getDeleteQueryByBizId(DeleteByBizIdReq req) {
        String sceneCode = req.getSceneCode();
        String bizId = req.getBizId();
        String bizTag = req.getBizTag();
        String divisionType = req.getDivisionType();
        String serviceId = req.getServiceId();
        if (StringUtils.isBlank(sceneCode) || StringUtils.isBlank(bizId) || StringUtils.isBlank(divisionType) || StringUtils.isBlank(serviceId)) {
            throw new BusException("场景编码、业务id、地区类型、服务id 均不能为空");
        }
        Criteria criteria = where(FeeRule.Fields.sceneCode).is(sceneCode)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(divisionType)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId);
        List<Criteria> bizIdCriteriaList = new ArrayList<>();
        bizIdCriteriaList.add(where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizId).is(bizId));
        bizIdCriteriaList.add(where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.userId).is(bizId));
        bizIdCriteriaList.add(where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterId).is(bizId));
        criteria.orOperator(bizIdCriteriaList.toArray(new Criteria[0]));
        if (StringUtils.isNotBlank(bizTag)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizTag).is(bizTag);
        }
        return new Query(criteria);
    }


    @Override
    public Map<String, AttributeValuePriceResp> getAttributeValuePriceByServiceId(Long serviceId, Long userId) {
        if (serviceId == null || serviceId <= 0) {
            return Collections.emptyMap();
        }
        Map<String, AttributeValuePriceResp> resultMap = new HashMap<>();
        // 如果有商家，则优先查商家定制一口价
        if (userId != null && userId > 0) {
            Criteria criteria = where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(String.valueOf(serviceId))
                    .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.userId).is(String.valueOf(userId))
                    .and(FeeRule.Fields.sceneCode).is(SceneCodeEnum.PLATFORM_FIXED_PRICE_USER.getCode())
                    .and(BaseDocument.Fields.del).is(false)
                    .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);
            List<FeeRule> feeRuleList = mongoTemplate.find(new Query(criteria), FeeRule.class);
            if (CollectionUtils.isNotEmpty(feeRuleList)) {
                for (FeeRule feeRule : feeRuleList) {
                    Map<String, String> bizRule = feeRule.getBizRule();
                    String divisionType = bizRule.get(CommonBizRule.Fields.divisionType);
                    // 只取全国价
                    if (!DivisionTypeEnum.COUNTRY.code.equals(divisionType)) {
                        continue;
                    }
                    resultMap.put(bizRule.get(CommonBizRule.Fields.skuNo),
                            new AttributeValuePriceResp(
                                    bizRule.get(CommonBizRule.Fields.feeName),
                                    bizRule.get(CommonBizRule.Fields.masterInputPrice),
                                    bizRule.get(CommonBizRule.Fields.feeUnit)
                            ));
                }
            }
        }
        Criteria criteria = where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(String.valueOf(serviceId))
                .and(FeeRule.Fields.sceneCode).is(SceneCodeEnum.PLATFORM_FIXED_PRICE.getCode())
                // 只取全国价
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(DivisionTypeEnum.COUNTRY.code)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);
        List<FeeRule> feeRuleList = mongoTemplate.find(new Query(criteria), FeeRule.class);
        if (CollectionUtils.isNotEmpty(feeRuleList)) {
            for (FeeRule feeRule : feeRuleList) {
                Map<String, String> bizRule = feeRule.getBizRule();
//                String divisionType = bizRule.get(CommonBizRule.Fields.divisionType);
                // 只取全国价
//                if (!DivisionTypeEnum.COUNTRY.code.equals(divisionType)) {
//                    continue;
//                }
                String skuNo = bizRule.get(CommonBizRule.Fields.skuNo);
                AttributeValuePriceResp resp = resultMap.get(skuNo);
                if (resp == null) {
                    resultMap.put(bizRule.get(CommonBizRule.Fields.skuNo),
                            new AttributeValuePriceResp(
                                    bizRule.get(CommonBizRule.Fields.feeName),
                                    bizRule.get(CommonBizRule.Fields.masterInputPrice),
                                    bizRule.get(CommonBizRule.Fields.feeUnit)
                            ));
                }
            }
        }
        return resultMap;
    }


    @Override
    public SimplePageInfo<BargainPriceEverydayFeeRuleResp> getBargainPriceEverydayFeeRuleByServiceId(BargainPriceEverydayFeeRuleReq req) {
        String sceneCode = req.getSceneCode();
        String serviceId = req.getServiceId();
        String skuNo = req.getSkuNo();
        String level2DivisionId = req.getLevel2DivisionId();
//        String feeTypeTag = req.getFeeTypeTag();
//        if (StringUtils.isBlank(feeTypeTag)) {
//            feeTypeTag = FeeTypeTagEnum.SERVICE_FEE.code;
//        }
        String collectionName = "bargainPriceEverydayFeeRule";
//        String redisKey = getRedisKey(sceneCode);
//        String lastVersionNo = redisHelper.get(redisKey);
//        if (StringUtils.isBlank(lastVersionNo)) {
//            Criteria criteria = where(BargainPriceEverydayFeeRule.Fields.sceneCode).is(sceneCode)
//                    .and(BaseDocument.Fields.del).is(false);
//            BargainPriceEverydayFeeRule rule = mongoTemplate.findOne(new Query(criteria), BargainPriceEverydayFeeRule.class);
//            if (rule == null) {
//                return new SimplePageInfo<>();
//            }
//            lastVersionNo = rule.getLastVersionNo();
//            if (StringUtils.isNotBlank(lastVersionNo)) {
//                redisHelper.set(redisKey, lastVersionNo, 0);
//            }
//        }

        // 采用预生成的方式，从BargainPriceEverydayFeeRule表中查询
        Criteria matchCriteria = where(BargainPriceEverydayFeeRule.Fields.serviceId).is(serviceId)
                .and(BargainPriceEverydayFeeRule.Fields.sceneCode).is(sceneCode)
                .and(BaseDocument.Fields.del).is(false);

        if (StringUtils.isNotBlank(skuNo)) {
            matchCriteria.and(BargainPriceEverydayFeeRule.Fields.skuNo).is(skuNo);
        }
        if (StringUtils.isNotBlank(level2DivisionId)) {
            matchCriteria.and(BargainPriceEverydayFeeRule.Fields.level2DivisionId).is(level2DivisionId);
        } else {
            matchCriteria.and(BargainPriceEverydayFeeRule.Fields.level2DivisionId).ne(null);
        }

        MatchOperation matchOperation = match(matchCriteria);

        // 运行使用磁盘排序
        AggregationOptions options = newAggregationOptions().allowDiskUse(true).build();

        GroupOperation groupOperation = group(
                BargainPriceEverydayFeeRule.Fields.serviceId,
                BargainPriceEverydayFeeRule.Fields.skuNo,
                BargainPriceEverydayFeeRule.Fields.level2DivisionId)
                .first("$$ROOT").as("info");

        Aggregation countAggregation = newAggregation(
                matchOperation, // 查询条件
                groupOperation,
                replaceRoot("$info"),
                count().as("count")
        ).withOptions(options);

        JSONObject rule = mongoTemplate.aggregate(countAggregation, collectionName, JSONObject.class).getUniqueMappedResult();
        if (rule == null) {
            return new SimplePageInfo<>();
        }
        Long count = rule.getLong("count");

        Pageable pageable = new PageRequest(req.getPageNum() - 1, req.getPageSize());

        SkipOperation skipOperation = skip((long) pageable.getOffset());

        LimitOperation limitOperation = limit(pageable.getPageSize());

        SortOperation sortOperation = sort(Sort.Direction.ASC,
                BargainPriceEverydayFeeRule.Fields.discountPrice,
                BargainPriceEverydayFeeRule.Fields.attributeDisplayNamePinYin,
                BargainPriceEverydayFeeRule.Fields.provinceNamePinYin,
                BargainPriceEverydayFeeRule.Fields.cityName
        );

        Aggregation aggregation = newAggregation(
                matchOperation,
                groupOperation,
                replaceRoot("$info"), // 这个不能丢，否则结果只有group中的几个字段
                sortOperation,
                skipOperation,
                limitOperation
        ).withOptions(options);

        List<BargainPriceEverydayFeeRule> rules = mongoTemplate.aggregate(aggregation, collectionName, BargainPriceEverydayFeeRule.class).getMappedResults();
        if (CollectionUtils.isEmpty(rules)) {
            return new SimplePageInfo<>();
        }

        List<BargainPriceEverydayFeeRuleResp> resultList = rules.stream().map(e -> {
            BargainPriceEverydayFeeRuleResp resp = new BargainPriceEverydayFeeRuleResp();
            BeanUtils.copyProperties(e, resp);
            Double discountPrice = e.getDiscountPrice();
            Double originPrice = e.getOriginPrice();
            Double savingPrice = e.getSavingPrice();
            if (discountPrice != null) {
                resp.setDiscountPrice(new BigDecimal(discountPrice));
            }
            if (originPrice != null) {
                resp.setOriginPrice(new BigDecimal(originPrice));
            }
            if (savingPrice != null) {
                resp.setSavingPrice(new BigDecimal(savingPrice));
            }
            return resp;
        }).collect(Collectors.toList());

        PageImpl<BargainPriceEverydayFeeRule> page = new PageImpl<>(rules, pageable, count);
        SimplePageInfo<BargainPriceEverydayFeeRuleResp> resultPage = new SimplePageInfo<>(resultList);
        resultPage.setPages(page.getTotalPages());
        resultPage.setPageNum(page.getNumber() + 1);
        resultPage.setPageSize(page.getSize());
        resultPage.setTotal(count);
        return resultPage;
    }


    @Override
    public DistrictDetailResp getPreGenerateDistrictDetail(DistrictDetailReq req) {
        String serviceId = req.getServiceId();
        String skuNo = req.getSkuNo();
        String level2DivisionId = req.getLevel2DivisionId();
        // 当前仅支持天天特价
        String sceneCode = SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.getCode();
        if (StringUtils.isBlank(serviceId) || StringUtils.isBlank(skuNo) || StringUtils.isBlank(level2DivisionId) || StringUtils.isBlank(sceneCode)) {
            return null;
        }
        Criteria criteria = where(FeeRule.Fields.sceneCode).is(sceneCode)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(skuNo)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId).is(level2DivisionId)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterInputPrice).ne(null)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code);
        List<FeeRule> feeRuleList = mongoTemplate.find(new Query(criteria), FeeRule.class);
        if (CollectionUtils.isEmpty(feeRuleList)) {
            return null;
        }

        Set<String> level3DivisionIdSet = feeRuleList.stream().map(rule -> rule.getBizRule().get(CommonBizRule.Fields.level3DivisionId)).collect(Collectors.toSet());
        Set<String> level4DivisionIdSet = feeRuleList.stream().map(rule -> rule.getBizRule().get(CommonBizRule.Fields.level4DivisionId)).collect(Collectors.toSet());
        Set<String> divisionIdSet = Stream.concat(level3DivisionIdSet.stream(), level4DivisionIdSet.stream())
                .filter(e -> !StringUtils.isBlank(e) && !e.equals("0"))
                .collect(Collectors.toSet());
        String divisionIds = StringUtils.join(divisionIdSet, ",");
        List<Address> addressList = addressApi.getDivisionInfoListByDivisionIds(divisionIds);
        Map<String, Address> addressMap = addressList.stream().collect(Collectors.toMap(e -> e.getDivisionId().toString(), Function.identity()));

        Map<String, List<FeeRule>> feeRuleMap = feeRuleList.stream().collect(Collectors.groupingBy(feeRule -> feeRule.getBizRule().get(CommonBizRule.Fields.level3DivisionId)));
        DistrictDetailResp resp = new DistrictDetailResp();
        resp.setCityName(req.getCityName());
        List<DistrictDetailResp.DistrictDetail> districtDetailList = new ArrayList<>();
        resp.setDistrictDetailList(districtDetailList);
        for (Map.Entry<String, List<FeeRule>> entry : feeRuleMap.entrySet()) {
            String level3DivisionId = entry.getKey();
            DistrictDetailResp.DistrictDetail districtDetail = new DistrictDetailResp.DistrictDetail();
            Address address = addressMap.get(level3DivisionId);
            if (address != null) {
                districtDetail.setDistrictName(address.getDivisionName());
            } else {
                districtDetail.setDistrictName("区县Id错误，" + level3DivisionId);
            }
            List<String> streetDetailList = new ArrayList<>();
            for (FeeRule feeRule : entry.getValue()) {
                Map<String, String> bizRule = feeRule.getBizRule();
                String level4DivisionId = bizRule.get(CommonBizRule.Fields.level4DivisionId);
                Address street = addressMap.get(level4DivisionId);
                if (street != null) {
                    streetDetailList.add(street.getDivisionName());
                } else {
                    streetDetailList.add("街道Id错误，" + level4DivisionId);
                }
            }
            districtDetail.setStreetNameList(streetDetailList);
            districtDetailList.add(districtDetail);
        }
        return resp;
    }


    @Override
    public DistrictDetailResp getDistrictDetail(DistrictDetailReq req) {
        String sceneCode = req.getSceneCode();
        String serviceId = req.getServiceId();
        String skuNo = req.getSkuNo();
        String level2DivisionId = req.getLevel2DivisionId();
        Criteria criteria = where(BargainPriceEverydayFeeRule.Fields.sceneCode).is(sceneCode)
                .and(BargainPriceEverydayFeeRule.Fields.serviceId).is(serviceId)
                .and(BargainPriceEverydayFeeRule.Fields.skuNo).is(skuNo)
                .and(BargainPriceEverydayFeeRule.Fields.level2DivisionId).is(level2DivisionId)
                .and(BaseDocument.Fields.del).is(false);
        BargainPriceEverydayFeeRule rule = mongoTemplate.findOne(new Query(criteria), BargainPriceEverydayFeeRule.class);
        if (rule == null) {
            return null;
        }
        DistrictDetailResp resp = new DistrictDetailResp();
        resp.setCityName(rule.getCityName());
        List<BargainPriceEverydayFeeRule.DistrictDetail> districtDetailList = rule.getDistrictDetailList();
        if (CollectionUtils.isEmpty(districtDetailList)) {
            return resp;
        }
        List<DistrictDetailResp.DistrictDetail> districtDetails = districtDetailList.stream().map(e -> {
            DistrictDetailResp.DistrictDetail detail = new DistrictDetailResp.DistrictDetail();
            BeanUtils.copyProperties(e, detail);
            return detail;
        }).collect(Collectors.toList());
        resp.setDistrictDetailList(districtDetails);
        return resp;
    }


    @Override
    @Async
    public void preGenerateBargainPriceEverydayFeeRule(String sceneCode) {
        log.warn("预生成天天特价规则开始...");
        List<String> serviceIdList = getServiceIdList(sceneCode);
        if (CollectionUtils.isEmpty(serviceIdList)) {
            log.warn("没有找到服务ID，场景代码：{}", sceneCode);
            return;
        }

        List<BargainPriceEverydayFeeRule> addList = getFeeRules(sceneCode, serviceIdList);
        if (CollectionUtils.isNotEmpty(addList)) {
            String redisKey = getRedisKey(sceneCode);
            log.warn("预生成天天特价规则，版本号的redisKey={}", redisKey);
            String newLastVersionNo = getNewLastVersionNo(sceneCode, redisKey);
            addList.forEach(e -> {
                e.setLastVersionNo(newLastVersionNo);
                e.setAttributeDisplayNamePinYin(PinyinUtil.getFirstLetter(e.getAttributeDisplayName(), ""));
                e.setProvinceNamePinYin(PinyinUtil.getFirstLetter(e.getProvinceName(), ""));
                e.setCityNamePinYin(PinyinUtil.getFirstLetter(e.getCityName(), ""));
            });

            mongoTemplate.insert(addList, BargainPriceEverydayFeeRule.class);

            // 更新redis
            redisHelper.set(redisKey, newLastVersionNo, 0);

            // 删除旧版本
            removeOldVersionRules(sceneCode, newLastVersionNo);
            log.warn("预生成天天特价规则完成，共{}条", addList.size());
        }

        log.warn("预生成天天特价规则完成");

        if (SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.getCode().equals(sceneCode)) {
            preDistrictDetail(sceneCode);
        }

        log.warn("预生成天天特价规则详情完成");
    }


    @Override
    @Async
    public void batchDeleteLogicallyDeletedFeeRules(String sceneCode) {
        Long lastId = null;

        // 判断是否已经启动删除程序了（当前的除外），如果是则终止
        String deleteLock = redisHelper.get(batchDeleteLogicallyDeletedFeeRuleLockRedisKey);
        if (StringUtils.isNotBlank(deleteLock)) {
            log.warn("已有线程正在删除，终止当前删除任务");
            return;
        }
        long startTime = System.currentTimeMillis();
        TaskDuration taskDuration = new TaskDuration();
        try {
            Date now = new Date();
            // 不需要严格控制并发，故无需将redisHelper.get set 做为原子操作
            redisHelper.set(batchDeleteLogicallyDeletedFeeRuleLockRedisKey, "1", 2 * 60 * 60);
            taskDuration.setTaskName("batchDeleteLogicallyDeletedFeeRules");
            taskDuration.setTaskId(SnowFlakeGenerator.INSTANCE.generate());
            taskDuration.setTaskCount(0L);
            taskDuration.setTaskDurationSeconds(0L);
            taskDuration.setCreateTime(now);
            taskDuration.setTaskStatus(AsyncTaskStatusEnum.RUNNING.getCode());
            taskDuration = taskDurationRepository.save(taskDuration);

            do {
                if (!batchDeleteLogicallyDeletedFlag) {
                    log.warn("物理删除已逻辑删除的数据开关已关闭，停止删除");
                    break;
                }

                // 判断是否在同步大数据，如果是，则暂停删除
                String lock = redisHelper.get(pullFeeRuleFromBigdataLockRedisKey);
                if (StringUtils.isNotBlank(lock)) {
                    log.warn("正在同步大数据，停止删除");
                    break;
                }

                // 判断是否在管控时间
                if (checkDeleteFeeRuleControlTime()) {
                    log.warn("管控时间，停止删除");
                    break;
                }

                Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(true);
                if (lastId != null) {
                    criteria.and(FeeRule.Fields.feeRuleId).gt(lastId);
                }
                if (StringUtils.isNotBlank(sceneCode)) {
                    criteria.and(FeeRule.Fields.sceneCode).is(sceneCode);
                }
                Query query = Query.query(criteria);
//                query.fields().include(FeeRule.Fields.feeRuleId);
                query.with(new Sort(Sort.Direction.ASC, FeeRule.Fields.feeRuleId));
                query.limit(deleteFeeRulePageSize);

                List<FeeRule> documentsToDelete = mongoTemplate.find(query, FeeRule.class);
                if (documentsToDelete.isEmpty()) {
                    break;
                }

                List<FeeRuleDeleted> feeRuleDeletedList = documentsToDelete.stream().map(e -> {
                    FeeRuleDeleted feeRuleDeleted = new FeeRuleDeleted();
                    BeanUtils.copyProperties(e, feeRuleDeleted);
                    feeRuleDeleted.setCreateTime(now);
                    feeRuleDeleted.setModifyTime(now);
                    return feeRuleDeleted;
                }).collect(Collectors.toList());
                mongoTemplate.insert(feeRuleDeletedList, FeeRuleDeleted.class);

                mongoTemplate.remove(new Query(Criteria.where(BaseDocument.Fields.id)
                        .in(documentsToDelete.stream().map(BaseDocument::getId).collect(Collectors.toList()))), FeeRule.class);

                // 更新lastId为当前批次中最大ID
                lastId = documentsToDelete.get(documentsToDelete.size() - 1).getFeeRuleId();

//                try {
//                    Thread.sleep(100); // 可选：为了减轻服务器负担，可以在每次删除后短暂休眠
//                } catch (InterruptedException e) {
//                    Thread.currentThread().interrupt();
//                }

                long endTime = System.currentTimeMillis();
                // 更新任务时间 和 任务数
                taskDuration.setTaskCount(taskDuration.getTaskCount() + documentsToDelete.size());
                taskDuration.setTaskDurationSeconds((endTime - startTime) / 1000);
                taskDuration.setModifyTime(now);
                taskDurationRepository.save(taskDuration);
            } while (true);
            taskDuration.setTaskStatus(AsyncTaskStatusEnum.SUCCESS.getCode());
            taskDurationRepository.save(taskDuration);
        } catch (Exception e) {
            log.error("物理删除 已逻辑删除数据任务中断", e);
            taskDuration.setTaskStatus(AsyncTaskStatusEnum.FAIL.getCode());
            taskDurationRepository.save(taskDuration);
        } finally {
            redisHelper.del(batchDeleteLogicallyDeletedFeeRuleLockRedisKey);
        }
    }


    @Override
    public void createRecruitActivityAsync(CreateRecruitActivityReq req) {
        String taskId = req.getRecruitId();
        asyncTasksInfoService.createTask(taskId, AsyncTaskTypeEnum.RECRUIT_ACTIVITY);
        try {
            String msgBody = JSON.toJSONString(req);
            log.info("createRecruitActivityAsync send message, msgBody = {}", msgBody);
            rocketMqSendService.sendSyncTransactionMessage(new NormalMessage(null, UUID.randomUUID().toString(), feeCenterServiceGeneralTopic, MQTagConstant.CREATE_RECRUIT_ACTIVITY, msgBody));
        } catch (Exception e) {
            asyncTasksInfoService.updateStatus(taskId, AsyncTaskTypeEnum.RECRUIT_ACTIVITY, AsyncTaskStatusEnum.FAIL, "发送MQ消息失败");
            log.error("createRecruitActivityAsync failed to send message", e);
        }
    }

    @Override
    public void deleteRecruitMasterBatchAsync(List<DeleteRecruitMasterReq> reqList) {
        if (CollectionUtils.isEmpty(reqList)) {
            return;
        }
        // Springboot版本太低，不支撑注解 处理事务，只能手动处理
        ClientSession session = mongoClient.startSession();
        try {
            session.startTransaction();
            reqList.forEach(e -> {
                String masterId = e.getMasterId();
                String bizTag = e.getRecruitId();
                if (StringUtils.isBlank(masterId) || StringUtils.isBlank(bizTag)) {
                    log.error("deleteRecruitMasterBatchAsync param error, masterId = {}, bizTag = {}", masterId, bizTag);
                    return;
                }
                Criteria criteria = where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterId).is(masterId)
                        .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizTag).is(bizTag);
                mongoTemplate.remove(new Query(criteria), FeeRule.class);
            });
            session.commitTransaction();
        } catch (Exception e) {
            session.abortTransaction();
            log.error("deleteRecruitMasterBatchAsync failed", e);
            throw e;
        } finally {
            session.close();
        }
    }


    private void preDistrictDetail(String sceneCode) {
//        String newLastVersionNo = getNewLastVersionNo(sceneCode, preGenerateBargainPriceEverydayFeeRuleRedisKey);
        String newLastVersionNo = redisHelper.get(preGenerateBargainPriceEverydayFeeRuleRedisKey);
        Criteria criteria = where(BargainPriceEverydayFeeRule.Fields.sceneCode).is(sceneCode)
                .and(BaseDocument.Fields.del).is(false)
                .and(BargainPriceEverydayFeeRule.Fields.lastVersionNo).is(newLastVersionNo);

        Query query = new Query(criteria);
        int pageNum = 0;
        final int pageSize = 20;
        while (true) {
            query.skip(pageNum * pageSize).limit(pageSize);
            List<BargainPriceEverydayFeeRule> rules = mongoTemplate.find(query, BargainPriceEverydayFeeRule.class);
            if (CollectionUtils.isEmpty(rules)) {
                break;
            }
            for (BargainPriceEverydayFeeRule rule : rules) {
                DistrictDetailReq req = new DistrictDetailReq();
                BeanUtils.copyProperties(rule, req);
                DistrictDetailResp detail = getPreGenerateDistrictDetail(req);
                if (detail == null) {
                    continue;
                }
                List<DistrictDetailResp.DistrictDetail> districtDetailList = detail.getDistrictDetailList();
                if (CollectionUtils.isEmpty(districtDetailList)) {
                    continue;
                }
                List<BargainPriceEverydayFeeRule.DistrictDetail> saveDistrictDetailList = districtDetailList.stream().map(e -> {
                    BargainPriceEverydayFeeRule.DistrictDetail districtDetail = new BargainPriceEverydayFeeRule.DistrictDetail();
                    BeanUtils.copyProperties(e, districtDetail);
                    return districtDetail;
                }).collect(Collectors.toList());
                rule.setDistrictDetailList(saveDistrictDetailList);
            }

            BulkOperations bulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, BargainPriceEverydayFeeRule.class);
            for (BargainPriceEverydayFeeRule rule : rules) {
                Query updateQuery = new Query(where(BaseDocument.Fields.id).is(rule.getId()));
                Update update = new Update();
                update.set(BargainPriceEverydayFeeRule.Fields.districtDetailList, rule.getDistrictDetailList());
                bulkOps.updateOne(updateQuery, update);
            }
            bulkOps.execute();
            pageNum++;
        }
    }


    private List<String> getServiceIdList(String sceneCode) {
        List serviceIdList = mongoTemplate.getCollection("feeRule")
                .distinct(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId,
                        new BasicDBObject()
                                .append(FeeRule.Fields.sceneCode, sceneCode)
                                .append(BaseDocument.Fields.del, false)
                                .append(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag, FeeTypeTagEnum.SERVICE_FEE.code)
                                .append(BaseDocument.Fields.status, RuleStatusEnum.ACTIVE.code)
                );
        List<String> serviceIdStrList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(serviceIdList)) {
            log.warn("预生成天天特价规则开始，服务共{}条", serviceIdList.size());
            for (Object serviceIdObj : serviceIdList) {
                serviceIdStrList.add(serviceIdObj.toString());
            }
        }
        return serviceIdStrList;
    }

    private List<BargainPriceEverydayFeeRule> getFeeRules(String sceneCode, List<String> serviceIdList) {
        List<BargainPriceEverydayFeeRule> addList = new ArrayList<>();
        List<BargainPriceEverydayFeeRule> rules = getBargainPriceEveryDayFeeRule(serviceIdList, sceneCode);
        if (CollectionUtils.isNotEmpty(rules)) {
            addList.addAll(rules);
        }
        return addList;
    }

    private String getRedisKey(String sceneCode) {
        if (SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.getCode().equals(sceneCode)) {
            return preGenerateBargainPriceEverydayFeeRuleRedisKey;
        } else if (SceneCodeEnum.PLATFORM_FIXED_PRICE.getCode().equals(sceneCode)) {
            return preGeneratePlatformFixedPriceFeeRuleRedisKey;
        }
        return "";
    }

    private String getNewLastVersionNo(String sceneCode, String redisKey) {
        String oldLastVersionNo = redisHelper.get(redisKey);
        String newLastVersionNo = "1";
        if (StringUtils.isBlank(oldLastVersionNo)) {
            Criteria criteria = Criteria.where(BargainPriceEverydayFeeRule.Fields.sceneCode).is(sceneCode);
            Query query = new Query(criteria);
            BargainPriceEverydayFeeRule one = mongoTemplate.findOne(query, BargainPriceEverydayFeeRule.class);
            if (one != null) {
                String lastVersionNo = one.getLastVersionNo();
                if (StringUtils.isNotBlank(lastVersionNo)) {
                    newLastVersionNo = Integer.parseInt(lastVersionNo) + 1 + "";
                }
            }
        } else {
            newLastVersionNo = Integer.parseInt(oldLastVersionNo) + 1 + "";
        }
        return newLastVersionNo;
    }

    private void removeOldVersionRules(String sceneCode, String newLastVersionNo) {
        Criteria criteria = Criteria.where(BargainPriceEverydayFeeRule.Fields.lastVersionNo).ne(newLastVersionNo)
                .and(BargainPriceEverydayFeeRule.Fields.sceneCode).is(sceneCode);
        mongoTemplate.remove(new Query(criteria), BargainPriceEverydayFeeRule.class);
    }


    private List<BargainPriceEverydayFeeRule> getBargainPriceEveryDayFeeRule(List<String> serviceIdList, String sceneCode) {
        if (CollectionUtils.isEmpty(serviceIdList) || StringUtils.isBlank(sceneCode)) {
            log.warn("预生成天天特价规则失败，参数为空，serviceIdList={}, sceneCode={}", serviceIdList, sceneCode);
            return null;
        }
        List<BargainPriceEverydayFeeRule> resultList = new ArrayList<>();
        for (String serviceId : serviceIdList) {
            BargainPriceEverydayFeeRuleReq req = new BargainPriceEverydayFeeRuleReq();
            req.setSceneCode(sceneCode);
            // 处理天天特价
            int pageNum = 1;
            int pageSize = 50;
            req.setPageSize(pageSize);
            Date now = new Date();
            while (true) {
                log.warn("预生成天天特价规则开始，pageNum={}, serviceId={}", pageNum, serviceId);
                req.setPageNum(pageNum);
                req.setServiceId(serviceId);
                SimplePageInfo<BargainPriceEverydayFeeRuleResp> rulePage = getPreGenerateBargainPriceEverydayFeeRule(req);
                List<BargainPriceEverydayFeeRuleResp> ruleList = rulePage.getList();
                if (CollectionUtils.isEmpty(ruleList)) {
                    log.warn("预生成天天特价规则结束，pageNum={}, serviceId={}", pageNum, serviceId);
                    break;
                }
                ruleList.forEach(rule -> {
                    BigDecimal savingPrice = rule.getSavingPrice();
                    if (savingPrice == null || savingPrice.compareTo(BigDecimal.ZERO) <= 0) {
                        log.warn("天天特价规则savingPrice为空小于等于0，skuNo:{}, serviceId:{}, savingPrice:{}", rule.getSkuNo(), rule.getServiceId(), savingPrice);
                        return;
                    }
                    BargainPriceEverydayFeeRule feeRule = new BargainPriceEverydayFeeRule();
                    BeanUtils.copyProperties(rule, feeRule);
                    feeRule.setSceneCode(sceneCode);
                    feeRule.setCreateTime(now);
                    feeRule.setModifyTime(now);
                    BigDecimal discountPrice = rule.getDiscountPrice();
                    if (discountPrice != null) {
                        feeRule.setDiscountPrice(discountPrice.doubleValue());
                    }
                    BigDecimal originPrice = rule.getOriginPrice();
                    if (originPrice != null) {
                        feeRule.setOriginPrice(originPrice.doubleValue());
                    }
                    feeRule.setSavingPrice(savingPrice.doubleValue());
                    resultList.add(feeRule);
                });

                pageNum++;
            }
        }
        return resultList;
    }


    private SimplePageInfo<BargainPriceEverydayFeeRuleResp> getPreGenerateBargainPriceEverydayFeeRule(BargainPriceEverydayFeeRuleReq req) {
        String sceneCode = req.getSceneCode();
        String serviceId = req.getServiceId();
        String skuNo = req.getSkuNo();
        String level2DivisionId = req.getLevel2DivisionId();
        String feeTypeTag = req.getFeeTypeTag();
        if (StringUtils.isBlank(feeTypeTag)) {
            feeTypeTag = FeeTypeTagEnum.SERVICE_FEE.code;
        }
        String collectionName = "feeRule";
        AggregationOptions options = newAggregationOptions().allowDiskUse(true).build();
        if (SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.getCode().equals(sceneCode)) {

            if (bargainPriceEveryDayNew) {
                Pageable pageable = new PageRequest(req.getPageNum() - 1, req.getPageSize());

                Criteria matchCriteria = where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                        .and(FeeRule.Fields.sceneCode).is(sceneCode)
                        .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterInputPrice).ne(null)
                        .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).is(feeTypeTag)
                        .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(DivisionTypeEnum.STREET.code)
                        .and(BaseDocument.Fields.del).is(false)
                        .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code);
                if (StringUtils.isNotBlank(skuNo)) {
                    matchCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(skuNo);
                }
                if (StringUtils.isNotBlank(level2DivisionId)) {
                    matchCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId).is(level2DivisionId);
                } else {
                    matchCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId).ne(null);
                }

                MatchOperation matchOperation = match(matchCriteria);

                Aggregation countAggregation = newAggregation(
                        match(matchCriteria), // 查询条件
                        group(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo,
                                FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId)
                                .first("$$ROOT").as("info"),
                        replaceRoot("$info"),
                        count().as("count")
                ).withOptions(options);

                JSONObject rule = mongoTemplate.aggregate(countAggregation, collectionName, JSONObject.class).getUniqueMappedResult();

                if (rule == null) {
                    return new SimplePageInfo<>();
                }
                Long count = rule.getLong("count");

                GroupOperation groupOperation = group(
                        FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo,
                        FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId)
                        .addToSet("bizRule.level3DivisionId").as("level3DivisionIds")
                        .addToSet("bizRule.level4DivisionId").as("level4DivisionIds")
                        .first(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.attributeDisplayName).as(CommonBizRule.Fields.attributeDisplayName)
                        .first(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId).as(CommonBizRule.Fields.level2DivisionId)
                        .first(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).as(CommonBizRule.Fields.skuNo)
                        .min(FeeRule.Fields.masterInputPriceDouble).as("discountPrice");

                ProjectionOperation project = project()
                        .andExclude("_id")
                        .and(CommonBizRule.Fields.skuNo).as(CommonBizRule.Fields.skuNo)
                        .and(CommonBizRule.Fields.level2DivisionId).as(CommonBizRule.Fields.level2DivisionId)
                        .and("level3DivisionIds").size().as("districtCount")
                        .and("level4DivisionIds").size().as("streetCount")
                        .and("discountPrice").as("discountPrice")
                        .and(CommonBizRule.Fields.attributeDisplayName).as(CommonBizRule.Fields.attributeDisplayName); // 使用previousOperation保持feeName字段不变

                // Skip stage for pagination
                SkipOperation skipOperation = skip((long) pageable.getOffset());

                // Limit stage for pagination
                LimitOperation limitOperation = limit(pageable.getPageSize());

                Aggregation aggregation = newAggregation(
                        matchOperation,
                        groupOperation,
                        project,
                        skipOperation,
                        limitOperation).withOptions(options);

                AggregationResults<BargainPriceAggregationResult> aggregationResults = mongoTemplate.aggregate(aggregation, collectionName, BargainPriceAggregationResult.class);

                if (aggregationResults == null) {
                    return new SimplePageInfo<>();
                }

                List<BargainPriceAggregationResult> countResults = aggregationResults.getMappedResults();
                if (CollectionUtils.isEmpty(countResults)) {
                    return new SimplePageInfo<>();
                }

                List<BargainPriceEverydayFeeRuleResp> resultList = new ArrayList<>();

                Set<String> skuNos = countResults.stream().map(BargainPriceAggregationResult::getSkuNo).collect(Collectors.toSet());
                Set<String> level2DivisionIds = countResults.stream().map(BargainPriceAggregationResult::getLevel2DivisionId).collect(Collectors.toSet());

                // 原价
                Criteria criteria = where(FeeRule.Fields.sceneCode).is(sceneCode)
                        .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                        .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterInputPrice).ne(null)
                        .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(DivisionTypeEnum.COUNTRY.code)
                        .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).is(feeTypeTag)
                        .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).in(skuNos)
                        .and(BaseDocument.Fields.del).is(false)
                        .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code);

                Aggregation feeRuleAggregate = newAggregation(
                        match(criteria), // 查询条件
                        group(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo)
                                .first("$$ROOT").as("info"),
                        replaceRoot("$info"));

                AggregationResults<FeeRule> feeRuleAggregateResult = mongoTemplate.aggregate(feeRuleAggregate, collectionName, FeeRule.class);

                List<FeeRule> feeRuleList = feeRuleAggregateResult.getMappedResults();
                if (CollectionUtils.isEmpty(feeRuleList)) {
                    return new SimplePageInfo<>();
                }
                Map<String, FeeRule> skuNoRuleMap = feeRuleList.stream().collect(Collectors.toMap(feeRule -> feeRule.getBizRule().get(CommonBizRule.Fields.skuNo), Function.identity()));

                List<Address> addressList = addressApi.getDivisionInfoListByDivisionIds(StringUtils.join(level2DivisionIds, ","));
                Map<Long, Address> addressMap = addressList.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));

                for (BargainPriceAggregationResult countResult : countResults) {

                    String skuNoInner = countResult.getSkuNo();
                    String level2DivisionIdInner = countResult.getLevel2DivisionId();

                    Address address = addressMap.get(Long.valueOf(level2DivisionIdInner));
                    if (address != null) {
                        countResult.setCityName(address.getDivisionAbbName());
                        countResult.setLevel1DivisionId(address.getLv2DivisionId().toString());
                        countResult.setProvinceName(address.getLv2DivisionName());
                    }

                    FeeRule feeRule = skuNoRuleMap.get(skuNoInner);
                    // 原价
                    countResult.setOriginPrice(0D);
                    if (feeRule != null) {
                        countResult.setOriginPrice(Double.valueOf(feeRule.getBizRule().get(CommonBizRule.Fields.masterInputPrice)));
                    }

                    // 补贴价
                    countResult.setSavingPrice(countResult.getOriginPrice() - countResult.getDiscountPrice());

                    BargainPriceEverydayFeeRuleResp resp = new BargainPriceEverydayFeeRuleResp();
                    BeanUtils.copyProperties(countResult, resp);
                    resp.setServiceId(serviceId);
                    resp.setOriginPrice(BigDecimal.valueOf(countResult.getOriginPrice()));
                    resp.setDiscountPrice(BigDecimal.valueOf(countResult.getDiscountPrice()));
                    resp.setSavingPrice(BigDecimal.valueOf(countResult.getSavingPrice()));
                    resp.setAttributeDisplayName(Optional.ofNullable(countResult.getAttributeDisplayName()).map(String::trim).orElse(""));
                    resultList.add(resp);
                }

                PageImpl<BargainPriceAggregationResult> page = new PageImpl<>(countResults, pageable, count);
                SimplePageInfo<BargainPriceEverydayFeeRuleResp> resultPage = new SimplePageInfo<>(resultList);
                resultPage.setPages(page.getTotalPages());
                resultPage.setPageNum(page.getNumber() + 1);
                resultPage.setPageSize(page.getSize());
                resultPage.setTotal(count);
                return resultPage;
            } else {
                Pageable pageable = new PageRequest(req.getPageNum() - 1, req.getPageSize());

                Criteria matchCriteria = where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                        .and(FeeRule.Fields.sceneCode).is(sceneCode)
                        .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterInputPrice).ne(null)
                        .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).is(feeTypeTag)
                        .and(BaseDocument.Fields.del).is(false)
                        .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code);
                if (StringUtils.isNotBlank(skuNo)) {
                    matchCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(skuNo);
                }
                if (StringUtils.isNotBlank(level2DivisionId)) {
                    matchCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId).is(level2DivisionId);
                } else {
                    matchCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId).ne(null);
                }

                MatchOperation matchOperation = match(matchCriteria);

                Aggregation countAggregation = newAggregation(
                        match(matchCriteria), // 查询条件
                        group(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo,
                                FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId)
                                .first("$$ROOT").as("info"),
                        replaceRoot("$info"),
                        count().as("count")
                ).withOptions(options);

                JSONObject rule = mongoTemplate.aggregate(countAggregation, collectionName, JSONObject.class).getUniqueMappedResult();
                if (rule == null) {
                    return new SimplePageInfo<>();
                }
                Long count = rule.getLong("count");

                GroupOperation groupOperation = group(
                        FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo,
                        FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId)
                        .first(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.attributeDisplayName).as(CommonBizRule.Fields.attributeDisplayName)
                        .push("$$ROOT").as("docs");

                // Skip stage for pagination
                SkipOperation skipOperation = skip((long) pageable.getOffset());

                // Limit stage for pagination
                LimitOperation limitOperation = limit(pageable.getPageSize());

                Aggregation aggregation = newAggregation(
                        matchOperation,
                        groupOperation,
                        skipOperation,
                        limitOperation).withOptions(options);

                AggregationResults<FeeRuleAggregationResult> aggregationResults = mongoTemplate.aggregate(aggregation, collectionName, FeeRuleAggregationResult.class);

                if (aggregationResults == null) {
                    return new SimplePageInfo<>();
                }

                List<FeeRuleAggregationResult> countResults = aggregationResults.getMappedResults();
                if (CollectionUtils.isEmpty(countResults)) {
                    return new SimplePageInfo<>();
                }

                List<BargainPriceEverydayFeeRuleResp> resultList = new ArrayList<>();
                for (FeeRuleAggregationResult countResult : countResults) {
                    BargainPriceEverydayFeeRuleResp resp = new BargainPriceEverydayFeeRuleResp();
                    resultList.add(resp);
                    resp.setServiceId(serviceId);
                    String skuNoInner = countResult.getSkuNo();
                    String attributeDisplayName = countResult.getAttributeDisplayName();
                    String level2DivisionIdInner = countResult.getLevel2DivisionId();

                    resp.setSkuNo(skuNoInner);
                    resp.setAttributeDisplayName(attributeDisplayName);
                    resp.setLevel2DivisionId(level2DivisionIdInner);

                    Address address = addressApi.getDivisionInfoByDivisionId(Long.valueOf(level2DivisionIdInner));
                    if (address != null) {
                        resp.setCityName(address.getDivisionAbbName());
                        resp.setLevel1DivisionId(address.getLv2DivisionId().toString());
                        resp.setProvinceName(address.getLv2DivisionName());
                    }

                    List<FeeRule> feeRuleList = countResult.getDocs();
                    int districtCount = feeRuleList.stream().map(f -> f.getBizRule().get(CommonBizRule.Fields.level3DivisionId)).collect(Collectors.toSet()).size();
                    int streetCount = feeRuleList.stream().map(f -> f.getBizRule().get(CommonBizRule.Fields.level4DivisionId)).collect(Collectors.toSet()).size();
                    resp.setDistrictCount(districtCount);
                    resp.setStreetCount(streetCount);

                    // 原价
                    Criteria criteria = where(FeeRule.Fields.sceneCode).is(sceneCode)
                            .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                            .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterInputPrice).ne(null)
                            .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(DivisionTypeEnum.COUNTRY.code)
                            .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).is(feeTypeTag)
                            .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(skuNoInner)
                            .and(BaseDocument.Fields.del).is(false)
                            .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code);
                    FeeRule feeRule = mongoTemplate.findOne(new Query(criteria), FeeRule.class);

                    // 原价
                    resp.setOriginPrice(BigDecimal.ZERO);
//                if (CollectionUtils.isNotEmpty(ruleList)) {
//                    ruleList.stream().findFirst().ifPresent(f -> resp.setOriginPrice(new BigDecimal(f.getBizRule().get(CommonBizRule.Fields.masterInputPrice))));
//                }
                    if (feeRule != null) {
                        resp.setOriginPrice(new BigDecimal(feeRule.getBizRule().get(CommonBizRule.Fields.masterInputPrice)));
                    }

                    // 补贴价
                    BigDecimal discountPrice = feeRuleList.stream().filter(f -> DivisionTypeEnum.STREET.code.equals(f.getBizRule().get(CommonBizRule.Fields.divisionType)))
                            .map(f -> f.getBizRule().get(CommonBizRule.Fields.masterInputPrice))
                            .map(price -> StringUtils.isBlank(price) ? BigDecimal.ZERO : new BigDecimal(price))
                            .min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
                    resp.setDiscountPrice(discountPrice);
                    resp.setSavingPrice(resp.getOriginPrice().subtract(discountPrice));
                }

                PageImpl<FeeRuleAggregationResult> page = new PageImpl<>(countResults, pageable, count);
                SimplePageInfo<BargainPriceEverydayFeeRuleResp> resultPage = new SimplePageInfo<>(resultList);
                resultPage.setPages(page.getTotalPages());
                resultPage.setPageNum(page.getNumber() + 1);
                resultPage.setPageSize(page.getSize());
                resultPage.setTotal(count);
                return resultPage;
            }


        } else if (SceneCodeEnum.PLATFORM_FIXED_PRICE.getCode().equals(sceneCode)) {

            // 只有优惠的才需要返回，所以先找动态价规则（城市维度），如果没有则直接返回空
            // 如果有，则获取所以支持的城市id，将城市id昨晚查询条件带入到规则查询
            log.warn("进入平台一口价格规则查询，serviceId={}", serviceId);
            Date now = new Date();
//            Criteria dynamicCriteria = where(BaseDocument.Fields.del).is(false)
//                    .and(BaseDocument.Fields.status).is(DynamicFeeRuleStatusEnum.PASS.code)
//                    .and(DynamicFeeRule.Fields.sceneCode).is(sceneCode)
//                    .and(DynamicFeeRule.Fields.feeTypeTag).is(FeeTypeTagEnum.SERVICE_FEE.code)
//                    .and(DynamicFeeRule.Fields.divisionType).is(DivisionTypeEnum.CITY.code)
//                    .and(DynamicFeeRule.Fields.serviceDataList).elemMatch(where(ServiceData.Fields.serviceId).is(Long.valueOf(serviceId)))
//                    .and(DynamicFeeRule.Fields.startTime).lte(now)
//                    .and(DynamicFeeRule.Fields.endTime).gte(now)
//                    .and(DynamicFeeRule.Fields.dynamicCalculateRuleData + PunConstant.DOT + DynamicCalculateRuleData.Fields.dynamicSymbol).is(DynamicSymbolEnum.DECREASE.code);
//            if (StringUtils.isNotBlank(level2DivisionId)) {
//                dynamicCriteria.and(DynamicFeeRule.Fields.divisionIds).is(Long.valueOf(level2DivisionId));
//            }
////            Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.modifyTime));
//            Query query = new Query(dynamicCriteria);
////            query.with(sort);
//            List<DynamicFeeRule> dynamicFeeRules = mongoTemplate.find(query, DynamicFeeRule.class);
//            if (CollectionUtils.isEmpty(dynamicFeeRules)) {
//                return new SimplePageInfo<>();
//            }
//
//            Set<Long> divisionIds = new HashSet<>();
//            dynamicFeeRules.forEach(e -> divisionIds.addAll(e.getDivisionIds()));
//
////            DynamicFeeRule dynamicFeeRule = dynamicFeeRules.get(0);
////            List<Long> divisionIds = dynamicFeeRule.getDivisionIds();
//            if (CollectionUtils.isEmpty(divisionIds)) {
//                log.warn("动态规则中没有城市id，sceneCode={}, serviceId={}", sceneCode, serviceId);
//                return new SimplePageInfo<>();
//            }
//            List<String> cityIdList = divisionIds.stream().map(String::valueOf).collect(Collectors.toList());

            Criteria criteria = where(FeeRule.Fields.sceneCode).is(sceneCode)
                    .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                    .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterInputPrice).ne(null)
                    .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).is(FeeTypeTagEnum.SERVICE_FEE.code)
                    .and(BaseDocument.Fields.del).is(false)
                    .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code)
                    .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(DivisionTypeEnum.CITY.code);
            if (StringUtils.isNotBlank(skuNo)) {
                criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(skuNo);
            }
            if (StringUtils.isNotBlank(level2DivisionId)) {
                criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId).is(level2DivisionId);
            }
//            else {
//                criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId).in(cityIdList);
//            }
            // 要有能匹配得上的动态规则
            criteria.and(FeeRule.Fields.dynamicFeeRuleCalculateRuleDataList).elemMatch(
                    where(DynamicFeeRuleCalculateRuleData.Fields.matchingRule + PunConstant.DOT + DynamicFeeRuleCalculateRuleData.MatchingRule.Fields.startTime).lte(now)
                            .and(DynamicFeeRuleCalculateRuleData.Fields.matchingRule + PunConstant.DOT + DynamicFeeRuleCalculateRuleData.MatchingRule.Fields.endTime).gte(now));

            Aggregation feeRuleAggregation = newAggregation(
                    match(criteria), // 查询条件
                    group(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo,
                            FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId).first("$$ROOT").as("info"),
                    replaceRoot("$info"),
                    count().as("count")
            ).withOptions(options);

            JSONObject rule = mongoTemplate.aggregate(feeRuleAggregation, collectionName, JSONObject.class).getUniqueMappedResult();
            if (rule == null) {
                log.warn("没有查询到规则,rule为null");
                return new SimplePageInfo<>();
            }
            Long count = rule.getLong("count");
            log.warn("查询到的规则数，count={}", count);

            if (count <= 0) {
                return new SimplePageInfo<>();
            }

//            Sort sortFeeRule = new Sort(new Sort.Order(Sort.Direction.ASC, "bizRule.masterInputPriceDouble"));
            Sort sortFeeRule = new Sort(new Sort.Order(Sort.Direction.ASC, FeeRule.Fields.masterInputPriceDouble));
            feeRuleAggregation = newAggregation(
//                    sort(Sort.Direction.ASC, "bizRule.masterInputPriceDecimal"),
                    match(criteria), // 查询条件
                    group(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo,
                            FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId).first("$$ROOT").as("info"),
                    replaceRoot("$info"),
                    sort(sortFeeRule),
                    skip((req.getPageNum() - 1) * req.getPageSize()),
                    limit(req.getPageSize())
            ).withOptions(options);

            AggregationResults<FeeRule> aggregate = mongoTemplate.aggregate(feeRuleAggregation, collectionName, FeeRule.class);
            if (Objects.isNull(aggregate)) {
                return new SimplePageInfo<>();
            }
            List<FeeRule> feeRuleList = aggregate.getMappedResults();
            if (CollectionUtils.isEmpty(feeRuleList)) {
                log.warn("没有找到规则");
                return new SimplePageInfo<>();
            }

            Set<String> level2DivisionIds = feeRuleList.stream().map(feeRule -> feeRule.getBizRule().get(CommonBizRule.Fields.level2DivisionId)).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            List<Address> addressList = addressApi.getDivisionInfoListByDivisionIds(StringUtils.join(level2DivisionIds, ","));
            Map<Long, Address> addressMap = addressList.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));

            List<BargainPriceEverydayFeeRuleResp> respList = new ArrayList<>();

            for (FeeRule feeRule : feeRuleList) {
                Map<String, String> bizRule = feeRule.getBizRule();
                BargainPriceEverydayFeeRuleResp resp = new BargainPriceEverydayFeeRuleResp();
                resp.setServiceId(serviceId);
                resp.setSkuNo(bizRule.get(CommonBizRule.Fields.skuNo));
                resp.setAttributeDisplayName(bizRule.get(CommonBizRule.Fields.attributeDisplayName));
                resp.setMasterInputPriceDouble(feeRule.getMasterInputPriceDouble());

                // 处理城市
                level2DivisionId = bizRule.get(CommonBizRule.Fields.level2DivisionId);
                resp.setLevel2DivisionId(level2DivisionId);
                if (StringUtils.isNotBlank(level2DivisionId)) {
                    Address address = addressMap.get(Long.valueOf(level2DivisionId));
                    if (address != null) {
                        resp.setCityName(address.getDivisionAbbName());
                        resp.setProvinceName(address.getLv2DivisionName());
                        resp.setLevel1DivisionId(address.getLv2DivisionId().toString());
                    } else {
                        resp.setCityName("城市Id异常，cityId=" + level2DivisionId);
                    }
                }

                // 原价
                String masterInputPrice = bizRule.get(CommonBizRule.Fields.masterInputPrice);
                BigDecimal originalPrice = StringUtils.isBlank(masterInputPrice) ? BigDecimal.ZERO : new BigDecimal(masterInputPrice);
                resp.setOriginPrice(originalPrice);

//                String feeTypeTagInner = bizRule.get(CommonBizRule.Fields.feeTypeTag);
//                FeeTypeTagEnum feeType = FeeTypeTagEnum.fromCode(feeTypeTagInner);

                String cityId = feeRule.getBizRule().get(CommonBizRule.Fields.level2DivisionId);

//                DynamicFeeRule dynamicFeeRule = dynamicFeeRules.stream().filter(e -> e.getDivisionIds().contains(Long.valueOf(cityId))).findFirst().orElse(null);
                DynamicFeeRuleCalculateRuleData ruleData = feeRule.getDynamicFeeRuleCalculateRuleDataList().stream().filter(
                                e -> e.getMatchingRule().getStartTime().compareTo(now) <= 0 && e.getMatchingRule().getEndTime().compareTo(now) >= 0)
                        .findFirst().orElse(null);

                if (ruleData == null) {
                    log.error("动态规则中没有匹配的规则，sceneCode={}, serviceId={}, cityId={}", sceneCode, serviceId, cityId);
                    continue;
                }

                try {
                    handleDynamicPrice(masterInputPrice, resp, ruleData, now);
                } catch (Exception e) {
                    log.error("天天特价 计算动态价异常，sceneCode={}, feeRuleId={}", sceneCode, feeRule.getFeeRuleId());
                    continue;
                }

                // 只有 服务费 需要计算动态价
//                if (FeeTypeTagEnum.SERVICE_FEE.equals(feeType)) {
//                    handleDynamicPrice(masterInputPrice, resp, dynamicFeeRule);
//                } else {
//                    resp.setDiscountPrice(originalPrice);
//                    resp.setSavingPrice(BigDecimal.ZERO);
//                }
                resp.setAttributeDisplayName(Optional.ofNullable(resp.getAttributeDisplayName()).map(String::trim).orElse(""));
                respList.add(resp);
            }
            Pageable pageable = new PageRequest(req.getPageNum() - 1, req.getPageSize());
            PageImpl<BargainPriceEverydayFeeRuleResp> page = new PageImpl<>(respList, pageable, count);
            SimplePageInfo<BargainPriceEverydayFeeRuleResp> resultPage = new SimplePageInfo<>(respList);
            resultPage.setPages(page.getTotalPages());
            resultPage.setPageNum(page.getNumber() + 1);
            resultPage.setPageSize(page.getSize());
            resultPage.setTotal(count);
            return resultPage;
        }
        return new SimplePageInfo<>();
    }


    @Override
    public List<AttributeValueResp> getAttributeValueByServiceId(String sceneCode, Long serviceId) {
        Criteria criteria = where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(String.valueOf(serviceId))
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).is(FeeTypeTagEnum.SERVICE_FEE.code)
                .and(FeeRule.Fields.sceneCode).is(sceneCode)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);

        MatchOperation matchOperation = match(criteria);
        GroupOperation groupOperation = group(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo);
        ProjectionOperation projectionOperation = project(CommonBizRule.Fields.skuNo, CommonBizRule.Fields.attributeDisplayName);

        Aggregation aggregation = Aggregation.newAggregation(
                matchOperation,
                groupOperation.first(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).as(CommonBizRule.Fields.skuNo)
                        .first(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.attributeDisplayName).as(CommonBizRule.Fields.attributeDisplayName),
                projectionOperation
        );
        AggregationResults<FeeRulePageResp> aggregate = mongoTemplate.aggregate(aggregation, "feeRule", FeeRulePageResp.class);
        List<FeeRulePageResp> respList = aggregate.getMappedResults();
        if (CollectionUtils.isEmpty(respList)) {
            return Collections.emptyList();
        }
        return respList.stream().map(resp -> new AttributeValueResp(
                resp.getSkuNo(),
                resp.getAttributeDisplayName()
        )).collect(Collectors.toList());
    }


    @Resource
    private Executor fetchBigdataTaskExecutor;


    @Override
    @Async
    public void pullFeeRuleFromBigdata(String sceneCode) {
        isManaged();

        redisHelper.set(pullFeeRuleFromBigdataLockRedisKey, "1", 2 * 60 * 60);
        SceneCodeEnum sceneCodeEnum = SceneCodeEnum.fromCode(sceneCode);
        if (sceneCodeEnum == null) {
            throw new BusException("sceneCode error");
        }

        pullFeeRuleFromBigdataLegacy(sceneCode, sceneCodeEnum);

        redisHelper.del(pullFeeRuleFromBigdataLockRedisKey);
    }

    /**
     * 从bigdata拉取计价模版、规则
     * @param sceneCode 场景编码
     */
    @Resource
    private BigdataSyncCoordinator bigdataSyncCoordinator;

    @Override
    @Async
    public void pullFeeRuleFromBigdataV2(String sceneCode) {
        isManaged();

        try {
            // 使用新的同步协调器执行同步
            bigdataSyncCoordinator.syncData(sceneCode);
        } catch (Exception e) {
            log.error("同步大数据失败，sceneCode={}", sceneCode, e);
            throw e;
        }
    }

    @Override
    public void pullCustomTemplateFromBigdata() {
        log.info("pullCustomTemplateFromBigdata start");
        List<SceneInfo> sceneInfoList = sceneInfoService.selectByPullBigDataCustomSku();
        if (CollectionUtil.isEmpty(sceneInfoList)) {
            return;
        }
        sceneInfoList.forEach(sceneInfo -> {
            log.info("pullCustomTemplateFromBigdata start, sceneCode:{}", sceneInfo.getSceneCode());
            pullFeeTemplateFromBigdata(sceneInfo.getSceneCode());
        });
    }

    /**
     * 从大数据同步自定义sku模版
     * @param sceneCode
     */
    private void pullFeeTemplateFromBigdata(String sceneCode) {

        //大数据有效的sku模版
        Set<String> bigdataCustomSku = Sets.newHashSet();

        int pageNum = 1;
        int pageSize = 200;
        while (true) {

            List<GetSkuTemplateInfoBySceneCodeResp> skuTemplateInfoBySceneCodeRespList = this.pullSkuTemplateFromBigdata(sceneCode, pageNum, pageSize);
            if (CollectionUtil.isEmpty(skuTemplateInfoBySceneCodeRespList)) {
                log.warn("分批调用大数据接口获取自定义sku模版结束!");
                break;
            }
            skuTemplateInfoBySceneCodeRespList = skuTemplateInfoBySceneCodeRespList.stream().filter(resp -> {
                if (StringUtils.isEmpty(resp.getSceneCode())
                        || StringUtils.isEmpty(resp.getSkuNo())
                        || StringUtils.isEmpty(resp.getServiceId())
                        || StringUtils.isEmpty(resp.getAttributeDisplayName())
                        || StringUtils.isEmpty(resp.getAttributeValueFixed())
                        || StringUtils.isEmpty(resp.getCustomSkuUserid())) {

                    log.info("scene_code、sku_no、service_id、attribute_display_Name、attribute_value_fixed、custom_sku_userId is empty,need filter, resp:{}", resp);
                    return false;
                }
                //查询是否存在标准sku类型且不是标准附加费的模版
                List<FeeTemplate> feeTemplateList = feeTemplateService.selectByServiceIdAndSkuNoAndSkuTypeNotStandardSurcharge(sceneCode, resp.getServiceId(), resp.getSkuNo(), FeeSkuTypeEnum.STANDARD_SKU.code);
                if (CollectionUtil.isEmpty(feeTemplateList)) {
                    log.info("scene_code:{},service_id:{},sku_no:{}不存在标准sku类型且不是标准附加费的模版", sceneCode, resp.getServiceId(), resp.getSkuNo());
                    return false;
                }
                List<SyncBlacklistStrategy> blacklistStrategyList = syncBlacklistStrategyService.selectBySceneCodeAndServiceIdAndUserId(sceneCode, Long.valueOf(resp.getServiceId()), resp.getCustomSkuUserid());
                if (CollectionUtil.isNotEmpty(blacklistStrategyList)) {
                    log.info("scene_code:{},service_id:{},sku_no:{},custom_sku_userId:{}存在黑名单策略，不拉取模版", sceneCode, resp.getServiceId(), resp.getSkuNo(), resp.getCustomSkuUserid());
                    return false;
                }

                return true;
            }).collect(Collectors.toList());

            if (CollectionUtil.isEmpty(skuTemplateInfoBySceneCodeRespList)) {
                log.info("第{}批调用大数据接口获取到的自定义sku模版校验过滤后为空！", pageNum);
                pageNum++;
                continue;
            }

            //新增或者更新
            for (GetSkuTemplateInfoBySceneCodeResp resp : skuTemplateInfoBySceneCodeRespList) {
                String serviceId = resp.getServiceId().trim();
                String skuNo = resp.getSkuNo().trim();
                String attributeDisplayName = resp.getAttributeDisplayName().trim();
                String attributeValueFixed = resp.getAttributeValueFixed().trim();
                String customSkuUserId = resp.getCustomSkuUserid().trim();

                bigdataCustomSku.add(serviceId + "-" + skuNo + "-" + attributeValueFixed + "-" + customSkuUserId);

                List<FeeTemplate> templateList = feeTemplateService.select4BigdataPullCustomSku(sceneCode, serviceId, skuNo, customSkuUserId, attributeValueFixed);
                if (CollectionUtil.isEmpty(templateList)) {
                    //没有，新增

                    //1. 先获取父sku
                    List<FeeTemplate> standardTemplateList = feeTemplateService.selectByServiceIdAndSkuNoAndSkuTypeNotStandardSurcharge(sceneCode, serviceId, skuNo, FeeSkuTypeEnum.STANDARD_SKU.code);
                    if (CollectionUtil.isEmpty(standardTemplateList)) {
                        log.warn("scene_code:{},service_id:{},sku_no:{}不存在标准sku类型且不是标准附加费的模版", sceneCode, serviceId, skuNo);
                        continue;
                    }
                    FeeTemplate standardTemplate = standardTemplateList.get(0);


                    Map<String, String> bizRule = standardTemplate.getBizRule();

                    FeeTemplate newTemplate = new FeeTemplate();
                    // 由于自定义SKU模板是建立在 当前标准模板（或称为父模板）的基础上的，共有部分的数据完全相同，只有自定义部分需要替换
                    BeanUtils.copyProperties(standardTemplate, newTemplate);
                    newTemplate.setId(null);
                    newTemplate.setTemplateId(SnowFlakeGenerator.INSTANCE.generate());
                    newTemplate.setCreateBy(null);
                    newTemplate.setCreateTime(null);
                    newTemplate.setUpdateBy(null);
                    newTemplate.setModifyTime(null);
                    newTemplate.setDel(false);
                    newTemplate.setStatus(TemplateStatusEnum.ACTIVE.code);
                    Map<String, String> newBizRule = new HashMap<>(bizRule);
                    newBizRule.put(CommonBizRule.Fields.customSkuValueType, CustomSkuValueTypeEnum.FIXED.getCode());
                    newBizRule.put(CommonBizRule.Fields.matchSkuType, MatchSkuTypeEnum.CURRENT_SKU.getCode());
                    newBizRule.put(CommonBizRule.Fields.skuType, FeeSkuTypeEnum.CUSTOM_SKU.code);
                    newBizRule.remove(CommonBizRule.Fields.attributeValueMin);
                    newBizRule.remove(CommonBizRule.Fields.attributeValueMax);
                    newBizRule.put(CommonBizRule.Fields.attributeValueFixed, attributeValueFixed);
                    newBizRule.put(CommonBizRule.Fields.attributeDisplayName, attributeDisplayName);
                    newBizRule.put(CommonBizRule.Fields.customSkuUserId, customSkuUserId);
                    newBizRule.put(CommonBizRule.Fields.skuNo, skuNo);
                    newBizRule.put(CommonBizRule.Fields.serviceId, serviceId);
                    //标记系统操作
                    newBizRule.put(CommonBizRule.Fields.pullCustomSkuCurrentVersion, String.valueOf(System.currentTimeMillis()/1000));
                    newTemplate.setBizRule(newBizRule);

                    feeTemplateRepository.save(newTemplate);

                    // 2.当添加了自定义模板时，父模板(如果是“标准”sku)需要锁定（即有自定义sku的父模板不能用来算价）
                    String skyType = bizRule.get(CommonBizRule.Fields.skuType);
                    if (FeeSkuTypeEnum.STANDARD_SKU.code.equals(skyType)) {
                        feeTemplateService.lock(standardTemplateList.stream().map(FeeTemplate::getTemplateId).collect(Collectors.toList()));
                    }


                } else {
                    //存在，更新
                    for (FeeTemplate template : templateList) {
                        Map<String, String> templateBizRule = template.getBizRule();
                        if (Objects.isNull(templateBizRule.get(CommonBizRule.Fields.pullCustomSkuCurrentVersion))) {
                            //人工操作记录
                            continue;
                        }
                        templateBizRule.put(CommonBizRule.Fields.attributeDisplayName, attributeDisplayName.trim());
                        template.setBizRule(templateBizRule);
                        template.setModifyTime(new Date());
                        feeTemplateRepository.save(template);
                    }
                }

            }

            pageNum++;

        }


        //删除

        //1. 查询该场景下所有系统操作的自定义模版(当前sku以及具体值的)
        List<FeeTemplate> customSkuTemplateList = feeTemplateService.select4BigdataPullBySceneCode(sceneCode);
        if (CollectionUtil.isEmpty(customSkuTemplateList)) {
            return;
        }

        for (FeeTemplate template : customSkuTemplateList) {
            Map<String, String> templateBizRule = template.getBizRule();
            String serviceId = templateBizRule.get(CommonBizRule.Fields.serviceId);
            String skuNo = templateBizRule.get(CommonBizRule.Fields.skuNo);
            String attributeValueFixed = templateBizRule.get(CommonBizRule.Fields.attributeValueFixed);
            String customSkuUserId = templateBizRule.get(CommonBizRule.Fields.customSkuUserId);
            String customSku = serviceId + "-" + skuNo + "-" + attributeValueFixed + "-" + customSkuUserId;

            if(!bigdataCustomSku.contains(customSku)){
               //大数据表中没有该sku模版数据，执行删除
                feeTemplateService.delete(template.getTemplateId());
            }
        }


    }


    /**
     * 原始的同步方法，已重构为新的组件架构
     * 保留此方法以备回滚使用
     * @deprecated 使用 BigdataSyncCoordinator 替代
     */
    @Deprecated
    private void pullFeeRuleFromBigdataLegacy(String sceneCode, SceneCodeEnum sceneCodeEnum) {
        long minId = 1L;
        // 最大不能超过5000000，否则该方案不可行，因为耗时太长，会延长到白天拉取数据，可能会影响高峰期业务
        long maxId = 5000000L;
        RangeReq currentIdRange = new RangeReq(minId, maxId);

        final String sceneName = sceneCodeEnum.getName();
        final String redisKeySuffix = this.getClass().getName() + ":pullFeeRuleFromBigdata:" + sceneCode;
        final String currentRedisKey = redisKeySuffix + ":currentVersion";
        final String lastRedisKey = redisKeySuffix + ":lastVersion";

        String currentVersion = redisHelper.get(currentRedisKey);
        log.warn("缓存中的currentVersion：{}", currentVersion);

        // 60天，如果超过60天还没有同步，可以认为不需要再同步了
        int cacheDuration = 60 * 60 * 24 * 60;

        // 从大数据
        InsertTimestamp data = bigdataGateway.getTimestamp(sceneCode);
        if (data == null) {
            log.error("获取大数据时间戳返回失败");
            return;
        }
        Long insertTimestamp = data.getInsertTimestamp();
        if (insertTimestamp == null) {
            log.error("获取大数据时间戳为空");
            return;
        }

        String bigDataVersion = insertTimestamp.toString();

        // 立即写入缓存，防止重复跑
        redisHelper.set(currentRedisKey, bigDataVersion, cacheDuration);

        log.warn("大数据的bigDataVersion：{}", bigDataVersion);
        if (bigDataVersion.equals(currentVersion)) {
            log.warn("时间戳一致，无需同步");
            return;
        }

        if (SceneCodeEnum.BARGAIN_PRICE_EVERYDAY == sceneCodeEnum) {
            this.preHandleManualMaintenanceDataToCache(sceneCodeEnum.getCode());
            IdRange idRange = bigdataGateway.getSkuIdRangeFromBigData(bigDataVersion);
//            if (range == null || range.getData() == null) {
//                log.error("获取大数据的id范围返回data为null");
//                return;
//            }
//            IdRange idRange = range.getData();
            if (idRange.getMinId() != null && idRange.getMaxId() != null) {
                currentIdRange.setBeginId(idRange.getMinId());
                currentIdRange.setEndId(idRange.getMaxId());
                log.warn("大数据的id范围：{}-{}", idRange.getMinId(), idRange.getMaxId());
            } else {
                log.error("获取大数据的id范围不完整，{}", JSON.toJSONString(idRange));
                return;
            }
        } else if (SceneCodeEnum.USER_HISTORICAL_APPOINT_PRICE == sceneCodeEnum) {
            this.preHandleManualMaintenanceDataToCache(sceneCodeEnum.getCode());
        }

        // 左右闭区间
        RangeReq req = new RangeReq(currentIdRange.getBeginId(), Math.min(currentIdRange.getBeginId() + pageSizeInner.longValue(), currentIdRange.getEndId()));
        // 当前版本号为空，则走初始化逻辑
        if (StringUtils.isBlank(currentVersion)) {

            isManaged();

            log.info("{}费用规则初始化中...，currentVersion={}", sceneName, bigDataVersion);

            Criteria removeCriteria = where(FeeRule.Fields.sceneCode).is(sceneCode)
                    .and(FeeRule.Fields.bizRule + PunConstant.DOT + "currentVersion").ne(null);
            mongoTemplate.remove(new Query(removeCriteria), FeeRule.class);

            final AtomicInteger pendingTasks = fetchBigdataCyclically(req, sceneCodeEnum, bigDataVersion, sceneName, currentIdRange);

            blockUntilTasksTerminate(pendingTasks);

            isManaged();

            // 等全部同步完成，del:true + currentVersion， 标记为del:false
            updateDelToFalse(sceneCode, bigDataVersion);

            log.info("{}费用规则初始化完成，currentVersion={}", sceneName, bigDataVersion);
        } else {

            redisHelper.set(lastRedisKey, currentVersion, cacheDuration);

            log.info("{}费用规则更新中...，currentVersion={}", sceneName, bigDataVersion);
            // 1、先将大数据最新的数据写入到feeRule表，并标记为del:true+当前版本号

            // 定义原子布尔变量作为终止标志（线程安全）
            final AtomicInteger pendingTasks = fetchBigdataCyclically(req, sceneCodeEnum, bigDataVersion, sceneName, currentIdRange);

            blockUntilTasksTerminate(pendingTasks);

            isManaged();

            // 2、等全部同步完成，del:true + currentVersion， 标记为del:false
            updateDelToFalse(sceneCode, bigDataVersion);

            // 3、等全部同步完成，del:false + lastVersion， 标记为del:false
            // 直接物理删除，减少磁盘浪费&提高性能
            Criteria removeCriteria = where(FeeTemplate.Fields.sceneCode).is(sceneCode)
                    .and(BaseDocument.Fields.del).is(false)
                    .and(FeeRule.Fields.bizRule + PunConstant.DOT + "currentVersion").ne(bigDataVersion);

            // 这些场景不能删除人工添加的规则
            if (SceneCodeEnum.BARGAIN_PRICE_EVERYDAY == sceneCodeEnum || SceneCodeEnum.USER_HISTORICAL_APPOINT_PRICE == sceneCodeEnum) {
                removeCriteria = new Criteria().andOperator(
                        removeCriteria,
                        where(FeeRule.Fields.bizRule + PunConstant.DOT + "currentVersion").exists(true)
                );
            }

            isManaged();

            mongoTemplate.remove(new Query(removeCriteria), FeeRule.class);

            log.warn("{}费用规则更新完成，currentVersion={}", sceneName, bigDataVersion);
        }

        log.warn("[{}]，beginId={}，endId={}", sceneName, currentIdRange.getBeginId(), req.getEndId());

        if (SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.equals(sceneCodeEnum)) {
            // 清缓存
            String delRedisKey = lowestPricePrefix + "*";
            log.warn("{}同步大数据后清缓存，key={}", sceneName, delRedisKey);
            redisHelper.del(delRedisKey);
            thisProxy.preGenerateLowestPrice();
            thisProxy.preGenerateBargainPriceEverydayFeeRule(sceneCode);
        }
    }

    private void isManaged() {
        if (!pullFeeRuleFromBigdataFlag) {
            throw new BusException("turn off，同步大数据计价规则任务终止");
        }
        if (checkDeleteFeeRuleControlTime()) {
            throw new BusException("处于管控时间范围，①不触发同步大数据任务；②不触发同步大数据计价规则后更新、删除任务");
        }
    }

    private AtomicInteger fetchBigdataCyclically(RangeReq req, SceneCodeEnum sceneCodeEnum, String bigDataVersion, String sceneName, RangeReq currentIdRange) {
        // 定义原子布尔变量作为终止标志（线程安全）
        AtomicBoolean shouldTerminate = new AtomicBoolean(false);
        final AtomicInteger pendingTasks = new AtomicInteger(0);
        final Semaphore semaphore = new Semaphore(5);
        while (!shouldTerminate.get()) {
            isManaged();
            try {
                semaphore.acquire();
                if (doFetchBigdata(req, sceneCodeEnum, shouldTerminate, bigDataVersion, sceneName, pendingTasks)) {
                    break;
                }
                req.setBeginId(req.getEndId() + 1);
                req.setEndId(Math.min(req.getEndId() + pageSizeInner.longValue(), currentIdRange.getEndId()));
            } catch (Exception e) {
                log.error("{}费用规则拉取异常...，currentVersion={}", sceneName, bigDataVersion, e);
            } finally {
                semaphore.release();
            }
        }
        return pendingTasks;
    }

    private void updateDelToFalse(String sceneCode, String bigDataVersion) {
        Criteria updateCriteria = where(FeeTemplate.Fields.sceneCode).is(sceneCode)
                .and(BaseDocument.Fields.del).is(true)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + "currentVersion").is(bigDataVersion);
        Update update = new Update().set(BaseDocument.Fields.del, false);
        mongoTemplate.updateMulti(new Query(updateCriteria), update, FeeRule.class);
    }

    private void blockUntilTasksTerminate(AtomicInteger pendingTasks) {
        int maxRetries = 10;  // 最大重试次数
        long timeout = System.currentTimeMillis() + 60_000;  // 超时时间（60秒）
        while (pendingTasks.get() > 0 && maxRetries-- > 0 && System.currentTimeMillis() < timeout) {
            try {
                Thread.sleep(10000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }

    private boolean doFetchBigdata(RangeReq req, SceneCodeEnum sceneCodeEnum, AtomicBoolean shouldTerminate,
                                   String bigDataVersion, String sceneName, AtomicInteger pendingTasks) {
        try {
            // 创建新请求对象避免并发修改
            RangeReq nextReq = new RangeReq();
            nextReq.setBeginId(req.getBeginId());
            nextReq.setEndId(req.getEndId());

            fetchBigdataTaskExecutor.execute(() -> {
                if (shouldTerminate.get()) {
                    return; // 提前终止
                }

                pendingTasks.incrementAndGet();
                FeeRuleFromBigDataResp skuFeeRuleResp = getFeeRuleFromBigDataResp(nextReq, sceneCodeEnum);
                if (skuFeeRuleResp == null) {
                    pendingTasks.decrementAndGet();
                    shouldTerminate.set(true);
                    return;
                }
                if (CollectionUtils.isEmpty(skuFeeRuleResp.getData())) {
                    pendingTasks.decrementAndGet();
                    shouldTerminate.set(true); // 应当以 大数据返回的结果为空 作为结束条件
                    return;
                }

                List<FeeRule> feeRuleList = this.getFeeRules(skuFeeRuleResp, sceneCodeEnum, bigDataVersion);
                if (CollectionUtils.isEmpty(feeRuleList)) {
                    pendingTasks.decrementAndGet();
//                    shouldTerminate.set(true); // 转换后的结果为空 不应当作为结束条件，切记
                    return;
                }

                // 这里保存的都是逻辑删除的， 等全部同步完才统一更新为可用
                feeRuleRepository.save(feeRuleList);
                pendingTasks.decrementAndGet();
            });
        }  catch (RejectedExecutionException e) {
            log.error("{}费用规则初始化失败，currentVersion={}，线程池拒绝任务", sceneName, bigDataVersion, e);
            // TODO 发送飞书通知
            shouldTerminate.set(true);
            return true;
        } catch (Exception e) {
            log.error("{}费用规则初始化失败，currentVersion={}", sceneName, bigDataVersion, e);
            // TODO 发送飞书通知
            return true;
        }
        return false;
    }

    private FeeRuleFromBigDataResp getFeeRuleFromBigDataResp(RangeReq req, SceneCodeEnum sceneCodeEnum) {
        FeeRuleFromBigDataResp skuFeeRule = bigdataGateway.getSkuFeeRule(req, sceneCodeEnum);
        if (skuFeeRule == null || skuFeeRule.getData() == null) {
            log.error("获取大数据skuFeeRule返回失败");
            return null;
        }
        List<BizRule> feeRuleData = skuFeeRule.getData();
        if (CollectionUtils.isEmpty(feeRuleData)) {
            log.error("获取大数据skuFeeRule为空");
            return null;
        }
        return skuFeeRule;
    }


    @Override
    public List<PriceAndFeeNameResp> getPriceAndFeeName(PriceAndFeeNameReq req) {
        String sceneCode = req.getSceneCode();
        if (StringUtils.isBlank(sceneCode)) {
            sceneCode = SceneCodeEnum.CONTRACT_MASTER.getCode();
        }
        List<String> serviceIds = req.getServiceIds();
        String masterId = req.getMasterId();
        String recruitId = req.getRecruitId();
        Criteria criteria = where(FeeRule.Fields.sceneCode).is(sceneCode)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).in(serviceIds)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterId).is(masterId)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizTag).is(recruitId)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterInputPrice).ne(null)
//                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).is(FeeTypeTagEnum.STANDARD_SURCHARGE.code)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code);
        criteria.orOperator(
                where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).is(FeeTypeTagEnum.STANDARD_SURCHARGE.code),
                where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).is(FeeTypeTagEnum.GOOD_SURCHARGE.code)
        );
        List<FeeRule> feeRuleList = mongoTemplate.find(new Query(criteria), FeeRule.class);
        if (CollectionUtils.isEmpty(feeRuleList)) {
            return Collections.emptyList();
        }
        Map<String, List<FeeRule>> groupByServiceIdMap = feeRuleList.stream().collect(Collectors.groupingBy(feeRule -> feeRule.getBizRule().get(CommonBizRule.Fields.serviceId)));
        List<PriceAndFeeNameResp> resultList = new ArrayList<>();
        for (String serviceId : serviceIds) {
            List<FeeRule> feeRules = groupByServiceIdMap.get(serviceId);
            if (CollectionUtils.isEmpty(feeRules)) {
                continue;
            }
            PriceAndFeeNameResp resp = new PriceAndFeeNameResp();
            resp.setServiceId(serviceId);
            resp.setPriceAndFeeNameList(feeRules.stream().map(feeRule -> {
                Map<String, String> bizRule = feeRule.getBizRule();
                return new PriceAndFeeNameResp.PriceAndFeeName(
                        bizRule.get(CommonBizRule.Fields.skuNo),
                        new BigDecimal(bizRule.get(CommonBizRule.Fields.masterInputPrice)),
                        bizRule.get(CommonBizRule.Fields.attributeDisplayName),
                        bizRule.get(CommonBizRule.Fields.feeUnit)
                );
            }).collect(Collectors.toList()));
            resultList.add(resp);
        }
        return resultList;
    }

    private List<FeeRule> getFeeRules(FeeRuleFromBigDataResp skuFeeRule, SceneCodeEnum sceneCodeEnum, String currentVersion) {
        if (skuFeeRule == null || skuFeeRule.getData() == null) {
            log.error("获取大数据skuFeeRule返回失败");
            return null;
        }
        List<BizRule> feeRuleData = skuFeeRule.getData();
        if (CollectionUtils.isEmpty(feeRuleData)) {
            log.error("获取大数据skuFeeRule为空");
            return null;
        }
        List<FeeRule> feeRuleList = new ArrayList<>();
        for (BizRule bizRule : feeRuleData) {
            BigDecimal masterInputPrice = bizRule.getMasterInputPrice();
            if (masterInputPrice == null || masterInputPrice.compareTo(BigDecimal.ZERO) <= 0) {
                log.warn("masterInputPrice为空或小于等于0，serviceId={}, skuNo={}", bizRule.getServiceId(), bizRule.getSkuNo());
                continue;
            }

            FeeTemplate template;
            Long templateIdFromBigData = bizRule.getTemplateId();

            if (sceneCodeEnum == SceneCodeEnum.USER_ORDER_OFFER_GUIDE_PRICE || sceneCodeEnum == SceneCodeEnum.USER_HISTORICAL_APPOINT_PRICE) {
                if (templateIdFromBigData == null || templateIdFromBigData <= 0) {
                    log.warn("templateIdFromBigData（{}）为空或小于等于0，跳过", templateIdFromBigData);
                    continue;
                }
                template = feeTemplateRepository.findByTemplateId(templateIdFromBigData);
                if (template == null) {
                    log.warn("templateIdFromBigData={}找不到模板，跳过", templateIdFromBigData);
                    continue;
                }
                bizRule.setSkuType(template.getBizRule().get(CommonBizRule.Fields.skuType));
            } else {

                String divisionType = bizRule.getDivisionType();
                if (StringUtils.isBlank(divisionType) || !DivisionTypeEnum.STREET.code.equals(divisionType.trim())) {
                    log.warn("divisionType错误，divisionType={}", divisionType);
                    continue;
                }

                String skuType = bizRule.getSkuType();
                FeeSkuTypeEnum feeSkuTypeEnum = FeeSkuTypeEnum.fromCode(skuType);
                if (feeSkuTypeEnum == null) {
                    log.error("skuType错误，skuType={}", skuType);
                    continue;
                }

                // 如果是标准sku，则从“天天特价”场景的模板中查找即可
                if (FeeSkuTypeEnum.STANDARD_SKU == feeSkuTypeEnum) {
                    Criteria criteria = where(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuType).is(FeeSkuTypeEnum.STANDARD_SKU.code)
                            .and(FeeTemplate.Fields.sceneCode).is(sceneCodeEnum.getCode())
                            .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(bizRule.getServiceId().toString())
                            .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(bizRule.getSkuNo())
                            .and(BaseDocument.Fields.del).is(false)
                            .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);
                    template = mongoTemplate.findOne(new Query(criteria), FeeTemplate.class);
                    if (template == null) {
                        log.warn("标准SKU未找到对应的模板，serviceId={}, skuNo={}", bizRule.getServiceId(), bizRule.getSkuNo());
                        continue;
                    }

                } else if (FeeSkuTypeEnum.CUSTOM_SKU == feeSkuTypeEnum) {
                    if (templateIdFromBigData == null) {
                        log.warn("templateIdFromBigData为空，serviceId={}, skuNo={}", bizRule.getServiceId(), bizRule.getSkuNo());
                        continue;
                    }

                    String sourceTemplateId;
                    Long mappingId;
                    if (ApolloConfig.NEW_TEMPLATE_MAPPING_SWITCH) {
                        Criteria criteria = where(FeeTemplateMapping.Fields.source + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.sceneCode).is(sceneCodeEnum.getCode())
                                .and(FeeTemplateMapping.Fields.source + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.serviceId).is(bizRule.getServiceId().toString())
                                .and(FeeTemplateMapping.Fields.source + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.skuNo).is(bizRule.getSkuNo())
                                .and(FeeTemplateMapping.Fields.target + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.sceneCode).is(SceneCodeEnum.CONTRACT_MASTER.getCode())
                                .and(FeeTemplateMapping.Fields.target + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.templateId).is(templateIdFromBigData.toString())
                                .and(FeeTemplateMapping.Fields.applyTypes).is(ApplyTypeEnum.PRICE_SYNC.getCode())
                                .and(BaseDocument.Fields.del).is(false)
                                .and(BaseDocument.Fields.status).is(MappingStatusEnum.PASS.code);
                        List<FeeTemplateMapping> mappings = mongoTemplate.find(new Query(criteria), FeeTemplateMapping.class);
                        // 跟远康确认了，如果多条，则抛弃
                        if (CollectionUtils.isEmpty(mappings)) {
                            log.warn("自定义SKU-未从映射中找到对应的模板，serviceId={}, skuNo={}, templateId={}", bizRule.getServiceId(), bizRule.getSkuNo(), templateIdFromBigData);
                            continue;
                        }
                        int size = mappings.size();
                        if (size > 1) {
                            log.warn("映射中找到多条对应的模板，serviceId={}, skuNo={}", bizRule.getServiceId(), bizRule.getSkuNo());
                            continue;
                        }
                        FeeTemplateMapping mapping = mappings.get(0);
                        FeeTemplateMapping.TemplateInfo source = mapping.getSource();
                        sourceTemplateId = source.getTemplateId() == null ? null : source.getTemplateId().toString();
                        mappingId = mapping.getMappingId();
                    } else {
                        Criteria criteria = where(BizRuleMapping.Fields.sceneCode).is(sceneCodeEnum.getCode())
                                .and(BizRuleMapping.Fields.toBizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(bizRule.getServiceId().toString())
                                .and(BizRuleMapping.Fields.toBizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(bizRule.getSkuNo())
                                .and(BizRuleMapping.Fields.fromBizRule + PunConstant.DOT + FeeTemplate.Fields.sceneCode).is(SceneCodeEnum.CONTRACT_MASTER.getCode())
                                .and(BizRuleMapping.Fields.fromBizRule + PunConstant.DOT + FeeTemplate.Fields.templateId).is(templateIdFromBigData.toString())
                                .and(BizRuleMapping.Fields.applyTypes).is(ApplyTypeEnum.PRICE_SYNC.getCode())
                                .and(BaseDocument.Fields.del).is(false)
                                .and(BaseDocument.Fields.status).is(MappingStatusEnum.PASS.code);
                        List<BizRuleMapping> bizRuleMappings = mongoTemplate.find(new Query(criteria), BizRuleMapping.class);
                        // 跟远康确认了，如果多条，则抛弃
                        if (CollectionUtils.isEmpty(bizRuleMappings)) {
                            log.warn("自定义SKU-未从映射中找到对应的模板，serviceId={}, skuNo={}, templateId={}", bizRule.getServiceId(), bizRule.getSkuNo(), templateIdFromBigData);
                            continue;
                        }
                        int size = bizRuleMappings.size();
                        if (size > 1) {
                            log.warn("映射中找到多条对应的模板，serviceId={}, skuNo={}", bizRule.getServiceId(), bizRule.getSkuNo());
                            continue;
                        }
                        BizRuleMapping bizRuleMapping = bizRuleMappings.get(0);
                        Map<String, String> toBizRule = bizRuleMapping.getToBizRule();
                        sourceTemplateId = toBizRule.get(CommonBizRule.Fields.templateId);
                        mappingId = bizRuleMapping.getBizRuleMappingId();
                    }
                    if (sourceTemplateId == null) {
                        log.warn("sourceTemplateId为空，serviceId={}, skuNo={}, mappingId={}", bizRule.getServiceId(), bizRule.getSkuNo(), mappingId);
                        continue;
                    }
                    Criteria templateCriteria = where(CommonBizRule.Fields.templateId).is(Long.valueOf(sourceTemplateId))
                            .and(BaseDocument.Fields.del).is(false)
                            .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);
                    template = mongoTemplate.findOne(new Query(templateCriteria), FeeTemplate.class);
                    if (template == null) {
                        log.warn("未找到对应的模板，serviceId={}, skuNo={}", bizRule.getServiceId(), bizRule.getSkuNo());
                        continue;
                    }

                } else {
                    log.error("skuType错误，skuType={}", skuType);
                    continue;
                }
            }

            // 重复性校验
            bizRule.setTemplateId(template.getTemplateId());
            Long streetId = bizRule.getStreetId();
            if ((SceneCodeEnum.BARGAIN_PRICE_EVERYDAY == sceneCodeEnum || SceneCodeEnum.USER_HISTORICAL_APPOINT_PRICE == sceneCodeEnum)
                    && streetId != null) {
                String redisKey = getBargainPriceEveryDayRedisKey(sceneCodeEnum.getCode(), template.getTemplateId(), streetId.toString());
                String redisValue = redisHelper.get(redisKey);
                if (StringUtils.isNotBlank(redisValue)) {
                    log.warn("存在人工维护的重复数据，跳过，sceneCode={}, serviceId={}, skuNo={}, templateId={}", sceneCodeEnum.getCode(),
                            bizRule.getServiceId(), bizRule.getSkuNo(), template.getTemplateId());
                    continue;
                }
            }

            FeeRule feeRule = new FeeRule();
            feeRule.setFeeRuleId(SnowFlakeGenerator.INSTANCE.generate());
            feeRule.setSceneCode(sceneCodeEnum.getCode());
            feeRule.setSceneName(sceneCodeEnum.getName());
            feeRule.setDel(true); // 初始状态为逻辑删除，当同步完以后才统一改为 未删除
            feeRule.setStatus(RuleStatusEnum.ACTIVE.code); // 初始状态为 不可用
            Date now = new Date();
            feeRule.setCreateTime(now);
            feeRule.setModifyTime(now);
            String createBy = "系统定时同步大数据";
            feeRule.setCreateBy(createBy);
            feeRule.setUpdateBy(createBy);

            Map<String, String> feeBizRuleMap = getBizRuleMap(bizRule);
            feeBizRuleMap.put("currentVersion", currentVersion);
            feeRule.setBizRule(feeBizRuleMap);

            this.setBizRuleMap(feeRule, template);

            Double masterInputPriceDouble = Optional.ofNullable(
                            feeRule.getBizRule().get(CommonBizRule.Fields.masterInputPrice))
                    .map(Double::new).orElse(0D);
            feeRule.setMasterInputPriceDouble(masterInputPriceDouble);

            feeRuleList.add(feeRule);
        }
        return feeRuleList;
    }


    private FeeRule getFeeRule(BizRule bizRule, String sceneCode) {
        // templateID+divisionType+lv1+lv2+lv3+lv4+bizTag作为唯一条件，是否存在人工导入的数据，存在则不再更新/创建该规则数据
        Long templateId = bizRule.getTemplateId();
        Long provinceId = bizRule.getProvinceId();
        Long cityId = bizRule.getCityId();
        Long districtId = bizRule.getDistrictId();
        Long streetId = bizRule.getStreetId();
        String divisionType = bizRule.getDivisionType();
        Criteria repeatCheckCriteria = where(FeeRule.Fields.sceneCode).is(sceneCode)
                .and(FeeRule.Fields.templateId).is(templateId)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(divisionType)
                // 由于大数据没有传bizTag，所以不考虑bizTag这个维度
//                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizTag).is(null)
                // 当currentVersion为null时，表明其为人工维护的
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + "currentVersion").is(null)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code);
        if (provinceId != null && provinceId > 0) {
            repeatCheckCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level1DivisionId).is(provinceId.toString());
        }
        if (cityId != null && cityId > 0) {
            repeatCheckCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId).is(cityId.toString());
        }
        if (districtId != null && districtId > 0) {
            repeatCheckCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level3DivisionId).is(districtId.toString());
        }
        if (streetId != null && streetId > 0) {
            repeatCheckCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level4DivisionId).is(streetId.toString());
        }
        return mongoTemplate.findOne(new Query(repeatCheckCriteria), FeeRule.class);
    }


    private void setBizRuleMap(FeeRule feeRule, FeeTemplate template) {
        Map<String, String> feeBizRuleMap = feeRule.getBizRule();
        CalculateRuleData calculateRuleData = template.getCalculateRuleData();
        feeRule.setCalculateRuleData(calculateRuleData);
        feeRule.setTemplateId(template.getTemplateId());
        feeRule.setTemplateVersion(template.getTemplateVersion());
        feeRule.setGroup(template.getGroup());
        Map<String, String> templateBizRule = template.getBizRule();
        feeBizRuleMap.put(CommonBizRule.Fields.feeName, templateBizRule.get(CommonBizRule.Fields.attributeDisplayName));
        feeBizRuleMap.put(CommonBizRule.Fields.attributeDisplayName, templateBizRule.get(CommonBizRule.Fields.attributeDisplayName));
        feeBizRuleMap.put(CommonBizRule.Fields.feeUnit, templateBizRule.get(CommonBizRule.Fields.feeUnit));
        feeBizRuleMap.put(CommonBizRule.Fields.feeTypeTag, templateBizRule.get(CommonBizRule.Fields.feeTypeTag));
//        feeBizRuleMap.put(CommonBizRule.Fields.templateId, templateBizRule.get(CommonBizRule.Fields.templateId));
        feeBizRuleMap.put(CommonBizRule.Fields.skuNumberPathNo, templateBizRule.get(CommonBizRule.Fields.skuNumberPathNo));
        feeBizRuleMap.put(CommonBizRule.Fields.skuAttributePathName, templateBizRule.get(CommonBizRule.Fields.skuAttributePathName));
        feeBizRuleMap.put(CommonBizRule.Fields.skuNumberName, templateBizRule.get(CommonBizRule.Fields.skuNumberName));
        feeBizRuleMap.put(CommonBizRule.Fields.applyFlag, templateBizRule.get(CommonBizRule.Fields.applyFlag));
        feeBizRuleMap.put(CommonBizRule.Fields.serviceId, templateBizRule.get(CommonBizRule.Fields.serviceId));
        feeBizRuleMap.put(CommonBizRule.Fields.serviceName, templateBizRule.get(CommonBizRule.Fields.serviceName));
        feeBizRuleMap.put(CommonBizRule.Fields.serviceCategoryId, templateBizRule.get(CommonBizRule.Fields.serviceCategoryId));
        feeBizRuleMap.put(CommonBizRule.Fields.skuNo, templateBizRule.get(CommonBizRule.Fields.skuNo));
        feeBizRuleMap.put(CommonBizRule.Fields.skuType, templateBizRule.get(CommonBizRule.Fields.skuType));
        // 跟远康确认，起步价30元，2024年6月3日
        feeBizRuleMap.put(CommonBizRule.Fields.basePrice, "30");

        // 设置一二三级商品类目和服务类型
        this.setGoodsCategoryAndServiceType(feeBizRuleMap, templateBizRule.get(CommonBizRule.Fields.serviceId));
    }


    @Override
    @Async
    public void preGenerateLowestPrice() {
        // 临时方案，暂时不再生成最低价列表
        if (preGenerateLowestPrice) {
            return;
        }

        log.info("天天特价预生成最低价开始...");
        List serviceIdList = mongoTemplate.getCollection("feeRule")
                .distinct(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId,
                        new BasicDBObject()
                                .append(FeeRule.Fields.sceneCode, SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.getCode())
                                .append(BaseDocument.Fields.del, false)
                                .append(BaseDocument.Fields.status, RuleStatusEnum.ACTIVE.code)
                );

        if (CollectionUtils.isEmpty(serviceIdList)) {
            return;
        }


        // 将serviceIdList转为字符串类型的
        List<String> serviceIds = new ArrayList<>();
        for (Object serviceId : serviceIdList) {
            if (serviceId != null) {
                serviceIds.add(serviceId.toString());
            }
        }
        int pageNum = 1;
        int pageSize = 50;
        List<BargainPriceEverydayLowestPrice> lowestPrices = new ArrayList<>();
        Date now = new Date();
        while (true) {
            SimplePageInfo<LowestPriceResp> pageInfo = preGenerateLowest(serviceIds, pageNum, pageSize);
            List<LowestPriceResp> priceRespList = pageInfo.getList();
            if (CollectionUtils.isEmpty(priceRespList)) {
                break;
            }
            for (LowestPriceResp priceResp : priceRespList) {
                Long serviceTypeId = priceResp.getServiceTypeId();
                Long level1GoodsCategoryId = priceResp.getLevel1GoodsCategoryId();
                Long goodsCategoryId = priceResp.getGoodsCategoryId();
                if (serviceTypeId == null || level1GoodsCategoryId == null || goodsCategoryId == null) {
                    GoodsAndServiceType goodsAndServiceType = serviceApi.getGoodsAndServiceTypeByServiceId(priceResp.getServiceId());
                    if (goodsAndServiceType != null) {
                        serviceTypeId = goodsAndServiceType.getServiceTypeId();
                        level1GoodsCategoryId = goodsAndServiceType.getLevel1GoodsCategoryId();
                        Long level2GoodsCategoryId = goodsAndServiceType.getLevel2GoodsCategoryId();
                        Long level3GoodsCategoryId = goodsAndServiceType.getLevel3GoodsCategoryId();
                        if (level3GoodsCategoryId != null && level3GoodsCategoryId > 0) {
                            goodsCategoryId = level3GoodsCategoryId;
                        } else if (level2GoodsCategoryId != null && level2GoodsCategoryId > 0) {
                            goodsCategoryId = level2GoodsCategoryId;
                        } else {
                            goodsCategoryId = level1GoodsCategoryId;
                        }
                        priceResp.setServiceTypeId(serviceTypeId);
                        priceResp.setLevel1GoodsCategoryId(level1GoodsCategoryId);
                        priceResp.setGoodsCategoryId(goodsCategoryId);
                    }
                }
                BargainPriceEverydayLowestPrice lowestPrice = new BargainPriceEverydayLowestPrice();
                BeanUtils.copyProperties(priceResp, lowestPrice);
                lowestPrice.setSceneCode(SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.getCode());
                lowestPrice.setDel(false);
                lowestPrice.setStatus(RuleStatusEnum.ACTIVE.code);
                lowestPrice.setCreateTime(now);
                lowestPrice.setModifyTime(now);
                lowestPrices.add(lowestPrice);
            }
            pageNum++;
        }
        mongoTemplate.dropCollection(BargainPriceEverydayLowestPrice.class);
        mongoTemplate.insertAll(lowestPrices);
        log.info("天天特价预生成最低价完成！");
    }


    @Override
    public SimplePageInfo<LowestPriceResp> getLowestPriceByLevel1GoodsCategoryIdOrServiceIds(LowestPriceReq req) {
        Integer pageNum = req.getPageNum();
        Integer pageSize = req.getPageSize();
        Long level1GoodsCategoryId = req.getLevel1GoodsCategoryId();
        Set<Long> serviceIds = req.getServiceIds();
        Criteria criteria = where(BargainPriceEverydayLowestPrice.Fields.sceneCode).is(SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.getCode())
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code);
        if (level1GoodsCategoryId != null) {
            criteria.and(BargainPriceEverydayLowestPrice.Fields.level1GoodsCategoryId).is(level1GoodsCategoryId);
        }
        if (CollectionUtils.isNotEmpty(serviceIds)) {
            criteria.and(BargainPriceEverydayLowestPrice.Fields.serviceId).in(serviceIds);
        }

        long count = mongoTemplate.count(new Query(criteria), BargainPriceEverydayLowestPrice.class);
        if (count <= 0) {
            return new SimplePageInfo<>();
        }

        Pageable pageable = new PageRequest(pageNum - 1, pageSize, Sort.Direction.ASC, BargainPriceEverydayLowestPrice.Fields.subsidyPrice);
        Query query = new Query(criteria);
        query.with(pageable);

        List<BargainPriceEverydayLowestPrice> lowestPrices = mongoTemplate.find(query, BargainPriceEverydayLowestPrice.class);

        if (CollectionUtils.isEmpty(lowestPrices)) {
            return new SimplePageInfo<>();
        }

        List<LowestPriceResp> respList = lowestPrices.stream().map(lowestPrice -> {
            LowestPriceResp resp = new LowestPriceResp();
            BeanUtils.copyProperties(lowestPrice, resp);
            return resp;
        }).collect(Collectors.toList());

        PageImpl<BargainPriceEverydayLowestPrice> page = new PageImpl<>(lowestPrices, pageable, count);
        SimplePageInfo<LowestPriceResp> resultPage = new SimplePageInfo<>(respList);
        resultPage.setPages(page.getTotalPages());
        resultPage.setPageNum(page.getNumber() + 1);
        resultPage.setPageSize(page.getSize());
        resultPage.setTotal(count);
        return resultPage;
    }


    @Override
    public void setGoodsCategoryAndServiceType(Map<String, String> feeBizRuleMap, String serviceId) {

        String redisKey = this.getClass().getName() + ":setGoodsCategoryAndServiceType" + ":serviceId=" + serviceId;
        String redisResult = redisHelper.get(redisKey);
        GoodsAndServiceType goodsAndServiceType;
        if (StringUtils.isNotBlank(redisResult)) {
            goodsAndServiceType = JSONObject.parseObject(redisResult, GoodsAndServiceType.class);
        } else {
            goodsAndServiceType = serviceApi.getGoodsAndServiceTypeByServiceId(Long.parseLong(serviceId));
            if (goodsAndServiceType != null) {
                redisHelper.set(redisKey, JSON.toJSONString(goodsAndServiceType), 60 * 60 * 8);
            }
        }

        if (goodsAndServiceType != null) {
            Long level1GoodsCategoryId = goodsAndServiceType.getLevel1GoodsCategoryId();
            Long level2GoodsCategoryId = goodsAndServiceType.getLevel2GoodsCategoryId();
            Long level3GoodsCategoryId = goodsAndServiceType.getLevel3GoodsCategoryId();
            String level1GoodsCategoryName = goodsAndServiceType.getLevel1GoodsCategoryName();
            String level2GoodsCategoryName = goodsAndServiceType.getLevel2GoodsCategoryName();
            String level3GoodsCategoryName = goodsAndServiceType.getLevel3GoodsCategoryName();
            Long serviceTypeId = goodsAndServiceType.getServiceTypeId();
            String serviceTypeName = goodsAndServiceType.getServiceTypeName();

            feeBizRuleMap.put(CommonBizRule.Fields.level1GoodsCategoryId, level1GoodsCategoryId == null ? null : level1GoodsCategoryId.toString());
            feeBizRuleMap.put(CommonBizRule.Fields.level2GoodsCategoryId, level2GoodsCategoryId == null ? null : level2GoodsCategoryId.toString());
            feeBizRuleMap.put(CommonBizRule.Fields.level3GoodsCategoryId, level3GoodsCategoryId == null ? null : level3GoodsCategoryId.toString());
            feeBizRuleMap.put(CommonBizRule.Fields.level1GoodsCategoryName, level1GoodsCategoryName);
            feeBizRuleMap.put(CommonBizRule.Fields.level2GoodsCategoryName, level2GoodsCategoryName);
            feeBizRuleMap.put(CommonBizRule.Fields.level3GoodsCategoryName, level3GoodsCategoryName);
            feeBizRuleMap.put(CommonBizRule.Fields.serviceTypeId, serviceTypeId == null ? null : serviceTypeId.toString());
            feeBizRuleMap.put(CommonBizRule.Fields.serviceTypeName, serviceTypeName);

            if (level3GoodsCategoryId != null) {
                feeBizRuleMap.put(CommonBizRule.Fields.goodsCategoryId, level3GoodsCategoryId.toString());
                feeBizRuleMap.put(CommonBizRule.Fields.goodsCategoryName, level3GoodsCategoryName);
            } else if (level2GoodsCategoryId != null) {
                feeBizRuleMap.put(CommonBizRule.Fields.goodsCategoryId, level2GoodsCategoryId.toString());
                feeBizRuleMap.put(CommonBizRule.Fields.goodsCategoryName, level2GoodsCategoryName);
            }
        } else {
            log.warn("未找到商品类目和服务类型，serviceId={}", serviceId);
        }
    }


    @Override
    public void handleDynamicPriceRule(FeeRule feeRule) {
        Long templateId = feeRule.getTemplateId();
        Map<String, String> bizRule = feeRule.getBizRule();
        String divisionType = bizRule.get(CommonBizRule.Fields.divisionType);
        String level1DivisionId = bizRule.get(CommonBizRule.Fields.level1DivisionId);
        String level2DivisionId = bizRule.get(CommonBizRule.Fields.level2DivisionId);
        String level3DivisionId = bizRule.get(CommonBizRule.Fields.level3DivisionId);
        String level4DivisionId = bizRule.get(CommonBizRule.Fields.level4DivisionId);
        CalculateRuleData calculateRuleData = feeRule.getCalculateRuleData();

        if (templateId == null || templateId <= 0 || StringUtils.isBlank(divisionType)) {
            return;
        }
        String bizId = bizRule.get(CommonBizRule.Fields.bizId);
        String bizTag = bizRule.get(CommonBizRule.Fields.bizTag);
        Criteria criteria = where(DynamicFeeRule.Fields.templateIds).is(templateId)
                .and(DynamicFeeRule.Fields.divisionType).is(divisionType);

        if (StringUtils.isNotBlank(bizId)) {
            criteria.and(DynamicFeeRule.Fields.userIds).is(Long.valueOf(bizId));
        } else {
            criteria.and(DynamicFeeRule.Fields.userIds).is(null);
        }

        if (StringUtils.isNotBlank(bizTag)) {
            criteria.and(DynamicFeeRule.Fields.bizTag).is(bizTag);
        } else {
            criteria.and(DynamicFeeRule.Fields.bizTag).is(null);
        }

        String divisionId;
        if (DivisionTypeEnum.PROVINCE.code.equals(divisionType)) {
            criteria.and(DynamicFeeRule.Fields.divisionIds).is(level1DivisionId);
            divisionId = level1DivisionId;
        } else if (DivisionTypeEnum.CITY.code.equals(divisionType)) {
            criteria.and(DynamicFeeRule.Fields.divisionIds).is(level2DivisionId);
            divisionId = level2DivisionId;
        } else if (DivisionTypeEnum.DISTRICT.code.equals(divisionType)) {
            criteria.and(DynamicFeeRule.Fields.divisionIds).is(level3DivisionId);
            divisionId = level3DivisionId;
        } else if (DivisionTypeEnum.STREET.code.equals(divisionType)) {
            criteria.and(DynamicFeeRule.Fields.divisionIds).is(level4DivisionId);
            divisionId = level4DivisionId;
        } else {
            divisionId = null;
        }

        List<DynamicFeeRule> dynamicFeeRuleList = mongoTemplate.find(new Query(criteria), DynamicFeeRule.class);
        if (CollectionUtils.isEmpty(dynamicFeeRuleList)) {
            return;
        }

        // 解析动态规则
        dynamicFeeRuleList.forEach(dynamicFeeRule -> {
            DynamicCalculateRuleData dynamicCalculateRuleData = dynamicFeeRule.getDynamicCalculateRuleData();

            DynamicFeeRuleCalculateRuleData dynamicCalculateRuleDataAfterFormula = formulaService.handleFormulas(calculateRuleData, dynamicCalculateRuleData, divisionType, divisionId, bizId);
            if (dynamicCalculateRuleDataAfterFormula == null) {
                dynamicCalculateRuleDataAfterFormula = new DynamicFeeRuleCalculateRuleData();
            }

            dynamicCalculateRuleDataAfterFormula.setDynamicFeeRuleId(dynamicFeeRule.getDynamicFeeRuleId());

            DynamicFeeRuleCalculateRuleData.MatchingRule matchingRule = new DynamicFeeRuleCalculateRuleData.MatchingRule();
            matchingRule.setStartTime(dynamicFeeRule.getStartTime());
            matchingRule.setEndTime(dynamicFeeRule.getEndTime());
            dynamicCalculateRuleDataAfterFormula.setMatchingRule(matchingRule);

            DynamicFeeRuleCalculateRuleData.CalculateData unitPrice = new DynamicFeeRuleCalculateRuleData.CalculateData();
            unitPrice.setExpress(dynamicCalculateRuleData.getExpress());
            unitPrice.setExpressionParamList(dynamicCalculateRuleData.getExpressionParamList());
            dynamicCalculateRuleDataAfterFormula.setUnitPrice(unitPrice);

            List<DynamicFeeRuleCalculateRuleData> dataList = feeRule.getDynamicFeeRuleCalculateRuleDataList();
            if (CollectionUtils.isEmpty(dataList)) {
                dataList = new ArrayList<>();
            }
            dataList.add(dynamicCalculateRuleDataAfterFormula);
            feeRule.setDynamicFeeRuleCalculateRuleDataList(dataList);
        });

    }


    @Override
    public void handleDynamicPriceRule(FeeRuleDraft feeRuleDraft) {
        Long templateId = feeRuleDraft.getTemplateId();
        Map<String, String> bizRule = feeRuleDraft.getBizRule();
        String divisionType = bizRule.get(CommonBizRule.Fields.divisionType);
        String level1DivisionId = bizRule.get(CommonBizRule.Fields.level1DivisionId);
        String level2DivisionId = bizRule.get(CommonBizRule.Fields.level2DivisionId);
        String level3DivisionId = bizRule.get(CommonBizRule.Fields.level3DivisionId);
        String level4DivisionId = bizRule.get(CommonBizRule.Fields.level4DivisionId);
        CalculateRuleData calculateRuleData = feeRuleDraft.getCalculateRuleData();

        if (templateId == null || templateId <= 0 || StringUtils.isBlank(divisionType)) {
            return;
        }
        String bizId = bizRule.get(CommonBizRule.Fields.bizId);
        String bizTag = bizRule.get(CommonBizRule.Fields.bizTag);
        Criteria criteria = where(DynamicFeeRule.Fields.templateIds).is(templateId)
                .and(DynamicFeeRule.Fields.divisionType).is(divisionType);

        if (StringUtils.isNotBlank(bizId)) {
            criteria.and(DynamicFeeRule.Fields.userIds).is(Long.valueOf(bizId));
        } else {
            criteria.and(DynamicFeeRule.Fields.userIds).is(null);
        }

        if (StringUtils.isNotBlank(bizTag)) {
            criteria.and(DynamicFeeRule.Fields.bizTag).is(bizTag);
        } else {
            criteria.and(DynamicFeeRule.Fields.bizTag).is(null);
        }

        String divisionId;
        if (DivisionTypeEnum.PROVINCE.code.equals(divisionType)) {
            criteria.and(DynamicFeeRule.Fields.divisionIds).is(level1DivisionId);
            divisionId = level1DivisionId;
        } else if (DivisionTypeEnum.CITY.code.equals(divisionType)) {
            criteria.and(DynamicFeeRule.Fields.divisionIds).is(level2DivisionId);
            divisionId = level2DivisionId;
        } else if (DivisionTypeEnum.DISTRICT.code.equals(divisionType)) {
            criteria.and(DynamicFeeRule.Fields.divisionIds).is(level3DivisionId);
            divisionId = level3DivisionId;
        } else if (DivisionTypeEnum.STREET.code.equals(divisionType)) {
            criteria.and(DynamicFeeRule.Fields.divisionIds).is(level4DivisionId);
            divisionId = level4DivisionId;
        } else {
            divisionId = null;
        }

        List<DynamicFeeRule> dynamicFeeRuleList = mongoTemplate.find(new Query(criteria), DynamicFeeRule.class);
        if (CollectionUtils.isEmpty(dynamicFeeRuleList)) {
            return;
        }

        // 解析动态规则
        dynamicFeeRuleList.forEach(dynamicFeeRule -> {
            DynamicCalculateRuleData dynamicCalculateRuleData = dynamicFeeRule.getDynamicCalculateRuleData();

            DynamicFeeRuleCalculateRuleData dynamicCalculateRuleDataAfterFormula = formulaService.handleFormulas(calculateRuleData, dynamicCalculateRuleData, divisionType, divisionId, bizId);
            if (dynamicCalculateRuleDataAfterFormula == null) {
                dynamicCalculateRuleDataAfterFormula = new DynamicFeeRuleCalculateRuleData();
            }

            dynamicCalculateRuleDataAfterFormula.setDynamicFeeRuleId(dynamicFeeRule.getDynamicFeeRuleId());

            DynamicFeeRuleCalculateRuleData.MatchingRule matchingRule = new DynamicFeeRuleCalculateRuleData.MatchingRule();
            matchingRule.setStartTime(dynamicFeeRule.getStartTime());
            matchingRule.setEndTime(dynamicFeeRule.getEndTime());
            dynamicCalculateRuleDataAfterFormula.setMatchingRule(matchingRule);

            DynamicFeeRuleCalculateRuleData.CalculateData unitPrice = new DynamicFeeRuleCalculateRuleData.CalculateData();
            unitPrice.setExpress(dynamicCalculateRuleData.getExpress());
            unitPrice.setExpressionParamList(dynamicCalculateRuleData.getExpressionParamList());
            dynamicCalculateRuleDataAfterFormula.setUnitPrice(unitPrice);

            List<DynamicFeeRuleCalculateRuleData> dataList = feeRuleDraft.getDynamicFeeRuleCalculateRuleDataList();
            if (CollectionUtils.isEmpty(dataList)) {
                dataList = new ArrayList<>();
            }
            dataList.add(dynamicCalculateRuleDataAfterFormula);
            feeRuleDraft.setDynamicFeeRuleCalculateRuleDataList(dataList);
        });

    }


    @Override
    public boolean applyOrderDetector(ApplyOrderCalculateReq applyOrderCalculateReq) {
        List<String> sceneCodeList = applyOrderCalculateReq.getSceneCode();
        List<SceneInfo> sceneInfos = sceneInfoRepository.findAllBySceneCodeInAndDelIsFalse(sceneCodeList);
        if (CollectionUtils.isEmpty(sceneInfos)) {
            log.error("无有效场景：{}", JSON.toJSONString(sceneCodeList, SerializerFeature.DisableCircularReferenceDetect));
            return false;
        }
        List<String> realSceneCode = sceneInfos.stream().map(SceneInfo::getSceneCode).collect(Collectors.toList());
        Collection<?> subtract = CollectionUtils.subtract(sceneCodeList, realSceneCode);
        if (CollectionUtils.isNotEmpty(subtract)) {
            log.error("有场景不存在：{}", JSON.toJSONString(sceneCodeList, SerializerFeature.DisableCircularReferenceDetect));
            return false;
        }
        ApplyOrderCalculateDTO applyOrderCalculateDTO = new ApplyOrderCalculateDTO(applyOrderCalculateReq);
        applyOrderCalculateDTO.setSceneInfoList(sceneInfos);
        List<Boolean> resultList = new ArrayList<>();
        for (SceneInfo sceneInfo : sceneInfos) {
            applyOrderCalculateDTO.setCurrentSceneInfo(sceneInfo);
            try {
                commonSceneFeeRuleStrategy.apply(applyOrderCalculateDTO);
                resultList.add(true);
            } catch (BusException e) {
                String message = e.getMessage();
                if (StringUtils.isNotBlank(message)) {
                    boolean templateIsBlank = message.contains("模板为空");
                    boolean feeRuleIsBlank = message.contains("计费规则为空");
                    boolean feeRuleIsIncomplete = message.contains("计费规则配置不完整");
                    boolean serviceFee = message.contains("无服务费");
                    boolean calculationErr = message.contains("计算错误");
                    boolean calculationException = message.contains("计算异常");
                    resultList.add(!(templateIsBlank || feeRuleIsBlank || feeRuleIsIncomplete || serviceFee || calculationErr || calculationException));
                } else {
                    // 只有上述异常才算探测失败
                    resultList.add(true);
                }
            }
        }
        // 有任一场景为true，即返回true
        return resultList.contains(true);
    }


    @Override
    public FeeRuleBaseInfoForMasterResp queryBaseInfoForMaster(FeeRuleBaseInfoForMasterReq feeRuleBaseInfoForMasterReq) {
        // 师傅端定制化需求，每个区县只会传一个街道（当有多个区县时，还是可能多个街道），故不需要group by了
        Criteria criteria = where(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code)
                .and(FeeRule.Fields.sceneCode).is(feeRuleBaseInfoForMasterReq.getSceneCode())
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceCategoryId).in(feeRuleBaseInfoForMasterReq.getServiceCategoryIdList())
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizTag).is(feeRuleBaseInfoForMasterReq.getBizTag())
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterId).is(feeRuleBaseInfoForMasterReq.getMasterId())
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(feeRuleBaseInfoForMasterReq.getDivisionType());
        completeDivisionId(feeRuleBaseInfoForMasterReq, criteria);

        Query query = new Query(criteria);
        query.fields().include(FeeRule.Fields.bizRule)
                .include(FeeRule.Fields.templateId)
                .include(FeeRule.Fields.feeRuleId);

        List<FeeRule> feeRuleList = mongoTemplate.find(query, FeeRule.class);
        if (CollectionUtils.isEmpty(feeRuleList)) {
            return null;
        }
        FeeRuleBaseInfoForMasterResp resp = new FeeRuleBaseInfoForMasterResp();
        List<FeeRuleBaseInfoForMasterResp.FeeRuleBaseInfoForMasterRespDetail> detailList = new ArrayList<>();
        resp.setDetailList(detailList);
        feeRuleList.forEach(feeRule -> {
            FeeRuleBaseInfoForMasterResp.FeeRuleBaseInfoForMasterRespDetail detail = new FeeRuleBaseInfoForMasterResp.FeeRuleBaseInfoForMasterRespDetail();
            Map<String, String> bizRule = feeRule.getBizRule();
            detail.setServiceCategoryId(bizRule.get(CommonBizRule.Fields.serviceCategoryId));
            detail.setMasterInputPrice(bizRule.get(CommonBizRule.Fields.masterInputPrice));
            detail.setFeeUnit(bizRule.get(CommonBizRule.Fields.feeUnit));
            detail.setFeeTypeTag(bizRule.get(CommonBizRule.Fields.feeTypeTag));
            detail.setFeeTypeTagName(bizRule.get(CommonBizRule.Fields.feeTypeTagName));
            detail.setAttributeDisplayName(bizRule.get(CommonBizRule.Fields.attributeDisplayName));
            detail.setLevel2DivisionId(bizRule.get(CommonBizRule.Fields.level2DivisionId));
            detail.setLevel3DivisionId(bizRule.get(CommonBizRule.Fields.level3DivisionId));
            detail.setLevel4DivisionId(bizRule.get(CommonBizRule.Fields.level4DivisionId));
            detail.setTemplateId(feeRule.getTemplateId());
            detail.setFeeRuleId(feeRule.getFeeRuleId());
            detailList.add(detail);
        });

        return resp;

        // FIXME 这段代码找庆龙确认 并尽快下掉，按约定 8月14日 版本可以下掉
//        Map<String, Set<String>> streetIdMap = feeRuleBaseInfoForMasterReq.getStreetIdMap();
//        if (MapUtils.isNotEmpty(streetIdMap)) {
//            Map<String, Set<Long>> feeRuleIdMap = new HashMap<>();
//            for (String streetId : streetIdMap.keySet()) {
//                Set<String> streetIds = streetIdMap.get(streetId);
//                if (CollectionUtils.isNotEmpty(streetIds)) {
//                    criteria = where(BaseDocument.Fields.del).is(false)
//                            .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code)
//                            .and(FeeRule.Fields.sceneCode).is(feeRuleBaseInfoForMasterReq.getSceneCode())
//                            .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level4DivisionId).in(streetIds)
//                            .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceCategoryId).in(feeRuleBaseInfoForMasterReq.getServiceCategoryIdList())
//                            .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizTag).is(feeRuleBaseInfoForMasterReq.getBizTag())
//                            .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterId).is(feeRuleBaseInfoForMasterReq.getMasterId());
//                    Query query = new Query(criteria);
//                    query.fields().include(FeeRule.Fields.feeRuleId);
//                    feeRuleList = mongoTemplate.find(query, FeeRule.class);
//                    if (CollectionUtils.isNotEmpty(feeRuleList)) {
//                        Set<Long> feeRuleIds = feeRuleList.stream().map(FeeRule::getFeeRuleId).collect(Collectors.toSet());
//                        feeRuleIdMap.put(streetId, feeRuleIds);
//                    }
//                }
//            }
//            resp.setFeeRuleIdMap(feeRuleIdMap);
//        }

    }

    private void completeDivisionId(FeeRuleBaseInfoForMasterReq feeRuleBaseInfoForMasterReq, Criteria criteria) {
        String divisionType = feeRuleBaseInfoForMasterReq.getDivisionType();
        Set<String> divisionIdList = feeRuleBaseInfoForMasterReq.getDivisionIdList();
        if (StringUtils.isBlank(divisionType) || divisionType.equals(DivisionTypeEnum.COUNTRY.code) || CollectionUtils.isEmpty(divisionIdList)) {
            return;
        }
        if (DivisionTypeEnum.PROVINCE.code.equals(divisionType)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level1DivisionId).in(divisionIdList);
        } else if (DivisionTypeEnum.CITY.code.equals(divisionType)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId).in(divisionIdList);
        } else if (DivisionTypeEnum.DISTRICT.code.equals(divisionType)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level3DivisionId).in(divisionIdList);
        } else if (DivisionTypeEnum.STREET.code.equals(divisionType)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level4DivisionId).in(divisionIdList);
        }
    }


    @Override
    public void createRecruitActivity(CreateRecruitActivityReq req) {
        final String baseSceneCode = SceneCodeEnum.MASTER_RECRUIT_BASE_PRICE.getCode();
        final String activeSceneCode = SceneCodeEnum.MASTER_RECRUIT_ACTIVE_PRICE.getCode();
        final String activeSceneName = SceneCodeEnum.MASTER_RECRUIT_ACTIVE_PRICE.getName();
        List<String> serviceCategoryIdList = req.getServiceCategoryIdList();
        List<String> divisionIds = req.getDivisionIds();
        String divisionType = req.getDivisionType();
        DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(divisionType);
        if (divisionTypeEnum == null) {
            throw new BusException("区域类型不正确");
        }

        FeeRuleQueryPageReq pageReq = new FeeRuleQueryPageReq();
        pageReq.setSceneCode(baseSceneCode);
        pageReq.setServiceCategoryIds(new HashSet<>(serviceCategoryIdList));
        pageReq.setDivisionType(divisionType);
        if (CollectionUtils.isNotEmpty(divisionIds)) {
            pageReq.setDivisionIds(new HashSet<>(divisionIds));
        }

        final int pageSize = 30000;
        int pageNum = 1;

        while (true) {
            pageReq.setPageNum(pageNum);
            pageReq.setPageSize(pageSize);
            Page<FeeRule> feeRulePage = queryPage(pageReq);
            List<FeeRule> feeRuleList = feeRulePage.getContent();
            if (CollectionUtils.isEmpty(feeRuleList)) {
                break;
            }
            FeeRule rule1rst = feeRuleList.get(0);
            FeeRule ruleLast = feeRuleList.get(feeRuleList.size() - 1);
            log.info("feeRulePage条数：{}, 起始Id：{}，终止Id：{}", feeRuleList.size(), rule1rst.getId(), ruleLast.getId());

            //查询自定义sku的templateId
            List<String> templateIdList = new ArrayList<>();
            for (FeeRule feeRule : feeRuleList) {
                Map<String, String> bR = feeRule.getBizRule();

                if (FeeSkuTypeEnum.CUSTOM_SKU.code.equals(bR.get(CommonBizRule.Fields.skuType))) {
                    templateIdList.add(String.valueOf(feeRule.getTemplateId()));
                }
            }

            Map<Long, Long> customTemplateIdMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(templateIdList)) {
                if (ApolloConfig.NEW_TEMPLATE_MAPPING_SWITCH) {
                    MappingQueryPageRequest request = new MappingQueryPageRequest();
                    request.setTargetTemplateIdList(templateIdList.stream().map(Long::valueOf).collect(Collectors.toList()));
                    request.setTargetSceneCode(baseSceneCode);
                    request.setPageNum(1);
                    request.setPageSize(10000);
                    List<FeeTemplateMapping> mappings = feeTemplateMappingService.queryPage(request).getContent();
                    if (CollectionUtils.isEmpty(mappings)) {
                        throw new BusException("价格中台自定义sku映射不存在");
                    }
                    for (FeeTemplateMapping mapping : mappings) {
                        FeeTemplateMapping.TemplateInfo source = mapping.getSource();
                        FeeTemplateMapping.TemplateInfo target = mapping.getTarget();
                        customTemplateIdMap.put(target.getTemplateId(), source.getTemplateId());
                    }
                } else {
                    BizRuleMappingBatchQueryReq bizRuleMappingBatchQueryReq = new BizRuleMappingBatchQueryReq();

                    Map<String, List<String>> fromBizRule = new HashMap<>();
                    fromBizRule.put("templateId", templateIdList);
                    fromBizRule.put("sceneCode", Collections.singletonList(baseSceneCode));
                    bizRuleMappingBatchQueryReq.setPageNum(1);
                    bizRuleMappingBatchQueryReq.setPageSize(10000);

                    bizRuleMappingBatchQueryReq.setFromBizRule(fromBizRule);

                    Page<BizRuleMapping> bizRuleMapping = bizRuleMappingService.batchQueryByCondition(bizRuleMappingBatchQueryReq);
                    List<BizRuleMapping> mappings = bizRuleMapping.getContent();
                    if (CollectionUtils.isEmpty(mappings)) {
                        throw new BusException("价格中台自定义sku映射不存在");
                    }
                    for (BizRuleMapping ruleMapping : mappings) {
                        Map<String, String> toBizRule = ruleMapping.getToBizRule();
                        customTemplateIdMap.put(ruleMapping.getTemplateId(), Long.valueOf(toBizRule.get(CommonBizRule.Fields.templateId)));
                    }
                }
            }

            //查询师傅招募活动价的服务模板
            FeeTemplateBatchQueryReq feeTemplateBatchQueryReq = new FeeTemplateBatchQueryReq();
            feeTemplateBatchQueryReq.setSceneCode(activeSceneCode);
            feeTemplateBatchQueryReq.setStatusList(Collections.singletonList(0));

            feeTemplateBatchQueryReq.setPageNum(1);
            feeTemplateBatchQueryReq.setPageSize(10000);
            Map<String, List<String>> feeTemplateBizRule = new HashMap<>();
            feeTemplateBizRule.put("serviceCategoryId", serviceCategoryIdList);
            feeTemplateBatchQueryReq.setBizRule(feeTemplateBizRule);
            Page<FeeTemplate> feeTemplate = feeTemplateService.batchQueryByCondition(feeTemplateBatchQueryReq);
            List<FeeTemplate> templates = feeTemplate.getContent();
            if (CollectionUtils.isEmpty(templates)) {
                throw new BusException("招募活动价服务模板不存在");
            }

            Map<String, Long> feeTemplateIdMap = new HashMap<>();
            Map<String, Integer> feeTemplateVersionMap = new HashMap<>();
            Map<Long, Integer> customSkuMap = new HashMap<>();
            for (FeeTemplate template : templates) {
                Map<String, String> templateBizRule = template.getBizRule();
                String skuNo = templateBizRule.get("skuNo");
                String serviceCategoryId = templateBizRule.get("serviceCategoryId");

                if (FeeSkuTypeEnum.STANDARD_SKU.code.equals(templateBizRule.get("skuType"))) {
                    //标准sku取值
                    feeTemplateIdMap.put(serviceCategoryId + skuNo, template.getTemplateId());
                    feeTemplateVersionMap.put(serviceCategoryId + skuNo, template.getTemplateVersion());
                } else {
                    //自定义sku的模版版本号
                    customSkuMap.put(template.getTemplateId(), template.getTemplateVersion());
                }
            }

            Map<Long, Address> addressMap = null;
            if (CollectionUtils.isNotEmpty(divisionIds)) {
                List<Address> addressList = addressApi.getDivisionInfoListByDivisionIds(StringUtils.join(divisionIds, ","));
                addressMap = addressList.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));
            }

            //构建活动价格
            List<FeeRuleBatchAddReq.FeeRuleData> feeRuleDataList = new ArrayList<>();
            for (FeeRule feeRule : feeRuleList) {
                Map<String, String> bR = feeRule.getBizRule();
                String skuNo = bR.get("skuNo");

                FeeRuleBatchAddReq.FeeRuleData feeRuleData = new FeeRuleBatchAddReq.FeeRuleData();
//                feeRuleData.setTemplateId(feeRule.getTemplateId());
                feeRuleData.setTemplateVersion(feeRule.getTemplateVersion());
                feeRuleData.setSceneCode(activeSceneCode);
                feeRuleData.setFeeName(activeSceneName);

                Map<String, String> bizRuleMap = new HashMap<>(bR);
                bizRuleMap.put("bizTag", req.getRecruitId());
//                bizRuleMap.put("sceneCode", activeSceneCode);
//                bizRuleMap.put("sceneName", activeSceneName);

                if (FeeSkuTypeEnum.CUSTOM_SKU.code.equals(bR.get("skuType"))) {
                    Long templateId = customTemplateIdMap.get(feeRule.getTemplateId());
                    if (templateId == null) {
                        throw new BusException("价格中台自定义sku映射不匹配");
                    }

                    feeRuleData.setTemplateId(templateId);
                    feeRuleData.setTemplateVersion(customSkuMap.get(templateId));
//                    bizRuleMap.put("templateId", String.valueOf(templateId));
                } else {
                    String serviceCategoryId = bR.get("serviceCategoryId");
                    String key = serviceCategoryId + skuNo;
                    if (feeTemplateIdMap.get(key) == null || feeTemplateVersionMap.get(key) == null) {
                        throw new BusException("价格中台标准sku获取失败,key:" + key);
                    }
                    feeRuleData.setTemplateId(feeTemplateIdMap.get(key));
                    feeRuleData.setTemplateVersion(feeTemplateVersionMap.get(key));
//                    bizRuleMap.put("templateId", String.valueOf(feeTemplateIdMap.get(key)));
                }

                feeRuleData.setBizRule(bizRuleMap);
                feeRuleData.setCalculateRuleData(feeRule.getCalculateRuleData());

                setGoodsCategoryAndServiceType(bizRuleMap, bR.get(CommonBizRule.Fields.serviceId));

                if (addressMap != null) {
                    setDivision(divisionTypeEnum, bizRuleMap, addressMap);
                }

                feeRuleDataList.add(feeRuleData);
            }

            if (CollectionUtils.isEmpty(feeRuleDataList)) {
                throw new BusException("feeRuleDataList不能为空");
            }
            log.info("feeRuleDataList条数：{}", feeRuleDataList.size());
//            handleDirtyData(feeRuleDataList);
            saveFeeRuleListNew(feeRuleDataList);

            pageNum++;
        }
    }


    @Override
    public void deleteForMaster(RemoveForMasterReq req) {
        Set<String> streetIds = req.getStreetIds();
        Criteria criteria = where(FeeRule.Fields.sceneCode).is(req.getSceneCode())
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizTag).is(req.getRecruitId())
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterId).is(req.getMasterId());

        if (CollectionUtils.isNotEmpty(streetIds)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level4DivisionId).in(streetIds);
        }

        Query query = new Query(criteria);
        Update update = new Update().set(BaseDocument.Fields.del, true)
                .set(BaseDocument.Fields.modifyTime, new Date())
                .set(BaseDocument.Fields.updateBy, CommonUtils.getCurrentLoginName());
        mongoTemplate.updateMulti(query, update, FeeRule.class);
    }

    @Override
    public void createRecruitMasterAsync(CreateRecruitMasterReq req) {
        try {
            String msgBody = JSON.toJSONString(req);
            log.info("createRecruitMasterAsync send message, msgBody = {}", msgBody);
            rocketMqSendService.sendSyncTransactionMessage(new NormalMessage(null, UUID.randomUUID().toString(), feeCenterServiceGeneralTopic, MQTagConstant.CREATE_RECRUIT_MASTER_ASYNC, msgBody));
            recruitActivityMasterCreateStatusRepository.saveStatus(req.getMasterId(), req.getRecruitId(),
                    RecruitActivityMasterBuildTypeEnum.SAVE_RECRUIT_MASTER.code, RecruitActivityMasterCreateStatusEnum.Auditing.code, null);
        } catch (Exception e) {
            log.error("createRecruitMasterAsync failed to send message or update status", e);
        }
    }

    @Override
    public void createRecruitMaster(CreateRecruitMasterReq req) {
        String recruitId = req.getRecruitId();
        String masterId = req.getMasterId();
//        String sceneCode = SceneCodeEnum.CONTRACT_MASTER.getCode();
        String sceneCode = req.getSceneCode();

        // 先清除可能存在的脏数据
        batchDeleteLogically(sceneCode, masterId, recruitId);

        Date now = new Date();
        String currentLoginName = CommonUtils.getCurrentLoginName();
        List<CreateRecruitFeeRuleInfo> feeRuleInfoList = req.getFeeRuleInfoList();
        if (CollectionUtils.isEmpty(feeRuleInfoList)) {
            throw new BusException("招募师傅规则不能为空");
        }

        Set<String> streetIdSet = feeRuleInfoList.stream().flatMap(f -> f.getStreetIds().stream()).collect(Collectors.toSet());
        List<Address> addressList = addressApi.getDivisionInfoListByDivisionIds(StringUtils.join(streetIdSet, ","));
        if (CollectionUtils.isEmpty(addressList)) {
            throw new BusException("街道信息不存在");
        }
        Map<Long, Address> addressMap = addressList.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));

        List<FeeRule> feeRuleList = new ArrayList<>();
        for (CreateRecruitFeeRuleInfo ruleInfo : feeRuleInfoList) {
            List<String> streetIds = ruleInfo.getStreetIds();
            List<CreateRecruitFeeRuleInfo.PriceInfo> priceInfos = ruleInfo.getPriceInfos();
            if (CollectionUtils.isEmpty(streetIds) || CollectionUtils.isEmpty(priceInfos)) {
                throw new BusException("招募师傅价格和街道Id不能为空");
            }
            Set<Long> templateIds = priceInfos.stream().map(CreateRecruitFeeRuleInfo.PriceInfo::getTemplateId).collect(Collectors.toSet());
            Criteria criteria = where(FeeTemplate.Fields.sceneCode).is(sceneCode)
                    .and(FeeTemplate.Fields.templateId).in(templateIds)
                    .and(BaseDocument.Fields.del).is(false)
                    .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);
            Query query = new Query(criteria);
            List<FeeTemplate> feeTemplates = mongoTemplate.find(query, FeeTemplate.class);
            if (CollectionUtils.isEmpty(feeTemplates)) {
                throw new BusException("招募师傅价格模板不存在");
            }

            Map<Long, FeeTemplate> feeTemplateMap = feeTemplates.stream().collect(Collectors.toMap(FeeTemplate::getTemplateId, Function.identity()));
            for (CreateRecruitFeeRuleInfo.PriceInfo priceInfo : priceInfos) {
                Long templateId = priceInfo.getTemplateId();
                FeeTemplate feeTemplate = feeTemplateMap.get(templateId);
                if (feeTemplate == null) {
                    throw new BusException("模板Id不存在或已逻辑删除，templateId=" + templateId);
                }
                for (String streetId : streetIds) {
                    FeeRule feeRule = new FeeRule();
                    BeanUtils.copyProperties(feeTemplate, feeRule);
                    feeRule.setId(null);
                    Map<String, String> bizRule = feeRule.getBizRule();
                    Map<String, String> bizRuleNew = new HashMap<>(bizRule.size());
                    bizRuleNew.putAll(bizRule);
                    bizRuleNew.put(CommonBizRule.Fields.bizTag, recruitId);
                    bizRuleNew.put(CommonBizRule.Fields.masterId, masterId);
                    bizRuleNew.put(CommonBizRule.Fields.bizId, masterId);
                    bizRuleNew.put(CommonBizRule.Fields.masterInputPrice, String.valueOf(priceInfo.getPrice()));
                    bizRuleNew.put(CommonBizRule.Fields.divisionType, DivisionTypeEnum.STREET.code);
                    bizRuleNew.put(CommonBizRule.Fields.level4DivisionId, streetId);

                    setGoodsCategoryAndServiceType(bizRuleNew, bizRuleNew.get(CommonBizRule.Fields.serviceId));

                    setDivision(DivisionTypeEnum.STREET, bizRuleNew, addressMap);

                    feeRule.setBizRule(bizRuleNew);
                    feeRule.setFeeRuleId(SnowFlakeGenerator.INSTANCE.generate());
                    feeRule.setDel(false);
                    feeRule.setStatus(RuleStatusEnum.ACTIVE.code);
                    feeRule.setCreateTime(now);
                    feeRule.setModifyTime(now);
                    feeRule.setCreateBy(currentLoginName);
                    feeRule.setUpdateBy(currentLoginName);

                    feeRuleList.add(feeRule);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(feeRuleList)) {
            feeRuleRepository.save(feeRuleList);
        }
    }


    @Override
    public void createRecruitActivityMaster(CreateRecruitActivityMasterReq req) {
        String masterId = req.getMasterId();
        String recruitId = req.getRecruitId();
        Set<String> serviceCategoryIds = req.getServiceCategoryIds();
        Set<String> streetIds = req.getStreetIds();
        String divisionType = req.getDivisionType();
        // 与师傅端（庆龙）确认，这个接口地区维度恒为街道
//        String divisionType = DivisionTypeEnum.STREET.code;
        String fromSceneCode = SceneCodeEnum.MASTER_RECRUIT_ACTIVE_PRICE.getCode();
        String toSceneCode = SceneCodeEnum.CONTRACT_MASTER.getCode();
//        String toSceneCode = req.getSceneCode();
//        if (StringUtils.isBlank(toSceneCode)) {
//            toSceneCode = SceneCodeEnum.CONTRACT_MASTER.getCode();
//        }

        // 先清除可能存在的脏数据
        // 如果是追加街道，则不清除
        if (!req.isAddStreet()) {
            batchDeleteLogically(toSceneCode, masterId, recruitId);
        }

        //获取招募地区服务价格
//        FeeRuleQueryReq feeRuleQueryReq = new FeeRuleQueryReq();
//        feeRuleQueryReq.setSceneCode(fromSceneCode);
//        Map<String, String> bizRule = new HashMap<>();
//        bizRule.put("bizTag", String.valueOf(recruitId));
//        feeRuleQueryReq.setBizRule(bizRule);

//        List<FeeRule> query = queryByCondition(feeRuleQueryReq);
//        if (CollectionUtils.isEmpty(query)) {
//            throw new BusException("招募价格不存在");
//        }
        final int pageSize = 1000;
        int pageNum = 1;
        FeeRuleQueryPageReq pageReq = new FeeRuleQueryPageReq();
        pageReq.setSceneCode(fromSceneCode);
        pageReq.setBizTag(recruitId);

        while (true) {
            pageReq.setPageNum(pageNum);
            pageReq.setPageSize(pageSize);
            Page<FeeRule> feeRulePage = queryPage(pageReq);
            List<FeeRule> feeRuleList = feeRulePage.getContent();
            if (CollectionUtils.isEmpty(feeRuleList)) {
                break;
            }

            List<FeeRuleBatchAddReq.FeeRuleData> feeRuleDataList = getFeeRuleData(feeRuleList, serviceCategoryIds, streetIds, divisionType, masterId, recruitId);

            if (CollectionUtils.isEmpty(feeRuleDataList)) {
                pageNum++;
                continue;
            }

            saveFeeRuleListNew(feeRuleDataList);

            pageNum++;
        }
    }


    @Override
    public void createRecruitActivityMasterAsync(CreateRecruitActivityMasterReq req) {
        try {
            String msgBody = JSON.toJSONString(req);
            log.info("createRecruitActivityMasterAsync send message, msgBody = {}", msgBody);
            rocketMqSendService.sendSyncTransactionMessage(new NormalMessage(null, UUID.randomUUID().toString(), feeCenterServiceGeneralTopic, MQTagConstant.RECRUIT_ACTIVITY_MASTER_ASYNC_CREATE, msgBody));
            recruitActivityMasterCreateStatusRepository.saveStatus(req.getMasterId(), req.getRecruitId(),
                    RecruitActivityMasterBuildTypeEnum.CREATE_RECRUIT_ACTIVITY_MASTER.code, RecruitActivityMasterCreateStatusEnum.Auditing.code, null);
        } catch (Exception e) {
            log.error("CreateRecruitActivityMasterAsync failed to send message or update status", e);
        }
    }

    @Override
    public void deleteRuleByMasterIdAsync(DeleteRuleByMasterIdRequest request) {
        String msgBody = JSON.toJSONString(request);
        String recruitId = request.getRecruitId();
        String taskId = recruitId + "-" + request.getMasterId();
        AsyncTaskTypeEnum taskTypeEnum = AsyncTaskTypeEnum.DELETE_RULE_BY_MASTER_ID;
        try {
            log.info("deleteRuleByMasterIdAsync send message, msgBody = {}", msgBody);
            rocketMqSendService.sendSyncTransactionMessage(new NormalMessage(null, UUID.randomUUID().toString(), feeCenterServiceGeneralTopic, MQTagConstant.DELETE_RULE_BY_MASTER_ID_ASYNC, msgBody));
            asyncTasksInfoService.createTask(taskId, taskTypeEnum);
        } catch (Exception e) {
            log.error("deleteRuleByMasterIdAsync failed to send message", e);
            asyncTasksInfoService.updateStatus(taskId, taskTypeEnum, AsyncTaskStatusEnum.FAIL, "发送MQ消息失败");
        }
    }



    @Override
    public List<FeeRule> saveFeeRuleListNew(List<FeeRuleBatchAddReq.FeeRuleData> feeRuleDataList) {
        List<FeeRule> feeRuleList = new LinkedList<>();
        for (FeeRuleBatchAddReq.FeeRuleData feeRuleData : feeRuleDataList) {
            FeeRule feeRule = new FeeRule();
            // 编译公式
            CalculateRuleData calculateRuleData = feeRuleData.getCalculateRuleData();
            if (Objects.nonNull(calculateRuleData) && Objects.nonNull(calculateRuleData.getExpressInfo())) {
                String compile = expressCompilerComposite.compile(calculateRuleData.getExpressInfo());
                try {
                    String[] outVarNames = expressRunner.getOutVarNames(compile);
                    calculateRuleData.setExpress(compile);
                    if (Objects.nonNull(outVarNames) && outVarNames.length > 0) {
                        calculateRuleData.setExpressionParamList(Arrays.asList(outVarNames));
                    }
                } catch (Exception e) {
                    throw new BusException(e.getMessage(), e);
                }
            }
            BeanUtils.copyProperties(feeRuleData, feeRule);
            feeRuleList.add(feeRule);
        }

        return batchSaveNew(feeRuleList);
    }

    @Override
    public List<BatchAddRulesResponse> saveFeeRuleList(List<BatchAddRulesRequest.FeeRuleData> feeRuleDataList) {
        List<FeeRule> feeRuleList = new LinkedList<>();
        for (BatchAddRulesRequest.FeeRuleData feeRuleData : feeRuleDataList) {
            FeeRule feeRule = new FeeRule();
            // 编译公式
            CalculateRuleData calculateRuleData = feeRuleData.getCalculateRuleData();
            if (Objects.nonNull(calculateRuleData) && Objects.nonNull(calculateRuleData.getExpressInfo())) {
                String compile = expressCompilerComposite.compile(calculateRuleData.getExpressInfo());
                try {
                    String[] outVarNames = expressRunner.getOutVarNames(compile);
                    calculateRuleData.setExpress(compile);
                    if (Objects.nonNull(outVarNames) && outVarNames.length > 0) {
                        calculateRuleData.setExpressionParamList(Arrays.asList(outVarNames));
                    }
                } catch (Exception e) {
                    throw new BusException(e.getMessage(), e);
                }
            }
            BeanUtils.copyProperties(feeRuleData, feeRule);

            FeeBizRuleDTO bizRuleDto = feeRuleData.getBizRuleDto();
            Map<String, String> bizRule = JSON.parseObject(
                    JSON.toJSONString(bizRuleDto),
                    new TypeReference<Map<String, String>>() {}
            );
            feeRule.setBizRule(bizRule);
            feeRule.setFeeName(bizRuleDto.getFeeName());
            feeRuleList.add(feeRule);
        }

        return batchSaveRules(feeRuleList);
    }

    @Override
    public void modifyRecruitMaster(ModifyRecruitMasterReq req) {
        String masterId = req.getMasterId();
        String recruitId = req.getRecruitId();
        List<ModifyRecruitMasterReq.PriceInfo> priceInfos = req.getPriceInfos();
        if (CollectionUtils.isEmpty(priceInfos)) {
            throw new BusException("价格信息不能为空");
        }
        Date now = new Date();
        String currentLoginName = CommonUtils.getCurrentLoginName();
        BulkOperations bulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, FeeRule.class);
        for (ModifyRecruitMasterReq.PriceInfo priceInfo : priceInfos) {
            Criteria criteria = where(FeeRule.Fields.templateId).is(priceInfo.getTemplateId())
                    .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizTag).is(recruitId)
                    .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level4DivisionId).in(priceInfo.getStreetIds())
                    .and(BaseDocument.Fields.del).is(false)
                    .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code)
                    .orOperator(Criteria.where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterId).is(masterId),
                            Criteria.where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizId).is(masterId));
            Query updateQuery = new Query(criteria);
            Update update = new Update().set(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterInputPrice, String.valueOf(priceInfo.getPrice()))
                    .set(BaseDocument.Fields.modifyTime, now)
                    .set(BaseDocument.Fields.updateBy, currentLoginName);
            bulkOps.updateMulti(updateQuery, update);
        }
        bulkOps.execute();


//        final int batchSize = 1000;
//        for (int i = 0; i < reqList.size(); i += batchSize) {
//            // bulkOps对象不能重复使用，故不能放到for外层
//            BulkOperations bulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, FeeRule.class);
//            reqList.subList(i, Math.min(i + batchSize, reqList.size())).forEach(req -> {
//                Query updateQuery = new Query(Criteria.where(FeeRule.Fields.feeRuleId).is(req.getFeeRuleId()));
//                Update update = new Update().set(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterInputPrice, String.valueOf(req.getPrice()));
//                bulkOps.updateOne(updateQuery, update);
//            });
//            bulkOps.execute();
//        }
    }


    @Override
    public List<GetRecruitActivityMasterCreateStatusResp> getRecruitActivityMasterCreateStatus(GetRecruitActivityMasterCreateStatusReq req) {

        if (req == null || req.getMasterId() == null || CollectionUtils.isEmpty(req.getRecruitIds())) {
            throw new IllegalArgumentException("Request parameters cannot be null or empty.");
        }

        Criteria criteria = where(RecruitActivityMasterCreateStatus.Fields.masterId).is(req.getMasterId())
                .and(RecruitActivityMasterCreateStatus.Fields.recruitId).in(req.getRecruitIds())
                .and(BaseDocument.Fields.del).is(false);
        List<RecruitActivityMasterCreateStatus> statusList = mongoTemplate.find(new Query(criteria), RecruitActivityMasterCreateStatus.class);
        if (CollectionUtils.isEmpty(statusList)) {
            return Collections.emptyList();
        }

        List<GetRecruitActivityMasterCreateStatusResp> resultList = new ArrayList<>(statusList.size());
        for (RecruitActivityMasterCreateStatus status : statusList) {
            GetRecruitActivityMasterCreateStatusResp statusResp = GetRecruitActivityMasterCreateStatusResp.builder()
                    .recruitId(status.getRecruitId())
                    .masterId(status.getMasterId())
                    .buildType(status.getBuildType())
                    .auditStatus(status.getAuditStatus())
                    .auditTime(status.getAuditTime())
                    .build();
            resultList.add(statusResp);
        }
        return resultList;
    }


    @Override
    public void modifyRecruitActivityMasterAsync(ModifyRecruitActivityMasterReq req) {
        try {
            String msgBody = JSON.toJSONString(req);
            log.info("modifyRecruitActivityMasterAsync send message, msgBody = {}", msgBody);
            rocketMqSendService.sendSyncTransactionMessage(new NormalMessage(null, UUID.randomUUID().toString(), feeCenterServiceGeneralTopic, MQTagConstant.MODIFY_RECRUIT_ACTIVITY_MASTER_ASYNC, msgBody));
            recruitActivityMasterCreateStatusRepository.saveStatus(req.getMasterId(), req.getRecruitId(),
                    RecruitActivityMasterBuildTypeEnum.MODIFY_RECRUIT_ACTIVITY_MASTER.code, RecruitActivityMasterCreateStatusEnum.Auditing.code, null);
        } catch (Exception e) {
            log.error("modifyRecruitActivityMasterAsync failed to send message or update status", e);
        }
    }

    @Override
    public void modifyRecruitActivityMaster(ModifyRecruitActivityMasterReq req) {
        /*
         思路：
         1、师傅端传过来两种场景的数据：master_recruit_active_price（师傅招募活动价），contract_master（合约师傅）
         2、当场景为 师傅招募活动价 时，则根据feeRuleId 获取对应的规则，然后调用 创建招募师傅 获取了活动价以后得部分的代码
         2、当场景为 合约师傅 时，则根据feeRuleId 获取对应的规则，然后根据streetId复制规则
         */

        List<ModifyRecruitActivityMasterReq.FeeRuleBaseInfo> feeRuleBaseInfoList = req.getFeeRuleBaseInfoList();
        // 在删除前先捞出来
        List<ModifyRecruitActivityMasterReq.FeeRuleBaseInfo> contractList = feeRuleBaseInfoList.stream().filter(e -> SceneCodeEnum.CONTRACT_MASTER.getCode().equals(e.getSceneCode())).collect(Collectors.toList());
        Set<Long> contractFeeRuleIds = contractList.stream().map(ModifyRecruitActivityMasterReq.FeeRuleBaseInfo::getFeeRuleId).collect(Collectors.toSet());
        Criteria criteria = where(FeeRule.Fields.feeRuleId).in(contractFeeRuleIds)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code);
        List<FeeRule> contractFeeRuleList = mongoTemplate.find(new Query(criteria), FeeRule.class);

        // 由于业务侧可能会删除部分数据，但他们不会将删除信息传过来，所以只能先删后增
        batchDeleteLogically(SceneCodeEnum.CONTRACT_MASTER.getCode(), req.getMasterId(), req.getRecruitId());

        // 合约师傅
        Set<String> streetIds = feeRuleBaseInfoList.stream().map(ModifyRecruitActivityMasterReq.FeeRuleBaseInfo::getStreetIds).flatMap(Collection::stream).collect(Collectors.toSet());
        List<Address> addressList = addressApi.getDivisionInfoListByDivisionIds(StringUtils.join(streetIds, ","));
        Map<Long, Address> addressMap = addressList.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));

        Map<Long, ModifyRecruitActivityMasterReq.FeeRuleBaseInfo> contractMap = contractList.stream().collect(Collectors.toMap(ModifyRecruitActivityMasterReq.FeeRuleBaseInfo::getFeeRuleId, Function.identity()));
        Date now = new Date();
        String currentLoginName = CommonUtils.getCurrentLoginName();
        List<FeeRule> insertFeeRules = new ArrayList<>();
        for (FeeRule feeRule : contractFeeRuleList) {
            ModifyRecruitActivityMasterReq.FeeRuleBaseInfo feeRuleBaseInfo = contractMap.get(feeRule.getFeeRuleId());
            Set<String> ruleStreetIds = feeRuleBaseInfo.getStreetIds();
            for (String streetId : ruleStreetIds) {
                FeeRule rule = new FeeRule();
                insertFeeRules.add(rule);
                BeanUtils.copyProperties(feeRule, rule);
                rule.setFeeRuleId(SnowFlakeGenerator.INSTANCE.generate());
                rule.setId(null);
                rule.setCreateTime(now);
                rule.setModifyTime(now);
                rule.setCreateBy(currentLoginName);
                rule.setUpdateBy(currentLoginName);
                Map<String, String> bizRule = rule.getBizRule();
                Map<String, String> newBizRule = new HashMap<>(bizRule);
                rule.setBizRule(newBizRule);
                newBizRule.put(CommonBizRule.Fields.masterInputPrice, String.valueOf(feeRuleBaseInfo.getPrice()));
                newBizRule.put(CommonBizRule.Fields.level4DivisionId, streetId);
                // 补全省市区信息
                setDivision(DivisionTypeEnum.STREET, newBizRule, addressMap);
            }
        }
        // 不涉及到编译算价公式，所以可以直接插入
        feeRuleRepository.save(insertFeeRules);

        // 师傅招募活动价
        String recruitId = req.getRecruitId();
        String masterId = req.getMasterId();
        List<ModifyRecruitActivityMasterReq.FeeRuleBaseInfo> activeList = feeRuleBaseInfoList.stream().filter(e -> SceneCodeEnum.MASTER_RECRUIT_ACTIVE_PRICE.getCode().equals(e.getSceneCode())).collect(Collectors.toList());

        for (ModifyRecruitActivityMasterReq.FeeRuleBaseInfo feeRuleBaseInfo : activeList) {
            Long feeRuleId = feeRuleBaseInfo.getFeeRuleId();
            BigDecimal price = feeRuleBaseInfo.getPrice();
            FeeRule feeRule = feeRuleRepository.findByFeeRuleId(feeRuleId);
            if (feeRule == null) {
                throw new BusException("feeRuleId:" + feeRuleId + " not found");
            }
            Map<String, String> bizRule = feeRule.getBizRule();
            String serviceCategoryId = bizRule.get(CommonBizRule.Fields.serviceCategoryId);
            String divisionType = bizRule.get(CommonBizRule.Fields.divisionType);
            bizRule.put(CommonBizRule.Fields.masterInputPrice, price.toString());

            List<FeeRuleBatchAddReq.FeeRuleData> feeRuleDataList = getFeeRuleData(
                    Collections.singletonList(feeRule),
                    Collections.singleton(serviceCategoryId),
                    feeRuleBaseInfo.getStreetIds(),
                    divisionType,
                    masterId,
                    recruitId);

            if (CollectionUtils.isNotEmpty(feeRuleDataList)) {
                saveFeeRuleListNew(feeRuleDataList);
            }
        }
    }


    @Override
    public Page<FeeRule> queryPage(FeeRuleQueryPageReq req) {
        String sceneCode = req.getSceneCode();
        if (StringUtils.isBlank(sceneCode)) {
            throw new BusException("sceneCode不能为空");
        }
        Integer pageSize = req.getPageSize();
        Integer pageNum = req.getPageNum();
        if (pageNum == null || pageSize == null) {
            throw new BusException("pageNum和pageSize不能为空");
        }
//        if (pageSize > 10000) {
//            throw new BusException("pageSize不能大于1000");
//        }
        Set<String> divisionIds = req.getDivisionIds();
        String divisionType = req.getDivisionType();
        Set<String> serviceIds = req.getServiceIds();
        Set<String> serviceCategoryIds = req.getServiceCategoryIds();
        String bizTag = req.getBizTag();

        Criteria criteria = where(FeeRule.Fields.sceneCode).is(sceneCode);
        if (StringUtils.isNotBlank(divisionType)) {
            DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(divisionType);
            if (divisionTypeEnum == null) {
                throw new BusException("区域类型不正确");
            }
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(divisionType);

            if (CollectionUtils.isNotEmpty(divisionIds)) {
                switch (divisionTypeEnum) {
                    case PROVINCE:
                        criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level1DivisionId).in(divisionIds);
                        break;
                    case CITY:
                        criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId).in(divisionIds);
                        break;
                    case DISTRICT:
                        criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level3DivisionId).in(divisionIds);
                        break;
                    case STREET:
                        criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level4DivisionId).in(divisionIds);
                        break;
                }
            }
        }

        if (CollectionUtils.isNotEmpty(serviceIds)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).in(serviceIds);
        }

        if (CollectionUtils.isNotEmpty(serviceCategoryIds)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceCategoryId).in(serviceCategoryIds);
        }

        if (StringUtils.isNotBlank(bizTag)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizTag).is(bizTag);
        }

        Query query = new Query(criteria);
//        query.with(new Sort(Sort.Direction.ASC, BaseDocument.Fields.id));
        query.skip((pageNum - 1) * pageSize).limit(pageSize);
        List<FeeRule> feeRules = mongoTemplate.find(query, FeeRule.class);
        return new PageImpl<>(feeRules);
    }



    @Override
    public void preHandleManualMaintenanceDataToCache(String sceneCode) {
        if (StringUtils.isBlank(sceneCode)) {
            return;
        }
        Criteria criteria = where(FeeTemplate.Fields.sceneCode).is(sceneCode)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + "currentVersion").exists(false);
        // 步长为2000，分页查询
        int pageNum = 0;
        final int pageSize = 2000;
        while (true) {
            Query query = new Query(criteria);
            query.skip(pageNum * pageSize).limit(pageSize);
            List<FeeRule> feeRuleList = mongoTemplate.find(query, FeeRule.class);
            if (CollectionUtils.isEmpty(feeRuleList)) {
                break;
            }
            for (FeeRule feeRule : feeRuleList) {
                // sceneCode+templateID+divisionType+lv4作为唯一条件，是否存在人工导入的数据，存在则不再更新/创建该规则数据
                String redisKey = getBargainPriceEveryDayRedisKey(
                        sceneCode,
                        feeRule.getTemplateId(),
                        feeRule.getBizRule().get(CommonBizRule.Fields.level4DivisionId));
                redisHelper.set(redisKey, feeRule.getId(), 12 * 60 * 60);
            }
            pageNum++;
        }
    }


    private void handleException(CreateRecruitActivityMasterReq req) {
        String toSceneCode = SceneCodeEnum.CONTRACT_MASTER.getCode();
        sendMessage(req.getRecruitId(), req.getMasterId(), "fail");
        batchDeleteLogically(toSceneCode, req.getMasterId(), req.getRecruitId());
    }


    private void sendMessage(String recruitId, String masterId, String status) {
        RecruitActivityMasterResult result = new RecruitActivityMasterResult();
        result.setRecruitId(recruitId);
        result.setMasterId(masterId);
        result.setStatus(status);
//        rocketMqSendService.sendSyncTransactionMessage(new NormalMessage(Long.valueOf(recruitId), UUID.randomUUID().toString(),feeCenterServiceGeneralTopic, "createRecruitActivityMasterAsync", JSON.toJSONString(result)));
        // bizId字段业务方不需要，所以传null
        rocketMqSendService.sendSyncTransactionMessage(new NormalMessage(null, UUID.randomUUID().toString(), feeCenterServiceGeneralTopic, "createRecruitActivityMasterAsync", JSON.toJSONString(result)));
        log.info("createRecruitActivityMasterAsync send message recruitId={} {}", recruitId, status);
    }


    private void handleDirtyData(List<FeeRuleBatchAddReq.FeeRuleData> feeRuleDataList) {
        FeeRuleBatchAddReq.FeeRuleData ruleData = feeRuleDataList.get(0);
        String sceneCode = ruleData.getSceneCode();
        Map<String, String> bizRule = ruleData.getBizRule();
        if (StringUtils.isNotBlank(sceneCode) && MapUtils.isNotEmpty(bizRule)) {
            String masterId = bizRule.get(CommonBizRule.Fields.masterId);
            String bizTag = bizRule.get(CommonBizRule.Fields.bizTag);
            batchDeleteLogically(sceneCode, masterId, bizTag);
        }
    }

//    private void saveFeeRuleListNew(List<FeeRuleBatchAddReq.FeeRuleData> feeRuleDataList) {
//        List<FeeRule> feeRuleList = new LinkedList<>();
//        for (FeeRuleBatchAddReq.FeeRuleData feeRuleData : feeRuleDataList) {
//            FeeRule feeRule = new FeeRule();
//            // 编译公式
//            CalculateRuleData calculateRuleData = feeRuleData.getCalculateRuleData();
//            if (Objects.nonNull(calculateRuleData) && Objects.nonNull(calculateRuleData.getExpressInfo())) {
//                String compile = expressCompilerComposite.compile(calculateRuleData.getExpressInfo());
//                try {
//                    String[] outVarNames = expressRunner.getOutVarNames(compile);
//                    calculateRuleData.setExpress(compile);
//                    if (Objects.nonNull(outVarNames) && outVarNames.length > 0) {
//                        calculateRuleData.setExpressionParamList(Arrays.asList(outVarNames));
//                    }
//                } catch (Exception e) {
//                    throw new BusException(e.getMessage(), e);
//                }
//            }
//            BeanUtils.copyProperties(feeRuleData, feeRule);
//            feeRuleList.add(feeRule);
//        }
//        batchSaveNew(feeRuleList);
//    }

    private Map<String, String> getBizRuleMap(BizRule bizRule) {
        Map<String, String> bizRuleMap = new HashMap<>();

        if (bizRule.getProvinceId() != null) {
            bizRuleMap.put("level1DivisionId", bizRule.getProvinceId().toString());
        }
        if (bizRule.getProvinceName() != null) {
            bizRuleMap.put("province", bizRule.getProvinceName());
        }
        if (bizRule.getCityId() != null) {
            bizRuleMap.put("level2DivisionId", bizRule.getCityId().toString());
        }
        if (bizRule.getCityName() != null) {
            bizRuleMap.put("city", bizRule.getCityName());
        }
        if (bizRule.getDistrictId() != null) {
            bizRuleMap.put("level3DivisionId", bizRule.getDistrictId().toString());
        }
        if (bizRule.getDistrictName() != null) {
            bizRuleMap.put("district", bizRule.getDistrictName());
        }
        if (bizRule.getStreetId() != null) {
            bizRuleMap.put("level4DivisionId", bizRule.getStreetId().toString());
        }
        if (bizRule.getStreetName() != null) {
            bizRuleMap.put("street", bizRule.getStreetName());
        }

        if (bizRule.getMasterInputPrice() != null) {
            bizRuleMap.put("masterInputPrice", bizRule.getMasterInputPrice().toString());
        }
        if (bizRule.getServiceId() != null) {
            bizRuleMap.put("serviceId", bizRule.getServiceId().toString());
        }
        if (bizRule.getServiceName() != null) {
            bizRuleMap.put("serviceName", bizRule.getServiceName());
        }

        if (bizRule.getServiceCategoryId() != null) {
            bizRuleMap.put("serviceCategoryId", bizRule.getServiceCategoryId().toString());
        }
        if (bizRule.getSkuType() != null) {
            bizRuleMap.put("skuType", bizRule.getSkuType());
        }
        if (bizRule.getDivisionType() != null) {
            bizRuleMap.put("divisionType", bizRule.getDivisionType());
            DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(bizRule.getDivisionType());
            if (divisionTypeEnum != null) {
                bizRuleMap.put("divisionTypeName", divisionTypeEnum.name);
            }
        }
        if (bizRule.getSkuNo() != null) {
            bizRuleMap.put("skuNo", bizRule.getSkuNo());
        }
        Long userId = bizRule.getUserId();
        if (userId != null && userId > 0) {
            bizRuleMap.put(CommonBizRule.Fields.userId, userId.toString());
            bizRuleMap.put(CommonBizRule.Fields.bizId, userId.toString());
        }

        return bizRuleMap;
    }

//    private void handleDynamicPrice(String masterInputPrice, BargainPriceEverydayFeeRuleResp resp, DynamicFeeRule dynamicFeeRule) {
//        if (StringUtils.isNotBlank(masterInputPrice)) {
//            resp.setOriginPrice(new BigDecimal(masterInputPrice));
//            if (Objects.nonNull(dynamicFeeRule)) {
//                DynamicCalculateRuleData ruleData = dynamicFeeRule.getDynamicCalculateRuleData();
//                if (Objects.nonNull(ruleData)) {
//                    String dynamicType = ruleData.getDynamicType();
//                    DynamicTypeEnum dynamicTypeEnum = DynamicTypeEnum.fromCode(dynamicType);
//                    if (dynamicTypeEnum != null) {
//                        BigDecimal dynamicPrice = BigDecimal.ZERO;
//                        BigDecimal savingPrice = BigDecimal.ZERO;
//                        BigDecimal originPrice = resp.getOriginPrice();
//                        switch (dynamicTypeEnum) {
//                            case FIXED:
//                                savingPrice = new BigDecimal(ruleData.getAdjustValue());
//                                dynamicPrice = originPrice.subtract(savingPrice);
//                                break;
//                            case PERCENT:
//                                savingPrice = originPrice.multiply(new BigDecimal(ruleData.getAdjustValue())).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
//                                dynamicPrice = originPrice.subtract(savingPrice);
//                                break;
//                            default:
//                                break;
//                        }
//                        resp.setDiscountPrice(dynamicPrice);
//                        resp.setSavingPrice(savingPrice);
//                    }
//                }
//            } else {
//                resp.setDiscountPrice(resp.getOriginPrice());
//                resp.setSavingPrice(BigDecimal.ZERO);
//            }
//        } else {
//            resp.setOriginPrice(BigDecimal.ZERO);
//            resp.setSavingPrice(BigDecimal.ZERO);
//        }
//    }

    private void handleDynamicPrice(String masterInputPrice, BargainPriceEverydayFeeRuleResp resp, DynamicFeeRuleCalculateRuleData dynamicFeeRuleCalculateRuleData, Date now) {
        if (StringUtils.isNotBlank(masterInputPrice)) {
            resp.setOriginPrice(new BigDecimal(masterInputPrice));
            if (Objects.nonNull(dynamicFeeRuleCalculateRuleData)) {
                DynamicFeeRuleCalculateRuleData.MatchingRule matchingRule = dynamicFeeRuleCalculateRuleData.getMatchingRule();
                Date endTime = matchingRule.getEndTime();
                Date startTime = matchingRule.getStartTime();
                if (Objects.nonNull(startTime) && Objects.nonNull(endTime) && Objects.nonNull(now)
                        && now.compareTo(startTime) >= 0 && now.compareTo(endTime) <= 0) {
                    // 计算调价费用结果
                    DynamicFeeRuleCalculateRuleData.CalculateData unitPrice = dynamicFeeRuleCalculateRuleData.getUnitPrice();

                    DefaultContext<String, Object> params = new DefaultContext<>();
                    params.put(DynamicCalculateRuleData.ORIGIN_PRICE, Double.valueOf(masterInputPrice));
                    String adjustFee;
                    try {
                        adjustFee = expressRunner.execute(unitPrice.getExpress(), params, null, true, false, 1500).toString();
                    } catch (Exception e) {
                        throw new BusException("调价费用计算异常");
                    }
                    BigDecimal adjustFeePrice = new BigDecimal(adjustFee);
                    if (BigDecimal.ZERO.compareTo(adjustFeePrice) > 0) {
                        throw new BusException("调价后费用小于0");
                    }

                    resp.setDiscountPrice(new BigDecimal(adjustFee));
                    resp.setSavingPrice(resp.getOriginPrice().subtract(adjustFeePrice));

//                    if (skuPrice == null) {
//                        resp.setDiscountPrice(new BigDecimal(adjustFee));
//                        resp.setSavingPrice(resp.getOriginPrice().subtract(adjustFeePrice));
//                        return resp;
//                    }
//
//                    params.put("masterInputPrice", adjustFeePrice);
//                    List<String> expressionParamList = skuPrice.getExpressionParamList();
//                    if (CollectionUtils.isNotEmpty(expressionParamList)) {
//                        for (String expressParam : expressionParamList) {
//                            ExpressParamEnum expressParamEnum = ExpressParamEnum.fromCode(expressParam);
//                            if (Objects.nonNull(expressParamEnum)) {
//                                String value = serviceInfoValueExtractor.extract(applyCalculateReq.getServiceInfo(), feeRule, cache, expressParamEnum);
//                                params.put(expressParam, value);
//                            }
//                        }
//                        // 特殊处理，下方两个值需要取动态价
//                        params.put(AP_SKU_PRICE.code, adjustFeePrice);
//                        params.put(MASTER_INPUT_PRICE.code, adjustFeePrice);
//                        ExpressResultInfo expressResultInfo = (ExpressResultInfo) expressRunner.execute(skuPrice.getExpress(), params, errorList, true, false, 1500);
//                        BigDecimal cost = expressResultInfo.getCost();
//                    }


//                DynamicCalculateRuleData ruleData = dynamicFeeRule.getDynamicCalculateRuleData();
//                if (Objects.nonNull(ruleData)) {
//                    String dynamicType = ruleData.getDynamicType();
//                    DynamicTypeEnum dynamicTypeEnum = DynamicTypeEnum.fromCode(dynamicType);
//                    if (dynamicTypeEnum != null) {
//                        BigDecimal dynamicPrice = BigDecimal.ZERO;
//                        BigDecimal savingPrice = BigDecimal.ZERO;
//                        BigDecimal originPrice = resp.getOriginPrice();
//                        switch (dynamicTypeEnum) {
//                            case FIXED:
//                                savingPrice = new BigDecimal(ruleData.getAdjustValue());
//                                dynamicPrice = originPrice.subtract(savingPrice);
//                                break;
//                            case PERCENT:
//                                savingPrice = originPrice.multiply(new BigDecimal(ruleData.getAdjustValue())).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
//                                dynamicPrice = originPrice.subtract(savingPrice);
//                                break;
//                            default:
//                                break;
//                        }
//                        resp.setDiscountPrice(dynamicPrice);
//                        resp.setSavingPrice(savingPrice);
//                    }
//                }
                } else {
                    resp.setDiscountPrice(resp.getOriginPrice());
                    resp.setSavingPrice(BigDecimal.ZERO);
                }
            } else {
                resp.setOriginPrice(BigDecimal.ZERO);
                resp.setSavingPrice(BigDecimal.ZERO);
            }
        }
    }

    private void handleFormula(FeeRule feeRule) {
        Map<String, String> bizRule = feeRule.getBizRule();
        String divisionType = bizRule.get(BizFieldEnum.DIVISION_TYPE.name);
        String bizId = bizRule.get(CommonBizRule.Fields.bizId);
        String divisionId = null;
        DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(divisionType);
        if (divisionTypeEnum != null) {
            switch (divisionTypeEnum) {
                case PROVINCE:
                    divisionId = bizRule.get(DivisionTypeEnum.PROVINCE.divisionKey);
                    break;
                case CITY:
                    divisionId = bizRule.get(DivisionTypeEnum.CITY.divisionKey);
                    break;
                case DISTRICT:
                    divisionId = bizRule.get(DivisionTypeEnum.DISTRICT.divisionKey);
                    break;
                case STREET:
                    divisionId = bizRule.get(DivisionTypeEnum.STREET.divisionKey);
                default:
                    break;
            }
            CalculateRuleData calculateRuleData = formulaService.handleFormulas(feeRule.getCalculateRuleData(), divisionType, divisionId, bizId);
            feeRule.setCalculateRuleData(calculateRuleData);
        }
    }


    private SimplePageInfo<LowestPriceResp> preGenerateLowest(List<String> serviceIds, int pageNum, int pageSize) {
        if (CollectionUtils.isEmpty(serviceIds)) {
            return new SimplePageInfo<>();
        }
        String countName = "count";
        String collectionName = "feeRule";
        Criteria criteria = where(FeeRule.Fields.sceneCode).is(SceneCodeEnum.BARGAIN_PRICE_EVERYDAY.getCode())
                .and(FeeRule.Fields.masterInputPriceDouble).ne(null)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).is(FeeTypeTagEnum.SERVICE_FEE.code)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code);
        if (CollectionUtils.isNotEmpty(serviceIds)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).in(serviceIds);
        }

        SortOperation sort = sort(Sort.Direction.ASC, FeeRule.Fields.masterInputPriceDouble);

        GroupOperation groupOperation = group(
                FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId)
                .min(FeeRule.Fields.masterInputPriceDouble).as("subsidyPrice")
                .first("$$ROOT").as("minPriceDoc");


        Aggregation aggregationCount = Aggregation.newAggregation(
                match(criteria),
                groupOperation,
                Aggregation.replaceRoot("$minPriceDoc"),
                Aggregation.count().as(countName)
        );

        JSONObject jsonObject = mongoTemplate.aggregate(aggregationCount, collectionName, JSONObject.class).getUniqueMappedResult();
        if (jsonObject == null) {
            return new SimplePageInfo<>();
        }
        long count = jsonObject.getLong(countName);

        AggregationOptions options = Aggregation.newAggregationOptions().allowDiskUse(true).build();

        Aggregation aggregation = Aggregation.newAggregation(
                match(criteria),
                sort,
                groupOperation,
                Aggregation.replaceRoot("$minPriceDoc"),
                Aggregation.skip((pageNum - 1) * pageSize),
                Aggregation.limit(pageSize)
        ).withOptions(options);

        AggregationResults<FeeRule> aggregate = mongoTemplate.aggregate(aggregation, collectionName, FeeRule.class);
        List<FeeRule> feeRuleList = aggregate.getMappedResults();
        if (CollectionUtils.isEmpty(feeRuleList)) {
            return new SimplePageInfo<>();
        }

        List<LowestPriceResp> respList = feeRuleList.stream().map(feeRule -> {
            Map<String, String> bizRule = feeRule.getBizRule();
            LowestPriceResp resp = new LowestPriceResp();
            resp.setServiceId(Long.valueOf(bizRule.get(CommonBizRule.Fields.serviceId)));

            String goodsCategoryId = bizRule.get(CommonBizRule.Fields.goodsCategoryId);
            resp.setGoodsCategoryId(goodsCategoryId == null ? null : Long.valueOf(goodsCategoryId));

            String level1GoodsCategoryIdInner = bizRule.get(CommonBizRule.Fields.level1GoodsCategoryId);
            resp.setLevel1GoodsCategoryId(StringUtils.isBlank(level1GoodsCategoryIdInner) ? null : Long.valueOf(level1GoodsCategoryIdInner));

            String serviceTypeId = bizRule.get(CommonBizRule.Fields.serviceTypeId);
            resp.setServiceTypeId(StringUtils.isBlank(serviceTypeId) ? null : Long.valueOf(serviceTypeId));

            resp.setSubsidyPrice(feeRule.getMasterInputPriceDouble());
            resp.setGoodsCategoryName(bizRule.get(CommonBizRule.Fields.goodsCategoryName));
            resp.setServiceTypeName(bizRule.get(CommonBizRule.Fields.serviceTypeName));
            return resp;
        }).collect(Collectors.toList());

        Pageable pageable = new PageRequest(pageNum - 1, pageSize);
        PageImpl<LowestPriceResp> page = new PageImpl<>(respList, pageable, count);
        SimplePageInfo<LowestPriceResp> resultPage = new SimplePageInfo<>(respList);
        resultPage.setPages(page.getTotalPages());
        resultPage.setPageNum(page.getNumber() + 1);
        resultPage.setPageSize(page.getSize());
        resultPage.setTotal(count);

        return resultPage;
    }


    private void setDivision(DivisionTypeEnum divisionTypeEnum, Map<String, String> bizRuleMap, Map<Long, Address> addressMap) {
        switch (divisionTypeEnum) {
            case PROVINCE:
                String level1DivisionId = bizRuleMap.get(CommonBizRule.Fields.level1DivisionId);
                Address address = addressMap.get(Long.parseLong(level1DivisionId));
                if (address == null) {
                    throw new BusException("地址信息不存在, level1DivisionId = " + level1DivisionId);
                }
                bizRuleMap.put(CommonBizRule.Fields.province, address.getDivisionName());
                break;
            case CITY:
                String level2DivisionId = bizRuleMap.get(CommonBizRule.Fields.level2DivisionId);
                Address address2 = addressMap.get(Long.parseLong(level2DivisionId));
                if (address2 == null) {
                    throw new BusException("地址信息不存在, level2DivisionId = " + level2DivisionId);
                }
                bizRuleMap.put(CommonBizRule.Fields.province, address2.getLv2DivisionName());
                bizRuleMap.put(CommonBizRule.Fields.level1DivisionId, address2.getLv2DivisionId().toString());
                bizRuleMap.put(CommonBizRule.Fields.city, address2.getDivisionName());
                break;
            case DISTRICT:
                String level3DivisionId = bizRuleMap.get(CommonBizRule.Fields.level3DivisionId);
                Address address3 = addressMap.get(Long.parseLong(level3DivisionId));
                if (address3 == null) {
                    throw new BusException("地址信息不存在, level3DivisionId = " + level3DivisionId);
                }
                bizRuleMap.put(CommonBizRule.Fields.province, address3.getLv2DivisionName());
                bizRuleMap.put(CommonBizRule.Fields.level1DivisionId, address3.getLv2DivisionId().toString());
                String lv3DivisionName = address3.getLv3DivisionName();
                String lv4DivisionName = address3.getLv4DivisionName();
                Long lv3DivisionId = address3.getLv3DivisionId();
                Long lv4DivisionId = address3.getLv4DivisionId();
                bizRuleMap.put(CommonBizRule.Fields.city, StringUtils.isNotBlank(lv3DivisionName) ? lv3DivisionName : lv4DivisionName);
                bizRuleMap.put(CommonBizRule.Fields.level2DivisionId, (lv3DivisionId == null || lv3DivisionId == 0L) ? lv4DivisionId.toString() : lv3DivisionId.toString());
                bizRuleMap.put(CommonBizRule.Fields.district, address3.getDivisionName());
                break;
            case STREET:
                String level4DivisionId = bizRuleMap.get(CommonBizRule.Fields.level4DivisionId);
                Address address4 = addressMap.get(Long.parseLong(level4DivisionId));
                if (address4 == null) {
                    throw new BusException("地址信息不存在, level4DivisionId = " + level4DivisionId);
                }
                bizRuleMap.put(CommonBizRule.Fields.province, address4.getLv2DivisionName());
                bizRuleMap.put(CommonBizRule.Fields.level1DivisionId, address4.getLv2DivisionId().toString());

                String lv3DivisionName4 = address4.getLv3DivisionName();
                String lv4DivisionName4 = address4.getLv4DivisionName();
                String lv5DivisionName4 = address4.getLv5DivisionName();
                Long lv3DivisionId4 = address4.getLv3DivisionId();
                Long lv4DivisionId4 = address4.getLv4DivisionId();
                Long lv5DivisionId4 = address4.getLv5DivisionId();

                bizRuleMap.put(CommonBizRule.Fields.city, StringUtils.isNotBlank(lv3DivisionName4) ? lv3DivisionName4 : lv4DivisionName4);
                bizRuleMap.put(CommonBizRule.Fields.level2DivisionId, (lv3DivisionId4 == null || lv3DivisionId4 == 0L) ? lv4DivisionId4.toString() : lv3DivisionId4.toString());
                bizRuleMap.put(CommonBizRule.Fields.district, StringUtils.isNotBlank(lv4DivisionName4) ? lv4DivisionName4 : lv5DivisionName4);
                bizRuleMap.put(CommonBizRule.Fields.level3DivisionId, (lv4DivisionId4 == null || lv4DivisionId4 == 0L) ? lv5DivisionId4.toString() : lv4DivisionId4.toString());
                bizRuleMap.put(CommonBizRule.Fields.street, address4.getDivisionName());
                break;
        }
    }


    private String getBargainPriceEveryDayRedisKey(String sceneCode, Long templateId, String level4DivisionId) {
        return String.format("%s:%s:%s:%s",
                sceneCode,
                templateId,
                DivisionTypeEnum.STREET.code,
                level4DivisionId);
    }


    private List<FeeRuleBatchAddReq.FeeRuleData> getFeeRuleData(List<FeeRule> feeRuleList, Set<String> serviceCategoryIds,
                                                                Set<String> streetIds, String divisionType, String masterId, String recruitId) {
        String fromSceneCode = SceneCodeEnum.MASTER_RECRUIT_ACTIVE_PRICE.getCode();
        String toSceneCode = SceneCodeEnum.CONTRACT_MASTER.getCode();
        //查询自定义sku的templateId
        List<String> templateIdList = new ArrayList<>();
        for (FeeRule feeRule : feeRuleList) {
            Map<String, String> bR = feeRule.getBizRule();

            if (FeeSkuTypeEnum.CUSTOM_SKU.code.equals(bR.get("skuType"))) {
                templateIdList.add(String.valueOf(feeRule.getTemplateId()));
            }
        }

        Map<Long, Long> customTemplateIdMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(templateIdList)) {

            if (ApolloConfig.NEW_TEMPLATE_MAPPING_SWITCH) {
                MappingQueryPageRequest request = new MappingQueryPageRequest();
                request.setTargetTemplateIdList(templateIdList.stream().map(Long::valueOf).collect(Collectors.toList()));
                request.setTargetSceneCode(fromSceneCode);
                request.setPageNum(1);
                request.setPageSize(2000);
                List<FeeTemplateMapping> mappings = feeTemplateMappingService.queryPage(request).getContent();
                if (CollectionUtils.isEmpty(mappings)) {
                    throw new BusException("价格中台自定义sku映射不存在");
                }
                for (FeeTemplateMapping mapping : mappings) {
                    FeeTemplateMapping.TemplateInfo source = mapping.getSource();
                    FeeTemplateMapping.TemplateInfo target = mapping.getTarget();
                    customTemplateIdMap.put(target.getTemplateId(), source.getTemplateId());
                }
            } else {
                BizRuleMappingBatchQueryReq bizRuleMappingBatchQueryReq = new BizRuleMappingBatchQueryReq();

                Map<String, List<String>> fromBizRule = new HashMap<>();
                fromBizRule.put("templateId", templateIdList);
                fromBizRule.put("sceneCode", Collections.singletonList(fromSceneCode));
                bizRuleMappingBatchQueryReq.setPageNum(1);
                bizRuleMappingBatchQueryReq.setPageSize(2000);

                bizRuleMappingBatchQueryReq.setFromBizRule(fromBizRule);

                Page<BizRuleMapping> bizRuleMapping = bizRuleMappingService.batchQueryByCondition(bizRuleMappingBatchQueryReq);
                List<BizRuleMapping> mappings = bizRuleMapping.getContent();
                if (CollectionUtils.isEmpty(mappings)) {
                    throw new BusException("价格中台自定义sku映射不存在");
                }
                for (BizRuleMapping ruleMapping : mappings) {
                    Map<String, String> toBizRule = ruleMapping.getToBizRule();

                    customTemplateIdMap.put(ruleMapping.getTemplateId(), Long.valueOf(toBizRule.get("templateId")));
                }
            }

        }

        //获取价格模版
        FeeTemplateBatchQueryReq feeTemplateBatchQueryReq = new FeeTemplateBatchQueryReq();
        feeTemplateBatchQueryReq.setSceneCode(toSceneCode);
        feeTemplateBatchQueryReq.setStatusList(Collections.singletonList(0));

        feeTemplateBatchQueryReq.setPageNum(1);
        feeTemplateBatchQueryReq.setPageSize(2000);
        Map<String, List<String>> br2 = new HashMap<>();
        br2.put("serviceCategoryId", new ArrayList<>(serviceCategoryIds));
        feeTemplateBatchQueryReq.setBizRule(br2);
        Page<FeeTemplate> feeTemplateSimplePageInfo = feeTemplateService.batchQueryByCondition(feeTemplateBatchQueryReq);
        List<FeeTemplate> templates = feeTemplateSimplePageInfo.getContent();
        if (CollectionUtils.isEmpty(templates)) {
            throw new BusException("价格模板不存在，sceneCode：" + toSceneCode, "，serviceCategoryIds=" + serviceCategoryIds);
        }

        Map<String, Long> feeTemplateIdMap = new HashMap<>();
        Map<String, Integer> feeTemplateVersionMap = new HashMap<>();
        Map<Long, Integer> customSkuMap = new HashMap<>();

        for (FeeTemplate feeTemplate : templates) {
            Map<String, String> templateBizRule = feeTemplate.getBizRule();
            String skuNo = templateBizRule.get("skuNo");
            String serviceCategoryId = templateBizRule.get("serviceCategoryId");

            if (FeeSkuTypeEnum.STANDARD_SKU.code.equals(templateBizRule.get("skuType"))) {
                feeTemplateIdMap.put(serviceCategoryId + skuNo, feeTemplate.getTemplateId());
                feeTemplateVersionMap.put(serviceCategoryId + skuNo, feeTemplate.getTemplateVersion());
            } else {
                //自定义sku的模版版本号
                customSkuMap.put(feeTemplate.getTemplateId(), feeTemplate.getTemplateVersion());
            }
        }

        List<FeeRuleBatchAddReq.FeeRuleData> feeRuleDataList = new ArrayList<>();

        List<Address> addressList = addressApi.getDivisionInfoListByDivisionIds(StringUtils.join(streetIds, ","));
        List<ServeBaseInfoResp> serveBaseInfoList = serveServiceApi.getServeBaseInfo(serviceCategoryIds.stream().map(Long::valueOf).collect(Collectors.toSet()));
        if (CollectionUtils.isEmpty(serveBaseInfoList)) {
            throw new BusException("服务类目不存在");
        }
        Map<Long, ServeBaseInfoResp> serveBaseInfoRespMap = serveBaseInfoList.stream().collect(Collectors.toMap(ServeBaseInfoResp::getServeId, Function.identity()));

        for (Address address : addressList) {
            Long lv2DivisionId = address.getLv2DivisionId();
            Long lv3DivisionId = address.getLv3DivisionId() == 0L ? address.getLv4DivisionId() : address.getLv3DivisionId();
            Long lv4DivisionId = address.getLv4DivisionId() == 0L ? address.getLv5DivisionId() : address.getLv4DivisionId();
            Long lv5DivisionId = address.getLv5DivisionId();

            String lv2DivisionName = address.getLv2DivisionName();
            String lv3DivisionName = StringUtils.isEmpty(address.getLv3DivisionName()) ? address.getLv4DivisionName() : address.getLv3DivisionName();
            String lv4DivisionName = StringUtils.isEmpty(address.getLv4DivisionName()) ? address.getLv5DivisionName() : address.getLv4DivisionName();
            String lv5DivisionName = address.getLv5DivisionName();

            for (FeeRule q : feeRuleList) {
                Map<String, String> rule = q.getBizRule();
                Long serviceCategoryId = Long.valueOf(rule.get("serviceCategoryId"));
                String skuNo = rule.get("skuNo");
                String skuType = rule.get("skuType");

                ServeBaseInfoResp serveBaseInfo = serveBaseInfoRespMap.get(serviceCategoryId);

                if (serveBaseInfo == null) {
                    continue;
                }

                if (DivisionTypeEnum.CITY.code.equals(divisionType)) {
                    // 按城市定价
                    String level2DivisionId = rule.get("level2DivisionId");
                    if (!level2DivisionId.equals(lv3DivisionId.toString())) {
                        continue;
                    }

                } else if (DivisionTypeEnum.DISTRICT.code.equals(divisionType)) {
                    // 按区县定价
                    String level3DivisionId = rule.get("level3DivisionId");
                    if (!level3DivisionId.equals(lv4DivisionId.toString())) {
                        continue;
                    }
                }

                Long templateId;
                int templateVersion;

                if (FeeSkuTypeEnum.CUSTOM_SKU.code.equals(skuType)) {
                    Long customTemplateId = customTemplateIdMap.get(q.getTemplateId());

                    if (customTemplateId == null) {
                        throw new BusException("价格中台自定义sku映射不匹配");
                    }

                    templateId = customTemplateId;
                    templateVersion = customSkuMap.get(customTemplateId);

                } else {
                    String key = rule.get("serviceCategoryId") + skuNo;

                    if (feeTemplateIdMap.get(key) == null || feeTemplateVersionMap.get(key) == null) {
                        log.info(JSONObject.toJSONString(rule));
                        throw new BusException("价格模板不匹配");
                    }
                    templateId = feeTemplateIdMap.get(key);
                    templateVersion = feeTemplateVersionMap.get(key);
                }

                FeeRuleBatchAddReq.FeeRuleData feeRuleData = new FeeRuleBatchAddReq.FeeRuleData();
                feeRuleData.setTemplateId(templateId);
                feeRuleData.setTemplateVersion(templateVersion);
                feeRuleData.setSceneCode(toSceneCode);
                feeRuleData.setSceneName("合约师傅");

                Map<String, String> bizRuleMap = new HashMap<>(rule);
                bizRuleMap.put("masterId", String.valueOf(masterId));
                bizRuleMap.put("bizId", String.valueOf(masterId));
                bizRuleMap.put("sceneId", "1");
                bizRuleMap.put("bizTag", String.valueOf(recruitId));
                bizRuleMap.put("divisionType", DivisionTypeEnum.STREET.code);
                bizRuleMap.put("sceneCode", toSceneCode);
                bizRuleMap.put("sceneName", "合约师傅");
                bizRuleMap.put("serviceCategoryId", String.valueOf(serviceCategoryId));
                bizRuleMap.put("templateId", String.valueOf(templateId));

                bizRuleMap.put("level1DivisionId", String.valueOf(lv2DivisionId));
                bizRuleMap.put("province", lv2DivisionName);
                bizRuleMap.put("level2DivisionId", String.valueOf(lv3DivisionId));
                bizRuleMap.put("city", lv3DivisionName);
                bizRuleMap.put("level3DivisionId", String.valueOf(lv4DivisionId));
                bizRuleMap.put("district", lv4DivisionName);
                bizRuleMap.put("level4DivisionId", String.valueOf(lv5DivisionId));
                bizRuleMap.put("street", lv5DivisionName);

                feeRuleData.setFeeName(q.getFeeName());
                feeRuleData.setBizRule(bizRuleMap);
                feeRuleData.setCalculateRuleData(q.getCalculateRuleData());

                feeRuleDataList.add(feeRuleData);
            }
        }
        return feeRuleDataList;
    }


    private void updateDelToTrue(String sceneCode) {

//        while() {
//
//            Criteria criteria = Criteria.where(FeeRuleFromBigdataToBeAdd.Fields.sceneCode).is(sceneCode);
//        }
    }


    /**
     * 大批量删除、更新计价规则有管控时间
     *
     * @return 管控时间检查结果
     */
    private boolean checkDeleteFeeRuleControlTime() {
        if (EnvUtil.PROFILE_TEST.equals(EnvUtil.getProfile()) || EnvUtil.PROFILE_DEV.equals(EnvUtil.getProfile())) {
            return false;
        }
        int hour = LocalDateTime.now().getHour();
        // [7:00, 22:00) 点之间不允许删除、更新计价规则
        return hour >= 7 && hour < 22;
    }

    /**
     * 大数据拉取自定义sku模版
     * @param sceneCode
     * @param pageNum
     * @param pageSize
     * @return
     */
    private List<GetSkuTemplateInfoBySceneCodeResp> pullSkuTemplateFromBigdata(String sceneCode, int pageNum, int pageSize) {

        try {
            GetSkuTemplateInfoBySceneCodeRqt rqt = new GetSkuTemplateInfoBySceneCodeRqt();
            rqt.setScene_code(sceneCode);
            rqt.setPageNum(pageNum);
            rqt.setPageSize(pageSize);
            return bigdataOpenServiceApi.getSkuTemplateInfoBySceneCode(rqt);
        } catch (Exception e) {
            log.error("调用大数据接口获取自定义sku模版失败！接口：/dataApi/getData/getSkuTemplateInfoBySceneCode，sceneCode:{},pageNum:{},pageSize:{}",
                    sceneCode, pageNum, pageSize);
            return null;
        }
    }

}
