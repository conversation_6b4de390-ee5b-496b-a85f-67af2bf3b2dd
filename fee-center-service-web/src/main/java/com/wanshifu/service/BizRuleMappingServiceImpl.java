package com.wanshifu.service;

import com.wanshifu.adapter.api.ServiceApi;
import com.wanshifu.adapter.api.ServiceCategoryApi;
import com.wanshifu.adapter.dto.service.ServiceDetail;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.*;
import com.wanshifu.fee.center.domain.enums.MappingStatusEnum;
import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.enums.TemplateStatusEnum;
import com.wanshifu.fee.center.domain.request.BizRuleMappingApplyTypesReq;
import com.wanshifu.fee.center.domain.request.BizRuleMappingBatchQueryReq;
import com.wanshifu.fee.center.domain.request.BizRuleMappingQueryReq;
import com.wanshifu.fee.center.domain.request.mapping.BizRuleMappingBatchAddReq;
import com.wanshifu.fee.center.domain.request.mapping.CopyBizRuleMappingReq;
import com.wanshifu.fee.center.domain.request.mapping.QueryPageReq;
import com.wanshifu.fee.center.domain.request.mapping.SaveBaseRequest;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.MapUtils;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.repository.*;
import com.wanshifu.infrastructure.utils.CommonUtils;
import com.wanshifu.infrastructure.utils.PatternUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BizRuleMappingServiceImpl implements BizRuleMappingService {

    @Resource
    private BizRuleMappingRepository bizRuleMappingRepository;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private SceneInfoRepository sceneInfoRepository;
    @Autowired
    private ServiceApi serviceApi;
    @Autowired
    private ServiceCategoryApi serviceCategoryApi;
    @Autowired
    private FeeTemplateMappingRepository feeTemplateMappingRepository;
    @Autowired
    private FeeTemplateRepository feeTemplateRepository;
    @Resource
    private FlushDataLogRepository flushDataLogRepository;

    @Override
    public Page<BizRuleMapping> queryByCondition(BizRuleMappingQueryReq bizRuleMappingQueryReq) {
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        if (Objects.nonNull(bizRuleMappingQueryReq.getTemplateId())) {
            criteria.and(BizRuleMapping.Fields.templateId).is(bizRuleMappingQueryReq.getTemplateId());
        }
        if (StringUtils.isNotBlank(bizRuleMappingQueryReq.getSceneCode())) {
            criteria.and(BizRuleMapping.Fields.sceneCode).is(bizRuleMappingQueryReq.getSceneCode());
        }
        if (StringUtils.isNotBlank(bizRuleMappingQueryReq.getGroup())) {
            criteria.and(BizRuleMapping.Fields.group).is(bizRuleMappingQueryReq.getGroup());
        }
        if (StringUtils.isNotBlank(bizRuleMappingQueryReq.getSceneName())) {
            criteria.and(BizRuleMapping.Fields.sceneName).regex(PatternUtils.toEscapeStr(bizRuleMappingQueryReq.getSceneName()));
        }
        if (Objects.nonNull(bizRuleMappingQueryReq.getBizRuleMappingId())) {

        }
        if (MapUtils.isNotEmpty(bizRuleMappingQueryReq.getFromBizRule())) {
            bizRuleMappingQueryReq.getFromBizRule().forEach((key, value) ->
                    criteria.and(BizRuleMapping.Fields.fromBizRule + PunConstant.DOT + key).is(value)
            );
        }
        if (MapUtils.isNotEmpty(bizRuleMappingQueryReq.getToBizRule())) {
            bizRuleMappingQueryReq.getToBizRule().forEach((key, value) ->
                    criteria.and(BizRuleMapping.Fields.toBizRule + PunConstant.DOT + key).is(value)
            );
        }

        Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.modifyTime));
        PageRequest pageRequest = new PageRequest(bizRuleMappingQueryReq.pageNum - 1, bizRuleMappingQueryReq.pageSize, sort);

        Query query = Query.query(criteria);
        long count = mongoTemplate.count(query, BizRuleMapping.class);
        if (count > 0) {
            query.with(pageRequest);
            List<BizRuleMapping> bizRuleMappings = mongoTemplate.find(query, BizRuleMapping.class);
            return new PageImpl<>(bizRuleMappings, pageRequest, count);
        } else {
            return new PageImpl<>(Collections.emptyList(), pageRequest, count);
        }
    }

    @Override
    public Page<BizRuleMapping> batchQueryByCondition(BizRuleMappingBatchQueryReq bizRuleMappingBatchQueryReq) {
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        if (Objects.nonNull(bizRuleMappingBatchQueryReq.getTemplateId())) {
            criteria.and(BizRuleMapping.Fields.templateId).is(bizRuleMappingBatchQueryReq.getTemplateId());
        }
        if (StringUtils.isNotBlank(bizRuleMappingBatchQueryReq.getSceneCode())) {
            criteria.and(BizRuleMapping.Fields.sceneCode).is(bizRuleMappingBatchQueryReq.getSceneCode());
        }
        if (StringUtils.isNotBlank(bizRuleMappingBatchQueryReq.getGroup())) {
            criteria.and(BizRuleMapping.Fields.group).is(bizRuleMappingBatchQueryReq.getGroup());
        }
        if (StringUtils.isNotBlank(bizRuleMappingBatchQueryReq.getSceneName())) {
            criteria.and(BizRuleMapping.Fields.sceneName).regex(PatternUtils.toEscapeStr(bizRuleMappingBatchQueryReq.getSceneName()));
        }
        if (Objects.nonNull(bizRuleMappingBatchQueryReq.getBizRuleMappingId())) {
            criteria.and(BizRuleMapping.Fields.bizRuleMappingId).is(bizRuleMappingBatchQueryReq.getBizRuleMappingId());
        }
        if (MapUtils.isNotEmpty(bizRuleMappingBatchQueryReq.getFromBizRule())) {
            bizRuleMappingBatchQueryReq.getFromBizRule().forEach((key, value) ->{
                if(CollectionUtils.isNotEmpty(value)){
                    criteria.and(BizRuleMapping.Fields.fromBizRule + PunConstant.DOT + key).in(value);
                }
            });
        }
        if (MapUtils.isNotEmpty(bizRuleMappingBatchQueryReq.getToBizRule())) {
            bizRuleMappingBatchQueryReq.getToBizRule().forEach((key, value) ->{
                if(CollectionUtils.isNotEmpty(value)){
                    criteria.and(BizRuleMapping.Fields.toBizRule + PunConstant.DOT + key).in(value);
                }
            });
        }

        Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.modifyTime));
        PageRequest pageRequest = new PageRequest(bizRuleMappingBatchQueryReq.pageNum - 1, bizRuleMappingBatchQueryReq.pageSize, sort);

        Query query = Query.query(criteria);
        long count = mongoTemplate.count(query, BizRuleMapping.class);
        if (count > 0) {
            query.with(pageRequest);
            List<BizRuleMapping> bizRuleMappings = mongoTemplate.find(query, BizRuleMapping.class);
            return new PageImpl<>(bizRuleMappings, pageRequest, count);
        } else {
            return new PageImpl<>(Collections.emptyList(), pageRequest, count);
        }
    }


    @Override
    public BizRuleMapping save(BizRuleMapping bizRuleMapping) {
//        BizRuleMapping byTemplateIdAndAndDelIsTrue = bizRuleMappingRepository.findByTemplateIdAndAndDelIsTrue(bizRuleMapping.getTemplateId());
//        if (Objects.nonNull(byTemplateIdAndAndDelIsTrue)) {
//            throw new BusException("映射已存在");
//        }
        Date now = new Date();
        bizRuleMapping.setBizRuleMappingId(SnowFlakeGenerator.INSTANCE.generate());
        bizRuleMapping.setDel(false);
        // 首次添加的还不能使用
        // 原因是只添加了from的映射，还没有添加to的映射，还是一条不完整的数据
        // 2024-08-15 跟远康确认，添加的时候即PASS
        bizRuleMapping.setStatus(MappingStatusEnum.AUDIT.code);
        bizRuleMapping.setCreateTime(now);
        bizRuleMapping.setCreateBy(CommonUtils.getCurrentLoginName());
        bizRuleMapping.setModifyTime(now);
        bizRuleMapping.setUpdateBy(CommonUtils.getCurrentLoginName());
        if (Objects.isNull(bizRuleMapping.getFromBizRule())) {
            throw new BusException("请选择SKU");
        }
        BizRuleMapping save = bizRuleMappingRepository.save(bizRuleMapping);
//        applicationEventPublisher.publishEvent(new BuildIndexEvent(save));
        return save;
    }


    @Override
    public void batchAdd(List<BizRuleMappingBatchAddReq> reqList) {
        if (CollectionUtils.isEmpty(reqList)) {
            return;
        }
        Date now = new Date();
        List<BizRuleMapping> bizRuleMappings = reqList.stream().map(req -> {

            Map<String, String> fromBizRule = req.getFromBizRule();
            if (MapUtils.isEmpty(fromBizRule)) {
                throw new BusException("请选择SKU");
            }
            Long templateId = req.getTemplateId();
            if (templateId == null || templateId <= 0L) {
                throw new BusException("模板id不能为空 或 非正整数");
            }
            fromBizRule.put(CommonBizRule.Fields.templateId, templateId.toString());
            String fromServiceId = fromBizRule.get(CommonBizRule.Fields.serviceId);
            if (org.apache.commons.lang.StringUtils.isBlank(fromServiceId)) {
                throw new BusException("from的serviceId不能为空");
            }

            BizRuleMapping bizRuleMapping = new BizRuleMapping();
            BeanUtils.copyProperties(req, bizRuleMapping);
            bizRuleMapping.setBizRuleMappingId(SnowFlakeGenerator.INSTANCE.generate());
            bizRuleMapping.setDel(false);
            bizRuleMapping.setStatus(MappingStatusEnum.AUDIT.code);
            bizRuleMapping.setCreateTime(now);
            bizRuleMapping.setCreateBy(CommonUtils.getCurrentLoginName());
            bizRuleMapping.setModifyTime(now);
            bizRuleMapping.setUpdateBy(CommonUtils.getCurrentLoginName());
            return bizRuleMapping;
        }).collect(Collectors.toList());
        bizRuleMappingRepository.save(bizRuleMappings);
    }

    @Override
    public BizRuleMapping update(BizRuleMapping bizRuleMapping) {
        BizRuleMapping realBizRuleMapping = bizRuleMappingRepository.findByBizRuleMappingId(bizRuleMapping.getBizRuleMappingId());
        if (Objects.isNull(realBizRuleMapping)) {
            throw new BusException("映射不存在");
        }
        Map<String, String> fromBizRule = bizRuleMapping.getFromBizRule();
        Map<String, String> toBizRule = bizRuleMapping.getToBizRule();
        String fromServiceId = fromBizRule.get(CommonBizRule.Fields.serviceId);
        String toServiceId = toBizRule.get(CommonBizRule.Fields.serviceId);
        if (StringUtils.isBlank(fromServiceId) || StringUtils.isBlank(toServiceId)) {
            throw new BusException("serviceId不能为空");
        }

        checkMappingDuplication(bizRuleMapping);

        Date now = new Date();
        String sceneCode = bizRuleMapping.getSceneCode();
        if ("base_service_price".equals(sceneCode)) {
            sceneCode = fromBizRule.get(BizRuleMapping.Fields.sceneCode);
            toBizRule.put("mappingType", "基础服务计价属性");
        }
        realBizRuleMapping.setModifyTime(now);
        realBizRuleMapping.setUpdateBy(CommonUtils.getCurrentLoginName());
        realBizRuleMapping.setSceneName(bizRuleMapping.getSceneName());
        realBizRuleMapping.setSceneCode(sceneCode);
        realBizRuleMapping.setGroup(bizRuleMapping.getGroup());
        realBizRuleMapping.setFromBizRule(fromBizRule);
        realBizRuleMapping.setToBizRule(toBizRule);
        realBizRuleMapping.setApplyTypes(bizRuleMapping.getApplyTypes());
        // 设置to的时候，需要将status设置为0
        realBizRuleMapping.setStatus(MappingStatusEnum.PASS.code);
        //        applicationEventPublisher.publishEvent(new BuildIndexEvent(save));
        return bizRuleMappingRepository.save(realBizRuleMapping);
    }

    @Override
    public BizRuleMapping delete(Long skuMappingId) {
        BizRuleMapping byBizRuleMappingId = bizRuleMappingRepository.findByBizRuleMappingId(skuMappingId);
        if (Objects.isNull(byBizRuleMappingId)) {
            throw new BusException("映射不存在");
        }
        Date now = new Date();
        byBizRuleMappingId.setModifyTime(now);
        byBizRuleMappingId.setUpdateBy(CommonUtils.getCurrentLoginName());
        byBizRuleMappingId.setDel(true);

        return bizRuleMappingRepository.save(byBizRuleMappingId);
    }

    @Override
    public Set<String> getServiceIdBySceneCode(String fromSceneCode, String toSceneCode) {
        // 外层的sceneCode是to的
        Criteria criteria = Criteria.where(BizRuleMapping.Fields.sceneCode).is(toSceneCode)
                .and(BizRuleMapping.Fields.fromBizRule + PunConstant.DOT + BizRuleMapping.Fields.sceneCode).is(fromSceneCode)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(MappingStatusEnum.PASS.code);
        Query query = Query.query(criteria);
        query.fields().include(BizRuleMapping.Fields.fromBizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId);
        List<BizRuleMapping> bizRuleMappings = mongoTemplate.find(query, BizRuleMapping.class);
        if (CollectionUtils.isEmpty(bizRuleMappings)) {
            return Collections.emptySet();
        }
        Set<String> serviceIds = new HashSet<>();
        bizRuleMappings.forEach(bizRuleMapping -> {
            Map<String, String> fromBizRule = bizRuleMapping.getFromBizRule();
            serviceIds.add(fromBizRule.get(CommonBizRule.Fields.serviceId));
        });
        return serviceIds;
    }


    @Override
    public void setApplyTypes(BizRuleMappingApplyTypesReq req) {
        List<BizRuleMapping> byBizRuleMappingList = bizRuleMappingRepository.findBizRuleMappingByBizRuleMappingIdIn(req.getBizRuleMappingIdList());
        if (CollectionUtils.isEmpty(byBizRuleMappingList)) {
            throw new BusException("映射不存在");
        }
        byBizRuleMappingList.forEach(
                bizRuleMapping -> {
                    bizRuleMapping.setApplyTypes(req.getApplyTypeList());
                    bizRuleMapping.setUpdateBy(CommonUtils.getCurrentLoginName());
                    bizRuleMapping.setModifyTime(new Date());
                });
        bizRuleMappingRepository.save(byBizRuleMappingList);
    }


    @Override
    public Page<BizRuleMapping> queryPage(QueryPageReq req) {
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        if (StringUtils.isNotBlank(req.getFromSceneCode())) {
            criteria.and(BizRuleMapping.Fields.fromBizRule + PunConstant.DOT + CommonBizRule.Fields.sceneCode).is(req.getFromSceneCode());
        }
        if (StringUtils.isNotBlank(req.getToSceneCode())) {
            criteria.and(BizRuleMapping.Fields.sceneCode).is(req.getToSceneCode());
        }
        if (StringUtils.isNotBlank(req.getFromServiceName())) {
            criteria.and(BizRuleMapping.Fields.fromBizRule + PunConstant.DOT + CommonBizRule.Fields.serviceName)
                    .regex(PatternUtils.toFuzzySearch(PatternUtils.toEscapeStr(req.getFromServiceName())));
        }
        if (StringUtils.isNotBlank(req.getToServiceName())) {
            criteria.and(BizRuleMapping.Fields.toBizRule + PunConstant.DOT + CommonBizRule.Fields.serviceName)
                    .regex(PatternUtils.toFuzzySearch(PatternUtils.toEscapeStr(req.getToServiceName())));
        }

        if (StringUtils.isNotBlank(req.getFromSkuNo())) {
            criteria.and(BizRuleMapping.Fields.fromBizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(req.getFromSkuNo());
        }
        if (StringUtils.isNotBlank(req.getToSkuNo())) {
            criteria.and(BizRuleMapping.Fields.toBizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(req.getToSkuNo());
        }
        String applyType = req.getApplyType();
        if (StringUtils.isNotBlank(applyType)) {
            if ("none".equals(applyType)) {
                criteria.orOperator(Criteria.where(BizRuleMapping.Fields.applyTypes).exists(false),
                        Criteria.where(BizRuleMapping.Fields.applyTypes).is(null),
                        Criteria.where(BizRuleMapping.Fields.applyTypes).size(0));
            } else if (!"all".equals(applyType)) {
                criteria.and(BizRuleMapping.Fields.applyTypes).is(applyType);
            }
        }
        Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.modifyTime));
        PageRequest pageRequest = new PageRequest(req.getPageNum() - 1, req.getPageSize(), sort);
        Query query = Query.query(criteria);
        long count = mongoTemplate.count(query, BizRuleMapping.class);
        if (count > 0) {
            query.with(pageRequest);
            List<BizRuleMapping> bizRuleMappings = mongoTemplate.find(query, BizRuleMapping.class);
            return new PageImpl<>(bizRuleMappings, pageRequest, count);
        } else {
            return new PageImpl<>(Collections.emptyList(), pageRequest, count);
        }
    }


    @Override
    public BizRuleMapping queryByMappingId(Long mappingId) {
        return bizRuleMappingRepository.findByBizRuleMappingId(mappingId);
    }

    @Override
    public void copyBizRuleMapping(CopyBizRuleMappingReq req) {
        /*
        2025-02-18 改动，具体代码体现在getServiceId方法中
        1、from部分复制
        1.1 所选内容的from的场景sku类型，与复制到的from的场景sku相同，则将所选内容的服务ID对应写入目标对象的服务ID
        1.2 所选内容的from的场景sku类型，与复制到的from的场景sku不相同，则将所选内容的服务ID转化后（即 一口价转报价招标 或 反过来），对应写入目标对象的服务ID

        2、to部分复制
        2.1 目标toSceneCode的sku类型，与复制到的toSceneCode的sku类型相同，则将目标toSceneCode的serviceId写入目标对象的serviceId
        2.2 目标toSceneCode的sku类型，与复制到的toSceneCode的sku类型不相同，则将目标toSceneCode的serviceId转化后（即 一口价转报价招标 或 反过来），对应写入目标对象的serviceId
        2.3 如果复制到的to的场景为“基础服务计价属性”，维持现状逻辑
         */
        Set<Long> bizRuleMappingIds = req.getBizRuleMappingIds();
        String targetToSceneCode = req.getTargetToSceneCode();
        SceneInfo toSceneInfo = getSceneInfo(targetToSceneCode, "目标映射toSceneCode不合法，targetToSceneCode=");
        String targetFromSceneCode = req.getTargetFromSceneCode();
        SceneInfo fromSceneInfo = getSceneInfo(targetFromSceneCode, "目标映射fromSceneCode不合法，targetFromSceneCode=");

        Criteria criteria = Criteria.where(BizRuleMapping.Fields.bizRuleMappingId).in(bizRuleMappingIds)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(MappingStatusEnum.PASS.code);
        List<BizRuleMapping> bizRuleMappings = mongoTemplate.find(Query.query(criteria), BizRuleMapping.class);
        if (CollectionUtils.isEmpty(bizRuleMappings)) {
            throw new BusException("已选映射不存在或状态不为「审核通过」");
        }

        List<BizRuleMapping> newBizRuleMappings = new ArrayList<>();
        AtomicInteger failCount = new AtomicInteger();
        Date now = new Date();
        String currentLoginName = CommonUtils.getCurrentLoginName();
        for (BizRuleMapping bizRuleMapping : bizRuleMappings) {
            BizRuleMapping mapping = new BizRuleMapping();
            BeanUtils.copyProperties(bizRuleMapping, mapping);
            mapping.setBizRuleMappingId(null);
            mapping.setId(null);
            mapping.setCreateTime(now);
            mapping.setModifyTime(now);
            mapping.setCreateBy(currentLoginName);
            mapping.setUpdateBy(currentLoginName);

            // 处理fromBizRule

            Map<String, String> fromBizRule = mapping.getFromBizRule();
            Map<String, String> newFromBizRule = new HashMap<>(fromBizRule);
            String fromServiceId = getServiceId(bizRuleMapping, fromBizRule, fromSceneInfo.getSkuType(), "from");
            mapping.setFromBizRule(newFromBizRule);
            mapping.setSceneCode(targetFromSceneCode);
            mapping.setSceneName(fromSceneInfo.getSceneName());
            newFromBizRule.put(CommonBizRule.Fields.sceneCode, targetFromSceneCode);
            newFromBizRule.put(CommonBizRule.Fields.sceneName, fromSceneInfo.getSceneName());
            String fromSkuNo = newFromBizRule.get(CommonBizRule.Fields.skuNo);
            if (replaceTemplateId(targetFromSceneCode, fromServiceId, fromSkuNo, failCount, newFromBizRule, "from")) continue;

            // to部分，如果已选场景的是“基础服务计价属性”，则不进行替换。
            if (SceneCodeEnum.BASE_SERVICE_PRICE.getCode().equals(targetToSceneCode)) {
                newBizRuleMappings.add(mapping);
                continue;
            }

            /*
              1. 场景编码替换：如果已选的是“基础服务计价属性”，则不进行替换。否则，已选的to场景编码，替换成复制弹窗的to场景编码。
              2. templateId替换：如果已选的是“基础服务计价属性”，则不进行替换。否则，用目标映射的to_sku_no+from场景编码 查出对应的templateId，
              替换已选的to_templateId，如果查不出来，或者查出来的templateID有多个则不进行替换，并丢弃已选的映射，加入失败数量当中，提示复制映射成功X条，失败X条
             */
            // 处理toBizRule
            Map<String, String> toBizRule = mapping.getToBizRule();
            Map<String, String> newToBizRule = new HashMap<>(toBizRule);
            mapping.setToBizRule(newToBizRule);
            newToBizRule.put(BizRuleMapping.Fields.sceneCode, targetToSceneCode);
            newToBizRule.put(BizRuleMapping.Fields.sceneName, toSceneInfo.getSceneName());
            String toSkuNo = newToBizRule.get(CommonBizRule.Fields.skuNo);
            String toServiceId = getServiceId(bizRuleMapping, toBizRule, toSceneInfo.getSkuType(), "to");
            if (replaceTemplateId(targetToSceneCode, toServiceId, toSkuNo, failCount, newToBizRule, "to")) continue;

            newBizRuleMappings.add(mapping);

        }

        // 重复性校验
        if (CollectionUtils.isNotEmpty(newBizRuleMappings)) {
            Iterator<BizRuleMapping> iterator = newBizRuleMappings.iterator();
            while (iterator.hasNext()) {
                BizRuleMapping mapping = iterator.next();
                try {
                    checkMappingDuplication(mapping);
                    mapping.setBizRuleMappingId(SnowFlakeGenerator.INSTANCE.generate());
                } catch (BusException e) {
                    failCount.getAndIncrement();
                    log.warn("校验重复性失败，mapping={}, exceptionMessage={}", mapping, e.getMessage());
                    iterator.remove();
                }
            }
        }

        if (CollectionUtils.isNotEmpty(newBizRuleMappings)) {
            bizRuleMappingRepository.save(newBizRuleMappings);
        }

        if (failCount.get() > 0) {
            throw new BusException("复制映射成功" + newBizRuleMappings.size() + "条，失败" + failCount.get() + "条");
        }
    }

    private String getServiceId(BizRuleMapping bizRuleMapping, Map<String, String> bizRule, String mappingSkuType, String fromOrTo) {
        String sceneCode = bizRuleMapping.getFromBizRule().get(CommonBizRule.Fields.sceneCode);
        if (StringUtils.isBlank(sceneCode)) {
            throw new BusException("选中的bizRuleMapping的" + fromOrTo + "的sceneCode为空，历史数据问题，请联系开发");
        }
        SceneInfo sceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(sceneCode);
        if (sceneInfo == null) {
            throw new BusException("选中的bizRuleMapping的" + fromOrTo + "的sceneCode错误（sceneCode=" + sceneCode + "），历史数据问题，请联系开发");
        }
        String skuType = sceneInfo.getSkuType();
        if (StringUtils.isBlank(skuType)) {
            throw new BusException("选中的bizRuleMapping的" + fromOrTo + "的sceneCode对应的skuType为空，历史数据问题，请联系开发");
        }
        String fromServiceId = bizRule.get(CommonBizRule.Fields.serviceId);
        if (StringUtils.isBlank(fromServiceId)) {
            throw new BusException("选中的bizRuleMapping的" + fromOrTo + "的serviceId为空，历史数据问题，请联系开发");
        }
        if (!mappingSkuType.equals(skuType)) {
            ServiceDetail serviceDetail = serviceApi.getServiceDetailById(Long.valueOf(fromServiceId));
            if (serviceDetail == null) {
                throw new BusException("选中的bizRuleMapping的" + fromOrTo + "的serviceId已失效（serviceId=" + fromServiceId + "）");
            }
            Long serviceCategoryId = serviceDetail.getServiceCategoryId();
            List<ServiceDetail> serviceDetailList = serviceCategoryApi.getServiceDetailListByServiceCategoryId(serviceCategoryId);
            Long detailServiceId = serviceDetailList.stream()
                    .map(ServiceDetail::getServiceId)
                    .filter(serviceId -> !serviceId.equals(serviceDetail.getServiceId())).findFirst().orElse(null);
            if (detailServiceId == null) {
                throw new BusException("选中的bizRuleMapping的" + fromOrTo + "的serviceId没有对应的兄弟服务（serviceId=" + fromServiceId + "）");
            }
            fromServiceId = detailServiceId.toString();
        }
        return fromServiceId;
    }

    @Override
    public BizRuleMapping findOneByTemplateId(Long templateId) {
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .orOperator(
                        Criteria.where(BizRuleMapping.Fields.templateId).is(templateId),
                        Criteria.where(BizRuleMapping.Fields.toBizRule + PunConstant.DOT + CommonBizRule.Fields.templateId).is(templateId.toString()));
        return mongoTemplate.findOne(Query.query(criteria), BizRuleMapping.class);
    }


    @Override
    @Async
    public void flushBizRuleMappingToTemplateMapping() {
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(MappingStatusEnum.PASS.code);
        List<BizRuleMapping> bizRuleMappingList = mongoTemplate.find(Query.query(criteria), BizRuleMapping.class);
        if (CollectionUtils.isEmpty(bizRuleMappingList)) {
            log.info("没有需要同步的bizRuleMapping");
            return;
        }
//        List<FeeTemplateMapping> feeTemplateMappingList = new ArrayList<>();
        for (BizRuleMapping bizRuleMapping : bizRuleMappingList) {

            Long bizRuleMappingId = bizRuleMapping.getBizRuleMappingId();
            // 幂等
//            FeeTemplateMapping feeTemplateMapping = feeTemplateMappingRepository.findOneByMappingId(bizRuleMappingId);
//            if (feeTemplateMapping != null) {
//                log.info("bizRuleMappingId={}对应的feeTemplateMapping已存在，无需同步", bizRuleMappingId);
//                continue;
//            }

            FeeTemplateMapping mapping = new FeeTemplateMapping();
            BeanUtils.copyProperties(bizRuleMapping, mapping);
            mapping.setMappingId(bizRuleMappingId);

            FeeTemplateMapping.TemplateInfo target = new FeeTemplateMapping.TemplateInfo();
            mapping.setTarget(target);

            Map<String, String> toBizRule = bizRuleMapping.getToBizRule();
            Map<String, String> fromBizRule = bizRuleMapping.getFromBizRule();
            // TODO 挨个显式赋值，并且需要做好必填、唯一性校验，记录出错的数据。
            String sourceSceneCode = bizRuleMapping.getSceneCode();
            String fromSceneCode = fromBizRule.get(CommonBizRule.Fields.sceneCode);
            // 处理source
            if (toBizRule != null) {
                FeeTemplateMapping.TemplateInfo source = new FeeTemplateMapping.TemplateInfo();
                String sceneCode = toBizRule.get(CommonBizRule.Fields.sceneCode);
                if (StringUtils.isBlank(sceneCode)) {
                    sceneCode  = sourceSceneCode;
                }
                if (StringUtils.isBlank(sceneCode)) {
                    FlushDataLog dataLog = new FlushDataLog(bizRuleMappingId, "to的sceneCode为空，历史数据问题，请联系开发");
                    flushDataLogRepository.save(dataLog);
                    log.warn("to的sceneCode为空，历史数据问题，请联系开发");
                    continue;
                }
                if (SceneCodeEnum.BASE_SERVICE_PRICE.getCode().equals(sceneCode)) {
                    source.setSceneCode(SceneCodeEnum.BASE_SERVICE_PRICE.getCode());
                    source.setSceneName(SceneCodeEnum.BASE_SERVICE_PRICE.getName());
                } else {
                    SceneInfo targetToSceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(sceneCode);
                    if (targetToSceneInfo == null) {
                        FlushDataLog dataLog = new FlushDataLog(bizRuleMappingId, "to的sceneCode错误（sceneCode=" + sceneCode + "），历史数据问题，请联系开发");
                        flushDataLogRepository.save(dataLog);
                        log.warn("to的sceneCode错误（sceneCode={}），历史数据问题，请联系开发", sceneCode);
                        continue;
                    }
                    source.setSceneCode(targetToSceneInfo.getSceneCode());
                    source.setSceneName(targetToSceneInfo.getSceneName());
                    source.setSceneSkuType(targetToSceneInfo.getSkuType());
                }

                String sourceTemplateId = toBizRule.get(CommonBizRule.Fields.templateId);
                if (StringUtils.isNotBlank(sourceTemplateId)) {
                    source.setTemplateId(Long.valueOf(sourceTemplateId));
                    FeeTemplate template = feeTemplateRepository.findByTemplateId(source.getTemplateId());
                    if (template != null) {
                        source.setTemplateSkuType(template.getBizRule().get(CommonBizRule.Fields.skuType));
                    }
                }
                String serviceId = toBizRule.get(CommonBizRule.Fields.serviceId);
                if (StringUtils.isBlank(serviceId)) {
                    FlushDataLog dataLog = new FlushDataLog(bizRuleMappingId, "to的serviceId为空，历史数据问题，请联系开发");
                    flushDataLogRepository.save(dataLog);
                    log.warn("to的serviceId为空，历史数据问题，请联系开发");
                    continue;
                }
                source.setServiceId(serviceId);
                source.setServiceName(toBizRule.get(CommonBizRule.Fields.serviceName));
                source.setSkuNo(toBizRule.get(CommonBizRule.Fields.skuNo));
                source.setSkuNumberPathNo(toBizRule.get(CommonBizRule.Fields.skuNumberPathNo));
                source.setSkuAttributePathName(toBizRule.get(CommonBizRule.Fields.skuAttributePathName));
                source.setAttributeDisplayName(toBizRule.get(CommonBizRule.Fields.attributeDisplayName));
                source.setSkuNumberPathNo(toBizRule.get(CommonBizRule.Fields.skuNumberPathNo));
                source.setSkuNumberName(toBizRule.get(CommonBizRule.Fields.skuNumberName));
                source.setFeeUnit(toBizRule.get(CommonBizRule.Fields.feeUnit));

                // 重复性校验
                SaveBaseRequest request = new SaveBaseRequest();
                BeanUtils.copyProperties(source, request);
                List<FeeTemplateMapping> mappings = checkMappingDuplication(request, fromSceneCode);
                if (CollectionUtils.isNotEmpty(mappings)) {
                    FlushDataLog dataLog = new FlushDataLog(bizRuleMappingId, "待落库mappingId=" + mapping.getMappingId() + ", 与mappingId=" + mappings.stream().map(FeeTemplateMapping::getMappingId).collect(Collectors.toList()) + "重复，跳过");
                    flushDataLogRepository.save(dataLog);
                    log.warn("待落库mappingId={}, 与mappingId={}重复，跳过", mapping.getMappingId(), mappings.stream().map(FeeTemplateMapping::getMappingId).collect(Collectors.toList()));
                    continue;
                }

                mapping.setSource(source);
            }

            // 处理target，由于fromBizRule一定存在，所以无需判空

            if (StringUtils.isBlank(fromSceneCode)) {
                FlushDataLog dataLog = new FlushDataLog(bizRuleMappingId, "from的sceneCode为空，历史数据问题，请联系开发");
                flushDataLogRepository.save(dataLog);
                log.warn("from的sceneCode为空，历史数据问题，请联系开发");
                continue;
            }
            if (SceneCodeEnum.BASE_SERVICE_PRICE.getCode().equals(fromSceneCode)) {
                target.setSceneCode(SceneCodeEnum.BASE_SERVICE_PRICE.getCode());
                target.setSceneName(SceneCodeEnum.BASE_SERVICE_PRICE.getName());
            } else {
                SceneInfo sceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(fromSceneCode);
                if (sceneInfo == null) {
                    FlushDataLog dataLog = new FlushDataLog(bizRuleMappingId, "from的sceneCode错误（sceneCode=" + fromSceneCode + "），历史数据问题，请联系开发");
                    flushDataLogRepository.save(dataLog);
                    log.warn("from的sceneCode错误（sceneCode={}），历史数据问题，请联系开发", fromSceneCode);
                    continue;
                }
                target.setSceneCode(sceneInfo.getSceneCode());
                target.setSceneName(sceneInfo.getSceneName());
                target.setSceneSkuType(sceneInfo.getSkuType());
            }

            Long templateId = bizRuleMapping.getTemplateId();
            String targetTemplateId = fromBizRule.get(CommonBizRule.Fields.templateId);
            if (StringUtils.isBlank(targetTemplateId)) {
                targetTemplateId = templateId.toString();
            }
            if (StringUtils.isBlank(targetTemplateId)) {
                FlushDataLog dataLog = new FlushDataLog(bizRuleMappingId, "from的templateId为空，历史数据问题，请联系开发");
                flushDataLogRepository.save(dataLog);
                log.warn("from的templateId为空，历史数据问题，请联系开发");
                continue;
            }
            target.setTemplateId(Long.valueOf(targetTemplateId));
            FeeTemplate template = feeTemplateRepository.findByTemplateId(target.getTemplateId());
            if (template != null) {
                target.setTemplateSkuType(template.getBizRule().get(CommonBizRule.Fields.skuType));
            }
            String serviceId = fromBizRule.get(CommonBizRule.Fields.serviceId);
            if (StringUtils.isBlank(serviceId)) {
                FlushDataLog dataLog = new FlushDataLog(bizRuleMappingId, "from的serviceId为空，历史数据问题，请联系开发");
                flushDataLogRepository.save(dataLog);
                log.warn("from的serviceId为空，历史数据问题，请联系开发");
                continue;
            }
            target.setServiceId(serviceId);
            target.setServiceName(fromBizRule.get(CommonBizRule.Fields.serviceName));
            target.setSkuNo(fromBizRule.get(CommonBizRule.Fields.skuNo));
            target.setSkuNumberPathNo(fromBizRule.get(CommonBizRule.Fields.skuNumberPathNo));
            target.setSkuAttributePathName(fromBizRule.get(CommonBizRule.Fields.skuAttributePathName));
            target.setAttributeDisplayName(fromBizRule.get(CommonBizRule.Fields.attributeDisplayName));
            target.setSkuNumberPathNo(fromBizRule.get(CommonBizRule.Fields.skuNumberPathNo));
            target.setSkuNumberName(fromBizRule.get(CommonBizRule.Fields.skuNumberName));
            target.setFeeUnit(fromBizRule.get(CommonBizRule.Fields.feeUnit));

//            feeTemplateMappingList.add(mapping);

            feeTemplateMappingRepository.save(mapping);
        }
//        feeTemplateMappingRepository.save(feeTemplateMappingList);
    }


    private SceneInfo getSceneInfo(String targetToSceneCode, String exceptionMessage) {
        if (SceneCodeEnum.BASE_SERVICE_PRICE.getCode().equals(targetToSceneCode)) {
            SceneInfo sceneInfo = new SceneInfo();
            sceneInfo.setSceneCode(SceneCodeEnum.BASE_SERVICE_PRICE.getCode());
            sceneInfo.setSceneName(SceneCodeEnum.BASE_SERVICE_PRICE.getName());
            return sceneInfo;
        } else {
            SceneInfo targetToSceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(targetToSceneCode);
            if (targetToSceneInfo == null) {
                throw new BusException(exceptionMessage + targetToSceneCode);
            }
            return targetToSceneInfo;
        }
    }


    private boolean replaceTemplateId(String sceneCode, String serviceId, String skuNo, AtomicInteger failCount, Map<String, String> bizRule, String fromOrTo) {
        Criteria templateCriteria = Criteria.where(FeeTemplate.Fields.sceneCode).is(sceneCode)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(skuNo)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);
        List<FeeTemplate> feeTemplates = mongoTemplate.find(new Query(templateCriteria), FeeTemplate.class);
        if (CollectionUtils.isEmpty(feeTemplates)) {
            log.warn("找不到匹配的模板，{}SceneCode={},{}SkuNo={}", fromOrTo, sceneCode, fromOrTo, skuNo);
            failCount.getAndIncrement();
            return true;
        } else {
            int size = feeTemplates.size();
            if (size > 1) {
                log.warn("找到{}条匹配的模板，{}SceneCode={},{}SkuNo={}", size, fromOrTo, sceneCode, fromOrTo, skuNo);
                failCount.getAndIncrement();
                return true;
            }
            FeeTemplate feeTemplate = feeTemplates.get(0);
            bizRule.put(CommonBizRule.Fields.templateId, feeTemplate.getTemplateId().toString());
        }
        return false;
    }

    private void checkMappingDuplication(BizRuleMapping bizRuleMapping) {
        Map<String, String> toBizRule = bizRuleMapping.getToBizRule();
        String toSkuNo = toBizRule.get(CommonBizRule.Fields.skuNo);
        String toSceneCode = toBizRule.get(BizRuleMapping.Fields.sceneCode);
        if (StringUtils.isBlank(toSceneCode)) {
            toSceneCode = bizRuleMapping.getSceneCode();
        }
        if (StringUtils.isBlank(toSceneCode)) {
            throw new BusException("toSceneCode不能为空");
        }
        SceneInfo sceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(toSceneCode);
        String toSceneName = sceneInfo == null ? toSceneCode : sceneInfo.getSceneName();
        String serviceId = toBizRule.get(CommonBizRule.Fields.serviceId);
        String templateId = toBizRule.get(CommonBizRule.Fields.templateId);

        // to没有templateId，则需要校验toSceneCode+serviceId+skuNo是否已存在
        Criteria criteria = Criteria.where(BizRuleMapping.Fields.sceneCode).is(toSceneCode)
                .and(BizRuleMapping.Fields.toBizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                .and(BizRuleMapping.Fields.toBizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(toSkuNo)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(MappingStatusEnum.PASS.code);
        if (StringUtils.isBlank(templateId)) {
            criteria.and(BizRuleMapping.Fields.toBizRule + PunConstant.DOT + CommonBizRule.Fields.templateId).exists(false);
        } else {
            // to有templateId，则需要校验toSceneCode+serviceId+skuNo+templateId是否已存在
            criteria.and(BizRuleMapping.Fields.toBizRule + PunConstant.DOT + CommonBizRule.Fields.templateId).is(templateId);
        }
        List<BizRuleMapping> bizRuleMappings = mongoTemplate.find(Query.query(criteria), BizRuleMapping.class);

        if (CollectionUtils.isNotEmpty(bizRuleMappings)) {
            BizRuleMapping ruleMapping = bizRuleMappings.get(0);
            Map<String, String> fromBizRule = ruleMapping.getFromBizRule();
            throw new BusException("「场景：" + toSceneName + "，serviceId：" + serviceId + "，skuNo：" + toSkuNo + "」已存在映射->「"
                    + ruleMapping.getSceneName() + "+" + fromBizRule.get(CommonBizRule.Fields.serviceId)
                    + "+" + fromBizRule.get(CommonBizRule.Fields.skuNo) + "」");
        }
    }

    public List<FeeTemplateMapping> checkMappingDuplication(SaveBaseRequest saveBaseRequest, String targetSceneCode) {
        if (StringUtils.isBlank(targetSceneCode)) {
            return null;
        }
        Long templateId = saveBaseRequest.getTemplateId();
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(MappingStatusEnum.PASS.code)
                .and(FeeTemplateMapping.Fields.target + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.sceneCode).is(targetSceneCode);
        if (templateId != null) {
            // 若有templateId，则只需根据templateId判断重复性即可（如果是自定义SKU，则一定有templateId）
            criteria.and(FeeTemplateMapping.Fields.source + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.templateId).is(templateId);
        } else {
            // 若没有templateId，则根据source的sceneCode+serviceId+skuNo组合唯一查询（因为这种情况一定不存在于自定义SKU）
            criteria.and(FeeTemplateMapping.Fields.source + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.sceneCode).is(saveBaseRequest.getSceneCode())
                    .and(FeeTemplateMapping.Fields.source + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.serviceId).is(saveBaseRequest.getServiceId())
                    .and(FeeTemplateMapping.Fields.source + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.skuNo).is(saveBaseRequest.getSkuNo());
        }
        return mongoTemplate.find(Query.query(criteria), FeeTemplateMapping.class);
    }
}
