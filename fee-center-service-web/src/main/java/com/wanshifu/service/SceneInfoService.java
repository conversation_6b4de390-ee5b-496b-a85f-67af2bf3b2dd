package com.wanshifu.service;

import com.wanshifu.domain.request.SceneInfoQueryPageReq;
import com.wanshifu.fee.center.domain.document.SceneInfo;
import com.wanshifu.fee.center.domain.request.SceneInfoAddReq;
import com.wanshifu.fee.center.domain.request.SceneInfoModifyReq;
import com.wanshifu.fee.center.domain.request.scene.GetComparisonSceneCodeListReq;
import com.wanshifu.fee.center.domain.response.SceneListResp;
import com.wanshifu.fee.center.domain.response.scene.GetComparisonSceneCodeListResp;
import org.springframework.data.domain.Page;

import java.util.List;

public interface SceneInfoService {
    Page<SceneInfo> query(SceneInfoQueryPageReq sceneInfoQueryPageReq);
    SceneInfo query(Long sceneId);
    SceneInfo query(String sceneCode);
    List<SceneInfo> queryAll();
    SceneInfo add(SceneInfoAddReq sceneInfoAddReq);
    SceneInfo modify(SceneInfoModifyReq sceneInfoModifyReq);
    SceneInfo del(Long sceneInfoId);

    List<SceneListResp> sceneList();

    List<GetComparisonSceneCodeListResp> getComparisonSceneCodeList(GetComparisonSceneCodeListReq req);

    List<SceneInfo> selectByPullBigDataCustomSku();
}
