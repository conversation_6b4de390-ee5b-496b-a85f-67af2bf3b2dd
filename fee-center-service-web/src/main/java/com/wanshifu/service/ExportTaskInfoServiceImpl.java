package com.wanshifu.service;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.wanshifu.adapter.api.ServiceApi;
import com.wanshifu.adapter.dto.service.ServiceDetail;
import com.wanshifu.adapter.dto.service.ServiceQueryByGoodsCategoryReq;
import com.wanshifu.adapter.dto.service.ServiceQueryResp;
import com.wanshifu.adapter.enums.ServiceMode2Enum;
import com.wanshifu.api.FileUploadUtils;
import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.bean.FileUploadResp;
import com.wanshifu.consumer.TagEnum;
import com.wanshifu.domain.dto.table.BaseExportTable;
import com.wanshifu.domain.request.CrossSceneExportReq;
import com.wanshifu.domain.request.ExportTaskAddReq;
import com.wanshifu.domain.request.ExportTaskQueryReq;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.*;
import com.wanshifu.fee.center.domain.enums.*;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.framework.rocketmq.autoconfigure.model.NormalMessage;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.config.ApolloConfig;
import com.wanshifu.infrastructure.utils.CommonUtils;
import com.wanshifu.repository.ExportTaskInfoRepository;
import com.wanshifu.repository.SceneInfoRepository;
import com.wanshifu.strategy.batch.InstructionWriteHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ExportTaskInfoServiceImpl implements ExportTaskInfoService{

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private ExportTaskInfoRepository exportTaskInfoRepository;
    @Resource
    private SceneInfoRepository sceneInfoRepository;

    @Resource
    private RocketMqSendService rocketMqSendService;

    @Value("${wanshifu.rocketMQ.fee-center-service-general-topic}")
    private String feeCenterServiceGeneralTopic;
    @Autowired
    private AddressApi addressApi;
    @Resource
    private ServiceApi serviceApi;
//    @Resource
//    private ApplicationContext applicationContext;

    @Qualifier("autoAdjustThreadPool")
    @Autowired
    private ExecutorService executorService;

//    private volatile ExportTaskInfoService thisProxy;

//    @PostConstruct
//    private void initProxy() {
//        if (thisProxy == null) {
//            synchronized (this) {
//                if (thisProxy == null) {
//                    thisProxy = applicationContext.getBean(ExportTaskInfoServiceImpl.class);
//                }
//            }
//        }
//    }


//    private ExportTaskInfoService getProxy() {
//        if (thisProxy == null) {
//            synchronized (this) {
//                if (thisProxy == null) {
//                    thisProxy = applicationContext.getBean(ExportTaskInfoServiceImpl.class);
//                }
//            }
//        }
//        return thisProxy;
//    }

    @Override
    public Page<ExportTaskInfo> queryByCondition(ExportTaskQueryReq exportTaskQueryReq) {
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        if(StringUtils.isNotBlank(exportTaskQueryReq.getSceneCode())) {
            criteria.and(ExportTaskInfo.Fields.sceneCode).is(exportTaskQueryReq.getSceneCode());
        }

        Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.createTime));
        PageRequest pageRequest = new PageRequest(exportTaskQueryReq.pageNum - 1, exportTaskQueryReq.pageSize, sort);

        Query query = Query.query(criteria);
        long count = mongoTemplate.count(query, ExportTaskInfo.class);
        if (count > 0) {
            query.with(pageRequest);
            List<ExportTaskInfo> exportTaskInfos = mongoTemplate.find(query, ExportTaskInfo.class);
            return new PageImpl<>(exportTaskInfos, pageRequest, count);
        } else {
            return new PageImpl<>(Collections.emptyList(), pageRequest, count);
        }
    }

    @Override
    public ExportTaskInfo addTask(ExportTaskAddReq exportTaskAddReq) {
        SceneInfo sceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(exportTaskAddReq.getSceneCode());
        if (Objects.isNull(sceneInfo)){
            throw new BusException("场景不存在");
        }


        ExportTaskInfo exportTaskInfo = new ExportTaskInfo();
        Date now = new Date();
        exportTaskInfo.setExportTaskId(SnowFlakeGenerator.INSTANCE.generate());
        exportTaskInfo.setDel(false);
        // 首次添加的还不能使用
        exportTaskInfo.setStatus(ExportTaskStatusEnum.EXPORT.code);
        exportTaskInfo.setCreateTime(now);
        exportTaskInfo.setModifyTime(now);
        exportTaskInfo.setSceneCode(sceneInfo.getSceneCode());
        exportTaskInfo.setSceneName(sceneInfo.getSceneName());

        DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(exportTaskAddReq.getDivisionType());
        if(Objects.nonNull(divisionTypeEnum)){
            exportTaskInfo.setDivisionType(divisionTypeEnum.code);
            exportTaskInfo.setDivisionTypeName(divisionTypeEnum.name);
        }

        BizIdTypeEnum bizIdTypeEnum = BizIdTypeEnum.fromCode(sceneInfo.getBizIdType());

//        exportTaskInfo.setOperator(exportTaskAddReq.getOperator());
        exportTaskInfo.setOperator(CommonUtils.getCurrentLoginName());
        if ( (Objects.isNull(bizIdTypeEnum) || bizIdTypeEnum == BizIdTypeEnum.EMPTY) && CollectionUtils.isNotEmpty(exportTaskAddReq.getBizIds())){
            throw new BusException("该场景不支持业务id");
        }

        if( !(Objects.isNull(bizIdTypeEnum) || bizIdTypeEnum == BizIdTypeEnum.EMPTY) && CollectionUtils.isEmpty(exportTaskAddReq.getBizIds())){
            throw new BusException("业务id为必填项");
        }

        if( !(Objects.isNull(bizIdTypeEnum) || bizIdTypeEnum == BizIdTypeEnum.EMPTY) && Objects.isNull(divisionTypeEnum)){
            throw new BusException("地区维度为必填项");
        }

        if((Objects.isNull(bizIdTypeEnum) || bizIdTypeEnum == BizIdTypeEnum.EMPTY) && CollectionUtils.isEmpty(exportTaskAddReq.getServiceIds())){
            throw new BusException("服务为必填项");
        }


        if(CollectionUtils.isNotEmpty(exportTaskAddReq.getBizIds())){
            if (Objects.isNull(bizIdTypeEnum) || bizIdTypeEnum == BizIdTypeEnum.EMPTY){
                throw new BusException("该场景不支持业务id");
            }
            if(divisionTypeEnum == DivisionTypeEnum.STREET && exportTaskAddReq.getBizIds().size() > 200){
                throw new BusException("街道维度最多只支持200个"+bizIdTypeEnum.name);
            }
            if(exportTaskAddReq.getBizIds().size() > 2000){
                throw new BusException("最多支持2000个"+bizIdTypeEnum.name);
            }
            exportTaskInfo.setBizIds(exportTaskAddReq.getBizIds());
        }

        if(CollectionUtils.isEmpty(exportTaskAddReq.getServiceIds())){
            throw new BusException("至少选择一项服务");
        }
        exportTaskInfo.setServiceIds(exportTaskAddReq.getServiceIds());

        ExportTaskInfo save = exportTaskInfoRepository.save(exportTaskInfo);

        rocketMqSendService.sendSyncTransactionMessage(new NormalMessage(save.getExportTaskId(), UUID.randomUUID().toString(),feeCenterServiceGeneralTopic, TagEnum.EXPORT_TASK.tag, JSON.toJSONString(save)));

        return save;

    }

    @Override
    public String resend(Set<Long> ids) {
        Criteria criteria = Criteria.where(ExportTaskInfo.Fields.exportTaskId).in(ids)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(ExportTaskStatusEnum.EXPORT.code);

        Query query = Query.query(criteria);
        List<ExportTaskInfo> exportTaskInfos = mongoTemplate.find(query, ExportTaskInfo.class);
        for (ExportTaskInfo exportTaskInfo : exportTaskInfos) {
            log.info("resend message exportTaskId={}",exportTaskInfo.getExportTaskId());
            rocketMqSendService.sendSyncTransactionMessage(new NormalMessage(exportTaskInfo.getExportTaskId(), UUID.randomUUID().toString(),feeCenterServiceGeneralTopic, TagEnum.EXPORT_TASK.tag, JSON.toJSONString(exportTaskInfo)));
            log.info("resend message exportTaskId={} success",exportTaskInfo.getExportTaskId());
        }

        return "success";
    }


    @Override
    public ExportTaskInfo crossSceneExport(CrossSceneExportReq req) {

         /*
          分3种情况处理：
          1、当from与to的场景配置的skuType相同，则说明serviceId相同，这种情况可以再分2种情况
            1.1 当为标准sku时，则直接from与to直接匹配即可；
            1.2 当为自定义sku时，则需要根据映射查找templateId；
          2、当from与to的场景配置的skuType不同，则说明serviceId不同，需要根据映射查找templateId；
         */
        String fromSceneCode = req.getFromSceneCode();
        String toSceneCode = req.getToSceneCode();
        String divisionType = req.getDivisionType();
        SceneInfo fromSceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(fromSceneCode);
        SceneInfo toSceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(toSceneCode);

        ExportTaskInfo exportTaskInfo = new ExportTaskInfo();
        Date now = new Date();
        exportTaskInfo.setExportTaskId(SnowFlakeGenerator.INSTANCE.generate());
        exportTaskInfo.setDel(false);
        // 首次添加的还不能使用
        exportTaskInfo.setStatus(ExportTaskStatusEnum.EXPORT.code);
        exportTaskInfo.setCreateTime(now);
        exportTaskInfo.setModifyTime(now);
        exportTaskInfo.setSceneCode(toSceneInfo.getSceneCode());
        exportTaskInfo.setSceneName(toSceneInfo.getSceneName());
        exportTaskInfo.setFromSceneCode(fromSceneInfo.getSceneCode());
        exportTaskInfo.setFromSceneName(fromSceneInfo.getSceneName());

        DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(req.getDivisionType());

        if (divisionTypeEnum == null) {
            throw new BusException("价格维度异常：divisionType=" + divisionType);
        }
        exportTaskInfo.setDivisionType(divisionTypeEnum.code);
        exportTaskInfo.setDivisionTypeName(divisionTypeEnum.name);
        exportTaskInfo.setOperator(req.getOperator());

        exportTaskInfoRepository.save(exportTaskInfo);

        CompletableFuture.runAsync(() -> doCrossSceneExport(req, exportTaskInfo), executorService)
                .thenRun(() -> log.info("exportTaskId={}, 导出成功", exportTaskInfo.getExportTaskId()))
                .exceptionally(e -> {
                    log.error("exportTaskId={},导出失败", exportTaskInfo.getExportTaskId(), e);
                    exportTaskInfo.setError("导出失败:" + e.getMessage().replace("com.wanshifu.framework.core.BusException:", ""));
                    exportTaskInfo.setStatus(ExportTaskStatusEnum.FAIL.code);
                    exportTaskInfoRepository.save(exportTaskInfo);
                    return null;
                });
        return exportTaskInfo;
    }


//    @Async
    private void doCrossSceneExport(CrossSceneExportReq req, ExportTaskInfo exportTaskInfo) {
        String fromSceneCode = req.getFromSceneCode();
        String toSceneCode = req.getToSceneCode();
        String divisionType = req.getDivisionType();
        List<String> serviceIds = req.getServiceIds();
        SceneInfo fromSceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(fromSceneCode);
        SceneInfo toSceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(toSceneCode);
        String fromSkuType = fromSceneInfo.getSkuType();
        String toSkuType = toSceneInfo.getSkuType();
        DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(req.getDivisionType());
        if (divisionTypeEnum == null) {
            throw new BusException("价格维度异常：divisionType=" + divisionType);
        }

        List<BaseExportTable> baseExportTables = new ArrayList<>();

        Criteria fromCriteria = Criteria.where(FeeRule.Fields.sceneCode).is(fromSceneCode)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(divisionType)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).in(serviceIds)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code);
        long count = mongoTemplate.count(Query.query(fromCriteria), FeeRule.class);
        if (count <= 0) {
            throw new BusException("当前条件查不到可以导出的费用");
        }

        for (String serviceId : serviceIds) {
            fromCriteria = Criteria.where(FeeRule.Fields.sceneCode).is(fromSceneCode)
                    .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(divisionType)
                    .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                    .and(BaseDocument.Fields.del).is(false)
                    .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code);

            Query query = Query.query(fromCriteria);
            List<FeeRule> feeRules = mongoTemplate.find(query, FeeRule.class);
            if (CollectionUtils.isEmpty(feeRules)) {
                continue;
            }
            Set<Long> divisionIds = new HashSet<>();
            feeRules.forEach(e -> {
                Map<String, String> bizRule = e.getBizRule();
                addDivisionIdIfPresent(divisionIds, bizRule, CommonBizRule.Fields.level1DivisionId);
                addDivisionIdIfPresent(divisionIds, bizRule, CommonBizRule.Fields.level2DivisionId);
                addDivisionIdIfPresent(divisionIds, bizRule, CommonBizRule.Fields.level3DivisionId);
                addDivisionIdIfPresent(divisionIds, bizRule, CommonBizRule.Fields.level4DivisionId);
            });

            Map<Long, Address> addressMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(divisionIds)) {
                List<Address> addressList = addressApi.getDivisionInfoListByDivisionIds(StringUtils.join(divisionIds, ","));
                if (CollectionUtils.isNotEmpty(addressList)) {
                    addressMap = addressList.stream().collect(Collectors.toMap(Address::getDivisionId, e -> e));
                }
            }

            if (fromSkuType.equals(toSkuType)) {
                Criteria toCriteria = Criteria.where(FeeTemplate.Fields.sceneCode).is(toSceneCode)
                        .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                        .and(BaseDocument.Fields.del).is(false)
                        .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);
                List<FeeTemplate> feeTemplates = mongoTemplate.find(Query.query(toCriteria), FeeTemplate.class);
                if (CollectionUtils.isEmpty(feeTemplates)) {
                    continue;
                }
                // 标准sku
                Map<String, FeeTemplate> skuNoTemplateMap = feeTemplates.stream()
                        .filter(e -> FeeSkuTypeEnum.STANDARD_SKU.code.equals(e.getBizRule().get(CommonBizRule.Fields.skuType)))
                        .collect(Collectors.toMap(e -> e.getBizRule().get(CommonBizRule.Fields.skuNo), e -> e));

                for (FeeRule feeRule : feeRules) {
                    Map<String, String> feeBizRule = feeRule.getBizRule();
                    String skuNo = feeBizRule.get(CommonBizRule.Fields.skuNo);
                    FeeTemplate feeTemplate = skuNoTemplateMap.get(skuNo);
                    if (feeTemplate == null) {
                        continue;
                    }

                    String province = getDivisionName(feeBizRule, addressMap, CommonBizRule.Fields.level1DivisionId);
                    String city = getDivisionName(feeBizRule, addressMap, CommonBizRule.Fields.level2DivisionId);
                    String district = getDivisionName(feeBizRule, addressMap, CommonBizRule.Fields.level3DivisionId);
                    String street = getDivisionName(feeBizRule, addressMap, CommonBizRule.Fields.level4DivisionId);

                    Map<String, String> templateBizRule = feeTemplate.getBizRule();
                    templateBizRule.put(CommonBizRule.Fields.templateId, feeTemplate.getTemplateId().toString());
                    BaseExportTable baseExportTable = getBaseExportTable(templateBizRule, feeBizRule, divisionTypeEnum, province, city, district, street);
                    baseExportTables.add(baseExportTable);
                }

                // 自定义sku
                List<String> skuNoList = feeTemplates.stream()
                        .filter(e -> FeeSkuTypeEnum.CUSTOM_SKU.code.equals(e.getBizRule().get(CommonBizRule.Fields.skuType)))
                        .map(e -> e.getBizRule().get(CommonBizRule.Fields.skuNo)).collect(Collectors.toList());

                if (ApolloConfig.NEW_TEMPLATE_MAPPING_SWITCH) {
                    Criteria mappingCriteria = Criteria.where(FeeTemplateMapping.Fields.target + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.sceneCode).is(fromSceneCode)
                            .and(FeeTemplateMapping.Fields.target + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.serviceId).is(serviceId)
                            .and(FeeTemplateMapping.Fields.target + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.skuNo).in(skuNoList)
                            .and(FeeTemplateMapping.Fields.applyTypes).is(ApplyTypeEnum.PRICE_SYNC.getCode())
                            .and(BaseDocument.Fields.del).is(false)
                            .and(BaseDocument.Fields.status).is(MappingStatusEnum.PASS.code);

                    Sort sort = new Sort(Sort.Direction.ASC, BaseDocument.Fields.modifyTime);
                    Query mappingQuery = Query.query(mappingCriteria);
                    mappingQuery.with(sort);
                    List<FeeTemplateMapping> mappings = mongoTemplate.find(mappingQuery, FeeTemplateMapping.class);
                    if (CollectionUtils.isEmpty(mappings)) {
                        continue;
                    }
                    List<BaseExportTable> tables = setBaseExportTablesNew(feeRules, mappings, baseExportTables, addressMap, divisionTypeEnum);
                    baseExportTables.addAll(tables);
                } else {
                    Criteria mappingCriteria = Criteria.where(BizRuleMapping.Fields.fromBizRule + PunConstant.DOT + CommonBizRule.Fields.sceneCode).is(fromSceneCode)
                            .and(BizRuleMapping.Fields.fromBizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                            .and(BizRuleMapping.Fields.fromBizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).in(skuNoList)
                            .and(BizRuleMapping.Fields.applyTypes).is(ApplyTypeEnum.PRICE_SYNC.getCode())
                            .and(BaseDocument.Fields.del).is(false)
                            .and(BaseDocument.Fields.status).is(MappingStatusEnum.PASS.code);

                    Sort sort = new Sort(Sort.Direction.ASC, BaseDocument.Fields.modifyTime);
                    Query mappingQuery = Query.query(mappingCriteria);
                    mappingQuery.with(sort);
                    List<BizRuleMapping> mappings = mongoTemplate.find(mappingQuery, BizRuleMapping.class);
                    if (CollectionUtils.isEmpty(mappings)) {
                        continue;
                    }
                    List<BaseExportTable> tables = setBaseExportTables(feeRules, mappings, baseExportTables, addressMap, divisionTypeEnum);
                    baseExportTables.addAll(tables);
                }


            } else {
                // skuType不同，即需要通过serviceModelId过渡，找到serviceId，即如果 一口价 -> 报价招标
                ServiceDetail service = serviceApi.getServiceDetailById(Long.valueOf(serviceId));
                if (service == null) {
                    continue;
                }
                ServiceQueryByGoodsCategoryReq goodsCategoryReq = getServiceQueryByGoodsCategoryReq(service);
                ServiceQueryResp serviceQueryResp = serviceApi.serviceQueryByGoodsCategory(goodsCategoryReq);
                if (serviceQueryResp == null) {
                    continue;
                }
                Long toServiceId = serviceQueryResp.getServiceId();

                if (ApolloConfig.NEW_TEMPLATE_MAPPING_SWITCH) {
                    Criteria mappingCriteria = Criteria.where(FeeTemplateMapping.Fields.target + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.sceneCode).is(fromSceneCode)
                            .and(FeeTemplateMapping.Fields.target + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.serviceId).is(serviceId)
                            .and(FeeTemplateMapping.Fields.source + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.sceneCode).is(toSceneCode)
                            .and(FeeTemplateMapping.Fields.source + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.serviceId).is(toServiceId.toString())
                            .and(FeeTemplateMapping.Fields.applyTypes).is(ApplyTypeEnum.PRICE_SYNC.getCode())
                            .and(BaseDocument.Fields.del).is(false)
                            .and(BaseDocument.Fields.status).is(MappingStatusEnum.PASS.code);

                    query = Query.query(mappingCriteria);
                    query.with(new Sort(Sort.Direction.ASC, BaseDocument.Fields.modifyTime));
                    List<FeeTemplateMapping> mappings = mongoTemplate.find(query, FeeTemplateMapping.class);
                    if (CollectionUtils.isEmpty(mappings)) {
                        continue;
                    }
                    List<BaseExportTable> tables = setBaseExportTablesNew(feeRules, mappings, baseExportTables, addressMap, divisionTypeEnum);
                    baseExportTables.addAll(tables);
                } else {
                    Criteria mappingCriteria = Criteria.where(BizRuleMapping.Fields.fromBizRule + PunConstant.DOT + CommonBizRule.Fields.sceneCode).is(fromSceneCode)
                            .and(BizRuleMapping.Fields.fromBizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                            .and(BizRuleMapping.Fields.sceneCode).is(toSceneCode)
                            .and(BizRuleMapping.Fields.toBizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(toServiceId.toString())
                            .and(BizRuleMapping.Fields.applyTypes).is(ApplyTypeEnum.PRICE_SYNC.getCode())
                            .and(BaseDocument.Fields.del).is(false)
                            .and(BaseDocument.Fields.status).is(MappingStatusEnum.PASS.code);

                    query = Query.query(mappingCriteria);
                    query.with(new Sort(Sort.Direction.ASC, BaseDocument.Fields.modifyTime));
                    List<BizRuleMapping> mappings = mongoTemplate.find(query, BizRuleMapping.class);
                    if (CollectionUtils.isEmpty(mappings)) {
                        continue;
                    }
                    List<BaseExportTable> tables = setBaseExportTables(feeRules, mappings, baseExportTables, addressMap, divisionTypeEnum);
                    baseExportTables.addAll(tables);
                }
            }

        }

        if (CollectionUtils.isEmpty(baseExportTables)) {
            throw new BusException("当前条件查不到可以导出的费用（映射）");
        }

        // 导出为表格
        log.info("exportTaskId={},正在导出表格,共{}条", exportTaskInfo.getExportTaskId(), baseExportTables.size());

        // 1. 构造说明文字
        String instruction = "填写说明:\n" +
                "1、业务ID,如果是商家价格,填写商家ID:如果是师傅价格,填写师傅ID\n" +
                "2、如果价格全国统一,省市区街道不填;如果是城市定价请填写省市;不同省市请自行复制添加一行,区县/街道价格以此类推\n" +
                "3、单价必须填写,即该服务sku的基础价格;单价max仅应用于期间价,特殊场合下填写\n" +
                "4、起步价选填,建议30元起,即一个订单的最低价格\n" +
                "5、白名单属性仅针对定制属性的商家,先检查所属配置的价格是不是白名单商家,如果是,则用白名单属性的sku配置置价格";

        // 3. 用反射 + 注解过滤真实要写出的列数
        Field[] allFields = BaseExportTable.class.getDeclaredFields();
        int columnCount = (int) Arrays.stream(allFields)
                // 必须有 @ExcelProperty
                .filter(f -> f.isAnnotationPresent(ExcelProperty.class))
                .count();

        ByteArrayOutputStream fileBytes = new ByteArrayOutputStream();
        try (ExcelWriter writer = EasyExcel.write(fileBytes, BaseExportTable.class)
                .registerWriteHandler(new InstructionWriteHandler(instruction, columnCount))
                .build()) {

            // sheet 名称用场景名称
            WriteSheet sheet = EasyExcel.writerSheet(fromSceneInfo.getSceneName())
                    .relativeHeadRowIndex(1)
                    .build();
            writer.write(baseExportTables, sheet);
            writer.finish();
        }

        // 存入结果
        String extend = "xlsx";
        String fileName = toSceneInfo.getSceneName() + "价格导出" + DateUtils.formatDateTime(exportTaskInfo.getCreateTime());
        log.info("exportTaskId={},正在上传文件", exportTaskInfo.getExportTaskId());
        FileUploadResp xlsx = FileUploadUtils.upload(fileBytes.toByteArray(), extend);
        if (xlsx.isSuccess()) {
            exportTaskInfo.setFileUrl(xlsx.getData().getFileUrl());
            exportTaskInfo.setFileId(xlsx.getData().getFileId());
            exportTaskInfo.setFileName(fileName + PunConstant.DOT + extend);
            exportTaskInfo.setError(null);
            exportTaskInfo.setStatus(ExportTaskStatusEnum.SUCCESS.code);
            log.info("exportTaskId={},导出完成，共{}条数据", exportTaskInfo.getExportTaskId(), count);
        } else {
            exportTaskInfo.setError("上传文件失败:" + xlsx.getMsg());
            exportTaskInfo.setStatus(ExportTaskStatusEnum.FAIL.code);
            log.info("exportTaskId={},导出失败，共{}条数据, error={}", exportTaskInfo.getExportTaskId(), count, xlsx.getMsg());
        }
        exportTaskInfoRepository.save(exportTaskInfo);
    }

    private List<BaseExportTable> setBaseExportTables(List<FeeRule> feeRules, List<BizRuleMapping> mappings, List<BaseExportTable> baseExportTables, Map<Long, Address> addressMap, DivisionTypeEnum divisionTypeEnum) {
        Map<Long, List<FeeRule>> feeRuleMap = feeRules.stream().collect(Collectors.groupingBy(FeeRule::getTemplateId));

        for (BizRuleMapping mapping : mappings) {
            Map<String, String> toBizRule = mapping.getToBizRule();
//            toBizRule.put(CommonBizRule.Fields.templateId, mapping.getTemplateId().toString());
            String toSkuNo = toBizRule.get(CommonBizRule.Fields.skuNo);
            Map<String, String> fromBizRule = mapping.getFromBizRule();
            Long fromTemplateId = Long.valueOf(fromBizRule.get(CommonBizRule.Fields.templateId));
            List<FeeRule> feeRuleList = feeRuleMap.get(fromTemplateId);
            if (CollectionUtils.isNotEmpty(feeRuleList)) {
                // 映射优先，故需要根据skuNo(由于是相同的skuType，即要么是一口价，要么是报价，且为标准sku，故可以根据skuNo)将已经存入baseExportTables的过滤掉
                baseExportTables = baseExportTables.stream().filter(e -> !toSkuNo.equals(e.getSkuNo())).collect(Collectors.toList());
                for (FeeRule feeRule : feeRuleList) {
                    Map<String, String> feeBizRule = feeRule.getBizRule();
                    String province = getDivisionName(feeBizRule, addressMap, CommonBizRule.Fields.level1DivisionId);
                    String city = getDivisionName(feeBizRule, addressMap, CommonBizRule.Fields.level2DivisionId);
                    String district = getDivisionName(feeBizRule, addressMap, CommonBizRule.Fields.level3DivisionId);
                    String street = getDivisionName(feeBizRule, addressMap, CommonBizRule.Fields.level4DivisionId);
                    BaseExportTable baseExportTable = getBaseExportTable(toBizRule, feeBizRule, divisionTypeEnum, province, city, district, street);
                    baseExportTables.add(baseExportTable);
                }
            }
        }
        return baseExportTables;
    }


    private List<BaseExportTable> setBaseExportTablesNew(List<FeeRule> feeRules, List<FeeTemplateMapping> mappings, List<BaseExportTable> baseExportTables, Map<Long, Address> addressMap, DivisionTypeEnum divisionTypeEnum) {
        Map<Long, List<FeeRule>> feeRuleMap = feeRules.stream().collect(Collectors.groupingBy(FeeRule::getTemplateId));

        for (FeeTemplateMapping mapping : mappings) {
            FeeTemplateMapping.TemplateInfo source = mapping.getSource();
            String sourceSkuNo = source.getSkuNo();
            FeeTemplateMapping.TemplateInfo target = mapping.getTarget();
            Long targetTemplateId = target.getTemplateId();
            List<FeeRule> feeRuleList = feeRuleMap.get(targetTemplateId);
            if (CollectionUtils.isNotEmpty(feeRuleList)) {
                // 映射优先，故需要根据skuNo(由于是相同的skuType，即要么是一口价，要么是报价，且为标准sku，故可以根据skuNo)将已经存入baseExportTables的过滤掉
                baseExportTables = baseExportTables.stream().filter(e -> !sourceSkuNo.equals(e.getSkuNo())).collect(Collectors.toList());
                for (FeeRule feeRule : feeRuleList) {
                    Map<String, String> feeBizRule = feeRule.getBizRule();
                    String province = getDivisionName(feeBizRule, addressMap, CommonBizRule.Fields.level1DivisionId);
                    String city = getDivisionName(feeBizRule, addressMap, CommonBizRule.Fields.level2DivisionId);
                    String district = getDivisionName(feeBizRule, addressMap, CommonBizRule.Fields.level3DivisionId);
                    String street = getDivisionName(feeBizRule, addressMap, CommonBizRule.Fields.level4DivisionId);
                    BaseExportTable baseExportTable = getBaseExportTableNew(source, feeBizRule, divisionTypeEnum, province, city, district, street);
                    baseExportTables.add(baseExportTable);
                }
            }
        }
        return baseExportTables;
    }

    private ServiceQueryByGoodsCategoryReq getServiceQueryByGoodsCategoryReq(ServiceDetail service) {
        Long serviceModelId = service.getServiceModelId();
        Long toServiceModelId;
        if (ServiceMode2Enum.DEFINITE_PRICE.getServiceModeId().equals(serviceModelId)) {
            toServiceModelId = ServiceMode2Enum.OFFER.getServiceModeId();
        } else if (ServiceMode2Enum.OFFER.getServiceModeId().equals(serviceModelId)) {
            toServiceModelId = ServiceMode2Enum.DEFINITE_PRICE.getServiceModeId();
        } else {
            throw new BusException("不支持的服务模式, serviceModeId=" + serviceModelId);
        }
        Long goodsCategoryId = service.getGoodsCategoryId();
        Long serviceTypeId = service.getServiceTypeId();
        ServiceQueryByGoodsCategoryReq goodsCategoryReq = new ServiceQueryByGoodsCategoryReq();
        goodsCategoryReq.setGoodsCategoryId(goodsCategoryId);
        goodsCategoryReq.setServiceModelId(toServiceModelId);
        goodsCategoryReq.setServiceTypeId(serviceTypeId);
        return goodsCategoryReq;
    }

    private String getDivisionName(Map<String, String> feeBizRule, Map<Long, Address> addressMap, String divisionLevel) {
        String divisionName = null;
        String provinceId = feeBizRule.get(divisionLevel);
        if (StringUtils.isNotBlank(provinceId)) {
            Address address = addressMap.get(Long.valueOf(provinceId));
            if (address != null) {
                divisionName = address.getDivisionName();
            }
        }
        return divisionName;
    }

    private BaseExportTable getBaseExportTable(Map<String, String> templateBizRule, Map<String, String> feeBizRule, DivisionTypeEnum divisionTypeEnum, String province, String city, String district, String street) {
        BaseExportTable baseExportTable = new BaseExportTable();
        // 替换 serviceId、serviceName、skuNo、templateId、attributeDisplayName
        baseExportTable.setServiceId(templateBizRule.get(CommonBizRule.Fields.serviceId));
        baseExportTable.setServiceName(templateBizRule.get(CommonBizRule.Fields.serviceName));
        baseExportTable.setSkuNo(templateBizRule.get(CommonBizRule.Fields.skuNo));
        baseExportTable.setTemplateId(templateBizRule.get(CommonBizRule.Fields.templateId));
        baseExportTable.setAttributeDisplayName(templateBizRule.get(CommonBizRule.Fields.attributeDisplayName));
        baseExportTable.setBasePrice(feeBizRule.get(CommonBizRule.Fields.basePrice));
        baseExportTable.setBizTag(feeBizRule.get(CommonBizRule.Fields.bizTag));
        baseExportTable.setMasterInputPrice(feeBizRule.get(CommonBizRule.Fields.masterInputPrice));
        baseExportTable.setMasterInputPriceMax(feeBizRule.get(CommonBizRule.Fields.masterInputPriceMax));
        baseExportTable.setDivisionTypeName(divisionTypeEnum.name);
        baseExportTable.setProvince(province);
        baseExportTable.setCity(city);
        baseExportTable.setDistrict(district);
        baseExportTable.setStreet(street);
        return baseExportTable;
    }

    private BaseExportTable getBaseExportTableNew(FeeTemplateMapping.TemplateInfo source, Map<String, String> feeBizRule, DivisionTypeEnum divisionTypeEnum, String province, String city, String district, String street) {
        BaseExportTable baseExportTable = new BaseExportTable();
        // 替换 serviceId、serviceName、skuNo、templateId、attributeDisplayName
        baseExportTable.setServiceId(source.getServiceId());
        baseExportTable.setServiceName(source.getServiceName());
        baseExportTable.setSkuNo(source.getSkuNo());
        baseExportTable.setTemplateId(source.getTemplateId() == null ? null: source.getTemplateId().toString());
        baseExportTable.setAttributeDisplayName(source.getAttributeDisplayName());
        baseExportTable.setBasePrice(feeBizRule.get(CommonBizRule.Fields.basePrice));
        baseExportTable.setBizTag(feeBizRule.get(CommonBizRule.Fields.bizTag));
        baseExportTable.setMasterInputPrice(feeBizRule.get(CommonBizRule.Fields.masterInputPrice));
        baseExportTable.setMasterInputPriceMax(feeBizRule.get(CommonBizRule.Fields.masterInputPriceMax));
        baseExportTable.setDivisionTypeName(divisionTypeEnum.name);
        baseExportTable.setProvince(province);
        baseExportTable.setCity(city);
        baseExportTable.setDistrict(district);
        baseExportTable.setStreet(street);
        return baseExportTable;
    }


    // 提取处理特定级别的逻辑到一个方法中
    private void addDivisionIdIfPresent(Set<Long> divisionIds, Map<String, String> bizRule, String divisionLevel) {
        String divisionId = bizRule.get(divisionLevel);
        if (StringUtils.isNotBlank(divisionId)) {
            divisionIds.add(Long.valueOf(divisionId));
        }
    }
}
