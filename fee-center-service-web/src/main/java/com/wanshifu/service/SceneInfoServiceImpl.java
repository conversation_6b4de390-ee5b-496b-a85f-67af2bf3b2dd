package com.wanshifu.service;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.domain.request.SceneInfoQueryPageReq;
import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.FeeTemplate;
import com.wanshifu.fee.center.domain.document.SceneInfo;
import com.wanshifu.fee.center.domain.enums.*;
import com.wanshifu.fee.center.domain.request.SceneInfoAddReq;
import com.wanshifu.fee.center.domain.request.SceneInfoModifyReq;
import com.wanshifu.fee.center.domain.request.SceneInfoSaveReq;
import com.wanshifu.fee.center.domain.request.scene.GetComparisonSceneCodeListReq;
import com.wanshifu.fee.center.domain.response.SceneListResp;
import com.wanshifu.fee.center.domain.response.scene.GetComparisonSceneCodeListResp;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.repository.SceneInfoRepository;
import com.wanshifu.infrastructure.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.springframework.data.mongodb.core.query.Criteria.where;

@Service
@Slf4j
public class SceneInfoServiceImpl implements SceneInfoService{
    @Resource
    private SceneInfoRepository sceneInfoRepository;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private SceneAccountPermissionsService sceneAccountPermissionsService;

    @Override
    public Page<SceneInfo> query(SceneInfoQueryPageReq sceneInfoQueryPageReq) {
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        List<String> sceneCodes = sceneInfoQueryPageReq.getSceneCodes();
        if (CollectionUtils.isNotEmpty(sceneCodes)) {
            criteria.and(SceneInfo.Fields.sceneCode).in(sceneCodes);
         }

        Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.modifyTime));
        PageRequest pageRequest = new PageRequest(sceneInfoQueryPageReq.pageNum - 1, sceneInfoQueryPageReq.pageSize, sort);

        Query query = Query.query(criteria);

        long count = mongoTemplate.count(query, SceneInfo.class);
        if (count > 0) {
            query.with(pageRequest);
            List<SceneInfo> sceneInfos = mongoTemplate.find(query, SceneInfo.class);
            return new PageImpl<>(sceneInfos, pageRequest, count);
        } else {
            return new PageImpl<>(Collections.emptyList(), pageRequest, count);
        }
    }

    @Override
    public SceneInfo query(Long sceneId) {
        return sceneInfoRepository.findBySceneIdIsAndDelIsFalse(sceneId);
    }

    @Override
    public SceneInfo query(String sceneCode) {
        return sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(sceneCode);
    }

    @Override
    public List<SceneInfo> queryAll() {
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.createTime));
        Query query = Query.query(criteria);
        query.with(sort);
        return mongoTemplate.find(query, SceneInfo.class);
    }

    @Override
    public SceneInfo add(SceneInfoAddReq sceneInfoAddReq) {
        SceneInfo sceneInfo = new SceneInfo();
        BeanUtils.copyProperties(sceneInfoAddReq,sceneInfo);
        setPriceUploadAlert(sceneInfoAddReq.getPriceUploadAlert(), sceneInfo);
        sceneInfo.setSceneId(SnowFlakeGenerator.INSTANCE.generate());
        Date now = new Date();
        sceneInfo.setDel(false);
        sceneInfo.setStatus(SceneStatusEnum.ACTIVE.code);
        sceneInfo.setCreateTime(now);
        sceneInfo.setModifyTime(now);
        sceneInfo.setCreateBy(CommonUtils.getCurrentLoginName());
        sceneInfo.setUpdateBy(CommonUtils.getCurrentLoginName());
        Criteria criteria = Criteria.where(SceneInfo.Fields.sceneCode).is(sceneInfoAddReq.getSceneCode());
        boolean exists = mongoTemplate.exists(Query.query(criteria), SceneInfo.class);
        if(exists){
            throw new BusException(sceneInfoAddReq.getSceneCode()+"已被使用");
        }
        return sceneInfoRepository.save(sceneInfo);
    }

    @Override
    public SceneInfo modify(SceneInfoModifyReq sceneInfoModifyReq) {
        SceneInfo sceneInfo = sceneInfoRepository.findBySceneIdIsAndDelIsFalse(sceneInfoModifyReq.getSceneId());
        if(Objects.isNull(sceneInfo) || sceneInfo.isDel()){
            throw new BusException(sceneInfoModifyReq.getSceneId()+"该场景不存在");
        }
        setPriceUploadAlert(sceneInfoModifyReq.getPriceUploadAlert(), sceneInfo);
        sceneInfo.setModifyTime(new Date());
        sceneInfo.setUpdateBy(CommonUtils.getCurrentLoginName());
        sceneInfo.setSceneName(sceneInfoModifyReq.getSceneName());
        sceneInfo.setNote(sceneInfoModifyReq.getNote());
        sceneInfo.setPriceMaintain(sceneInfoModifyReq.getPriceMaintain());
        sceneInfo.setIntegrationSys(sceneInfoModifyReq.getIntegrationSys());
        sceneInfo.setOrderRange(sceneInfoModifyReq.getOrderRange());
        sceneInfo.setIsMappingPricing(sceneInfoModifyReq.getIsMappingPricing());
        sceneInfo.setNeedIntegrityValidation(sceneInfoModifyReq.getNeedIntegrityValidation());
        sceneInfo.setAmountType(sceneInfoModifyReq.getAmountType());
        String divisionType = sceneInfoModifyReq.getDivisionType();
        if (StringUtils.isNotBlank(divisionType)) {
            sceneInfo.setDivisionType(divisionType);
        }
        Boolean needSearchParent = sceneInfoModifyReq.getNeedSearchParent();
        if (needSearchParent != null) {
            sceneInfo.setNeedSearchParent(needSearchParent);
        }
        sceneInfo.setPullBigDataCustomSku(sceneInfoModifyReq.getPullBigDataCustomSku());
        return sceneInfoRepository.save(sceneInfo);
    }

    private void setPriceUploadAlert(SceneInfoSaveReq.PriceUploadAlert reqAlert, SceneInfo sceneInfo) {
        if (reqAlert != null) {
            SceneInfo.PriceUploadAlert alert = new SceneInfo.PriceUploadAlert();
            alert.setEnabled(reqAlert.getEnabled());
            alert.setComparisonSceneCode(reqAlert.getComparisonSceneCode());
            alert.setGreaterThan(reqAlert.getGreaterThan());
            alert.setLessThan(reqAlert.getLessThan());
            sceneInfo.setPriceUploadAlert(alert);
        }
    }

    /**
     * 删除场景
     * @param sceneInfoId
     * @return
     */
    @Override
    public SceneInfo del(Long sceneInfoId) {
        SceneInfo sceneInfo = sceneInfoRepository.findBySceneIdIsAndDelIsFalse(sceneInfoId);
        if(Objects.isNull(sceneInfo)){
            throw new BusException(sceneInfoId+"该场景不存在");
        }
        Criteria criteria = Criteria.where(FeeTemplate.Fields.sceneCode).is(sceneInfo.getSceneCode()).and(BaseDocument.Fields.del).is(false);
        Query query = Query.query(criteria);
        boolean exists = mongoTemplate.exists(query, FeeTemplate.class);
        if (exists){
            throw new BusException("该场景存在正在使用的模板");
        }
        sceneInfo.setDel(true);
        sceneInfo.setModifyTime(new Date());
        sceneInfo.setUpdateBy(CommonUtils.getCurrentLoginName());
        return sceneInfoRepository.save(sceneInfo);
    }


    @Override
    public List<SceneListResp> sceneList() {
        List<SceneInfo> sceneInfos = null;
        String currentLoginName = CommonUtils.getCurrentLoginName();
        if (StringUtils.isNotBlank(currentLoginName)) {
            JSONObject jsonObject = JSONObject.parseObject(currentLoginName);
            if (jsonObject.containsKey("name")) {
                String name = jsonObject.getString("name");
                if (StringUtils.isNotBlank(name)) {
                    List<String> sceneCodeList = sceneAccountPermissionsService.getSceneCodesByAccount(name);
                    sceneInfos = sceneInfoRepository.findAllBySceneCodeInAndDelIsFalse(sceneCodeList);
                }
            }
        } else {
            sceneInfos = queryAll();
        }
        if (CollectionUtils.isEmpty(sceneInfos)) {
            return Collections.emptyList();
        }
        return sceneInfos.stream().map(sceneInfo -> {
            SceneListResp sceneListResp = new SceneListResp();
            sceneListResp.setName(sceneInfo.getSceneName());
            sceneListResp.setCode(sceneInfo.getSceneCode());
            sceneListResp.setBizIdType(sceneInfo.getBizIdType());
            sceneListResp.setPriceMaintain(sceneInfo.getPriceMaintain());
            sceneListResp.setSkuType(sceneInfo.getSkuType());
            return sceneListResp;
        }).collect(Collectors.toList());
    }


    @Override
    public List<GetComparisonSceneCodeListResp> getComparisonSceneCodeList(GetComparisonSceneCodeListReq req) {
        String skuType = req.getSkuType();
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(SceneStatusEnum.ACTIVE.code)
                .and(SceneInfo.Fields.skuType).is(skuType)
                .and(SceneInfo.Fields.bizIdType).is(BizIdTypeEnum.EMPTY.code);
        List<SceneInfo> sceneInfos = mongoTemplate.find(Query.query(criteria), SceneInfo.class);
        if (CollectionUtils.isEmpty(sceneInfos)) {
            return Collections.emptyList();
        }
        return sceneInfos.stream().map(sceneInfo -> {
            GetComparisonSceneCodeListResp resp = new GetComparisonSceneCodeListResp();
            resp.setSceneCode(sceneInfo.getSceneCode());
            resp.setSceneName(sceneInfo.getSceneName());
            return resp;
        }).collect(Collectors.toList());
    }

    @Override
    public List<SceneInfo> selectByPullBigDataCustomSku() {
        Criteria criteria = where(BaseDocument.Fields.del).is(false)
                .and(SceneInfo.Fields.pullBigDataCustomSku).is("on");

        return mongoTemplate.find(Query.query(criteria), SceneInfo.class);
    }
}
