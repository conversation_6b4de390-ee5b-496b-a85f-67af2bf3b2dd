package com.wanshifu.service;

import com.wanshifu.domain.dto.table.BatchCustomSkuTemplateExcelModel;
import com.wanshifu.domain.dto.table.CustomSkuTemplateExcelModel;
import com.wanshifu.domain.request.template.TemplateDelRequest;
import com.wanshifu.domain.response.template.TemplateDelResponse;
import com.wanshifu.fee.center.domain.document.FeeTemplate;
import com.wanshifu.fee.center.domain.request.FeeTemplateBatchQueryReq;
import com.wanshifu.fee.center.domain.request.FeeTemplateConfigureReq;
import com.wanshifu.fee.center.domain.request.FeeTemplateQueryByConditionReq;
import com.wanshifu.fee.center.domain.request.FeeTemplateQueryReq;
import com.wanshifu.fee.center.domain.request.template.GetFeeTemplateServiceIdAndNameReq;
import com.wanshifu.fee.center.domain.request.template.*;
import com.wanshifu.fee.center.domain.response.GetFeeTemplateServiceIdAndNameResp;
import com.wanshifu.fee.center.domain.response.GetFeeTemplateSkuListResp;
import com.wanshifu.fee.center.domain.response.SceneBaseInfo;
import com.wanshifu.fee.center.domain.response.template.GetByServiceIdResponse;
import com.wanshifu.framework.core.page.SimplePageInfo;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Set;


public interface FeeTemplateService {
    Page<FeeTemplate> queryByCondition(FeeTemplateQueryReq feeTemplateQueryReq);

    Page<FeeTemplate> queryServiceByCondition(FeeTemplateQueryByConditionReq feeTemplateQueryByConditionReq);

    Page<FeeTemplate> queryByCondition(FeeTemplateQueryByConditionReq feeTemplateQueryByConditionReq);

    Page<FeeTemplate> batchQueryByCondition(FeeTemplateBatchQueryReq feeTemplateBatchQueryReq);

    Page<FeeTemplate> getPageList(FeeTemplatePageListRequest request);

    void save(List<FeeTemplate> feeTemplate);

    List<FeeTemplate> update(List<FeeTemplate> feeTemplate);

    void delete(Long templateId);

    void lock(List<Long> feeTemplateIds);


    /**
     * 重复性校验，如果在同一场景下，已存在服务ID，则返回true，表示存在重复
     * @param serveId 服务id
     * @param sceneCode 场景编号
     * @return boolean
     */
    boolean duplicateValidate(Long serveId, String sceneCode);

    CopyPricingAttributeResp copyPricingAttribute(CopyPricingAttributeReq req);

    List<GetTemplateBySkuNoResp> getTemplateBySkuNo(GetTemplateBySkuNoReq req);

    List<FeeTemplate> selectByServiceIdAndSkuNo(String sceneCode, String serviceId, String skuNo);

    List<FeeTemplate> selectByServiceIdAndSkuNoAndSkuTypeNotStandardSurcharge(String sceneCode, String serviceId, String skuNo, String skuType);

    List<FeeTemplate> select4BigdataPullCustomSku(String sceneCode, String serviceId, String skuNo, String customSkuUserId, String attributeValueFixed);

    List<FeeTemplate> select4BigdataPullBySceneCode(String sceneCode);

    List<SceneBaseInfo> getSceneListExistServiceIdAndSkuNos(Long serviceId, List<String> skuNos);

    List<SceneBaseInfo> getSceneListNotExistServiceIdAndSkuNos(Long serviceId, List<String> skuNos);

    List<FeeTemplate> getFeeTemplates(FeeTemplateConfigureReq feeTemplateConfigureReq);

    void checkHasFeeTypeTag(List<FeeTemplateConfigureReq.FeeTemplateConfigure> configureList);

    void createTemplate(SaveTemplateSkuNoReq req);

    void modifyTemplate(SaveTemplateSkuNoReq req);

    SimplePageInfo<GetFeeTemplateServiceIdAndNameResp> getFeeRuleServiceIdAndName(GetFeeTemplateServiceIdAndNameReq req);

    SimplePageInfo<GetFeeTemplateSkuListResp> getFeeTemplateSkuList(GetFeeTemplateSkuListReq req);

    Page<FeeTemplate> queryTemplateByCondition(FeeTemplateReq req);

    Page<FeeTemplate> queryTemplateByCondition4DeleteCustom(FeeTemplate4DeleteCustomReq req);

    Set<Long> getServiceCategoryIds(GetServiceCategoryIdsReq req);

    void completeServiceCategoryId(String sceneCode, String updateBy);

    TemplateDelResponse delBatch(TemplateDelRequest templateDelRequest);

    List<FeeTemplate> getListByServiceId(GetByServiceIdRequest req);

    /**
     * 修正FeeTemplate的bizRule中的sceneCode和sceneName字段
     * TODO 均为冗余的，其实不应该存在，后续应该去掉，只保留FeeTemplate外层的即可
     */
    void correctSceneInfoOfTemplate();

    void saveCustomSkuTemplateBatch(Long templateId,
                                    String customSkuValueType,
                                    String matchSkuType,
                                    List<CustomSkuTemplateExcelModel> customSkuTemplateExcelModels);

    void saveCurrentCustomSkuTemplate(Long templateId,
                                    String customSkuValueType,
                                    String matchSkuType,
                                      BatchCustomSkuTemplateExcelModel data);

    boolean checkCustomSkuTemplateExist(
            Long templateId,
            String customSkuValueType,
            String matchSkuType,
            List<CustomSkuTemplateExcelModel> dataList);

    boolean checkCurrentCustomSkuTemplateExist(
            Long templateId,
            String customSkuValueType,
            String matchSkuType,
            BatchCustomSkuTemplateExcelModel data);

    void initCustomSkuValueToRange();

    void correctCustomSkuFatherStatusToLock();
}
