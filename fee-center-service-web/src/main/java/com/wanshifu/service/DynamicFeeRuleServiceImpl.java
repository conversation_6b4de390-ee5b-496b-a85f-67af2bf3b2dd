package com.wanshifu.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.mongodb.BasicDBObject;
import com.wanshifu.event.DocumentChangeEvent;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.*;
import com.wanshifu.fee.center.domain.enums.*;
import com.wanshifu.fee.center.domain.request.DynamicFeeRulePageReq;
import com.wanshifu.fee.center.domain.request.DynamicFeeRuleSaveReq;
import com.wanshifu.fee.center.domain.request.calculate.CalculateServiceDynamicPriceRequest;
import com.wanshifu.fee.center.domain.request.calculate.CalculateServiceDynamicPriceResponse;
import com.wanshifu.fee.center.domain.request.calculate.DynamicIndicatorParam;
import com.wanshifu.fee.center.domain.response.bigdata.BigDataResp;
import com.wanshifu.fee.center.domain.response.bigdata.GetIndicatorValueRequest;
import com.wanshifu.fee.center.domain.response.bigdata.GetIndicatorValueResponse;
import com.wanshifu.fee.center.domain.response.bigdata.OrderAvgPushDistance;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.infrastructure.gateway.BigdataGateway;
import com.wanshifu.infrastructure.utils.CommonUtils;
import com.wanshifu.infrastructure.utils.DynamicParamParser;
import com.wanshifu.infrastructure.utils.PatternUtils;
import com.wanshifu.repository.DynamicFeeRuleRepository;
import com.wanshifu.repository.FeeRuleRepository;
import com.wanshifu.service.impl.DynamicPriceCalculationEngine;
import com.wanshifu.strategy.apply.PriceAdjuster;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DynamicFeeRuleServiceImpl implements DynamicFeeRuleService {

    @Resource
    private DynamicFeeRuleRepository dynamicFeeRuleRepository;
    @Resource
    private MongoTemplate mongoTemplate;
    @Autowired
    private FeeRuleRepository feeRuleRepository;
    @Resource
    private FormulaService formulaService;
    @Resource
    private BigdataGateway bigdataGateway;
    @Resource
    private ApplicationEventPublisher eventPublisher;
    @Resource
    private DynamicPriceCalculationEngine dynamicPriceCalculationEngine;

    @Override
    public Page<DynamicFeeRule> queryByCondition(DynamicFeeRulePageReq dynamicFeeRulePageReq) {
        // 查询列表

        Criteria criteria = new Criteria();

        if (StringUtils.isNotBlank(dynamicFeeRulePageReq.getServiceName())) {
            criteria.and(DynamicFeeRule.Fields.serviceDataList).elemMatch(Criteria.where(ServiceData.Fields.serviceName).regex(PatternUtils.toEscapeStr(dynamicFeeRulePageReq.getServiceName())));
        }

        if (StringUtils.isNotBlank(dynamicFeeRulePageReq.getStrategyName())) {
            criteria.and(DynamicFeeRule.Fields.strategyName).regex(PatternUtils.toEscapeStr(dynamicFeeRulePageReq.getStrategyName()));
        }

        if (StringUtils.isNotBlank(dynamicFeeRulePageReq.getGoodsCategoryName())) {
            criteria.and(DynamicFeeRule.Fields.serviceDataList).elemMatch(Criteria.where(ServiceData.Fields.goodsCategoryName).is(dynamicFeeRulePageReq.getGoodsCategoryName()));
        }

        if (StringUtils.isNotBlank(dynamicFeeRulePageReq.getOperator())) {
            criteria.and(DynamicFeeRule.Fields.operator).is(dynamicFeeRulePageReq.getOperator());
        }

        if (StringUtils.isNotBlank(dynamicFeeRulePageReq.getSceneCode())) {
            criteria.and(DynamicFeeRule.Fields.sceneCode).is(dynamicFeeRulePageReq.getSceneCode());
        }

        if (Objects.nonNull(dynamicFeeRulePageReq.getStatus())) {
            criteria.and(BaseDocument.Fields.status).is(dynamicFeeRulePageReq.getStatus());
        }

        if (Objects.nonNull(dynamicFeeRulePageReq.getDel())) {
            criteria.and(BaseDocument.Fields.del).is(dynamicFeeRulePageReq.getDel());
        }

        Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.modifyTime));
        PageRequest pageRequest = new PageRequest(dynamicFeeRulePageReq.pageNum - 1, dynamicFeeRulePageReq.pageSize, sort);

        Query query = Query.query(criteria);
        long count = mongoTemplate.count(query, DynamicFeeRule.class);
        if (count > 0) {
            query.with(pageRequest);
            List<DynamicFeeRule> dynamicFeeRules = mongoTemplate.find(query, DynamicFeeRule.class);
            return new PageImpl<>(dynamicFeeRules, pageRequest, count);
        } else {
            return new PageImpl<>(Collections.emptyList(), pageRequest, count);
        }
    }

    @Override
    public DynamicFeeRule queryById(Long dynamicFeeRuleId) {
        return dynamicFeeRuleRepository.findDynamicFeeRuleByDynamicFeeRuleIdIs(dynamicFeeRuleId);
    }

    @Override
    public DynamicFeeRule modify(DynamicFeeRuleSaveReq dynamicFeeRuleSaveReq) {

        DynamicFeeRule dynamicFeeRule = dynamicFeeRuleRepository.findDynamicFeeRuleByDynamicFeeRuleIdIsAndDelIsFalse(dynamicFeeRuleSaveReq.getDynamicFeeRuleId());
        if (Objects.isNull(dynamicFeeRule)) {
            throw new BusException("规则不存在");
        }

        dynamicFeeRule.setSceneCode(dynamicFeeRuleSaveReq.getSceneCode());

        dynamicFeeRule.setStrategyName(dynamicFeeRuleSaveReq.getStrategyName());

        dynamicFeeRule.setStartTime(dynamicFeeRuleSaveReq.getStartTime());

        dynamicFeeRule.setEndTime(dynamicFeeRuleSaveReq.getEndTime());

        dynamicFeeRule.setDivisionType(dynamicFeeRuleSaveReq.getDivisionType());

        dynamicFeeRule.setDivisionIds(dynamicFeeRuleSaveReq.getDivisionIds());

        dynamicFeeRule.setUserIds(dynamicFeeRuleSaveReq.getUserIds());

        dynamicFeeRule.setServiceDataList(dynamicFeeRuleSaveReq.getServiceDataList());

        dynamicFeeRule.setFeeTypeTag(dynamicFeeRuleSaveReq.getFeeTypeTag());

        dynamicFeeRule.setOperator(dynamicFeeRuleSaveReq.getOperator());

        dynamicFeeRule.setNote(dynamicFeeRuleSaveReq.getNote());

        dynamicFeeRule.setDynamicCalculateRuleData(dynamicFeeRuleSaveReq.getDynamicCalculateRuleData());

        Date now = new Date();
        dynamicFeeRule.setStatus(DynamicFeeRuleStatusEnum.AUDIT.code);
        dynamicFeeRule.setModifyTime(now);
        dynamicFeeRule.setBizTag(dynamicFeeRuleSaveReq.getBizTag());
        dynamicFeeRule.setDynamicPricingIndicators(dynamicFeeRuleSaveReq.getDynamicPricingIndicators());

        String divisionType = dynamicFeeRule.getDivisionType();
        if (StringUtils.isNotBlank(divisionType)) {
            DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(divisionType);
            if (Objects.isNull(divisionTypeEnum)) {
                throw new BusException("divisionType错误");
            }
            dynamicFeeRule.setDivisionTypeName(divisionTypeEnum.name);
        }
        String adjustPriceUnit = dynamicFeeRuleSaveReq.getAdjustPriceUnit();
        setSkuParam(adjustPriceUnit, dynamicFeeRule, dynamicFeeRuleSaveReq);

        // 重复校验
        checkRepeat(dynamicFeeRule);

        DynamicFeeRule save = dynamicFeeRuleRepository.save(dynamicFeeRule);

        eventPublisher.publishEvent(new DocumentChangeEvent(dynamicFeeRule, "SAVE", dynamicFeeRule));

        return save;
    }


    @Override
    public DynamicFeeRule save(DynamicFeeRuleSaveReq dynamicFeeRuleSaveReq) {

        DynamicFeeRule dynamicFeeRule = new DynamicFeeRule();
        BeanUtils.copyProperties(dynamicFeeRuleSaveReq, dynamicFeeRule);
        Date now = new Date();
        dynamicFeeRule.setDynamicFeeRuleId(SnowFlakeGenerator.INSTANCE.generate());
        dynamicFeeRule.setStatus(DynamicFeeRuleStatusEnum.AUDIT.code);
        dynamicFeeRule.setModifyTime(now);
        dynamicFeeRule.setCreateTime(now);

        String divisionType = dynamicFeeRule.getDivisionType();
        if (StringUtils.isNotBlank(divisionType)) {
            DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(divisionType);
            if (Objects.isNull(divisionTypeEnum)) {
                throw new BusException("divisionType错误");
            }
            dynamicFeeRule.setDivisionTypeName(divisionTypeEnum.name);
        }

        String adjustPriceUnit = dynamicFeeRuleSaveReq.getAdjustPriceUnit();
        setSkuParam(adjustPriceUnit, dynamicFeeRule, dynamicFeeRuleSaveReq);

        checkRepeat(dynamicFeeRule);

        DynamicFeeRule save = dynamicFeeRuleRepository.save(dynamicFeeRule);

        eventPublisher.publishEvent(new DocumentChangeEvent(dynamicFeeRule, "SAVE", dynamicFeeRule));

        return save;
    }

    private void setSkuParam(String adjustPriceUnit, DynamicFeeRule dynamicFeeRule, DynamicFeeRuleSaveReq dynamicFeeRuleSaveReq) {
        if (AdjustPriceUnitEnum.SKU.getCode().equals(adjustPriceUnit)) {
            FeeTypeTagEnum feeTypeTageEnum = FeeTypeTagEnum.fromCode(dynamicFeeRule.getFeeTypeTag());
            if (Objects.isNull(feeTypeTageEnum)) {
                throw new BusException("feeTypeTag错误");
            }
            dynamicFeeRule.setFeeTypeTagName(feeTypeTageEnum.name);

            List<String> templateIds = getTemplateIds(dynamicFeeRuleSaveReq);
            dynamicFeeRule.setTemplateIds(templateIds);
        }
    }


    private List<String> getTemplateIds(DynamicFeeRuleSaveReq dynamicFeeRuleSaveReq) {
        List<String> templateIds = dynamicFeeRuleSaveReq.getTemplateIds();
        if (CollectionUtils.isEmpty(templateIds)) {
            Criteria criteria = Criteria.where(FeeTemplate.Fields.sceneCode).is(dynamicFeeRuleSaveReq.getSceneCode())
                    .and(BaseDocument.Fields.del).is(false)
                    .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code)
                    .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId)
                    .in(dynamicFeeRuleSaveReq.getServiceDataList().stream().map(ServiceData::getServiceId).map(String::valueOf).collect(Collectors.toList()))
                    .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).is(FeeTypeTagEnum.SERVICE_FEE.code);
            List<FeeTemplate> feeTemplates = mongoTemplate.find(Query.query(criteria), FeeTemplate.class);
            if (CollectionUtils.isEmpty(feeTemplates)) {
                throw new BusException("前端没有传模板Id，且后端未找到对应的计价模板");
            }
            templateIds = feeTemplates.stream().map(FeeTemplate::getTemplateId).map(String::valueOf).collect(Collectors.toList());
        }
        return templateIds;
    }

    /**
     * 重复校验，各维度组合不能重叠
     *
     * @param dynamicFeeRule 动态计费规则
     */
    private void checkRepeat(DynamicFeeRule dynamicFeeRule) {

        String sceneCode = dynamicFeeRule.getSceneCode();

        Criteria criteria = Criteria.where(DynamicFeeRule.Fields.sceneCode).is(sceneCode)
                .and(BaseDocument.Fields.del).is(false);

        String adjustPriceUnit = dynamicFeeRule.getAdjustPriceUnit();
        if (StringUtils.isNotBlank(adjustPriceUnit)) {
            criteria.and(DynamicFeeRule.Fields.adjustPriceUnit).is(adjustPriceUnit);
        } else {
            criteria.and(DynamicFeeRule.Fields.adjustPriceUnit).is(null);
        }

        String subSceneCode = dynamicFeeRule.getSubSceneCode();
        if (StringUtils.isNotBlank(subSceneCode)) {
            criteria.and(DynamicFeeRule.Fields.subSceneCode).is(subSceneCode);
        } else {
            criteria.and(DynamicFeeRule.Fields.subSceneCode).is(null);
        }

        String divisionType = dynamicFeeRule.getDivisionType();
        if (StringUtils.isNotBlank(divisionType)) {
            criteria.and(DynamicFeeRule.Fields.divisionType).is(divisionType);
        } else {
            criteria.and(DynamicFeeRule.Fields.divisionType).is(null);
        }
        // 修改时需要排除自己
        if (Objects.nonNull(dynamicFeeRule.getDynamicFeeRuleId())) {
            criteria.and(DynamicFeeRule.Fields.dynamicFeeRuleId).ne(dynamicFeeRule.getDynamicFeeRuleId());
        }

        // 时间不能有交叉
        // [ startTime ]
        Criteria section1 = Criteria.where(DynamicFeeRule.Fields.startTime).lte(dynamicFeeRule.getStartTime())
                .and(DynamicFeeRule.Fields.endTime).gte(dynamicFeeRule.getStartTime());

        // [ endTime ]
        Criteria section2 = Criteria.where(DynamicFeeRule.Fields.startTime).lte(dynamicFeeRule.getEndTime())
                .and(DynamicFeeRule.Fields.endTime).gte(dynamicFeeRule.getEndTime());

        // startTime [ ]  endTime
        Criteria section3 = Criteria.where(DynamicFeeRule.Fields.startTime).gte(dynamicFeeRule.getStartTime())
                .and(DynamicFeeRule.Fields.endTime).lte(dynamicFeeRule.getEndTime());

        criteria.orOperator(section1, section2, section3);

        // 地区 、 服务 、 用户id

        List<ServiceData> serviceDataList = dynamicFeeRule.getServiceDataList();
        if (CollectionUtils.isEmpty(serviceDataList)) {
            throw new BusException("serviceId不能为空");
        }

        List<Long> serviceIds = serviceDataList.stream().map(ServiceData::getServiceId).collect(Collectors.toList());

        criteria.and(DynamicFeeRule.Fields.serviceDataList).elemMatch(Criteria.where(ServiceData.Fields.serviceId).in(serviceIds));

        List<Long> divisionIds = dynamicFeeRule.getDivisionIds();
        if (CollectionUtils.isNotEmpty(divisionIds)) {
            criteria.and(DynamicFeeRule.Fields.divisionIds).in(divisionIds);
        } else {
            criteria.and(DynamicFeeRule.Fields.divisionIds).is(null);
        }

        List<Long> userIds = dynamicFeeRule.getUserIds();

        // userId不仅仅指代用户，也可以是师傅Id，这里的userId指 bizId，历史问题，不做修改
        if (CollectionUtils.isNotEmpty(userIds)) {
            criteria.and(DynamicFeeRule.Fields.userIds).in(userIds);
        } else {
            criteria.and(DynamicFeeRule.Fields.userIds).is(userIds);
        }

        List<String> templateIds = dynamicFeeRule.getTemplateIds();
        if (CollectionUtils.isNotEmpty(templateIds)) {
            criteria.and(DynamicFeeRule.Fields.templateIds).in(templateIds);
        } else {
            criteria.and(DynamicFeeRule.Fields.templateIds).is(null);
        }

        String bizTag = dynamicFeeRule.getBizTag();
        if (StringUtils.isNotBlank(bizTag)) {
            criteria.and(DynamicFeeRule.Fields.bizTag).is(bizTag.trim());
        } else {
            criteria.and(DynamicFeeRule.Fields.bizTag).is(null);
        }

        Query query = Query.query(criteria);
        boolean exists = mongoTemplate.exists(query, DynamicFeeRule.class);
        if (exists) {
            throw new BusException("该规则条件与已有数据规则重复");
        }

    }

    @Override
    public DynamicFeeRule del(Long dynamicFeeRuleId) {
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        criteria.and(DynamicFeeRule.Fields.dynamicFeeRuleId).is(dynamicFeeRuleId);
        DynamicFeeRule dynamicFeeRule = mongoTemplate.findOne(Query.query(criteria), DynamicFeeRule.class);
        if (Objects.isNull(dynamicFeeRule)) {
            throw new BusException("调价规则不存在");
        }
        dynamicFeeRule.setModifyTime(new Date());
        dynamicFeeRule.setDel(true);

        // 清除feeRule中有关联该动态价规则的数据
        criteria = Criteria.where(FeeRule.Fields.sceneCode).is(dynamicFeeRule.getSceneCode())
                .and(FeeRule.Fields.dynamicFeeRuleCalculateRuleDataList).elemMatch(
                        Criteria.where(DynamicFeeRuleCalculateRuleData.Fields.dynamicFeeRuleId).is(dynamicFeeRuleId)
                ).and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code);
        Query query = new Query(criteria);
        Update update = new Update();
        update.pull(FeeRule.Fields.dynamicFeeRuleCalculateRuleDataList, new BasicDBObject(DynamicFeeRuleCalculateRuleData.Fields.dynamicFeeRuleId, dynamicFeeRuleId));
        update.set(BaseDocument.Fields.modifyTime, new Date())
                .set(BaseDocument.Fields.updateBy, CommonUtils.getCurrentLoginName());
        mongoTemplate.updateMulti(query, update, "feeRule");

        DynamicFeeRule save = dynamicFeeRuleRepository.save(dynamicFeeRule);

        eventPublisher.publishEvent(new DocumentChangeEvent(dynamicFeeRule, "SAVE", dynamicFeeRule));

        return save;
    }

    @Override
    public DynamicFeeRule audit(Long dynamicFeeRuleId, DynamicFeeRuleStatusEnum dynamicFeeRuleStatus) {
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        criteria.and(DynamicFeeRule.Fields.dynamicFeeRuleId).is(dynamicFeeRuleId);
        criteria.and(BaseDocument.Fields.status).is(DynamicFeeRuleStatusEnum.AUDIT.code);
        DynamicFeeRule dynamicFeeRule = mongoTemplate.findOne(Query.query(criteria), DynamicFeeRule.class);
        if (Objects.isNull(dynamicFeeRule)) {
            throw new BusException("调价规则不存在或者不是待审核状态");
        }
        dynamicFeeRule.setModifyTime(new Date());
        dynamicFeeRule.setUpdateBy(CommonUtils.getCurrentLoginName());
        dynamicFeeRule.setStatus(dynamicFeeRuleStatus.code);
        String adjustPriceUnit = dynamicFeeRule.getAdjustPriceUnit();
        AdjustPriceUnitEnum priceUnitEnum = AdjustPriceUnitEnum.getEnumByCode(adjustPriceUnit);
        if (priceUnitEnum == null) {
            throw new BusException(StrUtil.format("非法调价单元={}", adjustPriceUnit));
        }
        // 如果是按服务动态调价，则执行更新状态即可
        if (priceUnitEnum == AdjustPriceUnitEnum.SERVICE) {
            dynamicFeeRuleRepository.save(dynamicFeeRule);
            eventPublisher.publishEvent(new DocumentChangeEvent(dynamicFeeRule, "SAVE", dynamicFeeRule));
            return dynamicFeeRule;
        }

        if (DynamicFeeRuleStatusEnum.PASS.equals(dynamicFeeRuleStatus)) {
            /* 根据dynamicFeeRuleId 查找feeRule,根据修改后的动态价规则 匹配feeRule，
           匹配规则： sceneCode、templateId(识别skuNo)、feeTypeTag、divisionType、divisionId（若不为country）、
           bizTag（若不为空），bizId（若不为空），如果匹配不上，则将 dynamicFeeRuleId、dynamicBizRule、
           dynamicCalculateRuleData(算sku单价脚本unitPrice+算sku总价脚本 totalPrice) 这3个字段设置为null
         */
            Criteria feeRuleCriteria = Criteria.where(FeeRule.Fields.sceneCode).is(dynamicFeeRule.getSceneCode())
                    .and(FeeRule.Fields.dynamicFeeRuleCalculateRuleDataList + PunConstant.DOT + DynamicFeeRuleCalculateRuleData.Fields.dynamicFeeRuleId)
                    .elemMatch(Criteria.where(DynamicFeeRuleCalculateRuleData.Fields.dynamicFeeRuleId).is(dynamicFeeRule.getDynamicFeeRuleId()))
                    .and(BaseDocument.Fields.del).is(false)
                    .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code);
            // 分页查找，每50条为一页
            List<Long> bizIds = dynamicFeeRule.getUserIds();
            String bizTag = dynamicFeeRule.getBizTag();
            String divisionType = dynamicFeeRule.getDivisionType();
            List<String> templateIds = dynamicFeeRule.getTemplateIds();
            String feeTypeTag = dynamicFeeRule.getFeeTypeTag();
            List<Long> divisionIds = dynamicFeeRule.getDivisionIds();

            Date startTime = dynamicFeeRule.getStartTime();
            Date endTime = dynamicFeeRule.getEndTime();
            DynamicCalculateRuleData dynamicCalculateRuleData = dynamicFeeRule.getDynamicCalculateRuleData();
            DynamicFeeRuleCalculateRuleData.MatchingRule matchingRule = new DynamicFeeRuleCalculateRuleData.MatchingRule(startTime, endTime);

            DynamicFeeRuleCalculateRuleData.CalculateData unitPrice = new DynamicFeeRuleCalculateRuleData.CalculateData();
            unitPrice.setExpress(dynamicFeeRule.getDynamicCalculateRuleData().getExpress());
            unitPrice.setExpressionParamList(dynamicFeeRule.getDynamicCalculateRuleData().getExpressionParamList());

            int initPageNum = 0;
            int pageSize = 50;
            // 先剔除 feeRule中包含了当前动态规则的数据，再将最新的动态规则写入
            while (true) {
                PageRequest pageRequest = new PageRequest(initPageNum, pageSize);
                Query feeRuleQuery = Query.query(feeRuleCriteria).with(pageRequest);
                List<FeeRule> feeRules = mongoTemplate.find(feeRuleQuery, FeeRule.class);
                if (CollectionUtils.isEmpty(feeRules)) {
                    break;
                }
                feeRules = feeRules.stream().filter(feeRule -> {
                    boolean match = true;
                    // 匹配规则，只要有一个不匹配，则返回false： templateId(识别skuNo)、feeTypeTag、divisionType、divisionId（若不为country）、
                    // bizTag（若不为空），bizId（若不为空）

                    // 匹配templateId
                    Long templateId = feeRule.getTemplateId();
                    if (!(Objects.nonNull(templateId) && CollectionUtils.isNotEmpty(templateIds) && templateIds.contains(templateId))) {
                        match = false;
                        return match;
                    }

                    // 匹配bizId
                    if (CollectionUtils.isNotEmpty(bizIds)) {
                        String bizId = feeRule.getBizRule().get(CommonBizRule.Fields.bizId);
                        String userId = feeRule.getBizRule().get(CommonBizRule.Fields.userId);
                        String masterId = feeRule.getBizRule().get(CommonBizRule.Fields.masterId);
                        Set<String> bizIdStrSet = bizIds.stream().map(String::valueOf).collect(Collectors.toSet());
                        if (!(bizIdStrSet.contains(bizId) || bizIdStrSet.contains(userId) || bizIdStrSet.contains(masterId))) {
                            match = false;
                            return match;
                        }
                    }

                    // 匹配bizTag
                    if (StringUtils.isNotBlank(bizTag)) {
                        String feeBizTag = feeRule.getBizRule().get(CommonBizRule.Fields.bizTag);
                        if (!bizTag.equals(feeBizTag)) {
                            match = false;
                            return match;
                        }
                    }

                    // 匹配 feeTypeTag
                    if (!feeTypeTag.equals(feeRule.getBizRule().get(CommonBizRule.Fields.feeTypeTag))) {
                        match = false;
                        return match;
                    }

                    // 匹配 divisionType
                    if (!divisionType.equals(feeRule.getBizRule().get(CommonBizRule.Fields.divisionType))) {
                        match = false;
                        return match;
                    }

                    // 匹配 divisionId
                    if (CollectionUtils.isNotEmpty(divisionIds)) {
                        if (DivisionTypeEnum.PROVINCE.code.equals(divisionType)) {
                            Long provinceId = Long.valueOf(feeRule.getBizRule().get(CommonBizRule.Fields.level1DivisionId));
                            if (!divisionIds.contains(provinceId)) {
                                match = false;
                                return match;
                            }
                        }
                        if (DivisionTypeEnum.CITY.code.equals(divisionType)) {
                            Long cityId = Long.valueOf(feeRule.getBizRule().get(CommonBizRule.Fields.level2DivisionId));
                            if (!divisionIds.contains(cityId)) {
                                match = false;
                                return match;
                            }
                        }
                        if (DivisionTypeEnum.DISTRICT.code.equals(divisionType)) {
                            Long districtId = Long.valueOf(feeRule.getBizRule().get(CommonBizRule.Fields.level3DivisionId));
                            if (!divisionIds.contains(districtId)) {
                                match = false;
                                return match;
                            }
                        }
                        if (DivisionTypeEnum.STREET.code.equals(divisionType)) {
                            Long streetId = Long.valueOf(feeRule.getBizRule().get(CommonBizRule.Fields.level4DivisionId));
                            if (!divisionIds.contains(streetId)) {
                                match = false;
                                return match;
                            }
                        }
                    }
                    return match;
                }).collect(Collectors.toList());

                // 剔除
                feeRules.forEach(feeRule -> {
                    List<DynamicFeeRuleCalculateRuleData> dataList = feeRule.getDynamicFeeRuleCalculateRuleDataList();
                    if (CollectionUtils.isNotEmpty(dataList)) {
                        dataList = dataList.stream().filter(data -> !dynamicFeeRuleId.equals(data.getDynamicFeeRuleId())).collect(Collectors.toList());
                        feeRule.setDynamicFeeRuleCalculateRuleDataList(dataList);
                    }
                    feeRule.setModifyTime(new Date());
                    feeRule.setUpdateBy(CommonUtils.getCurrentLoginName());
                });

                feeRuleRepository.save(feeRules);

                initPageNum++;
            }


        /*
           根据 sceneCode、templateId(识别skuNo)、feeTypeTag、divisionType、divisionId（若不为country）、
           bizTag（若不为空），bizId（若不为空） 查找feeRule，写入3个字段，dynamicFeeRuleId、dynamicBizRule(是否执行动态规则的匹配条件)、
           dynamicCalculateRuleData(算sku单价脚本unitPrice+算sku总价脚本 totalPrice)
         */
            // 保存
            initPageNum = 0;
            List<Long> templateIdList = templateIds.stream().map(Long::valueOf).collect(Collectors.toList());
            while (true) {
                feeRuleCriteria = Criteria.where(FeeRule.Fields.sceneCode).is(dynamicFeeRule.getSceneCode())
                        .and(FeeRule.Fields.templateId).in(templateIdList)
                        .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).is(feeTypeTag)
                        .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(divisionType)
                        .and(BaseDocument.Fields.del).is(false)
                        .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code);
                if (CollectionUtils.isNotEmpty(bizIds)) {
                    feeRuleCriteria.orOperator(
                            Criteria.where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizId).in(bizIds),
                            Criteria.where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.userId).in(bizIds),
                            Criteria.where(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterId).in(bizIds)
                    );
                }
                if (StringUtils.isNotBlank(bizTag)) {
                    feeRuleCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizTag).is(bizTag);
                }
                if (CollectionUtils.isNotEmpty(divisionIds)) {
                    List<String> divisionIdStrList = divisionIds.stream().map(String::valueOf).collect(Collectors.toList());
                    if (DivisionTypeEnum.PROVINCE.code.equals(divisionType)) {
                        feeRuleCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level1DivisionId).in(divisionIdStrList);
                    }
                    if (DivisionTypeEnum.CITY.code.equals(divisionType)) {
                        feeRuleCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId).in(divisionIdStrList);
                    }
                    if (DivisionTypeEnum.DISTRICT.code.equals(divisionType)) {
                        feeRuleCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level3DivisionId).in(divisionIdStrList);
                    }
                    if (DivisionTypeEnum.STREET.code.equals(divisionType)) {
                        feeRuleCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level4DivisionId).in(divisionIdStrList);
                    }
                }

                PageRequest pageRequest = new PageRequest(initPageNum, pageSize);
                Query feeRuleQuery = Query.query(feeRuleCriteria).with(pageRequest);
                List<FeeRule> feeRules = mongoTemplate.find(feeRuleQuery, FeeRule.class);
                if (CollectionUtils.isEmpty(feeRules)) {
                    break;
                }

                feeRules.forEach(feeRule -> {
                    CalculateRuleData calculateRuleData = feeRule.getCalculateRuleData();
                    Map<String, String> bizRule = feeRule.getBizRule();
                    String feeRuleDivisionType = bizRule.get(CommonBizRule.Fields.divisionType);
                    String bizId = bizRule.get(CommonBizRule.Fields.bizId);
                    String divisionId = null;
                    if (DivisionTypeEnum.PROVINCE.code.equals(feeRuleDivisionType)) {
                        divisionId = bizRule.get(CommonBizRule.Fields.level1DivisionId);
                    }
                    if (DivisionTypeEnum.CITY.code.equals(feeRuleDivisionType)) {
                        divisionId = bizRule.get(CommonBizRule.Fields.level2DivisionId);
                    }
                    if (DivisionTypeEnum.DISTRICT.code.equals(feeRuleDivisionType)) {
                        divisionId = bizRule.get(CommonBizRule.Fields.level3DivisionId);
                    }
                    if (DivisionTypeEnum.STREET.code.equals(feeRuleDivisionType)) {
                        divisionId = bizRule.get(CommonBizRule.Fields.level4DivisionId);
                    }
                    DynamicFeeRuleCalculateRuleData dynamicCalculateRuleDataAfterFormula = formulaService.handleFormulas(calculateRuleData, dynamicCalculateRuleData, feeRuleDivisionType, divisionId, bizId);
                    if (dynamicCalculateRuleDataAfterFormula == null) {
                        dynamicCalculateRuleDataAfterFormula = new DynamicFeeRuleCalculateRuleData();
                    }
                    dynamicCalculateRuleDataAfterFormula.setDynamicFeeRuleId(dynamicFeeRuleId);
                    dynamicCalculateRuleDataAfterFormula.setMatchingRule(matchingRule);
                    dynamicCalculateRuleDataAfterFormula.setDynamicStrategyName(dynamicFeeRule.getStrategyName());
                    List<DynamicFeeRuleCalculateRuleData> dataList = feeRule.getDynamicFeeRuleCalculateRuleDataList();
                    if (CollectionUtils.isEmpty(dataList)) {
                        dataList = new ArrayList<>();
                    }

                    dynamicCalculateRuleDataAfterFormula.setUnitPrice(unitPrice);

                    // 替换已存在的
                    dataList.removeIf(data -> dynamicFeeRuleId.equals(data.getDynamicFeeRuleId()));
                    dataList.add(dynamicCalculateRuleDataAfterFormula);

                    feeRule.setDynamicFeeRuleCalculateRuleDataList(dataList);

                    feeRule.setModifyTime(new Date());
                    feeRule.setUpdateBy(CommonUtils.getCurrentLoginName());
                });

                feeRuleRepository.save(feeRules);
                initPageNum++;
            }
        }

        return dynamicFeeRuleRepository.save(dynamicFeeRule);
    }


    @Override
    public BigDecimal getServiceDynamicFeeCost(String sceneCode,
                                               String subSceneCode,
                                               String serviceId,
                                               String userId,
                                               Long cityId,
                                               BigDecimal cost,
                                               BigDecimal dynamicFeeCost,
                                               List<DynamicIndicatorParam> indicatorParamList) {




        if (cost == null || !cost.equals(dynamicFeeCost) || StringUtils.isBlank(serviceId)) {
            return null;
        }

        CalculateServiceDynamicPriceRequest request = new CalculateServiceDynamicPriceRequest();
        request.setSceneCode(sceneCode);
        request.setSubSceneCode(subSceneCode);
        request.setServiceIds(Collections.singleton(Long.parseLong(serviceId.trim())));
        request.setUserId(userId == null ? null : Long.parseLong(userId));
        request.setIndicatorParamList(indicatorParamList);
        request.setOriginalCost(cost);

        List<DynamicFeeRule> rules = findEligibleRules(sceneCode, subSceneCode, serviceId, userId, cityId);

        return dynamicPriceCalculationEngine.calculate(request, rules);
//        if (CollectionUtils.isEmpty(rules)) {
//            return dynamicFeeCost;
//        }
//
//        List<DynamicPricingIndicator> indicators = getApplicableIndicators(rules);
//        if (CollectionUtils.isEmpty(indicators)) {
//            log.error("没有动态价指标");
//            return dynamicFeeCost;
//        }
//
//        // 动态价增量
//        BigDecimal dynamicFeeCostDelta = calculateAdjustedFee(
//                sceneCode,
//                subSceneCode,
//                serviceId == null ? null : Long.parseLong(serviceId),
//                userId1,
//                cityId,
//                indicators,
//                cost,
//                indicatorParamList);

//        return dynamicFeeCostDelta == null ? cost : cost.add(dynamicFeeCostDelta);
    }

    private void setIndicatorValue(Map<String, List<DynamicPricingIndicator>> groupedByIndicatorCodeMap, String orderNo, LocalDateTime dateTimeValue, List<DynamicPricingIndicator.IndicatorValue> indicatorValueList) {
        for (Map.Entry<String, List<DynamicPricingIndicator>> entry : groupedByIndicatorCodeMap.entrySet()) {
            String indicatorCode = entry.getKey();
            List<DynamicPricingIndicator> dynamicPricingIndicators = entry.getValue();
            AdjustPriceIndicatorEnum indicatorEnum = AdjustPriceIndicatorEnum.fromCode(indicatorCode);
            // 如果是平均推单距离，则需要调大数据获取平均推单距离
            Double targetValue = null;
            if (AdjustPriceIndicatorEnum.AVERAGE_PUSH_DISTANCE.equals(indicatorEnum)) {
                OrderAvgPushDistance orderAvgPushDistance = bigdataGateway.getOrderAvgPushDistance(orderNo);
                targetValue = orderAvgPushDistance.getAvgPushDistance();
            } else if (AdjustPriceIndicatorEnum.ORDER_TIME_ELAPSED.equals(indicatorEnum)) {
                targetValue = calculateEffectiveHours(dateTimeValue, LocalDateTime.now());
            } else if (AdjustPriceIndicatorEnum.FIXED.equals(indicatorEnum)) {
                DynamicPricingIndicator.IndicatorValue indicatorValue = filterIndicators(dynamicPricingIndicators);
                if (indicatorValue != null) {
                    indicatorValueList.add(indicatorValue);
                }
                continue;
            }

            DynamicPricingIndicator.IndicatorValue indicatorValue = filterIndicatorValue(dynamicPricingIndicators, targetValue);
            if (indicatorValue != null) {
                indicatorValueList.add(indicatorValue);
            }
        }
    }


    private void setIndicatorValueList(Map<String, List<DynamicPricingIndicator>> groupedByIndicatorCodeMap, String orderNo, LocalDateTime dateTimeValue, List<DynamicPricingIndicator.IndicatorValue> indicatorValueList) {
        for (Map.Entry<String, List<DynamicPricingIndicator>> entry : groupedByIndicatorCodeMap.entrySet()) {
            String indicatorCode = entry.getKey();
            List<DynamicPricingIndicator> dynamicPricingIndicators = entry.getValue();
            AdjustPriceIndicatorEnum indicatorEnum = AdjustPriceIndicatorEnum.fromCode(indicatorCode);
            // 如果是平均推单距离，则需要调大数据获取平均推单距离
            Double targetValue = null;
            if (AdjustPriceIndicatorEnum.AVERAGE_PUSH_DISTANCE.equals(indicatorEnum)) {
                OrderAvgPushDistance orderAvgPushDistance = bigdataGateway.getOrderAvgPushDistance(orderNo);
                targetValue = orderAvgPushDistance.getAvgPushDistance();
            } else if (AdjustPriceIndicatorEnum.ORDER_TIME_ELAPSED.equals(indicatorEnum)) {
                targetValue = calculateEffectiveHours(dateTimeValue, LocalDateTime.now());
            } else if (AdjustPriceIndicatorEnum.FIXED.equals(indicatorEnum)) {
                // 任一非null值均可
                targetValue = 0D;
            }

            List<DynamicPricingIndicator.IndicatorValue> indicatorValues = filterIndicatorValueList(dynamicPricingIndicators, targetValue);
            if (CollectionUtils.isNotEmpty(indicatorValues)) {
                indicatorValueList.addAll(indicatorValues);
            }
        }
    }


    private List<DynamicFeeRule> findEligibleRules(String sceneCode, String subSceneCode, String serviceId, String userId, Long cityId) {
        Date now = new Date();
        Criteria criteria = new Criteria().andOperator(
                Criteria.where(BaseDocument.Fields.del).is(false),
                Criteria.where(BaseDocument.Fields.status).is(DynamicFeeRuleStatusEnum.PASS.code),
                Criteria.where(DynamicFeeRule.Fields.adjustPriceUnit).is(AdjustPriceUnitEnum.SERVICE.getCode()),
                Criteria.where(DynamicFeeRule.Fields.sceneCode).is(sceneCode),
                Criteria.where(DynamicFeeRule.Fields.subSceneCode).is(subSceneCode),
                Criteria.where(DynamicFeeRule.Fields.serviceDataList).elemMatch(Criteria.where(ServiceData.Fields.serviceId).is(Long.parseLong(serviceId))),
                Criteria.where(DynamicFeeRule.Fields.userIds).is(userId == null ? null : Long.parseLong(userId)),
                Criteria.where(DynamicFeeRule.Fields.startTime).lte(now),
                Criteria.where(DynamicFeeRule.Fields.endTime).gte(now)
        );

        criteria.orOperator(
                Criteria.where(DynamicFeeRule.Fields.divisionType).is(DivisionTypeEnum.COUNTRY.code),
                new Criteria().andOperator(
                        Criteria.where(DynamicFeeRule.Fields.divisionType).is(DivisionTypeEnum.CITY.code),
                        Criteria.where(DynamicFeeRule.Fields.divisionIds).is(cityId)
                )
        );

        return mongoTemplate.find(Query.query(criteria), DynamicFeeRule.class);
    }


    private List<DynamicPricingIndicator> getApplicableIndicators(List<DynamicFeeRule> rules) {
        return rules.stream()
                .map(DynamicFeeRule::getDynamicPricingIndicators)
                .flatMap(Collection::stream)
                .filter(e -> Arrays.stream(AdjustPriceIndicatorEnum.values())
                        .anyMatch(indicatorEnum -> indicatorEnum.getCode().equals(e.getIndicatorCode())))
                .collect(Collectors.toList());
    }


    private BigDecimal calculateAdjustedFee(
            String sceneCode,
            String subSceneCode,
            Long serviceId,
            Long userId,
            Long cityId,
            List<DynamicPricingIndicator> indicators,
            BigDecimal originalCost,
            List<DynamicIndicatorParam> indicatorParamList) {
        // 筛选出需要调大数据的指标，留下不需要调大数据的指标
        if (CollectionUtils.isEmpty(indicators)) {
            return null;
        }
        DynamicParamParser paramParser = new DynamicParamParser(indicatorParamList);
        String orderNo = paramParser.getValue(DynamicParamKeyEnum.ORDER_NO.getCode());
        LocalDateTime dateTimeValue = paramParser.getLocalDateTimeValue(DynamicParamKeyEnum.ORDER_SUBMIT_TIME.getCode());
        Double orderElapsedHours = calculateEffectiveHours(dateTimeValue, LocalDateTime.now());

        // 当前，指标分为三类，一类是 “自动计算”，一类是 “平均推单距离”（动态指标值项取本地的，但要从大数据获取平均推单距离），其它的为一类（动态指标值项取本地的，指标值取上游传的）
        // 收集 自动计算 的
        List<DynamicPricingIndicator> autoCalculateIndicators = new ArrayList<>();
        List<DynamicPricingIndicator> avgPushDistanceIndicators = new ArrayList<>();
        Iterator<DynamicPricingIndicator> iterator = indicators.iterator();
        while (iterator.hasNext()) {
            DynamicPricingIndicator indicator = iterator.next();
            if (IndicatorOptionalConditionEnum.AUTO_CALCULATE.getCode().equals(indicator.getIndicatorOptionalCondition())) {
                autoCalculateIndicators.add(indicator);
                iterator.remove();
                continue;
            }
            if (AdjustPriceIndicatorEnum.AVERAGE_PUSH_DISTANCE.getCode().equals(indicator.getIndicatorCode())) {
                avgPushDistanceIndicators.add(indicator);
            }
        }

        // 获取indicatorValue列表
        List<DynamicPricingIndicator.IndicatorValue> indicatorValueList = new ArrayList<>();
        // 其它类型
        if (CollectionUtils.isNotEmpty(indicators)) {
            for (DynamicPricingIndicator indicator : indicators) {
                String indicatorCode = indicator.getIndicatorCode();
                String condition = indicator.getIndicatorOptionalCondition();
                IndicatorOptionalConditionEnum conditionEnum = IndicatorOptionalConditionEnum.fromCode(condition);
                if (conditionEnum == null) {
                    log.warn("逻辑条件不能为空");
                    continue;
                }
                List<DynamicPricingIndicator.IndicatorValue> valueList = indicator.getIndicatorValueList();
                if (CollectionUtils.isEmpty(valueList)) {
                    log.warn("动态调价指标项不能为空");
                    continue;
                }
                if (AdjustPriceIndicatorEnum.FIXED.getCode().equals(indicatorCode)) {
                    indicatorValueList.add(valueList.get(0));
                } else if (AdjustPriceIndicatorEnum.ORDER_TIME_ELAPSED.getCode().equals(indicatorCode)) {
                    for (DynamicPricingIndicator.IndicatorValue value : valueList) {
                        if (conditionEnum.matches(orderElapsedHours, value)) {
                            indicatorValueList.add(value);
                        }
                    }
                } else {
                    log.error("当前指标在不支持，indicatorCode={}", indicatorCode);
                }
            }
        }

        // 获取自动计算指标
        if (CollectionUtils.isNotEmpty(autoCalculateIndicators)) {
            List<GetIndicatorValueRequest.ServiceIndicator> serviceIndicators = new ArrayList<>();
            GetIndicatorValueRequest.ServiceIndicator serviceIndicator = new GetIndicatorValueRequest.ServiceIndicator();
            serviceIndicator.setServiceId(serviceId);
            serviceIndicator.setIndicatorCodes(
                    autoCalculateIndicators.stream().map(DynamicPricingIndicator::getIndicatorCode).collect(Collectors.toList()));
            serviceIndicators.add(serviceIndicator);
            GetIndicatorValueRequest getIndicatorValueRequest = new GetIndicatorValueRequest();
            getIndicatorValueRequest.setSceneCode(sceneCode);
            getIndicatorValueRequest.setSubSceneCode(subSceneCode);
            getIndicatorValueRequest.setUserId(userId);
            getIndicatorValueRequest.setCityId(cityId);
            getIndicatorValueRequest.setServiceIndicators(serviceIndicators);
            GetIndicatorValueResponse valueResponse = bigdataGateway.getIndicatorValue(getIndicatorValueRequest);
            if (valueResponse != null) {
                List<GetIndicatorValueResponse.ServiceIndicatorResult> indicatorResultList = valueResponse.getServiceIndicators();
                if (CollectionUtils.isNotEmpty(indicatorResultList)) {
                    // 单个服务，故取第一个即可
                    GetIndicatorValueResponse.ServiceIndicatorResult indicatorResult = indicatorResultList.get(0);
                    List<DynamicPricingIndicator> fromBigdataIndicators = indicatorResult.getIndicators();
                    if (CollectionUtils.isNotEmpty(fromBigdataIndicators)) {
                        Map<String, List<DynamicPricingIndicator>> autoCalculateGroupByIndicatorCodeMap = fromBigdataIndicators.stream()
                                .filter(Objects::nonNull)
                                .collect(Collectors.groupingBy(DynamicPricingIndicator::getIndicatorCode));

                        setIndicatorValueList(autoCalculateGroupByIndicatorCodeMap, orderNo, dateTimeValue, indicatorValueList);
                    }
                }
            }
        }

        // 平均推单距离
        if (CollectionUtils.isNotEmpty(avgPushDistanceIndicators)) {
            if (StringUtils.isNotBlank(orderNo)) {
                OrderAvgPushDistance avgPushDistance = bigdataGateway.getOrderAvgPushDistance(orderNo);
                Double distance = avgPushDistance.getAvgPushDistance();
                for (DynamicPricingIndicator indicator : avgPushDistanceIndicators) {
                    String condition = indicator.getIndicatorOptionalCondition();
                    IndicatorOptionalConditionEnum conditionEnum = IndicatorOptionalConditionEnum.fromCode(condition);
                    if (conditionEnum != null) {
                        for (DynamicPricingIndicator.IndicatorValue value : indicator.getIndicatorValueList()) {
                            if (conditionEnum.matches(distance, value)) {
                                indicatorValueList.add(value);
                            }
                        }
                    }
                }
            }
        }

        BigDecimal dynamicFeeCost = BigDecimal.ZERO;
        for (DynamicPricingIndicator.IndicatorValue value : indicatorValueList) {
            dynamicFeeCost = dynamicFeeCost.add(calculateDynamicFeeCostByValue(value, originalCost));
        }

        return dynamicFeeCost;
    }

    private BigDecimal applyFixedAdjustment(DynamicPricingIndicator indicator, BigDecimal cost, BigDecimal dynamicFeeCost) {
        DynamicPricingIndicator.IndicatorValue indicatorValue = indicator.getIndicatorValueList().get(0);
        return calculateDynamicFeeCostByIndicator(
                indicatorValue.getAdjustPriceAction(),
                indicatorValue.getAdjustPriceType(),
                indicatorValue.getAdjustValue(),
                cost,
                dynamicFeeCost);
    }


    private BigDecimal applyConditionalAdjustment(DynamicPricingIndicator indicator, BigDecimal cost, BigDecimal dynamicFeeCost, Double orderElapsedHours, IndicatorOptionalConditionEnum conditionEnum) {
        for (DynamicPricingIndicator.IndicatorValue iv : indicator.getIndicatorValueList()) {
            if (conditionEnum.matches(orderElapsedHours, iv)) {
                dynamicFeeCost = calculateDynamicFeeCostByIndicator(
                        iv.getAdjustPriceAction(),
                        iv.getAdjustPriceType(),
                        iv.getAdjustValue(),
                        cost,
                        dynamicFeeCost);
            }
        }
        return dynamicFeeCost;
    }


    /**
     * 计算从 start 到 end 的总小时数，扣除每天 23:00-08:00 的夜间小时数
     */
    private double calculateEffectiveHours(LocalDateTime start, LocalDateTime end) {
        if (end.isBefore(start)) {
            throw new IllegalArgumentException("结束时间必须在开始时间之后");
        }

        // 1. 总分钟数
        long totalMinutes = Duration.between(start, end).toMinutes();

        // 2. 累加所有夜间时段重叠的分钟数
        long blockedMinutes = 0;
        LocalDate date = start.toLocalDate();
        LocalDate lastDate = end.toLocalDate();
        while (!date.isAfter(lastDate)) {
            LocalDateTime blockStart = LocalDateTime.of(date, LocalTime.of(23, 0));
            LocalDateTime blockEnd = LocalDateTime.of(date.plusDays(1), LocalTime.of(8, 0));

            LocalDateTime overlapStart = max(start, blockStart);
            LocalDateTime overlapEnd = min(end, blockEnd);
            if (overlapEnd.isAfter(overlapStart)) {
                blockedMinutes += Duration.between(overlapStart, overlapEnd).toMinutes();
            }
            date = date.plusDays(1);
        }

        long effectiveMinutes = totalMinutes - blockedMinutes;

        // 3. 转换成小时，并四舍五入保留两位小数
        BigDecimal hours = BigDecimal.valueOf(effectiveMinutes)
                .divide(BigDecimal.valueOf(60), 4, RoundingMode.HALF_UP)
                .setScale(2, RoundingMode.HALF_UP);

        return hours.doubleValue();
    }


    private LocalDateTime max(LocalDateTime a, LocalDateTime b) {
        return a.isAfter(b) ? a : b;
    }


    private LocalDateTime min(LocalDateTime a, LocalDateTime b) {
        return a.isBefore(b) ? a : b;
    }


    private LocalDateTime getOrderSubmitTimeFromIndicatorParam(List<DynamicIndicatorParam> indicatorParamList) {
        if (CollectionUtils.isEmpty(indicatorParamList)) {
            return null;
        }
        AtomicReference<LocalDateTime> orderSubmitLocalTime = new AtomicReference<>();
        indicatorParamList.forEach(indicatorParam -> {
            String key = indicatorParam.getKey();
            if (DynamicParamKeyEnum.ORDER_SUBMIT_TIME.getCode().equals(key)) {
                orderSubmitLocalTime.set(DateUtil.parseLocalDateTime(indicatorParam.getValue()));
            }
        });
        return orderSubmitLocalTime.get();
    }


    private String getOrderNoFromIndicatorParam(List<DynamicIndicatorParam> indicatorParamList) {
        if (CollectionUtils.isEmpty(indicatorParamList)) {
            return null;
        }
        return indicatorParamList.stream()
                .filter(param -> DynamicParamKeyEnum.ORDER_NO.getCode().equals(param.getKey()))
                .map(DynamicIndicatorParam::getValue)
                .findFirst()
                .orElse(null);
    }

    private Map<String, String> getIndicatorParamMap(List<DynamicIndicatorParam> indicatorParamList) {
        if (CollectionUtils.isEmpty(indicatorParamList)) {
            return null;
        }
        return indicatorParamList.stream()
                .collect(Collectors.toMap(DynamicIndicatorParam::getKey, DynamicIndicatorParam::getValue));
    }


    private BigDecimal calculateDynamicFeeCostByIndicator(
            String adjustPriceAction,
            String adjustPriceType,
            Double adjustValue,
            BigDecimal cost, BigDecimal dynamicFeeCost) {
        DynamicSymbolEnum symbolEnum = DynamicSymbolEnum.fromCode(adjustPriceAction);
        AdjustPriceTypeEnum priceTypeEnum = AdjustPriceTypeEnum.fromCode(adjustPriceType);
        double adjustPrice = new PriceAdjuster(priceTypeEnum, symbolEnum, adjustValue, cost.doubleValue()).getAdjustPrice();
        dynamicFeeCost = dynamicFeeCost.add(BigDecimal.valueOf(adjustPrice));
        return dynamicFeeCost;
    }


    private BigDecimal calculateDynamicFeeCostByValue(DynamicPricingIndicator.IndicatorValue indicatorValue, BigDecimal cost) {
        String adjustPriceAction = indicatorValue.getAdjustPriceAction();
        String adjustPriceType = indicatorValue.getAdjustPriceType();
        Double adjustValue = indicatorValue.getAdjustValue();
        DynamicSymbolEnum symbolEnum = DynamicSymbolEnum.fromCode(adjustPriceAction);
        AdjustPriceTypeEnum priceTypeEnum = AdjustPriceTypeEnum.fromCode(adjustPriceType);
        double adjustPrice = new PriceAdjuster(priceTypeEnum, symbolEnum, adjustValue, cost.doubleValue()).getAdjustPrice();
        return BigDecimal.valueOf(adjustPrice);
    }


    /**
     * 按指标分组，并筛选出各组中最符合条件的指标项，筛选逻辑如下：
     * 1、如果均为固定值或均为百分比，则取值最小的
     * 2、如果既存在固定值，又存在百分比，则取百分比的
     *
     * @param indicators 指标
     * @param targetValue 目标值
     * @return 筛选后的指标项
     */
    private DynamicPricingIndicator.IndicatorValue filterIndicatorValue(List<DynamicPricingIndicator> indicators, final Double targetValue) {
        if (CollectionUtils.isEmpty(indicators)) {
            return null;
        }

        List<DynamicPricingIndicator> filteredIndicators = indicators.stream()
                .peek(indicator -> filterIndicatorValues(indicator, targetValue))
                .filter(Objects::nonNull)
                .filter(ind -> CollectionUtils.isNotEmpty(ind.getIndicatorValueList()))
                .collect(Collectors.toList());

        return filterIndicators(filteredIndicators);
    }


    private List<DynamicPricingIndicator.IndicatorValue> filterIndicatorValueList(List<DynamicPricingIndicator> indicators, final Double targetValue) {
        if (CollectionUtils.isEmpty(indicators)) {
            return null;
        }

        return indicators.stream()
                .peek(indicator -> filterIndicatorValues(indicator, targetValue))
                .filter(Objects::nonNull)
                .filter(ind -> CollectionUtils.isNotEmpty(ind.getIndicatorValueList()))
                .flatMap(ind -> ind.getIndicatorValueList().stream())
                .collect(Collectors.toList());
    }

    /**
     * 过滤单个Indicator的indicatorValue列表
     *
     * @param indicator   包含条件和值的对象
     * @param targetValue 用于比较的目标值
     */
    private void filterIndicatorValues(DynamicPricingIndicator indicator, Double targetValue) {
        if (indicator == null || CollectionUtils.isEmpty(indicator.getIndicatorValueList())) {
            return;
        }

        IndicatorOptionalConditionEnum condition = IndicatorOptionalConditionEnum.fromCode(
                indicator.getIndicatorOptionalCondition()
        );

        // 条件无效时清空结果
        if (condition == null) {
            indicator.setIndicatorValueList(Collections.emptyList());
            return;
        }

        List<DynamicPricingIndicator.IndicatorValue> filteredValues = indicator.getIndicatorValueList().stream()
                .filter(iv -> matchesCondition(condition, iv, targetValue))
                .collect(Collectors.toList());

        indicator.setIndicatorValueList(filteredValues);
    }

    /**
     * 检查值是否满足条件
     *
     * @param condition   条件枚举
     * @param iv          指标值对象
     * @param targetValue 目标比较值
     * @return 是否匹配
     */
    private boolean matchesCondition(IndicatorOptionalConditionEnum condition,
                                     DynamicPricingIndicator.IndicatorValue iv,
                                     Double targetValue) {
        if (targetValue == null || iv == null) return false;

        // 其他条件使用枚举内置逻辑
        return condition.matches(targetValue, iv);
    }


    /**
     * 过滤逻辑：
     * 1、如果均为固定值或均为百分比，则取值最小的
     * 2、如果既存在固定值，又存在百分比，则取百分比，且为最小的
     * <p>
     * 前提条件：indicators中各对象的indicatorCode相同
     * </p>
     *
     * @param indicators 指标列表
     */
    private DynamicPricingIndicator.IndicatorValue filterIndicators(List<DynamicPricingIndicator> indicators) {

        if (CollectionUtils.isEmpty(indicators)) {
            return null;
        }

        List<DynamicPricingIndicator.IndicatorValue> allValues = indicators.stream()
                .filter(Objects::nonNull)
                .map(DynamicPricingIndicator::getIndicatorValueList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(allValues)) {
            return null;
        }

        // 优先取最小 percent 类型
        return allValues.stream()
                .filter(v -> AdjustPriceTypeEnum.PERCENT.getCode().equals(v.getAdjustPriceType()))
                .min(Comparator.comparingDouble(v -> Optional.ofNullable(v.getAdjustValue()).orElse(Double.MAX_VALUE)))
                .orElseGet(() ->
                        allValues.stream()
                                .filter(v -> AdjustPriceTypeEnum.FIXED.getCode().equals(v.getAdjustPriceType()))
                                .min(Comparator.comparingDouble(v -> Optional.ofNullable(v.getAdjustValue()).orElse(Double.MAX_VALUE)))
                                .orElse(null)
                );
    }


    private Map<Long, List<DynamicPricingIndicator>> groupByServiceId(List<DynamicFeeRule> ruleList) {
        return ruleList.stream()
                // 过滤无效规则：serviceDataList 或 dynamicPricingIndicators 为空
                .filter(rule -> rule.getServiceDataList() != null && rule.getDynamicPricingIndicators() != null)
                // 展开规则：将每个规则拆分为 (serviceId, indicators) 键值对
                .flatMap(rule ->
                        rule.getServiceDataList().stream()
                                .filter(serviceData -> serviceData.getServiceId() != null) // 过滤无效 serviceId
                                .map(serviceData ->
                                        new AbstractMap.SimpleEntry<>(
                                                serviceData.getServiceId(),
                                                new ArrayList<>(rule.getDynamicPricingIndicators())
                                        )
                                )
                )
                // 分组并合并：按 serviceId 分组，合并所有 indicators
                .collect(Collectors.groupingBy(
                        AbstractMap.SimpleEntry::getKey,
                        Collectors.collectingAndThen(
                                Collectors.mapping(AbstractMap.SimpleEntry::getValue, Collectors.toList()),
                                lists -> lists.stream().flatMap(List::stream).collect(Collectors.toList())
                        )
                ));
    }


    /**
     * 动态调价指标分为三类：
     * 1、第一类，算价逻辑为 “自动计算”，此类的特点是 需要根据场景编码、子场景编码（可选）、服务id+指标code数组、用户id（可选）、城市id（可选），获取指标值列表（未经过过滤，所以需要在计价侧自行过滤，过滤用到的目标值又分多种类型：
     * 如果是“按距离下单时间”，则取上游传入的下单时间与当前时间的差，并且要去掉晚上23:00-8:00这几个小时；
     * 如果是“按平均推单距离”，则通过 订单号 调大数据接口获取平均推单距离，
     * 如果是“按固定调价”，则取大数据的调整值最小的那条，不用通过目标值过滤；
     * 如果是“按偏远地区”，则取本地配置的指标值是否与大数据返回的相同；
     *
     * 2、第二类，算价逻辑为 非“自动计算”，且指标为“按平均推单距离”，过滤用到的目标值（参照值）则也是要从大数据获取（通过订单号），然后与本地配置的区间范围匹配，符合条件的保留；
     *
     * 3、第三类，其它，即排除上两类的，过滤用到的目标值来源于入参（例如下单时间，可以算出当前距离下单时间相隔的小时数），指标的范围也来源于本地配置，符合条件的保留。
     *
     * @param request 计算服务动态价格请求体
     * @return CalculateServiceDynamicPriceResponse
     */
    @Override
    public CalculateServiceDynamicPriceResponse calculateServiceDynamicPrice(CalculateServiceDynamicPriceRequest request) {
        // 1. 查找适用的规则
        List<DynamicFeeRule> rules = findApplicableRules(request);
        if (CollectionUtils.isEmpty(rules)) {
            throw new BusException("未找到动态价规则");
        }

        // 2. 委托给计价引擎进行计算
        BigDecimal serviceDynamicPrice = dynamicPriceCalculationEngine.calculate(request, rules);
        return new CalculateServiceDynamicPriceResponse(serviceDynamicPrice);
    }


    private List<DynamicFeeRule> findApplicableRules(CalculateServiceDynamicPriceRequest request) {
        String sceneCode = request.getSceneCode();
        Set<Long> serviceIds = request.getServiceIds();
        Criteria criteria = Criteria.where(DynamicFeeRule.Fields.dynamicPricingIndicators).exists(true).ne(null).ne(Collections.emptyList())
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(DynamicFeeRuleStatusEnum.PASS.code)
                .and(DynamicFeeRule.Fields.adjustPriceUnit).is(AdjustPriceUnitEnum.SERVICE.getCode())
                .and(DynamicFeeRule.Fields.sceneCode).is(sceneCode)
                .and(DynamicFeeRule.Fields.serviceDataList).elemMatch(Criteria.where(ServiceData.Fields.serviceId).in(serviceIds));

        String subSceneCode = request.getSubSceneCode();
        if (com.wanshifu.framework.utils.StringUtils.isNotBlank(subSceneCode)) {
            criteria.and(DynamicFeeRule.Fields.subSceneCode).is(subSceneCode);
        }
        Long userId = request.getUserId();
        if (userId != null) {
            criteria.and(DynamicFeeRule.Fields.userIds).is(userId);
        }
        return mongoTemplate.find(Query.query(criteria), DynamicFeeRule.class);
    }
}

