package com.wanshifu.service;

import com.wanshifu.fee.center.domain.document.SceneAccountPermissions;
import com.wanshifu.fee.center.domain.request.permission.GetPageRequest;
import com.wanshifu.fee.center.domain.request.permission.UpdateRequest;
import com.wanshifu.framework.core.page.SimplePageInfo;

import java.util.List;

public interface SceneAccountPermissionsService {

    SimplePageInfo<SceneAccountPermissions> getPage(GetPageRequest request);

    void update(UpdateRequest request);

    List<String> getSceneCodesByAccount(String account);
}
