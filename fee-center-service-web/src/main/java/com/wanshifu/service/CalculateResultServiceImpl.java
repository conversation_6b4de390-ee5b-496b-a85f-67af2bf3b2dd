package com.wanshifu.service;

import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.CalculateResult;
import com.wanshifu.fee.center.domain.request.CalculateResultReq;
import com.wanshifu.framework.core.BusException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
public class CalculateResultServiceImpl implements CalculateResultService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public Page<CalculateResult> queryByCondition(CalculateResultReq calculateResultReq) {
        if (Objects.isNull(calculateResultReq.getCalculateResultId()) && Objects.isNull(calculateResultReq.getTid())) {
            throw new BusException("tid 和 calculateResultId不能同时为null");
        }

        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        if (Objects.nonNull(calculateResultReq.getTid())) {
            criteria.and(CalculateResult.Fields.tid).is(calculateResultReq.getTid());
        }
        if (Objects.nonNull(calculateResultReq.getCalculateResultId())) {
            criteria.and(CalculateResult.Fields.calculateResultId).is(calculateResultReq.getCalculateResultId());
        }

        Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.sort));
        PageRequest pageRequest = new PageRequest(calculateResultReq.pageNum - 1, calculateResultReq.pageSize, sort);

        Query query = Query.query(criteria);
        long count = mongoTemplate.count(query, CalculateResult.class);
        if (count > 0) {
            query.with(pageRequest);
            List<CalculateResult> calculateResults = mongoTemplate.find(query, CalculateResult.class);
            return new PageImpl<>(calculateResults, pageRequest, count);
        } else {
            return new PageImpl<>(Collections.emptyList(), pageRequest, count);
        }
    }
}
