package com.wanshifu.service;

import com.wanshifu.fee.center.domain.document.SyncBlacklistStrategy;
import com.wanshifu.fee.center.domain.request.strategy.SyncBlacklistStrategyBatchUpdateStatusReq;
import com.wanshifu.fee.center.domain.request.strategy.SyncBlacklistStrategyPageReq;
import com.wanshifu.fee.center.domain.request.strategy.SyncBlacklistStrategyPageResp;
import com.wanshifu.fee.center.domain.request.strategy.SyncBlacklistStrategySaveReq;
import com.wanshifu.framework.core.page.SimplePageInfo;

import java.util.List;

public interface SyncBlacklistStrategyService {

    void add(SyncBlacklistStrategySaveReq req);

    void modify(SyncBlacklistStrategySaveReq req);

    void delete(Long strategyId);

    void batchUpdateStatus(SyncBlacklistStrategyBatchUpdateStatusReq req);

    SimplePageInfo<SyncBlacklistStrategyPageResp> findPage(SyncBlacklistStrategyPageReq req);

    List<SyncBlacklistStrategy> selectListBySceneCode(String sceneCode);

    SyncBlacklistStrategy detail(Long strategyId);


    List<SyncBlacklistStrategy> selectBySceneCodeAndServiceIdAndUserId(String sceneCode, Long serviceId, String userId);
}
