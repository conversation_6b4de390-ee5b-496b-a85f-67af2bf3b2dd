package com.wanshifu.service;


import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.FeeReasonRelation;
import com.wanshifu.fee.center.domain.enums.FeeReasonRelationStatusEnum;
import com.wanshifu.fee.center.domain.request.FeeReasonRelationMaintainReq;
import com.wanshifu.fee.center.domain.request.FeeReasonRelationQueryReq;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.repository.FeeReasonRelationRepository;
import com.wanshifu.infrastructure.utils.PatternUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class FeeReasonRelationServiceImpl implements FeeReasonRelationService {

    @Resource
    private FeeReasonRelationRepository feeReasonRelationRepository;

    @Resource
    private MongoTemplate mongoTemplate;


    @Override
    public List<FeeReasonRelation> query(FeeReasonRelationQueryReq feeReasonRelationQueryReq) {
        Criteria findCondition = Criteria.where(BaseDocument.Fields.del).is(false);

        if (Objects.nonNull(feeReasonRelationQueryReq.getReasonId())) {
            findCondition.and(FeeReasonRelation.Fields.reasonId).is(feeReasonRelationQueryReq.getReasonId());
        }

        if (CollectionUtils.isNotEmpty(feeReasonRelationQueryReq.getServiceIds())) {
            findCondition.and(FeeReasonRelation.Fields.serviceId).in(feeReasonRelationQueryReq.getServiceIds());
        }
        // 场景
        if (StringUtils.isNotBlank(feeReasonRelationQueryReq.getSkuRuleScene())) {
            findCondition.and(FeeReasonRelation.Fields.skuRuleScene).is(feeReasonRelationQueryReq.getSkuRuleScene());
        }

        /**
         * 管理条件
         */

        if (StringUtils.isNotBlank(feeReasonRelationQueryReq.getServiceName())) {
            findCondition.and(FeeReasonRelation.Fields.serviceName).regex(PatternUtils.toEscapeStr(feeReasonRelationQueryReq.getServiceName()));
        }

        if (StringUtils.isNotBlank(feeReasonRelationQueryReq.getSkuType())) {
            findCondition.and(FeeReasonRelation.Fields.skuType).is(feeReasonRelationQueryReq.getSkuType());
        }

        if (StringUtils.isNotBlank(feeReasonRelationQueryReq.getServiceMode())) {
            findCondition.and(FeeReasonRelation.Fields.serviceMode).is(feeReasonRelationQueryReq.getServiceMode());
        }

        if (Objects.nonNull(feeReasonRelationQueryReq.getLevel1GoodsCategoryId())) {
            findCondition.and(FeeReasonRelation.Fields.level1GoodsCategoryId).is(feeReasonRelationQueryReq.getLevel1GoodsCategoryId());
        }

        if (Objects.nonNull(feeReasonRelationQueryReq.getLevel2GoodsCategoryId())) {
            findCondition.and(FeeReasonRelation.Fields.level2GoodsCategoryId).is(feeReasonRelationQueryReq.getLevel2GoodsCategoryId());
        }

        if (Objects.nonNull(feeReasonRelationQueryReq.getLevel3GoodsCategoryId())) {
            findCondition.and(FeeReasonRelation.Fields.level3GoodsCategoryId).is(feeReasonRelationQueryReq.getLevel3GoodsCategoryId());
        }

        // 查询
        Query query = Query.query(findCondition);


        return mongoTemplate.find(query, FeeReasonRelation.class);
    }

    @Override
    public List<FeeReasonRelation> maintain(FeeReasonRelationMaintainReq feeReasonRelationMaintainReq) {
        log.info("开始保存原因与sku关联关系");
        // FIXME 此处应该做成事务
        // 删除旧数据
        Criteria findCondition = Criteria.where(BaseDocument.Fields.del).is(false);
        /*
         * 管理条件
         */
        if (StringUtils.isNotBlank(feeReasonRelationMaintainReq.getServiceName())) {
            findCondition.and(FeeReasonRelation.Fields.serviceName).regex(PatternUtils.toEscapeStr(feeReasonRelationMaintainReq.getServiceName()));
        }
        if (StringUtils.isNotBlank(feeReasonRelationMaintainReq.getSkuRuleScene())) {
            findCondition.and(FeeReasonRelation.Fields.skuRuleScene).is(feeReasonRelationMaintainReq.getServiceName());
        }

        if (StringUtils.isNotBlank(feeReasonRelationMaintainReq.getSkuType())) {
            findCondition.and(FeeReasonRelation.Fields.skuType).is(feeReasonRelationMaintainReq.getSkuType());
        }

        if (StringUtils.isNotBlank(feeReasonRelationMaintainReq.getServiceMode())) {
            findCondition.and(FeeReasonRelation.Fields.serviceMode).is(feeReasonRelationMaintainReq.getServiceMode());
        }

        if (Objects.nonNull(feeReasonRelationMaintainReq.getLevel1GoodsCategoryId())) {
            findCondition.and(FeeReasonRelation.Fields.level1GoodsCategoryId).is(feeReasonRelationMaintainReq.getLevel1GoodsCategoryId());
        }

        if (Objects.nonNull(feeReasonRelationMaintainReq.getLevel2GoodsCategoryId())) {
            findCondition.and(FeeReasonRelation.Fields.level2GoodsCategoryId).is(feeReasonRelationMaintainReq.getLevel2GoodsCategoryId());
        }

        if (Objects.nonNull(feeReasonRelationMaintainReq.getLevel3GoodsCategoryId())) {
            findCondition.and(FeeReasonRelation.Fields.level3GoodsCategoryId).is(feeReasonRelationMaintainReq.getLevel3GoodsCategoryId());
        }

        if (Objects.nonNull(feeReasonRelationMaintainReq.getReasonId())) {
            findCondition.and(FeeReasonRelation.Fields.reasonId).is(feeReasonRelationMaintainReq.getReasonId());
        }

        // 查询
        Query query = Query.query(findCondition);
        List<FeeReasonRelation> removedDoc = mongoTemplate.findAllAndRemove(query, FeeReasonRelation.class);
        log.info("保存原因与sku关联关系 -- 旧数据已删除: count={}", Objects.nonNull(removedDoc) ? removedDoc.size() : 0);
        // 插入新数据
        List<FeeReasonRelationMaintainReq.FeeReasonRelationData> feeReasonRelationDataList = feeReasonRelationMaintainReq.getFeeReasonRelationDataList();
        if (CollectionUtils.isEmpty(feeReasonRelationDataList)){
            log.info("保存原因与sku关联关系 -- 新数据为空");
            return Collections.emptyList();
        }

        List<FeeReasonRelation> feeReasonRelations= new ArrayList<>();
        Date now = new Date();
        for (FeeReasonRelationMaintainReq.FeeReasonRelationData feeReasonRelationData : feeReasonRelationDataList) {
            FeeReasonRelation feeReasonRelation = new FeeReasonRelation();
            BeanUtils.copyProperties(feeReasonRelationData,feeReasonRelation);
            // 转换
            feeReasonRelation.setRelationId(SnowFlakeGenerator.INSTANCE.generate());
            feeReasonRelation.setDel(false);
            feeReasonRelation.setStatus(FeeReasonRelationStatusEnum.ACTIVE.code);
            feeReasonRelation.setCreateTime(now);
            feeReasonRelation.setModifyTime(now);

//            feeReasonRelation.setReasonId(feeReasonRelationData.getReasonId());
//            feeReasonRelation.setServiceId(feeReasonRelationData.getServiceId());
//            feeReasonRelation.setServiceMode(feeReasonRelationData.getServiceMode());
//            feeReasonRelation.setServiceName(feeReasonRelationData.getServiceName());
//            feeReasonRelation.setSkuNo(feeReasonRelationData.getSkuNo());
//            feeReasonRelation.setLevel1GoodsCategoryId(feeReasonRelationData.getLevel1GoodsCategoryId());
//            feeReasonRelation.setLevel1GoodsCategoryName(feeReasonRelationData.getLevel1GoodsCategoryName());
//            feeReasonRelation.setLevel2GoodsCategoryId(feeReasonRelationData.getLevel2GoodsCategoryId());
//            feeReasonRelation.setLevel2GoodsCategoryName(feeReasonRelationData.getLevel2GoodsCategoryName());
//            feeReasonRelation.setLevel3GoodsCategoryId(feeReasonRelationData.getLevel3GoodsCategoryId());
//            feeReasonRelation.setLevel3GoodsCategoryName(feeReasonRelationData.getLevel3GoodsCategoryName());
//            feeReasonRelation.setSkuPathName(feeReasonRelationData.getSkuPathName());
//            feeReasonRelation.setSkuType(feeReasonRelationData.getSkuType());


            feeReasonRelations.add(feeReasonRelation);
        }

        log.info("保存原因与sku关联关系 -- 新数据已转换: count={}", feeReasonRelations.size());
        List<FeeReasonRelation> reasonRelations = feeReasonRelationRepository.save(feeReasonRelations);
        log.info("保存原因与sku关联关系 -- 新数据已入库: count={}", reasonRelations.size());
        return reasonRelations;

    }
}
