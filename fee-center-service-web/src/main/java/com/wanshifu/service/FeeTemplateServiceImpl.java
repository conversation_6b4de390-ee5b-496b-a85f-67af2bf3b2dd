package com.wanshifu.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.ql.util.express.ExpressRunner;
import com.wanshifu.adapter.api.ServiceApi;
import com.wanshifu.adapter.api.ServiceSkuApi;
import com.wanshifu.adapter.dto.attribute.sku.ServiceSkuReq;
import com.wanshifu.adapter.dto.service.sku.ServiceSkuResp;
import com.wanshifu.domain.dto.table.BatchCustomSkuTemplateExcelModel;
import com.wanshifu.domain.dto.table.CustomSkuTemplateExcelModel;
import com.wanshifu.domain.enums.CheckReliedEnum;
import com.wanshifu.domain.request.template.TemplateDelRequest;
import com.wanshifu.domain.response.template.TemplateDelResponse;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.constant.Constant;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.*;
import com.wanshifu.fee.center.domain.dto.ServiceAttribute;
import com.wanshifu.fee.center.domain.dto.ServiceAttributeValue;
import com.wanshifu.fee.center.domain.enums.*;
import com.wanshifu.fee.center.domain.request.FeeTemplateBatchQueryReq;
import com.wanshifu.fee.center.domain.request.FeeTemplateConfigureReq;
import com.wanshifu.fee.center.domain.request.FeeTemplateQueryByConditionReq;
import com.wanshifu.fee.center.domain.request.FeeTemplateQueryReq;
import com.wanshifu.fee.center.domain.request.template.*;
import com.wanshifu.fee.center.domain.response.GetFeeTemplateServiceIdAndNameResp;
import com.wanshifu.fee.center.domain.response.GetFeeTemplateSkuListResp;
import com.wanshifu.fee.center.domain.response.SceneBaseInfo;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.MapUtils;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.utils.StrKit;
import com.wanshifu.repository.*;
import com.wanshifu.strategy.express.ExpressCompiler;
import com.wanshifu.infrastructure.utils.CommonUtils;
import com.wanshifu.infrastructure.utils.PatternUtils;
import com.wanshifu.repository.FeeRuleDraftRepository;
import com.wanshifu.repository.FeeRuleRepository;
import com.wanshifu.repository.FeeTemplateRepository;
import com.wanshifu.repository.SceneInfoRepository;
import com.wanshifu.strategy.express.ExpressCompiler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;
import static org.springframework.data.mongodb.core.query.Criteria.where;

@Slf4j
@Service
public class FeeTemplateServiceImpl implements FeeTemplateService {
    @Resource
    private FeeTemplateRepository feeTemplateRepository;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private ServiceSkuApi serviceSkuApi;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private ExpressRunner expressRunner;

    @Resource
    private ExpressCompiler expressCompilerComposite;
    @Resource
    private SceneInfoService sceneInfoService;
    @Resource
    private ServiceApi serviceApi;
    @Autowired
    private FeeRuleRepository feeRuleRepository;
    @Autowired
    private FeeRuleDraftRepository feeRuleDraftRepository;
    @Autowired
    private SceneInfoRepository sceneInfoRepository;
    @Autowired
    private FeeTemplateMappingService feeTemplateMappingService;

    @Override
    @Deprecated // 尽量使用queryByCondition
    public Page<FeeTemplate> queryByCondition(FeeTemplateQueryReq feeTemplateQueryReq) {

        Criteria criteria = where(BaseDocument.Fields.del).is(false);
        // 映射设置to的查询列表需要过滤掉无费用类型的数据
//                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).ne(null);
        if (Objects.nonNull(feeTemplateQueryReq.getTemplateId())) {
            criteria.and(FeeTemplate.Fields.templateId).is(feeTemplateQueryReq.getTemplateId());
        }
        if (StringUtils.isNotBlank(feeTemplateQueryReq.getSceneCode())) {
            criteria.and(FeeTemplate.Fields.sceneCode).is(feeTemplateQueryReq.getSceneCode());
        }
        if (StringUtils.isNotBlank(feeTemplateQueryReq.getGroup())) {
            criteria.and(FeeTemplate.Fields.group).is(feeTemplateQueryReq.getGroup());
        }
        if (StringUtils.isNotBlank(feeTemplateQueryReq.getSceneName())) {
            criteria.and(FeeTemplate.Fields.sceneName).regex(PatternUtils.toEscapeStr(feeTemplateQueryReq.getSceneName()));
        }
        if (CollectionUtils.isNotEmpty(feeTemplateQueryReq.getStatusList())) {
            criteria.and(BaseDocument.Fields.status).in(feeTemplateQueryReq.getStatusList());
        }
        if (feeTemplateQueryReq.getFeeTypeTagNotBlank()) {
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).ne(null);
        }

        Map<String, String> bizRule = feeTemplateQueryReq.getBizRule();
        if (MapUtils.isNotEmpty(bizRule)) {
            bizRule.forEach((key, value) -> {
                        // 服务名称 模糊匹配
                        if ("serviceName".equals(key)) {
                            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + key).regex(PatternUtils.toEscapeStr(value));
                        } else {
                            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + key).is(value);
                        }
                    }
            );
//            String userId = bizRule.get(CommonBizRule.Fields.userId);
//            if (StringUtils.isBlank(userId)) {
//                criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.customSkuUserId).exists(false);
//            }
        }

        Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.sort));
        PageRequest pageRequest = new PageRequest(feeTemplateQueryReq.pageNum - 1, feeTemplateQueryReq.pageSize, sort);

        Query query = Query.query(criteria);
        long count = mongoTemplate.count(query, FeeTemplate.class);
        if (count > 0) {
            query.with(pageRequest);
            List<FeeTemplate> feeTemplates = mongoTemplate.find(query, FeeTemplate.class);
            return new PageImpl<>(feeTemplates, pageRequest, count);
        } else {
            return new PageImpl<>(Collections.emptyList(), pageRequest, count);
        }
    }

    @Override
    public Page<FeeTemplate> queryServiceByCondition(FeeTemplateQueryByConditionReq feeTemplateQueryByConditionReq) {
        Criteria criteria = where(BaseDocument.Fields.del).is(false);
        // 分组依据必须要有
        criteria.and(FeeTemplate.Fields.group).ne(null);
        // tagName不能为空
//        criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + MasterAutoOfferPriceBizRule.Fields.feeTypeTagName).ne(null);
        if (StringUtils.isNotBlank(feeTemplateQueryByConditionReq.getSceneCode())) {
            criteria.and(FeeTemplate.Fields.sceneCode).is(feeTemplateQueryByConditionReq.getSceneCode());
        }

        if (StringUtils.isNotBlank(feeTemplateQueryByConditionReq.getSceneName())) {
            criteria.and(FeeTemplate.Fields.sceneName).regex(PatternUtils.toEscapeStr(feeTemplateQueryByConditionReq.getSceneName()));
        }
        if (!Strings.isNullOrEmpty(feeTemplateQueryByConditionReq.getQueryType())
                && "batchUpdateSkuByService".equals(feeTemplateQueryByConditionReq.getQueryType())) {
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag)
                    .ne("standardSurcharge");
        }

        List<FeeTemplateQueryByConditionReq.BizRuleCondition> bizRule = feeTemplateQueryByConditionReq.getBizRule();
        if (CollectionUtils.isNotEmpty(bizRule)) {
            bizRule.forEach(condition -> {
                if (StringUtils.isNotBlank(condition.getKey()) && StringUtils.isNotBlank(condition.getValue())) {
                    if (condition.getMode() == BizRuleConditionModeEnum.ALL_MATCH.code) {
                        criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + condition.getKey()).is(condition.getValue());
                    } else if (condition.getMode() == BizRuleConditionModeEnum.FUZZY_MATCH.code) {
                        criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + condition.getKey()).regex(PatternUtils.toFuzzySearch(PatternUtils.toEscapeStr(condition.getValue())));
                    }
                }
            });
//            addUserIdCondition(bizRule, criteria);
        }

        TypedAggregation<FeeTemplate> feeTemplateTypedAggregation = TypedAggregation.newAggregation(FeeTemplate.class,
                Aggregation.match(criteria),// 查询条件
                Aggregation.group(FeeTemplate.Fields.group).first("$$ROOT").as("info"),
                Aggregation.replaceRoot("$info"),
                Aggregation.count().as("count")
        );
        PageRequest pageRequest = new PageRequest(feeTemplateQueryByConditionReq.pageNum - 1, feeTemplateQueryByConditionReq.pageSize);

        Long count = 0L;
        JSONObject uniqueMappedResult = mongoTemplate.aggregate(feeTemplateTypedAggregation, JSONObject.class).getUniqueMappedResult();
        if (Objects.nonNull(uniqueMappedResult)) {
            count = uniqueMappedResult.getLong("count");
        }
        if (count > 0) {
            Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.createTime));
            feeTemplateTypedAggregation = TypedAggregation.newAggregation(FeeTemplate.class,
                    Aggregation.match(criteria),// 查询条件
                    Aggregation.group(FeeTemplate.Fields.group).first("$$ROOT").as("info"),
                    Aggregation.replaceRoot("$info"),
                    Aggregation.sort(sort),
                    Aggregation.skip((feeTemplateQueryByConditionReq.pageNum - 1) * feeTemplateQueryByConditionReq.pageSize),
                    Aggregation.limit(feeTemplateQueryByConditionReq.pageSize)
            );
            AggregationResults<FeeTemplate> aggregate = mongoTemplate.aggregate(feeTemplateTypedAggregation, FeeTemplate.class);
            List<FeeTemplate> mappedResults = aggregate.getMappedResults();
            return new PageImpl<>(mappedResults, pageRequest, count);
        } else {
            return new PageImpl<>(Collections.emptyList(), pageRequest, count);
        }
    }

    @Override
    public Page<FeeTemplate> queryByCondition(FeeTemplateQueryByConditionReq feeTemplateQueryReq) {

        Criteria criteria = where(BaseDocument.Fields.del).is(false);
        if (Objects.nonNull(feeTemplateQueryReq.getTemplateId())) {
            criteria.and(FeeTemplate.Fields.templateId).is(feeTemplateQueryReq.getTemplateId());
        }
        if (StringUtils.isNotBlank(feeTemplateQueryReq.getSceneCode())) {
            criteria.and(FeeTemplate.Fields.sceneCode).is(feeTemplateQueryReq.getSceneCode());
        }
        if (StringUtils.isNotBlank(feeTemplateQueryReq.getGroup())) {
            criteria.and(FeeTemplate.Fields.group).is(feeTemplateQueryReq.getGroup());
        }
//        if (StringUtils.isNotBlank(feeTemplateQueryReq.getSceneName())) {
//            criteria.and(FeeTemplate.Fields.sceneName).regex(PatternUtils.toEscapeStr(feeTemplateQueryReq.getSceneName()));
//        }
        if (CollectionUtils.isNotEmpty(feeTemplateQueryReq.getStatusList())) {
            criteria.and(BaseDocument.Fields.status).in(feeTemplateQueryReq.getStatusList());
        }

        List<FeeTemplateQueryByConditionReq.BizRuleCondition> bizRule = feeTemplateQueryReq.getBizRule();
        if (CollectionUtils.isNotEmpty(bizRule)) {
            bizRule.forEach(condition -> {
                if (StringUtils.isNotBlank(condition.getKey()) && StringUtils.isNotBlank(condition.getValue())) {
                    if (condition.getMode() == BizRuleConditionModeEnum.ALL_MATCH.code) {
                        criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + condition.getKey()).is(condition.getValue());
                    } else if (condition.getMode() == BizRuleConditionModeEnum.FUZZY_MATCH.code) {
                        criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + condition.getKey()).regex(PatternUtils.toFuzzySearch(PatternUtils.toEscapeStr(condition.getValue())));
                    }  else if (condition.getMode() == BizRuleConditionModeEnum.IN_MATCH.code) {
                        criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + condition.getKey()).in(Arrays.asList(condition.getValue().split(",")));
                    }
                }
            });
//            addUserIdCondition(bizRule, criteria);
        }

        Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.sort));
        PageRequest pageRequest = new PageRequest(feeTemplateQueryReq.pageNum - 1, feeTemplateQueryReq.pageSize, sort);

        Query query = Query.query(criteria);
        long count = mongoTemplate.count(query, FeeTemplate.class);
        if (count > 0) {
            query.with(pageRequest);
            List<FeeTemplate> feeTemplates = mongoTemplate.find(query, FeeTemplate.class);
            return new PageImpl<>(feeTemplates, pageRequest, count);
        } else {
            return new PageImpl<>(Collections.emptyList(), pageRequest, count);
        }
    }



    @Override
    public Page<FeeTemplate> batchQueryByCondition(FeeTemplateBatchQueryReq feeTemplateBatchQueryReq) {
        Criteria criteria = where(BaseDocument.Fields.del).is(false);

        if (Objects.nonNull(feeTemplateBatchQueryReq.getTemplateId())) {
            criteria.and(FeeTemplate.Fields.templateId).is(feeTemplateBatchQueryReq.getTemplateId());
        }
        if (StringUtils.isNotBlank(feeTemplateBatchQueryReq.getSceneCode())) {
            criteria.and(FeeTemplate.Fields.sceneCode).is(feeTemplateBatchQueryReq.getSceneCode());
        }
        if (CollectionUtils.isNotEmpty(feeTemplateBatchQueryReq.getStatusList())) {
            criteria.and(BaseDocument.Fields.status).in(feeTemplateBatchQueryReq.getStatusList());
        }
        if (StringUtils.isNotBlank(feeTemplateBatchQueryReq.getGroup())) {
            criteria.and(FeeTemplate.Fields.group).is(feeTemplateBatchQueryReq.getGroup());
        }
        if (StringUtils.isNotBlank(feeTemplateBatchQueryReq.getSceneName())) {
            criteria.and(FeeTemplate.Fields.sceneName).regex(feeTemplateBatchQueryReq.getSceneName());
        }

        Map<String, List<String>> bizRule = feeTemplateBatchQueryReq.getBizRule();
        if (MapUtils.isNotEmpty(bizRule)) {
            bizRule.forEach((key, value) -> {
                if (StringUtils.isNotBlank(key) && CollectionUtils.isNotEmpty(value)) {
                    criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + key).in(value);
                }
            });
            List<String> userIds = bizRule.get(CommonBizRule.Fields.userId);
            if (CollectionUtils.isEmpty(userIds)) {
                criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.customSkuUserId).exists(false);
            }
        }

        Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.sort));
        PageRequest pageRequest = new PageRequest(feeTemplateBatchQueryReq.pageNum - 1, feeTemplateBatchQueryReq.pageSize, sort);

        Query query = Query.query(criteria);
        long count = mongoTemplate.count(query, FeeTemplate.class);
        if (count > 0) {
            query.with(pageRequest);
            List<FeeTemplate> feeTemplates = mongoTemplate.find(query, FeeTemplate.class);
            return new PageImpl<>(feeTemplates, pageRequest, count);
        } else {
            return new PageImpl<>(Collections.emptyList(), pageRequest, count);
        }
    }


    @Override
    public Page<FeeTemplate> getPageList(FeeTemplatePageListRequest request) {
        List<String> serviceCategoryIdList = request.getServiceCategoryIdList();
        List<String> serviceIdList = request.getServiceIdList();
        List<String> level1GoodsCategoryIdList = request.getLevel1GoodsCategoryIdList();
        List<String> feeTypeTagList = request.getFeeTypeTagList();
        String sceneCode = request.getSceneCode();
        request.validate();
        Criteria criteria = where(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code)
                .and(FeeTemplate.Fields.sceneCode).is(sceneCode)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.customSkuUserId).exists(false);
        if (CollectionUtils.isNotEmpty(serviceCategoryIdList)) {
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceCategoryId).in(serviceCategoryIdList);
        }
        if (CollectionUtils.isNotEmpty(serviceIdList)) {
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).in(serviceIdList);
        }
        if (CollectionUtils.isNotEmpty(level1GoodsCategoryIdList)) {
            if (SceneCodeEnum.ENTERPRISE_ALL_CONTRACTORS_ATTRIBUTE_PRICE.getCode().equals(sceneCode)) {
                // 历史原因，需要兼容
                criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + "lv1goodsCategoryId").in(level1GoodsCategoryIdList);
            } else {
                criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level1GoodsCategoryId).in(level1GoodsCategoryIdList);
            }
        }
        if (CollectionUtils.isNotEmpty(feeTypeTagList)) {
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).in(feeTypeTagList);
        }
        Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.modifyTime));
        PageRequest pageRequest = new PageRequest(request.pageNum - 1, request.pageSize, sort);

        Query query = Query.query(criteria);
        long count = mongoTemplate.count(query, FeeTemplate.class);
        if (count > 0) {
            query.with(pageRequest);
            List<FeeTemplate> feeTemplates = mongoTemplate.find(query, FeeTemplate.class);
            return new PageImpl<>(feeTemplates, pageRequest, count);
        } else {
            return new PageImpl<>(Collections.emptyList(), pageRequest, count);
        }
    }



    @Override
    public synchronized void save(List<FeeTemplate> feeTemplates) {
//        for (FeeTemplate feeTemplate : feeTemplates) {
//            String sceneCode = feeTemplate.getSceneCode();
//            String serviceId = feeTemplate.getBizRule().get(CommonBizRule.Fields.serviceId);
//            this.checkForDuplicateBySceneCodeAndServiceId(sceneCode, serviceId);
//        }
        for (FeeTemplate feeTemplate : feeTemplates) {
            Date now = new Date();
            feeTemplate.setTemplateId(SnowFlakeGenerator.INSTANCE.generate());
            feeTemplate.setDel(false);
            feeTemplate.setTemplateVersion(1);
            feeTemplate.setStatus(TemplateStatusEnum.ACTIVE.code);
            feeTemplate.setCreateTime(now);
            feeTemplate.setModifyTime(now);
            feeTemplate.setCreateBy(CommonUtils.getCurrentLoginName());
            feeTemplate.setUpdateBy(CommonUtils.getCurrentLoginName());
            Map<String, String> bizRule = feeTemplate.getBizRule();
            if (bizRule != null) {
                String feeTypeTag = bizRule.get(CommonBizRule.Fields.feeTypeTag);
                // 当费用类型不为空时，算法规则不能为空
                if (StringUtils.isNotBlank(feeTypeTag) && (Objects.isNull(feeTemplate.getCalculateRuleData()) || StringUtils.isEmpty(feeTemplate.getCalculateRuleData().getExpress()))) {
                    throw new BusException("算费规则为空");
                }
            }
        }

        feeTemplateRepository.save(feeTemplates);
//        List<FeeTemplate> save = feeTemplateRepository.save(feeTemplates);
//        applicationEventPublisher.publishEvent(new BuildIndexEvent(save));
//        return save;
    }

    @Override
    public synchronized List<FeeTemplate> update(List<FeeTemplate> feeTemplates) {
        List<Long> ids = feeTemplates.stream().map(FeeTemplate::getTemplateId).collect(Collectors.toList());
        List<FeeTemplate> templates = feeTemplateRepository.findAllByTemplateIdIn(ids);
        Map<Long, FeeTemplate> templateMap = templates.stream().collect(Collectors.toMap(FeeTemplate::getTemplateId, Function.identity(), (i, j) -> i));
        for (FeeTemplate feeTemplate : feeTemplates) {
            FeeTemplate template = templateMap.get(feeTemplate.getTemplateId());
            if (Objects.isNull(template)) {
                throw new BusException("模板不存在");
            }
            Date now = new Date();
            template.setModifyTime(now);
            template.setUpdateBy(CommonUtils.getCurrentLoginName());
            template.setTemplateVersion(template.getTemplateVersion() + 1);
            template.setSceneName(feeTemplate.getSceneName());
            template.setSceneCode(feeTemplate.getSceneCode());
//            template.setGroup(feeTemplate.getGroup());
            template.setBizRule(feeTemplate.getBizRule());
            if (template.getSort() != feeTemplate.getSort()) {
                template.setSort(feeTemplate.getSort());
            }
            Map<String, String> bizRule = feeTemplate.getBizRule();
            if (bizRule != null) {
                String feeTypeTag = bizRule.get(CommonBizRule.Fields.feeTypeTag);
                // 当费用类型不为空时，算法规则不能为空
                if (StringUtils.isNotBlank(feeTypeTag) && (Objects.isNull(feeTemplate.getCalculateRuleData()) || StringUtils.isEmpty(feeTemplate.getCalculateRuleData().getExpress()))) {
                    throw new BusException("算费规则为空");
                }
                template.getBizRule().remove(CommonBizRule.Fields.pullCustomSkuCurrentVersion);
            }
//            if (Objects.isNull(feeTemplate.getCalculateRuleData()) || StringUtils.isEmpty(feeTemplate.getCalculateRuleData().getExpress())) {
//                throw new BusException("算费规则为空");
//            }
            template.setCalculateRuleData(feeTemplate.getCalculateRuleData());

        }

        List<FeeTemplate> save = feeTemplateRepository.save(templates);
//        applicationEventPublisher.publishEvent(new BuildIndexEvent(save));
        return save;
    }

    @Override
    public void delete(Long templateId) {
        FeeTemplate template = feeTemplateRepository.findByTemplateId(templateId);
        if (Objects.isNull(template)) {
            throw new BusException("模板不存在");
        }
        Date now = new Date();
        template.setModifyTime(now);
        template.setUpdateBy(CommonUtils.getCurrentLoginName());
        template.setTemplateVersion(template.getTemplateVersion() + 1);
        template.setDel(true);

        feeTemplateRepository.save(template);
    }

    @Override
    public void lock(List<Long> feeTemplateIds) {
        List<FeeTemplate> allByTemplateIdIn = feeTemplateRepository.findAllByTemplateIdIn(feeTemplateIds);
        allByTemplateIdIn.forEach(
                feeTemplate -> {
                    feeTemplate.setStatus(TemplateStatusEnum.LOCK.code);
                    feeTemplate.setModifyTime(new Date());
                    feeTemplate.setUpdateBy(CommonUtils.getCurrentLoginName());
                });
        feeTemplateRepository.save(allByTemplateIdIn);
    }

    @Override
    public boolean duplicateValidate(Long serveId, String sceneCode) {
        if (serveId == null || serveId.equals(0L)) {
            return false;
        }
        List<FeeTemplate> feeTemplateList = feeTemplateRepository.findAllByGroupAndSceneCodeAndDelIsFalse(serveId.toString(), sceneCode);
        if (CollectionUtils.isNotEmpty(feeTemplateList)) {
            return true;
        }
        return false;
    }


    @Override
    public CopyPricingAttributeResp copyPricingAttribute(CopyPricingAttributeReq req) {
        // 为了防止勾选太多服务，导致查询的数据量过大，这里采用单个服务copy的方式
        List<Long> serviceIdList = req.getServiceIdList();
        if (CollectionUtils.isEmpty(serviceIdList)) {
            throw new BusException("服务ID不能为空");
        }

        int failureCount = 0;
        Date now = new Date();
        String currentLoginName = CommonUtils.getCurrentLoginName();
        for (Long serviceId : serviceIdList) {
            // 根据服务Id查询目标场景的模板，如果有，则不需要复制，并且记录为失败
            FeeTemplateQueryByConditionReq toConditionReq = new FeeTemplateQueryByConditionReq();
            toConditionReq.setSceneCode(req.getTargetSceneCode());
            toConditionReq.setPageSize(1);
            Page<FeeTemplate> toTemplate = findTemplateByCondition(serviceId, toConditionReq);
            if (toTemplate != null && toTemplate.getTotalElements() > 0) {
                failureCount++;
                continue;
            }

            FeeTemplateQueryByConditionReq conditionReq = new FeeTemplateQueryByConditionReq();
            conditionReq.setSceneCode(req.getSourceSceneCode());
            conditionReq.setPageSize(Integer.MAX_VALUE);
            Page<FeeTemplate> sourceTemplate = findTemplateByCondition(serviceId, conditionReq);

            if (Objects.isNull(sourceTemplate) || CollectionUtils.isEmpty(sourceTemplate.getContent())) {
                throw new BusException("源场景没有模板数据，serviceId=" + serviceId);
            }

            List<FeeTemplate> feeTemplates = sourceTemplate.getContent();
            List<FeeTemplate> toSave = new ArrayList<>();
            for (FeeTemplate feeTemplate : feeTemplates) {
                FeeTemplate copy = new FeeTemplate();
                copy.setSceneCode(req.getTargetSceneCode());
                copy.setSceneName(req.getTargetSceneName());
                copy.setGroup(feeTemplate.getGroup());
                copy.setBizRule(feeTemplate.getBizRule());
                copy.setCalculateRuleData(feeTemplate.getCalculateRuleData());
                copy.setSort(feeTemplate.getSort());
                copy.setTemplateId(SnowFlakeGenerator.INSTANCE.generate());
                copy.setDel(false);
                copy.setTemplateVersion(1);
                copy.setStatus(feeTemplate.getStatus());
                copy.setCreateTime(now);
                copy.setModifyTime(now);
                copy.setCreateBy(currentLoginName);
                copy.setUpdateBy(currentLoginName);
                toSave.add(copy);
            }
            feeTemplateRepository.save(toSave);

        }
        CopyPricingAttributeResp resp = new CopyPricingAttributeResp();
        resp.setFailureCount(failureCount);
        resp.setSuccessCount(serviceIdList.size() - failureCount);
        return resp;
    }


    @Override
    public List<GetTemplateBySkuNoResp> getTemplateBySkuNo(GetTemplateBySkuNoReq req) {
        // 1、根据 场景编码 + 服务Id + feeTypeTag 不能为空 查找出所有的计价模板
        // 2、如果模板为空，则返回空集合
        // 3、遍历模板 和 服务信息，如果服务信息中的skuNo和模板中的skuNo相同，
        // 则 ①如果模板是自定义类型，则需要从服务信息中解析出属性的值，如果值落在模板的value区间，则返回模板
        //    ②如果模板是标准类型，则返回模板
        List<GetTemplateBySkuNoResp> resultList = new ArrayList<>();
        String sceneCode = req.getSceneCode();
        for (GetTemplateBySkuNoReq.ServiceInfo serviceInfo : req.getServiceInfoList()) {
            GetTemplateBySkuNoResp resp = new GetTemplateBySkuNoResp();
            resultList.add(resp);
            Long serviceId = serviceInfo.getServiceId();
            resp.setServiceId(serviceId);
            resp.setTemplateInfoList(new ArrayList<>());
            Criteria criteria = where(BaseDocument.Fields.del).is(false)
                    .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code)
                    .and(FeeTemplate.Fields.sceneCode).is(sceneCode)
                    .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).ne(null)
                    .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId.toString())
                    .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.customSkuUserId).exists(false);
            String feeTypeTag = req.getFeeTypeTag();
            if (StringUtils.isNotBlank(feeTypeTag)) {
                criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).is(feeTypeTag);
            }
            List<FeeTemplate> feeTemplateList = mongoTemplate.find(Query.query(criteria), FeeTemplate.class);
            if (CollectionUtils.isEmpty(feeTemplateList)) {
                continue;
            }
            List<ServiceAttribute> serviceAttributeList = serviceInfo.getRootAttributeDetailList();
            List<GetTemplateBySkuNoResp.TemplateInfo> templateInfoList = resp.getTemplateInfoList();
            for (FeeTemplate feeTemplate : feeTemplateList) {
                Map<String, String> bizRule = feeTemplate.getBizRule();
                String skuNo = bizRule.get(CommonBizRule.Fields.skuNo);
                if (StringUtils.isBlank(skuNo)) {
                    continue;
                }
                boolean checkSkuNo = checkSkuNo(serviceAttributeList, feeTemplate, skuNo);
                if (checkSkuNo) {
                    GetTemplateBySkuNoResp.TemplateInfo templateInfo = new GetTemplateBySkuNoResp.TemplateInfo();
                    templateInfo.setSkuNo(skuNo);
                    templateInfo.setFeeTypeTag(bizRule.get(CommonBizRule.Fields.feeTypeTag));
                    templateInfo.setSkuType(bizRule.get(CommonBizRule.Fields.skuType));
                    templateInfo.setTemplateId(feeTemplate.getTemplateId());
                    templateInfo.setFeeDisplayName(bizRule.get(CommonBizRule.Fields.attributeDisplayName));
                    templateInfo.setFeeTypeTagName(bizRule.get(CommonBizRule.Fields.feeTypeTagName));
                    resp.setServiceId(serviceId);
                    templateInfoList.add(templateInfo);
                }
            }
            boolean anyMatch = templateInfoList.stream().anyMatch(e -> e.getFeeTypeTag().equals(FeeTypeTagEnum.SERVICE_FEE.code));
            if (!anyMatch) {
                // 场景+服务+skuNo(AP48014959)，找到基础服务费 模板
                criteria = where(BaseDocument.Fields.del).is(false)
                        .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code)
                        .and(FeeTemplate.Fields.sceneCode).is(sceneCode)
                        .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId.toString())
                        .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(Constant.BASE_SERVICE_FEE_SKU_NO)
                        .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.customSkuUserId).exists(false);
                List<FeeTemplate> feeTemplates = mongoTemplate.find(Query.query(criteria), FeeTemplate.class);
                if (CollectionUtils.isNotEmpty(feeTemplates)) {
                    FeeTemplate feeTemplate = feeTemplates.get(0);
                    GetTemplateBySkuNoResp.TemplateInfo info = new GetTemplateBySkuNoResp.TemplateInfo();
                    templateInfoList.add(info);
                    info.setSkuNo(Constant.BASE_SERVICE_FEE_SKU_NO);
                    info.setSkuType(FeeSkuTypeEnum.STANDARD_SKU.code);
                    info.setTemplateId(feeTemplate.getTemplateId());
                    info.setFeeDisplayName(feeTemplate.getBizRule().get(CommonBizRule.Fields.attributeDisplayName));
                    info.setFeeTypeTag(feeTemplate.getBizRule().get(CommonBizRule.Fields.feeTypeTag));
                    info.setFeeTypeTagName(feeTemplate.getBizRule().get(CommonBizRule.Fields.feeTypeTagName));
                }
            }
        }
        if (CollectionUtils.isEmpty(resultList)) {
            resultList = new ArrayList<>();
        }

        return resultList;
    }


    @Override
    public List<FeeTemplate> selectByServiceIdAndSkuNo(String sceneCode, String serviceId, String skuNo) {
        Criteria criteria = where(FeeTemplate.Fields.sceneCode).is(sceneCode)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(skuNo)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);
        return mongoTemplate.find(Query.query(criteria), FeeTemplate.class);
    }

    @Override
    public List<FeeTemplate> selectByServiceIdAndSkuNoAndSkuTypeNotStandardSurcharge(String sceneCode, String serviceId, String skuNo, String skuType) {
        Criteria criteria = where(FeeTemplate.Fields.sceneCode).is(sceneCode)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(skuNo)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuType).is(skuType)
                .and(BaseDocument.Fields.del).is(false)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag)
                .ne(FeeTypeTagEnum.STANDARD_SURCHARGE.code);

        return mongoTemplate.find(Query.query(criteria), FeeTemplate.class);
    }

    @Override
    public List<FeeTemplate> select4BigdataPullCustomSku(String sceneCode, String serviceId, String skuNo, String customSkuUserId, String attributeValueFixed) {

        Criteria criteria = where(BaseDocument.Fields.del).is(false)
                .and(FeeTemplate.Fields.sceneCode).is(sceneCode)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(skuNo)
                .and(FeeTemplate.Fields.bizRule+ PunConstant.DOT + CommonBizRule.Fields.skuType).is( FeeSkuTypeEnum.CUSTOM_SKU.code)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.customSkuValueType).is(CustomSkuValueTypeEnum.FIXED.getCode())
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.matchSkuType).is(MatchSkuTypeEnum.CURRENT_SKU.getCode())
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.attributeValueFixed).is(attributeValueFixed)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.customSkuUserId).is(customSkuUserId);

        return mongoTemplate.find(Query.query(criteria), FeeTemplate.class);
    }

    @Override
    public List<FeeTemplate> select4BigdataPullBySceneCode(String sceneCode) {
        Criteria criteria = where(BaseDocument.Fields.del).is(false)
                .and(FeeTemplate.Fields.sceneCode).is(sceneCode)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.pullCustomSkuCurrentVersion).exists(true)
                .and(FeeTemplate.Fields.bizRule+ PunConstant.DOT + CommonBizRule.Fields.skuType).is( FeeSkuTypeEnum.CUSTOM_SKU.code)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.customSkuValueType).is(CustomSkuValueTypeEnum.FIXED.getCode())
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.matchSkuType).is(MatchSkuTypeEnum.CURRENT_SKU.getCode());

        return mongoTemplate.find(Query.query(criteria), FeeTemplate.class);
    }


    @Override
    public List<SceneBaseInfo> getSceneListExistServiceIdAndSkuNos(Long serviceId, List<String> skuNos) {
        Criteria criteria = where(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId.toString())
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).in(skuNos)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);
        TypedAggregation<FeeTemplate> feeTemplateTypedAggregation = TypedAggregation.newAggregation(FeeTemplate.class,
                Aggregation.match(criteria),// 查询条件
                Aggregation.group(FeeTemplate.Fields.sceneCode).first("$$ROOT").as("info"),
                Aggregation.replaceRoot("$info")
        );
        AggregationResults<FeeTemplate> aggregate = mongoTemplate.aggregate(feeTemplateTypedAggregation, FeeTemplate.class);
        List<FeeTemplate> feeTemplates = aggregate.getMappedResults();
        if (CollectionUtils.isNotEmpty(feeTemplates)) {
            return feeTemplates.stream().map(e -> {
                SceneBaseInfo sceneBaseInfo = new SceneBaseInfo();
                sceneBaseInfo.setSceneCode(e.getSceneCode());
                sceneBaseInfo.setSceneName(e.getSceneName());
                return sceneBaseInfo;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }


    @Override
    public List<SceneBaseInfo> getSceneListNotExistServiceIdAndSkuNos(Long serviceId, List<String> skuNos) {
        List<SceneBaseInfo> existInfos = getSceneListExistServiceIdAndSkuNos(serviceId, skuNos);
        List<SceneInfo> sceneInfos = sceneInfoService.queryAll();
        sceneInfos = sceneInfos.stream().filter(e -> !e.isDel() && SceneStatusEnum.ACTIVE.code == e.getStatus()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(existInfos)) {
            List<String> existSceneCodes = existInfos.stream().map(SceneBaseInfo::getSceneCode).collect(Collectors.toList());
            sceneInfos = sceneInfos.stream().filter(e -> !existSceneCodes.contains(e.getSceneCode())).collect(Collectors.toList());
        }
        return sceneInfos.stream().map(e -> {
            SceneBaseInfo sceneBaseInfo = new SceneBaseInfo();
            sceneBaseInfo.setSceneCode(e.getSceneCode());
            sceneBaseInfo.setSceneName(e.getSceneName());
            return sceneBaseInfo;
        }).collect(Collectors.toList());
    }


    @Override
    public List<FeeTemplate> getFeeTemplates(FeeTemplateConfigureReq feeTemplateConfigureReq) {
        List<FeeTemplateConfigureReq.FeeTemplateConfigure> configureList = feeTemplateConfigureReq.getConfigureList();
        this.validateParams(feeTemplateConfigureReq, configureList);

        Set<Long> serviceIds = configureList.stream().map(e -> e.getBizRule().get(CommonBizRule.Fields.serviceId)).map(Long::valueOf).collect(Collectors.toSet());
        Map<Long, Long> serviceIdAndServiceCategoryIdMap = serviceApi.getServiceCategoryIdsByServiceIds(serviceIds);
        if (MapUtils.isEmpty(serviceIdAndServiceCategoryIdMap)) {
            throw new BusException("服务ID对应的服务类目ID为空");
        }
        return configureList.stream().map(i -> {
            FeeTemplate feeTemplate = new FeeTemplate();
            BeanUtils.copyProperties(i, feeTemplate);
            Map<String, String> bizRule = feeTemplate.getBizRule();
            bizRule.put(CommonBizRule.Fields.serviceCategoryId, serviceIdAndServiceCategoryIdMap.get(Long.valueOf(i.getBizRule().get(CommonBizRule.Fields.serviceId))).toString());
            feeTemplate.getCalculateRuleData().parseExpressionParam();
            if (Objects.nonNull(feeTemplate.getCalculateRuleData().getExpressInfo())) {
                String compile = expressCompilerComposite.compile(feeTemplate.getCalculateRuleData().getExpressInfo());
                try {
                    String[] outVarNames = expressRunner.getOutVarNames(compile);
                    feeTemplate.getCalculateRuleData().setExpress(compile);
                    if (Objects.nonNull(outVarNames) && outVarNames.length > 0) {
                        feeTemplate.getCalculateRuleData().setExpressionParamList(Arrays.asList(outVarNames));
                    }
                } catch (Exception e) {
                    throw new BusException(e.getMessage(), e);
                }

            }

            List<String> fieldsToTrim = Arrays.asList(
                    CommonBizRule.Fields.attributeValueFixed,
                    CommonBizRule.Fields.attributeValueMin,
                    CommonBizRule.Fields.attributeValueMax,
                    CommonBizRule.Fields.customSkuUserId
            );
            fieldsToTrim.forEach(field -> bizRule.compute(field, (k, v) -> StrKit.trimSafely(v)));

            return feeTemplate;
        }).collect(Collectors.toList());
    }


    @Override
    public synchronized void createTemplate(SaveTemplateSkuNoReq req) {
        List<String> sceneCodeList = req.getSceneCodeList();
        List<SaveTemplateSkuNoReq.BizRule> bizRuleList = req.getBizRuleList();
        this.validateSaveParam(sceneCodeList, bizRuleList);
        String serviceId = req.getServiceId().toString();
        // 防止重复提交
//        for (String sceneCode : sceneCodeList) {
//            checkForDuplicateBySceneCodeAndServiceId(sceneCode, serviceId);
//        }
        List<FeeTemplate> feeTemplates = new ArrayList<>();
        String serviceName = req.getServiceName();
        String serviceCategoryId = getServiceCategoryId(serviceId);

        Date now = new Date();
        String currentLoginName = CommonUtils.getCurrentLoginName();
        for (String sceneCode : sceneCodeList) {
            String sceneName;
            SceneInfo sceneInfo = sceneInfoService.query(sceneCode);
            if (sceneInfo != null) {
                sceneName = sceneInfo.getSceneName();
            } else {
                sceneName = "未知场景，编码：" + sceneCode;
            }
            for (SaveTemplateSkuNoReq.BizRule bizRule : bizRuleList) {
                FeeTemplate feeTemplate = new FeeTemplate();
                feeTemplate.setSceneCode(sceneCode);
                feeTemplate.setSceneName(sceneName);
                feeTemplate.setGroup(serviceId);
                feeTemplate.setTemplateId(SnowFlakeGenerator.INSTANCE.generate());
                feeTemplate.setDel(false);
                feeTemplate.setTemplateVersion(1);
                feeTemplate.setStatus(TemplateStatusEnum.ACTIVE.code);
                feeTemplate.setCreateTime(now);
                feeTemplate.setModifyTime(now);
                feeTemplate.setCreateBy(currentLoginName);
                feeTemplate.setUpdateBy(currentLoginName);
                Map<String, String> bizRuleMap = getBizRuleMap(bizRule, serviceId, serviceName, serviceCategoryId);
                String feeTypeTag = bizRule.getFeeTypeTag();
                setSort(feeTypeTag, feeTemplate);
                feeTemplate.setBizRule(bizRuleMap);

                CalculateRuleData calculateRuleData = new CalculateRuleData();
                calculateRuleData.setExpress("masterInputPrice * " + bizRule.getSkuNumberNo());
                calculateRuleData.setExpressionParamList(Arrays.asList("masterInputPrice", bizRule.getSkuNumberNo()));
                feeTemplate.setCalculateRuleData(calculateRuleData);

                feeTemplates.add(feeTemplate);
            }
        }
        feeTemplateRepository.save(feeTemplates);
    }




    @Override
    public synchronized void modifyTemplate(SaveTemplateSkuNoReq req) {
        List<String> sceneCodeList = req.getSceneCodeList();
        List<SaveTemplateSkuNoReq.BizRule> bizRuleList = req.getBizRuleList();
        this.validateSaveParam(sceneCodeList, bizRuleList);
        List<FeeTemplate> feeTemplates = new ArrayList<>();
        String serviceId = req.getServiceId().toString();
        String serviceName = req.getServiceName();
        String serviceCategoryId = getServiceCategoryId(serviceId);

        Date now = new Date();
        String currentLoginName = CommonUtils.getCurrentLoginName();

        for (String sceneCode : sceneCodeList) {
            String sceneName;
            SceneInfo sceneInfo = sceneInfoService.query(sceneCode);
            if (sceneInfo != null) {
                sceneName = sceneInfo.getSceneName();
            } else {
                sceneName = "未知场景，编码：" + sceneCode;
            }
            Criteria criteria = where(FeeTemplate.Fields.sceneCode).is(sceneCode)
                    .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                    .and(BaseDocument.Fields.del).is(false)
                    .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);
            List<FeeTemplate> feeTemplateList = mongoTemplate.find(Query.query(criteria), FeeTemplate.class);


            for (SaveTemplateSkuNoReq.BizRule bizRule : bizRuleList) {
                String skuNo = bizRule.getSkuNo();
                List<FeeTemplate> needUpdateFeeTemplates = feeTemplateList.stream().filter(e -> skuNo.equals(e.getBizRule().get(CommonBizRule.Fields.skuNo))).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(needUpdateFeeTemplates)) {
                    FeeTemplate feeTemplate = new FeeTemplate();
                    feeTemplate.setSceneCode(sceneCode);
                    feeTemplate.setSceneName(sceneName);
                    feeTemplate.setGroup(serviceId);
                    feeTemplate.setTemplateId(SnowFlakeGenerator.INSTANCE.generate());
                    feeTemplate.setDel(false);
                    feeTemplate.setTemplateVersion(1);
                    feeTemplate.setStatus(TemplateStatusEnum.ACTIVE.code);
                    feeTemplate.setCreateTime(now);
                    feeTemplate.setModifyTime(now);
                    feeTemplate.setCreateBy(currentLoginName);
                    feeTemplate.setUpdateBy(currentLoginName);
                    Map<String, String> bizRuleMap = getBizRuleMap(bizRule, serviceId, serviceName, serviceCategoryId);
                    feeTemplate.setBizRule(bizRuleMap);

                    String feeTypeTag = bizRule.getFeeTypeTag();
                    setSort(feeTypeTag, feeTemplate);

                    CalculateRuleData calculateRuleData = new CalculateRuleData();
                    calculateRuleData.setExpress("masterInputPrice * " + bizRule.getSkuNumberNo());
                    calculateRuleData.setExpressionParamList(Arrays.asList("masterInputPrice", bizRule.getSkuNumberNo()));
                    feeTemplate.setCalculateRuleData(calculateRuleData);

                    feeTemplates.add(feeTemplate);
                } else {
                    for (FeeTemplate template : needUpdateFeeTemplates) {
                        Map<String, String> ruleMap = template.getBizRule();
                        String feeTypeTag = setBizRule(bizRule, ruleMap, serviceId, serviceName, serviceCategoryId);

                        if (template.getSort() == 0) {
                            setSort(feeTypeTag, template);
                        }

                        CalculateRuleData calculateRuleData = template.getCalculateRuleData();
                        calculateRuleData.setExpress("masterInputPrice * " + bizRule.getSkuNumberNo());
                        calculateRuleData.setExpressionParamList(Arrays.asList("masterInputPrice", bizRule.getSkuNumberNo()));

                        template.setModifyTime(now);
                        template.setUpdateBy(currentLoginName);
                        feeTemplates.add(template);
                    }
                }
            }
            boolean hasFeeTypeTag = feeTemplates.stream().anyMatch(e -> StringUtils.isNotBlank(e.getBizRule().get(CommonBizRule.Fields.feeTypeTag)));
            if (!hasFeeTypeTag) {
                throw new BusException("场景：" + sceneName + "，serviceId：" + serviceId + "，至少有一项sku需要计费");
            }
        }
        feeTemplateRepository.save(feeTemplates);
    }


    @Override
    public SimplePageInfo<GetFeeTemplateServiceIdAndNameResp> getFeeRuleServiceIdAndName(GetFeeTemplateServiceIdAndNameReq req) {
        String sceneCode = req.getSceneCode();
        List<String> serviceIdList = req.getServiceIdList();
        Integer pageSize = req.getPageSize();
        Integer pageNum = req.getPageNum();
        final String collectionName = "feeTemplate";
        // Define the match stage
        MatchOperation matchStage = Aggregation.match(
                where(FeeTemplate.Fields.sceneCode).is(sceneCode)
                        .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).in(serviceIdList)
                        .and(BaseDocument.Fields.del).is(false)
                        .and(BaseDocument.Fields.status).is(0)
        );

        // Define the group stage
        GroupOperation groupStage = Aggregation.group(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId)
                .first(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).as(CommonBizRule.Fields.serviceId)
                .first(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceName).as(CommonBizRule.Fields.serviceName);

        AggregationOptions options = newAggregationOptions().allowDiskUse(true).build();

        Aggregation aggregationCount = newAggregation(
                matchStage,
                groupStage,
                count().as("count")).withOptions(options);

        JSONObject mappedResult = mongoTemplate.aggregate(aggregationCount, collectionName, JSONObject.class).getUniqueMappedResult();
        if (mappedResult == null) {
            return new SimplePageInfo<>();
        }
        Long count = mappedResult.getLong("count");

        // Define the project stage
        ProjectionOperation projectStage = Aggregation.project()
                .andExclude("_id")
                .and(CommonBizRule.Fields.serviceId).as(CommonBizRule.Fields.serviceId)
                .and(CommonBizRule.Fields.serviceName).as(CommonBizRule.Fields.serviceName);

        // Define the sort stage
        SortOperation sortStage = Aggregation.sort(Sort.Direction.ASC, CommonBizRule.Fields.serviceName);

        // Define the skip stage for pagination
        SkipOperation skipStage = Aggregation.skip((pageNum - 1) * pageSize);

        // Define the limit stage for pagination
        LimitOperation limitStage = Aggregation.limit(pageSize);

        // Create the aggregation pipeline
        Aggregation aggregation = Aggregation.newAggregation(
                matchStage,
                groupStage,
                projectStage,
                sortStage,
                skipStage,
                limitStage
        ).withOptions(options);

        // Execute the aggregation
        AggregationResults<GetFeeTemplateServiceIdAndNameResp> results = mongoTemplate.aggregate(aggregation, collectionName, GetFeeTemplateServiceIdAndNameResp.class);
        if (results == null) {
            return new SimplePageInfo<>();
        }

        List<GetFeeTemplateServiceIdAndNameResp> mappedResults = results.getMappedResults();
        if (CollectionUtils.isEmpty(mappedResults)) {
            return new SimplePageInfo<>();
        }

        Pageable pageable = new PageRequest(pageNum - 1, pageSize);
        PageImpl<GetFeeTemplateServiceIdAndNameResp> page = new PageImpl<>(mappedResults, pageable, count);
        SimplePageInfo<GetFeeTemplateServiceIdAndNameResp> resultPage = new SimplePageInfo<>(mappedResults);
        resultPage.setPages(page.getTotalPages());
        resultPage.setPageNum(page.getNumber() + 1);
        resultPage.setPageSize(page.getSize());
        resultPage.setTotal(count);
        return resultPage;
    }


    @Override
    public SimplePageInfo<GetFeeTemplateSkuListResp> getFeeTemplateSkuList(GetFeeTemplateSkuListReq req) {
        String sceneCode = req.getSceneCode();
        List<String> serviceIdList = req.getServiceIdList();
        Integer pageSize = req.getPageSize();
        Integer pageNum = req.getPageNum();
        String feeTypeTag = req.getFeeTypeTag();
        String skuNo = req.getSkuNo();

        Criteria criteria = where(FeeRule.Fields.sceneCode).is(sceneCode)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(0);
        if (StringUtils.isNotBlank(feeTypeTag)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).is(feeTypeTag);
        }
        if (CollectionUtils.isNotEmpty(serviceIdList)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).in(serviceIdList);
        }
        if (StringUtils.isNotBlank(skuNo)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(skuNo);
        }

        Query query = new Query();
        query.addCriteria(criteria);

        long count = mongoTemplate.count(query, FeeTemplate.class);

        Sort sort = new Sort(
                new Sort.Order(Sort.Direction.ASC, FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceName),
                new Sort.Order(Sort.Direction.ASC, FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.attributeDisplayName));
        query.with(sort);

        query.skip((pageNum - 1) * pageSize);
        query.limit(pageSize);

        List<FeeTemplate> feeTemplates = mongoTemplate.find(query, FeeTemplate.class);
        if (CollectionUtils.isEmpty(feeTemplates)) {
            return new SimplePageInfo<>();
        }

        List<GetFeeTemplateSkuListResp> respList = feeTemplates.stream().map(e -> {
            GetFeeTemplateSkuListResp resp = new GetFeeTemplateSkuListResp();
            resp.setTemplateId(e.getTemplateId().toString());
            resp.setServiceName(e.getBizRule().get(CommonBizRule.Fields.serviceName));
            resp.setFeeTypeTag(e.getBizRule().get(CommonBizRule.Fields.feeTypeTag));
            resp.setSkuNo(e.getBizRule().get(CommonBizRule.Fields.skuNo));
            resp.setAttributeDisplayName(e.getBizRule().get(CommonBizRule.Fields.attributeDisplayName));
            return resp;
        }).collect(Collectors.toList());

        Pageable pageable = new PageRequest(pageNum - 1, pageSize);
        PageImpl<GetFeeTemplateSkuListResp> page = new PageImpl<>(respList, pageable, count);
        SimplePageInfo<GetFeeTemplateSkuListResp> resultPage = new SimplePageInfo<>(respList);
        resultPage.setPages(page.getTotalPages());
        resultPage.setPageNum(page.getNumber() + 1);
        resultPage.setPageSize(page.getSize());
        resultPage.setTotal(count);
        return resultPage;
    }


    @Override
    public Page<FeeTemplate> queryTemplateByCondition(FeeTemplateReq req) {
        String sceneCode = req.getSceneCode();
        String serviceId =  req.getServiceId();
        String feeTypeTag = req.getFeeTypeTag();
        String skuNo = req.getSkuNo();
        Criteria criteria = where(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(0)
                .and(FeeTemplate.Fields.sceneCode).is(sceneCode);
        if (StringUtils.isNotBlank(serviceId)) {
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId);
        }
        if (StringUtils.isNotBlank(feeTypeTag) && !"unlimited".equals(feeTypeTag)) { // 为了兼容配置管理平台
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).is(feeTypeTag);
        } else if (!"unlimited".equals(feeTypeTag)){ // 为了兼容配置管理平台
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).exists(true);
        }
        if (StringUtils.isNotBlank(skuNo)) {
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(skuNo);
        }

        Query query = new Query(criteria);
//        Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.modifyTime));
        Sort sort = new Sort(new Sort.Order(Sort.Direction.ASC, CommonBizRule.Fields.skuNo));
        PageRequest pageRequest = new PageRequest(req.getPageNum() - 1, req.getPageSize(), sort);
        long count = mongoTemplate.count(query, FeeTemplate.class);
        if (count > 0) {
            query.with(pageRequest);
            List<FeeTemplate> feeTemplates = mongoTemplate.find(query, FeeTemplate.class);
            return new PageImpl<>(feeTemplates, pageRequest, count);
        } else {
            return new PageImpl<>(Collections.emptyList(), pageRequest, count);
        }
    }

    @Override
    public Page<FeeTemplate> queryTemplateByCondition4DeleteCustom(FeeTemplate4DeleteCustomReq req) {
        String sceneCode = req.getSceneCode();
        String serviceId =  req.getServiceId();
        String feeTypeTag = req.getFeeTypeTag();
        String skuType = req.getSkuType();
        String skuNo = req.getSkuNo();
        String customSkuUserId = req.getCustomSkuUserId();

        Criteria criteria = where(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(0)
                .and(FeeTemplate.Fields.sceneCode).is(sceneCode);
        if (StringUtils.isNotBlank(serviceId)) {
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId);
        }
        if (StringUtils.isNotBlank(feeTypeTag)) {
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).is(feeTypeTag);
        }
        if (StringUtils.isNotBlank(skuNo)) {
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(skuNo);
        }
        if (StringUtils.isNotBlank(skuType)) {
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuType).is(skuType);
        }

        if (StringUtils.isNotBlank(customSkuUserId)) {
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.customSkuUserId).is(customSkuUserId);
        }

        Query query = new Query(criteria);
        Sort sort = new Sort(new Sort.Order(Sort.Direction.ASC, CommonBizRule.Fields.skuNo));
        PageRequest pageRequest = new PageRequest(req.getPageNum() - 1, req.getPageSize(), sort);
        long count = mongoTemplate.count(query, FeeTemplate.class);
        if (count > 0) {
            query.with(pageRequest);
            List<FeeTemplate> feeTemplates = mongoTemplate.find(query, FeeTemplate.class);
            return new PageImpl<>(feeTemplates, pageRequest, count);
        } else {
            return new PageImpl<>(Collections.emptyList(), pageRequest, count);
        }
    }


    @Override
    public Set<Long> getServiceCategoryIds(GetServiceCategoryIdsReq req) {
        String sceneCode = req.getSceneCode();
//        List<FeeTemplate> feeTemplates = feeTemplateRepository.findAllBySceneCodeAndDelIsFalse(sceneCode);
        Criteria criteria = where(FeeTemplate.Fields.sceneCode).is(sceneCode)
                .and(BaseDocument.Fields.del).is(false)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.customSkuUserId).exists(false);
        Query query = new Query(criteria);
        query.fields().include(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceCategoryId);
        List<FeeTemplate> feeTemplates = mongoTemplate.find(query, FeeTemplate.class);
        if (CollectionUtils.isEmpty(feeTemplates)) {
            log.info("No feeTemplate found for scene code: {}", sceneCode);
            return Collections.emptySet();
        }
        return feeTemplates.stream()
                .map(e -> {
                    Map<String, String> bizRule = e.getBizRule();
                    if (bizRule == null) {
                        log.warn("BizRule is null for FeeTemplate with id: {}", e.getId());
                        return null;
                    }
                    return bizRule.get(CommonBizRule.Fields.serviceCategoryId);
                })
                .filter(StringUtils::isNotBlank)
                .map(serviceCategoryIdStr -> {
                    try {
                        return Long.parseLong(serviceCategoryIdStr);
                    } catch (NumberFormatException ex) {
                        log.error("Failed to parse serviceCategoryId: {} as Long", serviceCategoryIdStr, ex);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }



    @Override
    @Async
    public void completeServiceCategoryId(String sceneCode, String updateBy) {
        // 分页查询，每页100条
        final int pageSize = 1000;
        final int startPage = 0;
        int updatedCount = 0;
        int updateFailCount = 0;
        StringBuilder sb = new StringBuilder();

        Criteria criteria = where(BaseDocument.Fields.del).is(false);
         if (StringUtils.isNotBlank(sceneCode)) {
             criteria.and(FeeRule.Fields.sceneCode).is(sceneCode);
         }
        criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceCategoryId).is(null);
        PageRequest pageRequest = new PageRequest(startPage, pageSize);
        Query query = Query.query(criteria).with(pageRequest);
        query.with(new Sort(Sort.Direction.ASC, FeeTemplate.Fields.templateId));

        Map<Long, Long> mapping = serviceApi.getServiceIdWithServiceCategoryIdMapping(null);

        while (true) {
            // 由于后面会更新数据，所以每次都从头重新查询
            List<FeeTemplate> feeTemplates = mongoTemplate.find(query, FeeTemplate.class);
            if (CollectionUtils.isEmpty(feeTemplates)) {
                log.info("补全服务类目ID完成");
                break;
            }
            List<FeeTemplate> needUpdateList = new ArrayList<>();
            for (FeeTemplate template : feeTemplates) {
                String serviceId = template.getBizRule().get(CommonBizRule.Fields.serviceId);
                if (StringUtils.isBlank(serviceId)) {
                    continue;
                }
                Long serviceCategoryId = mapping.get(Long.valueOf(serviceId));
                if (serviceCategoryId != null) {
                    template.getBizRule().put(CommonBizRule.Fields.serviceCategoryId, String.valueOf(serviceCategoryId));
                    template.setUpdateBy(updateBy);
                    needUpdateList.add(template);
                    updatedCount++;
                } else {
                    updateFailCount++;
                    sb.append("服务ID: ").append(serviceId).append(" 服务类目ID获取失败，请手动补全\n");
                }
            }
            feeTemplateRepository.save(needUpdateList);
        }
        log.info("补全服务类目ID完成，共更新{}条数据，失败{}条数据，失败serviceId：{}", updatedCount, updateFailCount, sb);

    }


    @Override
    public TemplateDelResponse delBatch(TemplateDelRequest templateDelRequest) {
        Set<String> templateIds = templateDelRequest.getTemplateIds();
        Set<Long> templateIdSet = templateIds.stream().map(Long::valueOf).collect(Collectors.toSet());
        Set<Long> ruleReliedTemplateIds = new HashSet<>();
        Set<Long> ruleDraftReliedTemplateIds = new HashSet<>();
        Set<Long> mappingReliedTemplateIds = new HashSet<>();
        // 先校验模板是否被其它模块依赖
        for (Long templateId : templateIdSet) {
            FeeRule feeRule = feeRuleRepository.findOneByTemplateIdAndDelIsFalse(templateId);
            if (feeRule != null) {
                ruleReliedTemplateIds.add(templateId);
            }

            FeeRuleDraft feeRuleDraft =  feeRuleDraftRepository.findOneByTemplateIdAndDelIsFalse(templateId);
            if (feeRuleDraft != null) {
                ruleDraftReliedTemplateIds.add(templateId);
            }
//            BizRuleMapping mapping = bizRuleMappingService.findOneByTemplateId(templateId);
//            if (mapping != null) {
//                mappingReliedTemplateIds.add(templateId);
//            }

            FeeTemplateMapping feeTemplateMapping = feeTemplateMappingService.findOneBySourceTemplateId(templateId);
            if (feeTemplateMapping != null) {
                mappingReliedTemplateIds.add(templateId);
            }
        }
        // 依赖并集
        Set<Long> reliedTemplates = Stream.of(ruleReliedTemplateIds, ruleDraftReliedTemplateIds, mappingReliedTemplateIds)
                                     .flatMap(Set::stream)
                                     .collect(Collectors.toSet());
        int failCount = reliedTemplates.size();
        int successCount = templateIdSet.size() - failCount;
        TemplateDelResponse response = new TemplateDelResponse(successCount, failCount, null);

        if (CollectionUtils.isNotEmpty(reliedTemplates)) {
            List<FeeTemplate> templates = feeTemplateRepository.findAllByTemplateIdIn(reliedTemplates);
            List<TemplateDelResponse.FailItem> failItems = templates.stream()
                    .map(template -> new TemplateDelResponse.FailItem(
                            template.getTemplateId().toString(),
                            template.getBizRule().get(CommonBizRule.Fields.skuNo),
                            template.getBizRule().get(CommonBizRule.Fields.serviceName),
                            new ArrayList<>()))
                    .collect(Collectors.toList());
            for (TemplateDelResponse.FailItem failItem : failItems) {
                if (ruleReliedTemplateIds.contains(Long.valueOf(failItem.getTemplateId()))) {
                    failItem.getCheckReliedEnums().add(CheckReliedEnum.FEE_RULE);
                }
                if (ruleDraftReliedTemplateIds.contains(Long.valueOf(failItem.getTemplateId()))) {
                    failItem.getCheckReliedEnums().add(CheckReliedEnum.FEE_RULE_DRAFT);
                }
                if (mappingReliedTemplateIds.contains(Long.valueOf(failItem.getTemplateId()))) {
                    failItem.getCheckReliedEnums().add(CheckReliedEnum.MAPPING);
                }
            }
            failItems.sort(Comparator.comparing(TemplateDelResponse.FailItem::getSkuNo));
            response.setFailItems(failItems);
        }

        templateIdSet.removeAll(reliedTemplates);
        // 逻辑删除
        if (CollectionUtils.isNotEmpty(templateIdSet)) {
            Criteria criteria = Criteria.where("templateId").in(templateIdSet);
            mongoTemplate.updateMulti(
                    Query.query(criteria),
                    new Update().set(BaseDocument.Fields.del, true),
                    FeeTemplate.class);
        }
        return response;
    }


    @Override
    public List<FeeTemplate> getListByServiceId(GetByServiceIdRequest req) {
        String sceneCode = req.getSceneCode();
        String serviceId = req.getServiceId();
        Criteria criteria = where(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                .and(FeeTemplate.Fields.sceneCode).is(sceneCode)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.customSkuUserId).exists(false);
        List<FeeTemplate> feeTemplates = mongoTemplate.find(new Query(criteria), FeeTemplate.class);

        ServiceSkuReq skuReq = new ServiceSkuReq();
        skuReq.setServiceId(Long.valueOf(serviceId));
        List<ServiceSkuResp> skuList = serviceSkuApi.getServiceSkuResp(skuReq);
        if (CollectionUtils.isEmpty(feeTemplates) && CollectionUtils.isEmpty(skuList)) {
            return Collections.emptyList();
        }

        List<FeeTemplate> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(skuList)) {
            feeTemplates.forEach(template -> {
                template.getBizRule().put("updateStatus", TemplateCompareStatusEnum.DELETED.getCode());
                resultList.add(template);
            });
            return resultList;
        }

        Map<String, ServiceSkuResp> skuMap = skuList.stream().collect(Collectors.toMap(ServiceSkuResp::getSkuNo, Function.identity()));
        feeTemplates.forEach(template -> {
            Map<String, String> bizRule = template.getBizRule();
            String skuNo = bizRule.get(CommonBizRule.Fields.skuNo);
            ServiceSkuResp serviceSkuResp = skuMap.get(skuNo);
            if (serviceSkuResp == null) {
                bizRule.put("updateStatus", TemplateCompareStatusEnum.DELETED.getCode());
            } else {
                String skuNumberPathNo1 = bizRule.get(CommonBizRule.Fields.skuNumberPathNo);
                String skuNumberPathNo2 = serviceSkuResp.getSkuNumberPathNo();
                if (StringUtils.isBlank(skuNumberPathNo1)) {
                    if (StringUtils.isBlank(skuNumberPathNo2)) {
                        bizRule.put("updateStatus", TemplateCompareStatusEnum.UNMODIFIED.getCode());
                    }
                } else if (!skuNumberPathNo1.equals(skuNumberPathNo2)) {
                    bizRule.put("updateStatus", TemplateCompareStatusEnum.MODIFIED.getCode());
                } else {
                    bizRule.put("updateStatus", TemplateCompareStatusEnum.UNMODIFIED.getCode());
                }
            }
            resultList.add(template);
            skuMap.remove(skuNo);
        });

        // 剩下的则为新增的
        if (MapUtils.isNotEmpty(skuMap)) {
            skuMap.forEach((skuNo, serviceSkuResp) -> {
                FeeTemplate template = new FeeTemplate();
                template.setDel(false);
                template.setStatus(TemplateStatusEnum.ACTIVE.code);
                template.setSceneCode(sceneCode);
                SceneInfo sceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(sceneCode);
                template.setSceneName(sceneInfo.getSceneName());
                template.setGroup(serviceSkuResp.getServiceId().toString());
                Map<String, String> bizRule = new HashMap<>();
                bizRule.put(CommonBizRule.Fields.skuNo, skuNo);
                bizRule.put(CommonBizRule.Fields.skuNumberPathNo, serviceSkuResp.getSkuNumberPathNo());
                bizRule.put(CommonBizRule.Fields.serviceId, serviceSkuResp.getServiceId().toString());
                bizRule.put(CommonBizRule.Fields.serviceName, serviceSkuResp.getServiceName());
                bizRule.put(CommonBizRule.Fields.goodsCategoryName, serviceSkuResp.getGoodsCategoryName());
                bizRule.put(CommonBizRule.Fields.skuNumberName, serviceSkuResp.getSkuNumberName());
                bizRule.put(CommonBizRule.Fields.feeUnit, serviceSkuResp.getFeeUnit());
                bizRule.put(CommonBizRule.Fields.feeName, serviceSkuResp.getFeeName());
                bizRule.put(CommonBizRule.Fields.attributeDisplayName, serviceSkuResp.getFeeName());
                bizRule.put(CommonBizRule.Fields.skuType, FeeSkuTypeEnum.STANDARD_SKU.code);
                bizRule.put(CommonBizRule.Fields.skuAttributePathName, serviceSkuResp.getSkuAttributePathName());
                bizRule.put("updateStatus", TemplateCompareStatusEnum.ADD.getCode());
                template.setBizRule(bizRule);
                resultList.add(template);
            });
        }

        return resultList;
    }


    @Override
    public void correctSceneInfoOfTemplate() {
        int page = 0;
        int pageSize = 1000;
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        Query query = new Query(criteria);
        while (true) {
            query.skip(page * pageSize);
            query.limit(pageSize);
            Sort sort = new Sort(Sort.Direction.ASC, BaseDocument.Fields.id);
            query.with(sort);
            List<FeeTemplate> feeTemplates = mongoTemplate.find(query, FeeTemplate.class);
            if (CollectionUtils.isEmpty(feeTemplates)) {
                break;
            }
            page++;
            feeTemplates.forEach(feeTemplate -> {
                feeTemplate.getBizRule().put(CommonBizRule.Fields.sceneCode, feeTemplate.getSceneCode());
                feeTemplate.getBizRule().put(CommonBizRule.Fields.sceneName, feeTemplate.getSceneName());
            });
            feeTemplateRepository.save(feeTemplates);
        }

    }


    @Override
    public void saveCustomSkuTemplateBatch(Long templateId,
                                           String customSkuValueType,
                                           String matchSkuType,
                                           List<CustomSkuTemplateExcelModel> customSkuTemplateExcelModels) {
        FeeTemplate template = feeTemplateRepository.findOneByTemplateIdAndDelIsFalse(templateId);
        if (template == null) {
            throw new BusException(StrUtil.format("模板已逻辑删除,templateId={}", templateId));
        }
        Map<String, String> bizRule = template.getBizRule();

        List<FeeTemplate> resultList = new ArrayList<>();
        for (CustomSkuTemplateExcelModel model : customSkuTemplateExcelModels) {
            FeeTemplate newTemplate = new FeeTemplate();
            // 由于自定义SKU模板是建立在 当前标准模板（或称为父模板）的基础上的，共有部分的数据完全相同，只有自定义部分需要替换
            BeanUtils.copyProperties(template, newTemplate);
            newTemplate.setId(null);
            newTemplate.setTemplateId(SnowFlakeGenerator.INSTANCE.generate());
            newTemplate.setCreateBy(null);
            newTemplate.setCreateTime(null);
            newTemplate.setUpdateBy(null);
            newTemplate.setModifyTime(null);
            newTemplate.setDel(false);
            newTemplate.setStatus(TemplateStatusEnum.ACTIVE.code);
            Map<String, String> newBizRule = new HashMap<>(bizRule);
            newBizRule.put(CommonBizRule.Fields.customSkuValueType, customSkuValueType);
            newBizRule.put(CommonBizRule.Fields.matchSkuType, matchSkuType);
            newBizRule.put(CommonBizRule.Fields.skuType, FeeSkuTypeEnum.CUSTOM_SKU.code);
            if (StringUtils.isNotBlank(model.getAttributeValueMin())) {
                newBizRule.put(CommonBizRule.Fields.attributeValueMin, model.getAttributeValueMin().trim());
            } else {
                newBizRule.remove(CommonBizRule.Fields.attributeValueMin);
            }
            if (StringUtils.isNotBlank(model.getAttributeValueMax())) {
                newBizRule.put(CommonBizRule.Fields.attributeValueMax, model.getAttributeValueMax().trim());
            } else {
                newBizRule.remove(CommonBizRule.Fields.attributeValueMax);
            }
            if (StringUtils.isNotBlank(model.getAttributeValueFixed())) {
                newBizRule.put(CommonBizRule.Fields.attributeValueFixed, model.getAttributeValueFixed().trim());
            } else {
                newBizRule.remove(CommonBizRule.Fields.attributeValueFixed);
            }
            if (StringUtils.isNotBlank(model.getSkuName())) {
                newBizRule.put(CommonBizRule.Fields.attributeDisplayName, model.getSkuName().trim());
            } else {
                throw new BusException("自定义SKU名称不能为空");
            }
            if (StringUtils.isNotBlank(model.getCustomSkuUserId())) {
                newBizRule.put(CommonBizRule.Fields.customSkuUserId, model.getCustomSkuUserId().trim());
            } else {
                newBizRule.remove(CommonBizRule.Fields.customSkuUserId);
            }
            newTemplate.setBizRule(newBizRule);
            resultList.add(newTemplate);
        }
        feeTemplateRepository.save(resultList);

        // 当添加了自定义模板时，父模板(如果是“标准”sku)需要锁定（即有自定义sku的父模板不能用来算价）
        String skyType = bizRule.get(CommonBizRule.Fields.skuType);
        if (FeeSkuTypeEnum.STANDARD_SKU.code.equals(skyType)) {
            lock(Collections.singletonList(templateId));
        }
    }

    @Override
    public void saveCurrentCustomSkuTemplate(Long templateId, String customSkuValueType, String matchSkuType, BatchCustomSkuTemplateExcelModel data) {
        FeeTemplate template = feeTemplateRepository.findOneByTemplateIdAndDelIsFalse(templateId);
        if (template == null) {
            throw new BusException(StrUtil.format("模板已逻辑删除,templateId={}", templateId));
        }
        Map<String, String> bizRule = template.getBizRule();

        FeeTemplate newTemplate = new FeeTemplate();
        // 由于自定义SKU模板是建立在 当前标准模板（或称为父模板）的基础上的，共有部分的数据完全相同，只有自定义部分需要替换
        BeanUtils.copyProperties(template, newTemplate);
        newTemplate.setId(null);
        newTemplate.setTemplateId(SnowFlakeGenerator.INSTANCE.generate());
        newTemplate.setCreateBy(null);
        newTemplate.setCreateTime(null);
        newTemplate.setUpdateBy(null);
        newTemplate.setModifyTime(null);
        newTemplate.setDel(false);
        newTemplate.setStatus(TemplateStatusEnum.ACTIVE.code);
        Map<String, String> newBizRule = new HashMap<>(bizRule);
        newBizRule.put(CommonBizRule.Fields.customSkuValueType, customSkuValueType);
        newBizRule.put(CommonBizRule.Fields.matchSkuType, matchSkuType);
        newBizRule.put(CommonBizRule.Fields.skuType, FeeSkuTypeEnum.CUSTOM_SKU.code);
        if (StringUtils.isNotBlank(data.getAttributeValueMin())) {
            newBizRule.put(CommonBizRule.Fields.attributeValueMin, data.getAttributeValueMin().trim());
        } else {
            newBizRule.remove(CommonBizRule.Fields.attributeValueMin);
        }
        if (StringUtils.isNotBlank(data.getAttributeValueMax())) {
            newBizRule.put(CommonBizRule.Fields.attributeValueMax, data.getAttributeValueMax().trim());
        } else {
            newBizRule.remove(CommonBizRule.Fields.attributeValueMax);
        }
        if (StringUtils.isNotBlank(data.getAttributeValueFixed())) {
            newBizRule.put(CommonBizRule.Fields.attributeValueFixed, data.getAttributeValueFixed().trim());
        } else {
            newBizRule.remove(CommonBizRule.Fields.attributeValueFixed);
        }
        if (StringUtils.isNotBlank(data.getSkuName())) {
            newBizRule.put(CommonBizRule.Fields.attributeDisplayName, data.getSkuName().trim());
        } else {
            throw new BusException("自定义SKU名称不能为空");
        }
        if (StringUtils.isNotBlank(data.getCustomSkuUserId())) {
            newBizRule.put(CommonBizRule.Fields.customSkuUserId, data.getCustomSkuUserId().trim());
        } else {
            newBizRule.remove(CommonBizRule.Fields.customSkuUserId);
        }
        newTemplate.setBizRule(newBizRule);

        feeTemplateRepository.save(newTemplate);

        // 当添加了自定义模板时，父模板(如果是“标准”sku)需要锁定（即有自定义sku的父模板不能用来算价）
        String skyType = bizRule.get(CommonBizRule.Fields.skuType);
        if (FeeSkuTypeEnum.STANDARD_SKU.code.equals(skyType)) {
            lock(Collections.singletonList(templateId));
        }
    }


    @Override
    public boolean checkCustomSkuTemplateExist(Long templateId,
                                               String customSkuValueType,
                                               String matchSkuType,
                                               List<CustomSkuTemplateExcelModel> dataList) {
        FeeTemplate template = feeTemplateRepository.findOneByTemplateIdAndDelIsFalse(templateId);
        if (template == null) {
            throw new BusException(StrUtil.format("模板已逻辑删除,templateId={}", templateId));
        }

        String sceneCode = template.getSceneCode();
        Map<String, String> bizRule = template.getBizRule();
        String serviceId = bizRule.get(CommonBizRule.Fields.serviceId);
        String skuNo = bizRule.get(CommonBizRule.Fields.skuNo);
        List<Criteria> criteriaList = new ArrayList<>();
        for (CustomSkuTemplateExcelModel model : dataList) {
            String userId = StrKit.trimSafely(model.getCustomSkuUserId());
            String valueMin = StrKit.trimSafely(model.getAttributeValueMin());
            String valueMax = StrKit.trimSafely(model.getAttributeValueMax());
            String valueFixed = StrKit.trimSafely(model.getAttributeValueFixed());
            Criteria criteria = where(BaseDocument.Fields.del).is(false)
                    .and(FeeTemplate.Fields.sceneCode).is(sceneCode)
                    .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                    .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(skuNo)
                    .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.customSkuValueType).is(customSkuValueType)
                    .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.matchSkuType).is(matchSkuType);
            if (CustomSkuValueTypeEnum.FIXED.getCode().equals(customSkuValueType)) {
                criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.attributeValueFixed).is(valueFixed);
            } else if (CustomSkuValueTypeEnum.RANGE.getCode().equals(customSkuValueType)) {
                criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.attributeValueMin).is(valueMin);
                criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.attributeValueMax).is(valueMax);
            }
            if (StringUtils.isNotBlank(userId)) {
                criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.userId).is(userId);
            }
            criteriaList.add(criteria);
        }
        Criteria criteria = new Criteria().orOperator(criteriaList.toArray(new Criteria[0]));
        FeeTemplate feeTemplate = mongoTemplate.findOne(Query.query(criteria), FeeTemplate.class);
        return feeTemplate != null;
    }

    @Override
    public boolean checkCurrentCustomSkuTemplateExist(Long templateId, String customSkuValueType, String matchSkuType, BatchCustomSkuTemplateExcelModel data) {
        FeeTemplate template = feeTemplateRepository.findOneByTemplateIdAndDelIsFalse(templateId);
        if (template == null) {
            throw new BusException(StrUtil.format("模板已逻辑删除,templateId={}", templateId));
        }

        String sceneCode = template.getSceneCode();
        Map<String, String> bizRule = template.getBizRule();
        String serviceId = bizRule.get(CommonBizRule.Fields.serviceId);
        String skuNo = bizRule.get(CommonBizRule.Fields.skuNo);

        String userId = StrKit.trimSafely(data.getCustomSkuUserId());
        String valueMin = StrKit.trimSafely(data.getAttributeValueMin());
        String valueMax = StrKit.trimSafely(data.getAttributeValueMax());
        String valueFixed = StrKit.trimSafely(data.getAttributeValueFixed());

        Criteria criteria = where(BaseDocument.Fields.del).is(false)
                .and(FeeTemplate.Fields.sceneCode).is(sceneCode)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(skuNo)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.customSkuValueType).is(customSkuValueType)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.matchSkuType).is(matchSkuType);
        if (CustomSkuValueTypeEnum.FIXED.getCode().equals(customSkuValueType)) {
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.attributeValueFixed).is(valueFixed);
        } else if (CustomSkuValueTypeEnum.RANGE.getCode().equals(customSkuValueType)) {
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.attributeValueMin).is(valueMin);
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.attributeValueMax).is(valueMax);
        }
        if (StringUtils.isNotBlank(userId)) {
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.userId).is(userId);
        }

        FeeTemplate feeTemplate = mongoTemplate.findOne(Query.query(criteria), FeeTemplate.class);
        return feeTemplate != null;
    }

    @Override
    public void initCustomSkuValueToRange() {
        Criteria criteria = where(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuType).is(FeeSkuTypeEnum.CUSTOM_SKU.code)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.customSkuValueType).exists(false);
        mongoTemplate.updateMulti(
                Query.query(criteria),
                new Update()
                        .set(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.customSkuValueType, CustomSkuValueTypeEnum.RANGE.getCode())
                        .set(BaseDocument.Fields.modifyTime, new Date())
                        .set(BaseDocument.Fields.updateBy, "initCustomSkuValueToRangeBySystem"),
                FeeTemplate.class);
    }


    @Override
    public void correctCustomSkuFatherStatusToLock() {
        MatchOperation initialMatch = match(Criteria.where(BaseDocument.Fields.del).is(false));
        GroupOperation groupOps = group(
                FeeTemplate.Fields.sceneCode,
                FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId,
                FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo)
                .count().as("sum")
                .first("$$ROOT").as("info");

        MatchOperation finalMatch = match(Criteria.where("sum").gt(1));
        Aggregation aggregation = newAggregation(
                initialMatch,
                groupOps,
                finalMatch,
                Aggregation.replaceRoot("$info")
        );
        AggregationResults<FeeTemplate> results = mongoTemplate.aggregate(
                aggregation,
                "feeTemplate",  // 集合名称
                FeeTemplate.class   // 结果类型
        );
        List<FeeTemplate> templateList = results.getMappedResults();
        for (FeeTemplate template : templateList) {
            String sceneCode = template.getSceneCode();
            Map<String, String> bizRule = template.getBizRule();
            String serviceId = bizRule.get(CommonBizRule.Fields.serviceId);
            String skuNo = bizRule.get(CommonBizRule.Fields.skuNo);
            Criteria criteria = where(BaseDocument.Fields.del).is(false)
                    .and(FeeTemplate.Fields.sceneCode).is(sceneCode)
                    .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                    .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(skuNo)
                    .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuType).is(FeeSkuTypeEnum.STANDARD_SKU.code)
                    .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).exists(true)
                    .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);
            List<FeeTemplate> feeTemplates = mongoTemplate.find(Query.query(criteria), FeeTemplate.class);
            if (CollectionUtils.isEmpty(feeTemplates)) {
                continue;
            }
            lock(feeTemplates.stream().map(FeeTemplate::getTemplateId).collect(Collectors.toList()));
        }
    }

    private String setBizRule(SaveTemplateSkuNoReq.BizRule bizRule, Map<String, String> ruleMap, String serviceId, String serviceName, String serviceCategoryId) {
        ruleMap.put(CommonBizRule.Fields.serviceId, serviceId);
        ruleMap.put(CommonBizRule.Fields.serviceName, serviceName);
        ruleMap.put(CommonBizRule.Fields.serviceCategoryId, serviceCategoryId);
        ruleMap.put(CommonBizRule.Fields.feeName, bizRule.getFeeName());
        ruleMap.put(CommonBizRule.Fields.feeUnit, bizRule.getFeeUnit());
        String feeTypeTag = bizRule.getFeeTypeTag();
        if (StringUtils.isNotBlank(feeTypeTag)) {
            ruleMap.put(CommonBizRule.Fields.feeTypeTag, feeTypeTag);
            FeeTypeTagEnum feeTypeTagEnum = FeeTypeTagEnum.fromCode(feeTypeTag);
            if (feeTypeTagEnum == null) {
                throw new BusException("费用类型标签不存在");
            }
            ruleMap.put(CommonBizRule.Fields.feeTypeTagName, feeTypeTagEnum.name);
        }
        ruleMap.put(CommonBizRule.Fields.applyFlag, bizRule.getApplyFlag());
        ruleMap.put(CommonBizRule.Fields.masterInputPrice, bizRule.getPrice());
        ruleMap.put(CommonBizRule.Fields.skuNumberPathNo, bizRule.getSkuNumberNo());
        ruleMap.put(CommonBizRule.Fields.skuNo, bizRule.getSkuNo());
        ruleMap.put(CommonBizRule.Fields.skuAttributePathName, bizRule.getSkuAttributePathName());
        ruleMap.put(CommonBizRule.Fields.skuNumberName, bizRule.getSkuNumberAttributePathName());
        ruleMap.put(CommonBizRule.Fields.attributeDisplayName, bizRule.getFeeName());
        return feeTypeTag;
    }

    @Override
    public void checkHasFeeTypeTag(List<FeeTemplateConfigureReq.FeeTemplateConfigure> configureList) {
        boolean hasFeeTypeTag = configureList.stream().anyMatch(e -> com.wanshifu.framework.utils.StringUtils.isNotBlank(e.getBizRule().get(CommonBizRule.Fields.feeTypeTag)));
        if (!hasFeeTypeTag) {
            throw new BusException("至少有一项sku需要计费");
        }
    }

    private Page<FeeTemplate> findTemplateByCondition(Long serviceId, FeeTemplateQueryByConditionReq toConditionReq) {
        FeeTemplateQueryByConditionReq.BizRuleCondition toBizRuleCondition = new FeeTemplateQueryByConditionReq.BizRuleCondition();
        toBizRuleCondition.setKey("serviceId");
        toBizRuleCondition.setValue(serviceId.toString());
        toBizRuleCondition.setMode(BizRuleConditionModeEnum.ALL_MATCH.code);
        toConditionReq.setBizRule(Lists.newArrayList(toBizRuleCondition));
        return queryByCondition(toConditionReq);
    }

    private final static String ATTRIBUTE_VALUE_MAX = "attributeValueMax";
    private final static String ATTRIBUTE_VALUE_MIN = "attributeValueMin";

    private boolean checkSkuNo(List<ServiceAttribute> serviceAttributes, FeeTemplate feeTemplate, String skuNo) {
        for (ServiceAttribute serviceAttribute : serviceAttributes) {
            for (ServiceAttributeValue serviceAttributeValue : serviceAttribute.getChildList()) {
                if (skuNo.equals(serviceAttributeValue.getAttributePathNo())) {
                    String valueStr = StrKit.trimSafely(serviceAttributeValue.getValue());
                    boolean isCustomSku = FeeSkuTypeEnum.CUSTOM_SKU.code.equals(feeTemplate.getBizRule().get(CommonBizRule.Fields.skuType));
                    if (isCustomSku) {
                        String valueMaxStr = feeTemplate.getBizRule().get(ATTRIBUTE_VALUE_MAX);
                        String valueMinStr = feeTemplate.getBizRule().get(ATTRIBUTE_VALUE_MIN);
                        try {
                            BigDecimal valueMax = new BigDecimal(valueMaxStr);
                            BigDecimal valueMin = new BigDecimal(valueMinStr);
                            BigDecimal value = new BigDecimal(valueStr);
                            return value.compareTo(valueMax) <= 0 && value.compareTo(valueMin) > 0;
                        } catch (Exception e) {
                            log.info("服务属性解析失败：无法转成数字 value={},max={},min={},e={}", valueStr, valueMaxStr, valueMinStr, e);
                            return false;
                        }
                    }
                    return true;
                }
                List<ServiceAttribute> childList = serviceAttributeValue.getChildList();
                if(CollectionUtils.isNotEmpty(childList)){
                    boolean checkSkuNo = checkSkuNo(childList, feeTemplate, skuNo);
                    if(checkSkuNo){
                        return true;
                    }
                }
            }
        }
        return false;
    }


    private Map<String, String> getBizRuleMap(SaveTemplateSkuNoReq.BizRule bizRule, String serviceId, String serviceName, String serviceCategoryId) {
        Map<String, String> bizRuleMap = new HashMap<>();
        setBizRule(bizRule, bizRuleMap, serviceId, serviceName, serviceCategoryId);
        bizRuleMap.put(CommonBizRule.Fields.skuType, FeeSkuTypeEnum.STANDARD_SKU.code);
        return bizRuleMap;
    }


    private void setSort(String feeTypeTag, FeeTemplate feeTemplate) {
        FeeTypeTagEnum feeTypeTagEnum = FeeTypeTagEnum.fromCode(feeTypeTag);
        if (feeTypeTagEnum == null) {
//            throw new BusException("费用类型标签不存在");
            return;
        }
        if (FeeTypeTagEnum.SERVICE_FEE.equals(feeTypeTagEnum)) {
            feeTemplate.setSort(50);
        } else if (FeeTypeTagEnum.GOOD_SURCHARGE.equals(feeTypeTagEnum)) {
            feeTemplate.setSort(20);
        } else if (FeeTypeTagEnum.STANDARD_SURCHARGE.equals(feeTypeTagEnum)) {
            feeTemplate.setSort(10);
        }
    }

//    private void checkForDuplicateBySceneCodeAndServiceId(String sceneCode, String serviceId) {
//        FeeTemplate feeTemplate = feeTemplateStorage.findOneBySceneCodeAndServiceId(sceneCode, serviceId);
//        if (feeTemplate != null) {
//            throw new BusException(String.format("场景：%s，服务ID：%s 已存在模板，请勿重复添加", sceneCode, serviceId));
//        }
//    }

    private void validateSaveParam(List<String> sceneCodeList, List<SaveTemplateSkuNoReq.BizRule> bizRuleList) {
        if (CollectionUtils.isEmpty(sceneCodeList)) {
            throw new BusException("场景编码不能为空");
        }
        if (CollectionUtils.isEmpty(bizRuleList)) {
            throw new BusException("bizRuleList不能为空");
        }
        Set<String> skuSet = bizRuleList.stream().map(SaveTemplateSkuNoReq.BizRule::getSkuNo).collect(Collectors.toSet());
        // 标准skuNo去重后如果数量和去重前数量不一致，说明有重复的标准sku
        if (bizRuleList.size() != skuSet.size()) {
            throw new BusException("存在重复的skuNo，请检查并纠正后再提交");
        }
        boolean hasFeeTypeTag = bizRuleList.stream().anyMatch(e -> StringUtils.isNotBlank(e.getFeeTypeTag()));
        if (!hasFeeTypeTag) {
            throw new BusException("至少有一项sku需要计费");
        }
        for (SaveTemplateSkuNoReq.BizRule bizRule : bizRuleList) {
            String feeTypeTag = bizRule.getFeeTypeTag();
            String applyFlag = bizRule.getApplyFlag();
            if (StringUtils.isNotBlank(feeTypeTag) && ApplyFlagEnum.NO_APPLY_IF_HAVE.code.equals(applyFlag)) {
                throw new BusException("「费用类型」和「不返价」不能同时存在");
            }
        }

    }

    private void validateParams(FeeTemplateConfigureReq feeTemplateConfigureReq, List<FeeTemplateConfigureReq.FeeTemplateConfigure> configureList) {
        if (org.springframework.util.CollectionUtils.isEmpty(configureList)) {
            throw new BusException("ConfigureList配置为空");
        }
        List<FeeTemplateConfigureReq.FeeTemplateConfigure> standardConfigureList = configureList.stream()
                .filter(e -> FeeSkuTypeEnum.STANDARD_SKU.code.equals(e.getBizRule().get(CommonBizRule.Fields.skuType)))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(standardConfigureList)) {
            Set<String> skuSet = standardConfigureList.stream().map(e -> e.getBizRule().get(CommonBizRule.Fields.skuNo)).collect(Collectors.toSet());
            // 标准skuNo去重后如果数量和去重前数量不一致，说明有重复的标准sku
            if (standardConfigureList.size() != skuSet.size()) {
                throw new BusException("存在重复的skuNo，请检查并纠正后再提交");
            }
        }
        if (feeTemplateConfigureReq.isCheckHasFeeTypeTag()) {
            checkHasFeeTypeTag(configureList);
        }
        for (FeeTemplateConfigureReq.FeeTemplateConfigure configure : configureList) {
            Map<String, String> bizRule = configure.getBizRule();
            String applyFlag = bizRule.get(CommonBizRule.Fields.applyFlag);
            String feeTypeTag = bizRule.get(CommonBizRule.Fields.feeTypeTag);
            if (com.wanshifu.framework.utils.StringUtils.isNotBlank(feeTypeTag) && ApplyFlagEnum.NO_APPLY_IF_HAVE.code.equals(applyFlag)) {
                throw new BusException("「费用类型」和「不返价」不能同时存在");
            }
        }
    }

    private String getServiceCategoryId(String serviceId) {
        Map<Long, Long> serviceIdMap = serviceApi.getServiceCategoryIdsByServiceIds(new HashSet<>(Collections.singletonList(Long.parseLong(serviceId))));
        return Optional.ofNullable(serviceIdMap)
                .flatMap(m -> m.containsKey(Long.parseLong(serviceId)) ? Optional.of(m.get(Long.parseLong(serviceId))) : Optional.empty())
                .map(Object::toString).orElseThrow(() -> new BusException(StrUtil.format("服务ID({})对应的服务类目ID为空", serviceId)));
    }


    private void addUserIdCondition(List<FeeTemplateQueryByConditionReq.BizRuleCondition> bizRule, Criteria criteria) {
        boolean hasUserId = bizRule.stream()
                .anyMatch(e -> CommonBizRule.Fields.userId.equals(e.getKey())
                        && StringUtils.isNotBlank(e.getValue()));
        if (!hasUserId) {
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.customSkuUserId).exists(false);
        }
    }

}
