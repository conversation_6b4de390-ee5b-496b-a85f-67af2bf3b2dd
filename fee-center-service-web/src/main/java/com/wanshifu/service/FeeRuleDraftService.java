package com.wanshifu.service;

import com.wanshifu.fee.center.domain.document.FeeRuleDraft;
import com.wanshifu.fee.center.domain.request.FeeRuleBatchOperationReq;
import com.wanshifu.fee.center.domain.request.FeeRuleDraftModifyReq;
import com.wanshifu.fee.center.domain.request.FeeRulePageReq;
import com.wanshifu.fee.center.domain.request.FeeRuleServicePageReq;
import com.wanshifu.fee.center.domain.request.feeRule.DeleteByBizIdReq;
import com.wanshifu.fee.center.domain.request.feeRule.QueryByBizIdReq;
import com.wanshifu.fee.center.domain.request.feeRule.QueryByBizIdResp;
import com.wanshifu.fee.center.domain.response.FeeRulePageResp;
import com.wanshifu.fee.center.domain.response.FeeRuleServicePageResp;
import com.wanshifu.framework.core.page.SimplePageInfo;

import java.util.List;

/**
 * @author: <PERSON>
 * @create: 2023-09-11 14:41
 * @description: 计价规则草稿service
 */
public interface FeeRuleDraftService {

    void batchDelete(FeeRuleBatchOperationReq req);

    FeeRuleDraft modify(FeeRuleDraftModifyReq req);

    void batchReview(FeeRuleBatchOperationReq req);

    SimplePageInfo<FeeRulePageResp> getFeeRuleDraftPage(FeeRulePageReq req);

    SimplePageInfo<FeeRuleServicePageResp> getFeeRuleDraftServicePage(FeeRuleServicePageReq req);

    int deleteByCondition(DeleteByBizIdReq req);

    List<QueryByBizIdResp> getByBizIdGroupByCondition(QueryByBizIdReq req);

    void sendMessagePreGenerateMessage(String sceneCode);
}
