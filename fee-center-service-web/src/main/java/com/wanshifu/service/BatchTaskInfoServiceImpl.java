package com.wanshifu.service;

import com.alibaba.fastjson.JSON;
import com.wanshifu.api.FileUploadUtils;
import com.wanshifu.bean.FileUploadResp;
import com.wanshifu.consumer.TagEnum;
import com.wanshifu.domain.request.BatchTaskGenerateReq;
import com.wanshifu.domain.request.BatchTaskUploadReq;
import com.wanshifu.domain.request.CustomSkuTemplateUploadReq;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.BatchTaskInfo;
import com.wanshifu.fee.center.domain.document.SceneInfo;
import com.wanshifu.fee.center.domain.enums.BatchTaskStatusEnum;
import com.wanshifu.fee.center.domain.enums.DivisionTypeEnum;
import com.wanshifu.fee.center.domain.request.BatchTaskQueryReq;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.framework.rocketmq.autoconfigure.model.NormalMessage;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.repository.BatchTaskInfoRepository;
import com.wanshifu.repository.SceneInfoRepository;
import com.wanshifu.strategy.batch.BatchTableInvoker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

@Service
@Slf4j
public class BatchTaskInfoServiceImpl implements BatchTaskInfoService{
    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private BatchTaskInfoRepository batchTaskInfoRepository;

    @Resource
    private SceneInfoRepository sceneInfoRepository;

    @Resource
    private BatchTableInvoker batchTableInvoker;

    @Resource
    private RocketMqSendService rocketMqSendService;

    @Value("${wanshifu.rocketMQ.fee-center-service-general-topic}")
    private String feeCenterServiceGeneralTopic;
    @Value("${spring.profiles.active}")
    private String activeProfiles;

    @Override
    public Page<BatchTaskInfo> queryByCondition(BatchTaskQueryReq batchTaskQueryReq) {
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        criteria.and(BatchTaskInfo.Fields.sceneCode).is(batchTaskQueryReq.getSceneCode());

        Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, BaseDocument.Fields.createTime));
        PageRequest pageRequest = new PageRequest(batchTaskQueryReq.pageNum - 1, batchTaskQueryReq.pageSize, sort);

        Query query = Query.query(criteria);
        long count = mongoTemplate.count(query, BatchTaskInfo.class);
        if (count > 0) {
            query.with(pageRequest);
            List<BatchTaskInfo> bizRuleMappings = mongoTemplate.find(query, BatchTaskInfo.class);
            return new PageImpl<>(bizRuleMappings, pageRequest, count);
        } else {
            return new PageImpl<>(Collections.emptyList(), pageRequest, count);
        }
    }

    @Override
    public BatchTaskInfo upload(BatchTaskUploadReq batchTaskUploadReq) {
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(BatchTaskStatusEnum.IMPORT.code);
        BatchTaskInfo one = mongoTemplate.findOne(new Query(criteria), BatchTaskInfo.class);
        if (one != null) {
            throw new BusException("存在正在「导入中」的任务，请稍后再试...");
        }
        SceneInfo sceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(batchTaskUploadReq.getSceneCode());
        if (Objects.isNull(sceneInfo)){
            throw new BusException("场景不存在");
        }
        DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(batchTaskUploadReq.getDivisionType());
        BatchTaskInfo batchTaskInfo = new BatchTaskInfo();
        Date now = new Date();
        batchTaskInfo.setBatchTaskId(SnowFlakeGenerator.INSTANCE.generate());
        batchTaskInfo.setDel(false);
        // 首次添加的还不能使用
        batchTaskInfo.setStatus(BatchTaskStatusEnum.IMPORT.code);
        batchTaskInfo.setCreateTime(now);
        batchTaskInfo.setModifyTime(now);
        batchTaskInfo.setDirectFeeRule(batchTaskUploadReq.isDirectFeeRule());
        if(Objects.nonNull(divisionTypeEnum)){
            batchTaskInfo.setDivisionType(divisionTypeEnum.code);
            batchTaskInfo.setDivisionTypeName(divisionTypeEnum.name);
        }

        batchTaskInfo.setSceneCode(sceneInfo.getSceneCode());
        batchTaskInfo.setOperator(batchTaskUploadReq.getOperator());
        batchTaskInfo.setSceneName(sceneInfo.getSceneName());
        batchTaskInfo.setBizId(batchTaskUploadReq.getBizId());
        String originalFilename = batchTaskUploadReq.getFile().getOriginalFilename();
        try{
            FileUploadResp fileUploadResp = FileUploadUtils.upload(batchTaskUploadReq.getFile().getBytes(), originalFilename.substring(originalFilename.lastIndexOf(PunConstant.DOT)));
            if (!fileUploadResp.isSuccess()) {
                log.error("文件上传失败:"+ fileUploadResp.getMsg(),fileUploadResp);
                throw new BusException("文件上传失败"+fileUploadResp.getMsg());
            }
            String fileUrl = fileUploadResp.getData().getFileUrl();
            batchTaskInfo.setFileUrl(fileUrl);
            batchTaskInfo.setFileName(originalFilename);
            batchTaskInfo.setFileId(fileUploadResp.getData().getFileId());

            BatchTaskInfo save = batchTaskInfoRepository.save(batchTaskInfo);
            // 便于调试
//            if ("dev".equals(activeProfiles) || "test".equals(activeProfiles)) {
//                batchTableInvoker.handle(sceneInfo, save);
//            } else {
                rocketMqSendService.sendSyncTransactionMessage(new NormalMessage(batchTaskInfo.getBatchTaskId(), UUID.randomUUID().toString(),feeCenterServiceGeneralTopic, TagEnum.IMPORT_TASK.tag, JSON.toJSONString(save)));
//            }
            return save;
        }catch (IOException e){
            log.error("文件上传失败"+e.getMessage(),e);
            throw new BusException("文件上传失败"+e.getMessage());
        }
    }

    @Override
    public byte[] generateTable(BatchTaskGenerateReq batchTaskGenerateReq) {
        String sceneCode = batchTaskGenerateReq.getSceneCode();
        SceneInfo sceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(sceneCode);
        if (Objects.isNull(sceneInfo)) {
            throw new BusException("场景编码不存在:"+sceneCode);
        }
        return batchTableInvoker.generate(sceneInfo,batchTaskGenerateReq);
    }


    @Override
    public String resend(Set<Long> ids) {
        Criteria criteria = Criteria.where(BatchTaskInfo.Fields.batchTaskId).in(ids)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(BatchTaskStatusEnum.IMPORT.code);

        Query query = Query.query(criteria);
        List<BatchTaskInfo> batchTaskInfos = mongoTemplate.find(query, BatchTaskInfo.class);
        for (BatchTaskInfo batchTaskInfo : batchTaskInfos) {
            log.info("resend message getBatchTaskId={}",batchTaskInfo.getBatchTaskId());
            rocketMqSendService.sendSyncTransactionMessage(new NormalMessage(batchTaskInfo.getBatchTaskId(), UUID.randomUUID().toString(),feeCenterServiceGeneralTopic, TagEnum.IMPORT_TASK.tag, JSON.toJSONString(batchTaskInfo)));
            log.info("resend message getBatchTaskId={} success",batchTaskInfo.getBatchTaskId());
        }

        return "success";
    }

    @Override
    public void uploadCustomSkuTemplate(CustomSkuTemplateUploadReq req) {
        
    }
}
