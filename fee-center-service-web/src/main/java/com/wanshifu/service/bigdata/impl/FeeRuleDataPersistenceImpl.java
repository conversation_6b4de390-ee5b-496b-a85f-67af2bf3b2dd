package com.wanshifu.service.bigdata.impl;

import com.mongodb.WriteResult;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.document.FeeTemplate;
import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.repository.FeeRuleRepository;
import com.wanshifu.service.bigdata.FeeRuleDataPersistence;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static org.springframework.data.mongodb.core.query.Criteria.where;

/**
 * 费用规则数据持久化实现类
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class FeeRuleDataPersistenceImpl implements FeeRuleDataPersistence {
    
    @Resource
    private FeeRuleRepository feeRuleRepository;
    
    @Resource
    private MongoTemplate mongoTemplate;
    
    @Override
    public int batchSaveAsDeleted(List<FeeRule> feeRules) {
        if (CollectionUtils.isEmpty(feeRules)) {
            log.warn("费用规则列表为空，跳过保存");
            return 0;
        }
        
        try {
            // 确保所有规则都标记为逻辑删除状态
            feeRules.forEach(feeRule -> feeRule.setDel(true));
            
            List<FeeRule> savedRules = feeRuleRepository.save(feeRules);
            int savedCount = savedRules != null ? savedRules.size() : 0;
            
            log.info("批量保存费用规则完成，保存数量={}", savedCount);
            return savedCount;
            
        } catch (Exception e) {
            log.error("批量保存费用规则异常，数量={}", feeRules.size(), e);
            return 0;
        }
    }
    
    @Override
    public int markAsActive(String sceneCode, String version) {
        try {
            Criteria criteria = where(FeeTemplate.Fields.sceneCode).is(sceneCode)
                    .and(BaseDocument.Fields.del).is(true)
                    .and(FeeRule.Fields.bizRule + PunConstant.DOT + "currentVersion").is(version);
            
            Update update = new Update().set(BaseDocument.Fields.del, false);

            WriteResult result = mongoTemplate.updateMulti(new Query(criteria), update, FeeRule.class);
            long modifiedCount = result.getN();
            
            log.info("标记为可用完成，sceneCode={}, version={}, 更新数量={}", 
                    sceneCode, version, modifiedCount);
            
            return (int) modifiedCount;
            
        } catch (Exception e) {
            log.error("标记为可用异常，sceneCode={}, version={}", sceneCode, version, e);
            return 0;
        }
    }
    
    @Override
    public int deleteOldVersions(String sceneCode, String currentVersion) {
        try {
            Criteria criteria = where(FeeTemplate.Fields.sceneCode).is(sceneCode)
                    .and(BaseDocument.Fields.del).is(false)
                    .and(FeeRule.Fields.bizRule + PunConstant.DOT + "currentVersion").ne(currentVersion);
            
            // 对于某些场景，不能删除人工添加的规则
            SceneCodeEnum sceneCodeEnum = SceneCodeEnum.fromCode(sceneCode);
            if (sceneCodeEnum == SceneCodeEnum.BARGAIN_PRICE_EVERYDAY || 
                sceneCodeEnum == SceneCodeEnum.USER_HISTORICAL_APPOINT_PRICE) {
                
                criteria = new Criteria().andOperator(
                        criteria,
                        where(FeeRule.Fields.bizRule + PunConstant.DOT + "currentVersion").exists(true)
                );
            }

            WriteResult result = mongoTemplate.remove(new Query(criteria), FeeRule.class);
            long deletedCount = result.getN();
            
            log.info("删除旧版本数据完成，sceneCode={}, currentVersion={}, 删除数量={}", 
                    sceneCode, currentVersion, deletedCount);
            
            return (int) deletedCount;
            
        } catch (Exception e) {
            log.error("删除旧版本数据异常，sceneCode={}, currentVersion={}", sceneCode, currentVersion, e);
            return 0;
        }
    }
    
    @Override
    public int deleteAllVersionedData(String sceneCode) {
        try {
            Criteria criteria = where(FeeRule.Fields.sceneCode).is(sceneCode)
                    .and(FeeRule.Fields.bizRule + PunConstant.DOT + "currentVersion").ne(null);

            WriteResult result = mongoTemplate.remove(new Query(criteria), FeeRule.class);
            long deletedCount = result.getN();
            
            log.info("删除所有版本数据完成，sceneCode={}, 删除数量={}", sceneCode, deletedCount);
            
            return (int) deletedCount;
            
        } catch (Exception e) {
            log.error("删除所有版本数据异常，sceneCode={}", sceneCode, e);
            return 0;
        }
    }
    
    @Override
    public boolean isConnectionHealthy() {
        try {
            // 简单的健康检查
            mongoTemplate.getCollection("feeRule").findOne();
            return true;
        } catch (Exception e) {
            log.error("数据库连接健康检查失败", e);
            return false;
        }
    }
    
    @Override
    public long countBySceneAndVersion(String sceneCode, String version) {
        try {
            Criteria criteria = where(FeeRule.Fields.sceneCode).is(sceneCode)
                    .and(FeeRule.Fields.bizRule + PunConstant.DOT + "currentVersion").is(version);
            
            long count = mongoTemplate.count(new Query(criteria), FeeRule.class);
            
            log.debug("统计场景版本数据，sceneCode={}, version={}, count={}", 
                    sceneCode, version, count);
            
            return count;
            
        } catch (Exception e) {
            log.error("统计场景版本数据异常，sceneCode={}, version={}", sceneCode, version, e);
            return 0;
        }
    }
}
