package com.wanshifu.service.bigdata;

import com.wanshifu.fee.center.domain.request.common.RangeReq;

import java.util.concurrent.CompletableFuture;
import java.util.function.Function;

/**
 * 任务执行管理接口
 * 负责管理并发任务的执行，包括任务提交、进度跟踪、异常处理等功能
 * 
 * <AUTHOR> Assistant
 */
public interface TaskExecutionManager {
    
    /**
     * 提交异步任务
     * 
     * @param task 任务函数
     * @param range 数据范围
     * @param <T> 任务返回类型
     * @return CompletableFuture对象
     */
    <T> CompletableFuture<T> submitTask(Function<RangeReq, T> task, RangeReq range);
    
    /**
     * 等待所有任务完成
     * 
     * @param timeoutSeconds 超时时间（秒）
     * @return true-所有任务完成；false-超时或有任务失败
     */
    boolean waitForAllTasks(long timeoutSeconds);
    
    /**
     * 获取当前正在执行的任务数量
     * 
     * @return 任务数量
     */
    int getPendingTaskCount();
    
    /**
     * 获取已完成的任务数量
     * 
     * @return 任务数量
     */
    int getCompletedTaskCount();
    
    /**
     * 获取失败的任务数量
     * 
     * @return 任务数量
     */
    int getFailedTaskCount();
    
    /**
     * 取消所有未完成的任务
     */
    void cancelAllTasks();
    
    /**
     * 重置任务管理器状态
     */
    void reset();
    
    /**
     * 检查是否应该终止任务执行
     * 
     * @return true-应该终止；false-继续执行
     */
    boolean shouldTerminate();
    
    /**
     * 设置终止标志
     */
    void setTerminateFlag();
}
