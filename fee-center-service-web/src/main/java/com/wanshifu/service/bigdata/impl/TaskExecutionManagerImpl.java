package com.wanshifu.service.bigdata.impl;

import com.wanshifu.fee.center.domain.request.common.RangeReq;
import com.wanshifu.service.bigdata.TaskExecutionManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;

/**
 * 任务执行管理器实现类
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class TaskExecutionManagerImpl implements TaskExecutionManager {
    
    @Resource
    private Executor fetchBigdataTaskExecutor;
    
    private final AtomicInteger pendingTasks = new AtomicInteger(0);
    private final AtomicInteger completedTasks = new AtomicInteger(0);
    private final AtomicInteger failedTasks = new AtomicInteger(0);
    private final AtomicBoolean shouldTerminate = new AtomicBoolean(false);
    private final Semaphore semaphore = new Semaphore(5); // 限制并发数
    
    @Override
    public <T> CompletableFuture<T> submitTask(Function<RangeReq, T> task, RangeReq range) {
        if (shouldTerminate.get()) {
            return CompletableFuture.completedFuture(null);
        }
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                semaphore.acquire();
                pendingTasks.incrementAndGet();
                
                if (shouldTerminate.get()) {
                    return null;
                }
                
                T result = task.apply(range);
                completedTasks.incrementAndGet();
                
                log.debug("任务执行完成，range={}-{}, 待处理={}, 已完成={}", 
                        range.getBeginId(), range.getEndId(), pendingTasks.get(), completedTasks.get());
                
                return result;
                
            } catch (Exception e) {
                failedTasks.incrementAndGet();
                log.error("任务执行异常，range={}-{}", range.getBeginId(), range.getEndId(), e);
                return null;
            } finally {
                pendingTasks.decrementAndGet();
                semaphore.release();
            }
        }, fetchBigdataTaskExecutor);
    }
    
    @Override
    public boolean waitForAllTasks(long timeoutSeconds) {
        long startTime = System.currentTimeMillis();
        long timeoutMillis = timeoutSeconds * 1000;
        
        while (pendingTasks.get() > 0 && !shouldTerminate.get()) {
            if (System.currentTimeMillis() - startTime > timeoutMillis) {
                log.warn("等待任务完成超时，待处理任务数={}, 超时时间={}秒", pendingTasks.get(), timeoutSeconds);
                return false;
            }
            
            try {
                Thread.sleep(1000); // 每秒检查一次
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("等待任务完成被中断");
                return false;
            }
        }
        
        boolean allCompleted = pendingTasks.get() == 0;
        log.info("任务等待结束，所有任务完成={}, 待处理={}, 已完成={}, 失败={}", 
                allCompleted, pendingTasks.get(), completedTasks.get(), failedTasks.get());
        
        return allCompleted;
    }
    
    @Override
    public int getPendingTaskCount() {
        return pendingTasks.get();
    }
    
    @Override
    public int getCompletedTaskCount() {
        return completedTasks.get();
    }
    
    @Override
    public int getFailedTaskCount() {
        return failedTasks.get();
    }
    
    @Override
    public void cancelAllTasks() {
        shouldTerminate.set(true);
        log.info("取消所有任务，待处理={}, 已完成={}, 失败={}", 
                pendingTasks.get(), completedTasks.get(), failedTasks.get());
    }
    
    @Override
    public void reset() {
        pendingTasks.set(0);
        completedTasks.set(0);
        failedTasks.set(0);
        shouldTerminate.set(false);
        log.info("重置任务管理器状态");
    }
    
    @Override
    public boolean shouldTerminate() {
        return shouldTerminate.get();
    }
    
    @Override
    public void setTerminateFlag() {
        shouldTerminate.set(true);
        log.info("设置终止标志");
    }
}
