package com.wanshifu.service.bigdata.impl;

import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.request.common.RangeReq;
import com.wanshifu.fee.center.domain.response.bigdata.BizRule;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.service.bigdata.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;

/**
 * 大数据同步协调器实现类
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class BigdataSyncCoordinatorImpl implements BigdataSyncCoordinator {
    
    private static final String SYNC_LOCK_KEY = "feeCenterService:pullFeeRuleFromBigdataLock";
    private static final long DEFAULT_MIN_ID = 1L;
    private static final long DEFAULT_MAX_ID = 5000000L;
    
    @Resource
    private DataVersionManager dataVersionManager;
    
    @Resource
    private BigdataDataFetcher bigdataDataFetcher;
    
    @Resource
    private FeeRuleDataProcessor feeRuleDataProcessor;
    
    @Resource
    private FeeRuleDataPersistence feeRuleDataPersistence;
    
    @Resource
    private TaskExecutionManager taskExecutionManager;
    
    @Resource
    private List<SceneProcessingStrategy> sceneProcessingStrategies;
    
    @Resource
    private RedisHelper redisHelper;
    
    @Value("${pull-feeRule-from-bigdata-pageSize:1000}")
    private Integer pageSize;
    
    @Override
    public void syncData(String sceneCode) {
        log.info("开始同步大数据，sceneCode={}", sceneCode);
        
        try {
            // 检查前置条件
            if (!checkPreconditions(sceneCode)) {
                return;
            }
            
            // 设置同步锁
            redisHelper.set(SYNC_LOCK_KEY, "1", 2 * 60 * 60);
            
            SceneCodeEnum sceneCodeEnum = SceneCodeEnum.fromCode(sceneCode);
            if (sceneCodeEnum == null) {
                throw new BusException("无效的场景编码: " + sceneCode);
            }
            
            // 执行同步
            doSyncData(sceneCode, sceneCodeEnum);
            
            log.info("同步大数据完成，sceneCode={}", sceneCode);
            
        } catch (Exception e) {
            log.error("同步大数据异常，sceneCode={}", sceneCode, e);
            throw e;
        } finally {
            // 清理同步锁
            redisHelper.del(SYNC_LOCK_KEY);
        }
    }
    
    @Override
    public boolean checkPreconditions(String sceneCode) {
        // 检查版本是否变化
        if (!dataVersionManager.isVersionChanged(sceneCode)) {
            log.info("版本未变化，无需同步，sceneCode={}", sceneCode);
            return false;
        }
        
        // 检查大数据连接
        if (!bigdataDataFetcher.isConnectionHealthy()) {
            log.error("大数据连接异常，sceneCode={}", sceneCode);
            return false;
        }
        
        // 检查数据库连接
        if (!feeRuleDataPersistence.isConnectionHealthy()) {
            log.error("数据库连接异常，sceneCode={}", sceneCode);
            return false;
        }
        
        return true;
    }
    
    @Override
    public String getSyncStatus(String sceneCode) {
        String lockValue = redisHelper.get(SYNC_LOCK_KEY);
        if (StringUtils.isNotBlank(lockValue)) {
            return "同步中";
        }
        
        String currentVersion = dataVersionManager.getCurrentVersion(sceneCode);
        String latestVersion = dataVersionManager.getLatestVersionFromBigdata(sceneCode);
        
        if (currentVersion == null) {
            return "未初始化";
        } else if (currentVersion.equals(latestVersion)) {
            return "已同步";
        } else {
            return "需要同步";
        }
    }
    
    @Override
    public void stopSync(String sceneCode) {
        taskExecutionManager.setTerminateFlag();
        redisHelper.del(SYNC_LOCK_KEY);
        log.info("停止同步任务，sceneCode={}", sceneCode);
    }


    private void doSyncData(String sceneCode, SceneCodeEnum sceneCodeEnum) {
        String currentVersion = dataVersionManager.getCurrentVersion(sceneCode);
        String latestVersion = dataVersionManager.getLatestVersionFromBigdata(sceneCode);
        
        if (latestVersion == null) {
            throw new BusException("获取最新版本失败");
        }
        
        // 立即更新版本缓存，防止重复执行
        dataVersionManager.updateCurrentVersion(sceneCode, latestVersion);
        
        // 获取场景处理策略
        SceneProcessingStrategy strategy = getSceneStrategy(sceneCodeEnum);
        
        // 场景预处理
        strategy.preProcess(sceneCode);
        
        // 获取ID范围
        RangeReq idRange = getIdRange(sceneCode, latestVersion, strategy);
        
        if (StringUtils.isBlank(currentVersion)) {
            // 初始化同步
            doInitialSync(sceneCode, sceneCodeEnum, latestVersion, idRange);
        } else {
            // 增量同步
            doIncrementalSync(sceneCode, sceneCodeEnum, currentVersion, latestVersion, idRange);
        }
        
        // 场景后处理
        strategy.postProcess(sceneCode);
    }

    
    private void doInitialSync(String sceneCode, SceneCodeEnum sceneCodeEnum, String version, RangeReq idRange) {
        log.info("开始初始化同步，sceneCode={}, version={}", sceneCode, version);
        
        // 删除所有带版本号的数据
        feeRuleDataPersistence.deleteAllVersionedData(sceneCode);
        
        // 分页同步数据
        syncDataInBatches(sceneCodeEnum, version, idRange);
        
        // 等待所有任务完成
        taskExecutionManager.waitForAllTasks(60);
        
        // 标记为可用
        feeRuleDataPersistence.markAsActive(sceneCode, version);
        
        log.info("初始化同步完成，sceneCode={}, version={}", sceneCode, version);
    }

    
    private void doIncrementalSync(String sceneCode, SceneCodeEnum sceneCodeEnum, String currentVersion, 
                                   String latestVersion, RangeReq idRange) {
        log.info("开始增量同步，sceneCode={}, currentVersion={}, latestVersion={}", 
                sceneCode, currentVersion, latestVersion);
        
        // 保存上一版本
        dataVersionManager.saveLastVersion(sceneCode, currentVersion);
        
        // 分页同步数据
        syncDataInBatches(sceneCodeEnum, latestVersion, idRange);
        
        // 等待所有任务完成
        taskExecutionManager.waitForAllTasks(60);
        
        // 标记新版本为可用
        feeRuleDataPersistence.markAsActive(sceneCode, latestVersion);
        
        // 删除旧版本数据
        feeRuleDataPersistence.deleteOldVersions(sceneCode, latestVersion);
        
        log.info("增量同步完成，sceneCode={}, latestVersion={}", sceneCode, latestVersion);
    }


    private void syncDataInBatches(SceneCodeEnum sceneCodeEnum, String version, RangeReq idRange) {
        taskExecutionManager.reset();
        
        long currentBegin = idRange.getBeginId();
        long endId = idRange.getEndId();
        
        while (currentBegin <= endId && !taskExecutionManager.shouldTerminate()) {
            long currentEnd = Math.min(currentBegin + pageSize - 1, endId);
            RangeReq batchRange = new RangeReq(currentBegin, currentEnd);
            
            // 提交异步任务
            taskExecutionManager.submitTask(range -> processBatch(range, sceneCodeEnum, version), batchRange);
            
            currentBegin = currentEnd + 1;
        }
    }


    private Boolean processBatch(RangeReq range, SceneCodeEnum sceneCodeEnum, String version) {
        try {
            // 获取原始数据
            List<BizRule> rawData = bigdataDataFetcher.fetchBizRules(range, sceneCodeEnum);
            if (CollectionUtils.isEmpty(rawData)) {
                return true; // 数据为空，正常结束
            }
            
            // 处理数据
            List<FeeRule> feeRules = feeRuleDataProcessor.processData(rawData, sceneCodeEnum, version);
            if (CollectionUtils.isEmpty(feeRules)) {
                return true; // 处理后数据为空，正常结束
            }
            
            // 保存数据
            feeRuleDataPersistence.batchSaveAsDeleted(feeRules);
            
            return true;
        } catch (Exception e) {
            log.error("处理批次数据异常，range={}-{}", range.getBeginId(), range.getEndId(), e);
            return false;
        }
    }


    private SceneProcessingStrategy getSceneStrategy(SceneCodeEnum sceneCodeEnum) {
        return sceneProcessingStrategies.stream()
                .filter(strategy -> strategy.supports(sceneCodeEnum))
                .min(Comparator.comparingInt(SceneProcessingStrategy::getPriority))
                .orElseThrow(() -> new BusException("未找到支持的场景处理策略: " + sceneCodeEnum));
    }

    
    private RangeReq getIdRange(String sceneCode, String version, SceneProcessingStrategy strategy) {
        // 先尝试从策略获取特定的ID范围
        RangeReq sceneRange = strategy.getSceneSpecificIdRange(sceneCode, version);
        if (sceneRange != null) {
            return sceneRange;
        }
        
        // 使用默认范围
        return new RangeReq(DEFAULT_MIN_ID, DEFAULT_MAX_ID);
    }
}
