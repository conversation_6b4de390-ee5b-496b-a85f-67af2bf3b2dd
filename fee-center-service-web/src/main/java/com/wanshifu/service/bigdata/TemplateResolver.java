package com.wanshifu.service.bigdata;

import com.wanshifu.fee.center.domain.document.FeeTemplate;
import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.response.bigdata.BizRule;

/**
 * 模板解析器接口
 * 负责根据业务规则数据查找对应的费用模板
 * 
 * <AUTHOR> Assistant
 */
public interface TemplateResolver {
    
    /**
     * 根据业务规则查找对应的费用模板
     * 
     * @param bizRule 业务规则数据
     * @param sceneCode 场景编码
     * @return 费用模板，如果找不到返回null
     */
    FeeTemplate resolveTemplate(BizRule bizRule, SceneCodeEnum sceneCode);
    
    /**
     * 查找标准SKU对应的模板
     * 
     * @param bizRule 业务规则数据
     * @param sceneCode 场景编码
     * @return 费用模板，如果找不到返回null
     */
    FeeTemplate resolveStandardSkuTemplate(BizRule bizRule, SceneCodeEnum sceneCode);
    
    /**
     * 查找自定义SKU对应的模板
     * 
     * @param bizRule 业务规则数据
     * @param sceneCode 场景编码
     * @return 费用模板，如果找不到返回null
     */
    FeeTemplate resolveCustomSkuTemplate(BizRule bizRule, SceneCodeEnum sceneCode);
    
    /**
     * 根据模板ID直接查找模板
     * 
     * @param templateId 模板ID
     * @return 费用模板，如果找不到返回null
     */
    FeeTemplate findTemplateById(Long templateId);
}
