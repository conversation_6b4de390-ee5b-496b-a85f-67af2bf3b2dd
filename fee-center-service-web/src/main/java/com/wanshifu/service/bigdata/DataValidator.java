package com.wanshifu.service.bigdata;

import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.response.bigdata.BizRule;

/**
 * 数据验证器接口
 * 负责验证从大数据获取的原始数据的有效性
 * 
 * <AUTHOR> Assistant
 */
public interface DataValidator {
    
    /**
     * 验证业务规则数据的基本有效性
     * 
     * @param bizRule 业务规则数据
     * @return true-数据有效；false-数据无效
     */
    boolean validateBasic(BizRule bizRule);
    
    /**
     * 验证价格数据的有效性
     * 
     * @param bizRule 业务规则数据
     * @return true-价格有效；false-价格无效
     */
    boolean validatePrice(BizRule bizRule);
    
    /**
     * 验证地区信息的有效性
     * 
     * @param bizRule 业务规则数据
     * @return true-地区信息有效；false-地区信息无效
     */
    boolean validateDivision(BizRule bizRule);
    
    /**
     * 验证服务信息的有效性
     * 
     * @param bizRule 业务规则数据
     * @return true-服务信息有效；false-服务信息无效
     */
    boolean validateService(BizRule bizRule);
    
    /**
     * 验证SKU类型的有效性
     * 
     * @param bizRule 业务规则数据
     * @param sceneCode 场景编码
     * @return true-SKU类型有效；false-SKU类型无效
     */
    boolean validateSkuType(BizRule bizRule, SceneCodeEnum sceneCode);
}
