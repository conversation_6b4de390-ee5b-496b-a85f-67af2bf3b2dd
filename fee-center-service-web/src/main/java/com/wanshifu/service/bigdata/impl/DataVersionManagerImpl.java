package com.wanshifu.service.bigdata.impl;

import com.wanshifu.fee.center.domain.response.bigdata.InsertTimestamp;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.infrastructure.gateway.BigdataGateway;
import com.wanshifu.infrastructure.utils.CommonUtils;
import com.wanshifu.service.bigdata.DataVersionManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 数据版本管理实现类
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class DataVersionManagerImpl implements DataVersionManager {
    
    private static final String VERSION_KEY_PREFIX = "pullFeeRuleFromBigdata:";
    private static final String CURRENT_VERSION_SUFFIX = ":currentVersion";
    private static final String LAST_VERSION_SUFFIX = ":lastVersion";
    private static final int CACHE_DURATION = 60 * 60 * 24 * 60; // 60天
    
    @Resource
    private RedisHelper redisHelper;
    
    @Resource
    private BigdataGateway bigdataGateway;
    
    @Override
    public boolean isVersionChanged(String sceneCode) {
        try {
            String currentVersion = getCurrentVersion(sceneCode);
            String latestVersion = getLatestVersionFromBigdata(sceneCode);
            
            if (latestVersion == null) {
                log.error("获取大数据最新版本失败，sceneCode={}", sceneCode);
                return false;
            }
            
            boolean changed = !latestVersion.equals(currentVersion);
            log.info("版本检查结果，sceneCode={}, currentVersion={}, latestVersion={}, changed={}", 
                    sceneCode, currentVersion, latestVersion, changed);
            
            return changed;
        } catch (Exception e) {
            log.error("检查版本变化异常，sceneCode={}", sceneCode, e);
            return false;
        }
    }
    
    @Override
    public String getCurrentVersion(String sceneCode) {
        String key = buildCurrentVersionKey(sceneCode);
        String version = redisHelper.get(key);
        log.debug("获取当前版本，sceneCode={}, version={}", sceneCode, version);
        return version;
    }
    
    @Override
    public String getLatestVersionFromBigdata(String sceneCode) {
        try {
            InsertTimestamp data = bigdataGateway.getTimestamp(sceneCode);
            if (data == null || data.getInsertTimestamp() == null) {
                log.error("获取大数据时间戳失败，sceneCode={}", sceneCode);
                return null;
            }
            
            String version = data.getInsertTimestamp().toString();
            log.debug("获取大数据最新版本，sceneCode={}, version={}", sceneCode, version);
            return version;
        } catch (Exception e) {
            log.error("获取大数据最新版本异常，sceneCode={}", sceneCode, e);
            return null;
        }
    }
    
    @Override
    public void updateCurrentVersion(String sceneCode, String version) {
        String key = buildCurrentVersionKey(sceneCode);
        redisHelper.set(key, version, CACHE_DURATION);
        log.info("更新当前版本，sceneCode={}, version={}", sceneCode, version);
    }
    
    @Override
    public void saveLastVersion(String sceneCode, String version) {
        String key = buildLastVersionKey(sceneCode);
        redisHelper.set(key, version, CACHE_DURATION);
        log.info("保存上一版本，sceneCode={}, version={}", sceneCode, version);
    }
    
    @Override
    public String getLastVersion(String sceneCode) {
        String key = buildLastVersionKey(sceneCode);
        String version = redisHelper.get(key);
        log.debug("获取上一版本，sceneCode={}, version={}", sceneCode, version);
        return version;
    }
    
    @Override
    public void clearVersionCache(String sceneCode) {
        String currentKey = buildCurrentVersionKey(sceneCode);
        String lastKey = buildLastVersionKey(sceneCode);
        
        redisHelper.del(currentKey);
        redisHelper.del(lastKey);
        
        log.info("清理版本缓存，sceneCode={}", sceneCode);
    }
    
    private String buildCurrentVersionKey(String sceneCode) {
        return VERSION_KEY_PREFIX + sceneCode + CURRENT_VERSION_SUFFIX;
    }
    
    private String buildLastVersionKey(String sceneCode) {
        return VERSION_KEY_PREFIX + sceneCode + LAST_VERSION_SUFFIX;
    }
}
