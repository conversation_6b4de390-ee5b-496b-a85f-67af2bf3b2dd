package com.wanshifu.service.bigdata.impl;

import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.response.bigdata.BizRule;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.service.bigdata.DuplicateChecker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 重复性检查器实现类
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class DuplicateCheckerImpl implements <PERSON>plicate<PERSON>hecker {
    
    @Resource
    private RedisHelper redisHelper;
    
    @Override
    public boolean isDuplicate(FeeRule feeRule, SceneCodeEnum sceneCode) {
        // 这里可以实现更复杂的重复性检查逻辑
        // 目前简化实现，主要检查人工维护的数据
        
        if (feeRule == null || feeRule.getBizRule() == null) {
            return false;
        }
        
        String templateId = feeRule.getBizRule().get("templateId");
        String level4DivisionId = feeRule.getBizRule().get("level4DivisionId");
        
        if (StringUtils.isBlank(templateId) || StringUtils.isBlank(level4DivisionId)) {
            return false;
        }
        
        return hasManualMaintainedDataByCache(sceneCode.getCode(), Long.valueOf(templateId), level4DivisionId);
    }
    
    @Override
    public boolean hasManualMaintainedData(BizRule bizRule, SceneCodeEnum sceneCode, Long templateId) {
        // 检查是否存在人工维护的重复数据
        if ((sceneCode == SceneCodeEnum.BARGAIN_PRICE_EVERYDAY || 
             sceneCode == SceneCodeEnum.USER_HISTORICAL_APPOINT_PRICE) && 
            bizRule.getStreetId() != null) {
            
            return hasManualMaintainedDataByCache(sceneCode.getCode(), templateId, bizRule.getStreetId().toString());
        }
        
        return false;
    }
    
    @Override
    public String generateCacheKey(String sceneCode, Long templateId, String level4DivisionId) {
        return String.format("bargainPriceEveryday:%s:%s:%s", sceneCode, templateId, level4DivisionId);
    }
    
    private boolean hasManualMaintainedDataByCache(String sceneCode, Long templateId, String level4DivisionId) {
        try {
            String cacheKey = generateCacheKey(sceneCode, templateId, level4DivisionId);
            String cacheValue = redisHelper.get(cacheKey);
            
            boolean hasDuplicate = StringUtils.isNotBlank(cacheValue);
            
            if (hasDuplicate) {
                log.warn("存在人工维护的重复数据，sceneCode={}, templateId={}, level4DivisionId={}", 
                        sceneCode, templateId, level4DivisionId);
            }
            
            return hasDuplicate;
            
        } catch (Exception e) {
            log.error("检查人工维护数据异常，sceneCode={}, templateId={}, level4DivisionId={}", 
                    sceneCode, templateId, level4DivisionId, e);
            return false;
        }
    }
}
