package com.wanshifu.service.bigdata;

import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.response.bigdata.BizRule;

/**
 * 重复性检查器接口
 * 负责检查数据的重复性，避免重复数据的处理
 * 
 * <AUTHOR> Assistant
 */
public interface DuplicateChecker {
    
    /**
     * 检查费用规则是否与已有数据重复
     * 
     * @param feeRule 费用规则
     * @param sceneCode 场景编码
     * @return true-存在重复；false-不重复
     */
    boolean isDuplicate(FeeRule feeRule, SceneCodeEnum sceneCode);
    
    /**
     * 检查是否存在人工维护的重复数据
     * 
     * @param bizRule 业务规则数据
     * @param sceneCode 场景编码
     * @param templateId 模板ID
     * @return true-存在人工维护的重复数据；false-不存在
     */
    boolean hasManualMaintainedData(BizRule bizRule, SceneCodeEnum sceneCode, Long templateId);
    
    /**
     * 生成重复性检查的缓存键
     * 
     * @param sceneCode 场景编码
     * @param templateId 模板ID
     * @param level4DivisionId 四级地区ID
     * @return 缓存键
     */
    String generateCacheKey(String sceneCode, Long templateId, String level4DivisionId);
}
