package com.wanshifu.service.bigdata.strategy;

import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.request.common.RangeReq;
import com.wanshifu.fee.center.domain.response.bigdata.IdRange;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.infrastructure.gateway.BigdataGateway;
import com.wanshifu.service.bigdata.SceneProcessingStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 天天特价场景处理策略
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class BargainPriceSceneStrategy implements SceneProcessingStrategy {
    
    @Resource
    private BigdataGateway bigdataGateway;
    
    @Resource
    private RedisHelper redisHelper;
    
    private static final String LOWEST_PRICE_PREFIX = "getLowestPriceByLevel1GoodsCategoryIdOrServiceIds";
    
    @Override
    public boolean supports(SceneCodeEnum sceneCode) {
        return SceneCodeEnum.BARGAIN_PRICE_EVERYDAY == sceneCode;
    }
    
    @Override
    public void preProcess(String sceneCode) {
        log.info("天天特价场景预处理开始，sceneCode={}", sceneCode);
        
        try {
            // 预处理人工维护数据到缓存
            preHandleManualMaintenanceDataToCache(sceneCode);
            
            log.info("天天特价场景预处理完成，sceneCode={}", sceneCode);
        } catch (Exception e) {
            log.error("天天特价场景预处理异常，sceneCode={}", sceneCode, e);
        }
    }
    
    @Override
    public RangeReq getSceneSpecificIdRange(String sceneCode, String version) {
        try {
            log.info("获取天天特价场景ID范围，sceneCode={}, version={}", sceneCode, version);
            
            IdRange idRange = bigdataGateway.getSkuIdRangeFromBigData(version);
            if (idRange == null || idRange.getMinId() == null || idRange.getMaxId() == null) {
                log.error("获取天天特价场景ID范围失败，sceneCode={}, version={}", sceneCode, version);
                return null;
            }
            
            RangeReq rangeReq = new RangeReq(idRange.getMinId(), idRange.getMaxId());
            log.info("获取天天特价场景ID范围成功，sceneCode={}, range={}-{}", 
                    sceneCode, rangeReq.getBeginId(), rangeReq.getEndId());
            
            return rangeReq;
            
        } catch (Exception e) {
            log.error("获取天天特价场景ID范围异常，sceneCode={}, version={}", sceneCode, version, e);
            return null;
        }
    }
    
    @Override
    public void postProcess(String sceneCode) {
        log.info("天天特价场景后处理开始，sceneCode={}", sceneCode);
        
        try {
            // 清理最低价缓存
            String delRedisKey = LOWEST_PRICE_PREFIX + "*";
            redisHelper.del(delRedisKey);
            log.info("清理最低价缓存完成，key={}", delRedisKey);
            
            // 这里可以调用预生成最低价和天天特价规则的方法
            // 由于这些方法在原始类中，这里先记录日志
            log.info("需要调用预生成最低价和天天特价规则的方法");
            
            log.info("天天特价场景后处理完成，sceneCode={}", sceneCode);
        } catch (Exception e) {
            log.error("天天特价场景后处理异常，sceneCode={}", sceneCode, e);
        }
    }
    
    @Override
    public int getPriority() {
        return 1; // 高优先级
    }
    
    private void preHandleManualMaintenanceDataToCache(String sceneCode) {
        // 这里应该实现预处理人工维护数据到缓存的逻辑
        // 由于涉及到复杂的数据库查询，这里先记录日志
        log.info("预处理人工维护数据到缓存，sceneCode={}", sceneCode);
    }
}
