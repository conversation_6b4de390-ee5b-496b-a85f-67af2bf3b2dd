package com.wanshifu.service.bigdata.impl;

import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.enums.RuleStatusEnum;
import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.response.bigdata.BizRule;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.service.bigdata.DataTransformer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 数据转换器实现类
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class DataTransformerImpl implements DataTransformer {
    
    private static final String SYSTEM_USER = "系统定时同步大数据";
    
    @Override
    public FeeRule transform(BizRule bizRule, SceneCodeEnum sceneCode, String version) {
        FeeRule feeRule = new FeeRule();
        
        // 设置基本信息
        setBasicInfo(feeRule, sceneCode, version);
        
        // 构建业务规则映射
        Map<String, String> bizRuleMap = buildBizRuleMap(bizRule);
        bizRuleMap.put("currentVersion", version);
        
        // 设置业务规则信息
        setBizRuleInfo(feeRule, bizRuleMap);
        
        // 设置价格相关信息
        setPriceInfo(feeRule, bizRule);
        
        log.debug("成功转换费用规则，serviceId={}, skuNo={}, feeRuleId={}", 
                bizRule.getServiceId(), bizRule.getSkuNo(), feeRule.getFeeRuleId());
        
        return feeRule;
    }
    
    @Override
    public Map<String, String> buildBizRuleMap(BizRule bizRule) {
        Map<String, String> bizRuleMap = new HashMap<>();
        
        // 服务相关
        if (bizRule.getServiceId() != null) {
            bizRuleMap.put("serviceId", bizRule.getServiceId().toString());
        }
        if (bizRule.getSkuNo() != null) {
            bizRuleMap.put("skuNo", bizRule.getSkuNo());
        }
        if (bizRule.getSkuType() != null) {
            bizRuleMap.put("skuType", bizRule.getSkuType());
        }
        
        // 地区相关
        if (bizRule.getDivisionType() != null) {
            bizRuleMap.put("divisionType", bizRule.getDivisionType());
        }
        if (bizRule.getProvinceId() != null) {
            bizRuleMap.put("level1DivisionId", bizRule.getProvinceId().toString());
        }
        if (bizRule.getCityId() != null) {
            bizRuleMap.put("level2DivisionId", bizRule.getCityId().toString());
        }
        if (bizRule.getDistrictId() != null) {
            bizRuleMap.put("level3DivisionId", bizRule.getDistrictId().toString());
        }
        if (bizRule.getStreetId() != null) {
            bizRuleMap.put("level4DivisionId", bizRule.getStreetId().toString());
        }
        
        // 价格相关
        if (bizRule.getMasterInputPrice() != null) {
            bizRuleMap.put("masterInputPrice", bizRule.getMasterInputPrice().toString());
        }
        
        // 模板相关
        if (bizRule.getTemplateId() != null) {
            bizRuleMap.put("templateId", bizRule.getTemplateId().toString());
        }
        
        return bizRuleMap;
    }
    
    @Override
    public void setBasicInfo(FeeRule feeRule, SceneCodeEnum sceneCode, String version) {
        Date now = new Date();
        
        feeRule.setFeeRuleId(SnowFlakeGenerator.INSTANCE.generate());
        feeRule.setSceneCode(sceneCode.getCode());
        feeRule.setSceneName(sceneCode.getName());
        feeRule.setDel(true); // 初始状态为逻辑删除，同步完成后统一改为未删除
        feeRule.setStatus(RuleStatusEnum.ACTIVE.code);
        feeRule.setCreateTime(now);
        feeRule.setModifyTime(now);
        feeRule.setCreateBy(SYSTEM_USER);
        feeRule.setUpdateBy(SYSTEM_USER);
    }
    
    @Override
    public void setBizRuleInfo(FeeRule feeRule, Map<String, String> bizRuleMap) {
        feeRule.setBizRule(bizRuleMap);
    }
    
    private void setPriceInfo(FeeRule feeRule, BizRule bizRule) {
        // 设置价格相关信息
        Double masterInputPriceDouble = Optional.ofNullable(bizRule.getMasterInputPrice())
                .map(price -> price.doubleValue())
                .orElse(0D);
        feeRule.setMasterInputPriceDouble(masterInputPriceDouble);
    }
}
