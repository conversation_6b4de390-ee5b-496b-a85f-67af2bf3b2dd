package com.wanshifu.service.bigdata;

import com.wanshifu.fee.center.domain.document.FeeRule;

import java.util.List;

/**
 * 费用规则数据持久化接口
 * 负责费用规则的数据库操作，包括批量保存、更新、删除等功能
 * 
 * <AUTHOR> Assistant
 */
public interface FeeRuleDataPersistence {
    
    /**
     * 批量保存费用规则（标记为逻辑删除状态）
     * 
     * @param feeRules 费用规则列表
     * @return 保存成功的数量
     */
    int batchSaveAsDeleted(List<FeeRule> feeRules);
    
    /**
     * 将指定版本的逻辑删除数据标记为可用
     * 
     * @param sceneCode 场景编码
     * @param version 版本号
     * @return 更新成功的数量
     */
    int markAsActive(String sceneCode, String version);
    
    /**
     * 物理删除旧版本数据
     * 
     * @param sceneCode 场景编码
     * @param currentVersion 当前版本号（保留此版本，删除其他版本）
     * @return 删除成功的数量
     */
    int deleteOldVersions(String sceneCode, String currentVersion);
    
    /**
     * 删除指定场景的所有带版本号的数据（用于初始化）
     * 
     * @param sceneCode 场景编码
     * @return 删除成功的数量
     */
    int deleteAllVersionedData(String sceneCode);
    
    /**
     * 检查数据库连接是否正常
     * 
     * @return true-连接正常；false-连接异常
     */
    boolean isConnectionHealthy();
    
    /**
     * 获取指定场景和版本的数据数量
     * 
     * @param sceneCode 场景编码
     * @param version 版本号
     * @return 数据数量
     */
    long countBySceneAndVersion(String sceneCode, String version);
}
