package com.wanshifu.service.bigdata;

import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.response.bigdata.BizRule;

import java.util.Map;

/**
 * 数据转换器接口
 * 负责将原始业务规则数据转换为费用规则对象
 * 
 * <AUTHOR> Assistant
 */
public interface DataTransformer {
    
    /**
     * 将业务规则转换为费用规则
     * 
     * @param bizRule 业务规则数据
     * @param sceneCode 场景编码
     * @param version 版本号
     * @return 转换后的费用规则
     */
    FeeRule transform(BizRule bizRule, SceneCodeEnum sceneCode, String version);
    
    /**
     * 构建业务规则映射
     * 
     * @param bizRule 业务规则数据
     * @return 业务规则映射
     */
    Map<String, String> buildBizRuleMap(BizRule bizRule);
    
    /**
     * 设置费用规则的基本信息
     * 
     * @param feeRule 费用规则
     * @param sceneCode 场景编码
     * @param version 版本号
     */
    void setBasicInfo(FeeRule feeRule, SceneCodeEnum sceneCode, String version);
    
    /**
     * 设置费用规则的业务规则信息
     * 
     * @param feeRule 费用规则
     * @param bizRuleMap 业务规则映射
     */
    void setBizRuleInfo(FeeRule feeRule, Map<String, String> bizRuleMap);
}
