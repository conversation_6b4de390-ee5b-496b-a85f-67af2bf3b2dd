package com.wanshifu.service.bigdata.impl;

import com.wanshifu.fee.center.domain.enums.DivisionTypeEnum;
import com.wanshifu.fee.center.domain.enums.FeeSkuTypeEnum;
import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.response.bigdata.BizRule;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.service.bigdata.DataValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 数据验证器实现类
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class DataValidatorImpl implements DataValidator {
    
    @Override
    public boolean validateBasic(BizRule bizRule) {
        if (bizRule == null) {
            log.warn("业务规则为空");
            return false;
        }
        
        // 验证必要字段
        if (bizRule.getServiceId() == null) {
            log.warn("服务ID为空");
            return false;
        }
        
        if (StringUtils.isBlank(bizRule.getSkuNo())) {
            log.warn("SKU编号为空");
            return false;
        }
        
        return true;
    }
    
    @Override
    public boolean validatePrice(BizRule bizRule) {
        BigDecimal masterInputPrice = bizRule.getMasterInputPrice();
        if (masterInputPrice == null || masterInputPrice.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("masterInputPrice为空或小于等于0，serviceId={}, skuNo={}", 
                    bizRule.getServiceId(), bizRule.getSkuNo());
            return false;
        }
        
        return true;
    }
    
    @Override
    public boolean validateDivision(BizRule bizRule) {
        String divisionType = bizRule.getDivisionType();
        
        // 对于某些场景，地区信息是必须的
        if (StringUtils.isBlank(divisionType)) {
            log.warn("地区类型为空，serviceId={}, skuNo={}", 
                    bizRule.getServiceId(), bizRule.getSkuNo());
            return false;
        }
        
        // 验证地区类型是否有效
        DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(divisionType.trim());
        if (divisionTypeEnum == null) {
            log.warn("地区类型无效，divisionType={}, serviceId={}, skuNo={}", 
                    divisionType, bizRule.getServiceId(), bizRule.getSkuNo());
            return false;
        }
        
        // 对于街道级别，需要验证是否为街道类型
        if (!DivisionTypeEnum.STREET.code.equals(divisionType.trim())) {
            log.warn("地区类型不是街道级别，divisionType={}, serviceId={}, skuNo={}", 
                    divisionType, bizRule.getServiceId(), bizRule.getSkuNo());
            return false;
        }
        
        return true;
    }
    
    @Override
    public boolean validateService(BizRule bizRule) {
        if (bizRule.getServiceId() == null || bizRule.getServiceId() <= 0) {
            log.warn("服务ID无效，serviceId={}", bizRule.getServiceId());
            return false;
        }
        
        if (StringUtils.isBlank(bizRule.getSkuNo())) {
            log.warn("SKU编号为空，serviceId={}", bizRule.getServiceId());
            return false;
        }
        
        return true;
    }
    
    @Override
    public boolean validateSkuType(BizRule bizRule, SceneCodeEnum sceneCode) {
        // 对于某些场景，需要验证SKU类型
        if (sceneCode == SceneCodeEnum.BARGAIN_PRICE_EVERYDAY) {
            String skuType = bizRule.getSkuType();
            if (StringUtils.isBlank(skuType)) {
                log.warn("SKU类型为空，serviceId={}, skuNo={}", 
                        bizRule.getServiceId(), bizRule.getSkuNo());
                return false;
            }
            
            FeeSkuTypeEnum feeSkuTypeEnum = FeeSkuTypeEnum.fromCode(skuType);
            if (feeSkuTypeEnum == null) {
                log.warn("SKU类型无效，skuType={}, serviceId={}, skuNo={}", 
                        skuType, bizRule.getServiceId(), bizRule.getSkuNo());
                return false;
            }
        }
        
        return true;
    }
}
