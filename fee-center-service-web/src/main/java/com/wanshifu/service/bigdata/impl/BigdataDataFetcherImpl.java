package com.wanshifu.service.bigdata.impl;

import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.request.common.RangeReq;
import com.wanshifu.fee.center.domain.response.bigdata.BizRule;
import com.wanshifu.fee.center.domain.response.bigdata.FeeRuleFromBigDataResp;
import com.wanshifu.fee.center.domain.response.bigdata.IdRange;
import com.wanshifu.fee.center.domain.response.bigdata.InsertTimestamp;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.infrastructure.gateway.BigdataGateway;
import com.wanshifu.service.bigdata.BigdataDataFetcher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 大数据数据获取实现类
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class BigdataDataFetcherImpl implements BigdataDataFetcher {
    
    @Resource
    private BigdataGateway bigdataGateway;
    
    @Override
    public List<BizRule> fetchBizRules(RangeReq range, SceneCodeEnum sceneCode) {
        try {
            log.debug("开始获取大数据，sceneCode={}, range={}-{}", 
                    sceneCode.getCode(), range.getBeginId(), range.getEndId());
            
            FeeRuleFromBigDataResp response = bigdataGateway.getSkuFeeRule(range, sceneCode);
            
            if (response == null) {
                log.error("获取大数据响应为空，sceneCode={}, range={}-{}", 
                        sceneCode.getCode(), range.getBeginId(), range.getEndId());
                return Collections.emptyList();
            }
            
            List<BizRule> data = response.getData();
            if (CollectionUtils.isEmpty(data)) {
                log.info("获取大数据为空，sceneCode={}, range={}-{}", 
                        sceneCode.getCode(), range.getBeginId(), range.getEndId());
                return Collections.emptyList();
            }
            
            log.info("成功获取大数据，sceneCode={}, range={}-{}, count={}", 
                    sceneCode.getCode(), range.getBeginId(), range.getEndId(), data.size());
            
            return data;
            
        } catch (Exception e) {
            log.error("获取大数据异常，sceneCode={}, range={}-{}", 
                    sceneCode.getCode(), range.getBeginId(), range.getEndId(), e);
            return Collections.emptyList();
        }
    }
    
    @Override
    public IdRange getIdRange(String version) {
        try {
            log.debug("开始获取ID范围，version={}", version);
            
            IdRange idRange = bigdataGateway.getSkuIdRangeFromBigData(version);
            
            if (idRange == null) {
                log.error("获取ID范围失败，version={}", version);
                return null;
            }
            
            if (idRange.getMinId() == null || idRange.getMaxId() == null) {
                log.error("获取ID范围不完整，version={}, idRange={}", version, idRange);
                return null;
            }
            
            log.info("成功获取ID范围，version={}, range={}-{}", 
                    version, idRange.getMinId(), idRange.getMaxId());
            
            return idRange;
            
        } catch (Exception e) {
            log.error("获取ID范围异常，version={}", version, e);
            return null;
        }
    }
    
    @Override
    public boolean isConnectionHealthy() {
        try {
            // 简单的健康检查，可以调用一个轻量级的接口
            return bigdataGateway != null;
        } catch (Exception e) {
            log.error("大数据连接健康检查失败", e);
            return false;
        }
    }
    
    @Override
    public Long getTimestamp(String sceneCode) {
        try {
            log.debug("开始获取时间戳，sceneCode={}", sceneCode);

            InsertTimestamp data = bigdataGateway.getTimestamp(sceneCode);
            if (data == null || data.getInsertTimestamp() == null) {
                log.error("获取时间戳失败，sceneCode={}", sceneCode);
                return null;
            }
            
            Long timestamp = data.getInsertTimestamp();
            log.debug("成功获取时间戳，sceneCode={}, timestamp={}", sceneCode, timestamp);
            
            return timestamp;
            
        } catch (Exception e) {
            log.error("获取时间戳异常，sceneCode={}", sceneCode, e);
            return null;
        }
    }
}
