package com.wanshifu.service.bigdata.impl;

import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.document.FeeTemplate;
import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.response.bigdata.BizRule;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.service.bigdata.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 费用规则数据处理器实现类
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class FeeRuleDataProcessorImpl implements FeeRuleDataProcessor {
    
    @Resource
    private DataValidator dataValidator;
    
    @Resource
    private DataTransformer dataTransformer;
    
    @Resource
    private TemplateResolver templateResolver;
    
    @Resource
    private DuplicateChecker duplicateChecker;
    
    @Override
    public List<FeeRule> processData(List<BizRule> rawData, SceneCodeEnum sceneCode, String version) {
        if (CollectionUtils.isEmpty(rawData)) {
            log.warn("原始数据为空，sceneCode={}", sceneCode.getCode());
            return new ArrayList<>();
        }
        
        List<FeeRule> feeRuleList = new ArrayList<>();
        int processedCount = 0;
        int validCount = 0;
        int duplicateCount = 0;
        
        for (BizRule bizRule : rawData) {
            processedCount++;
            
            try {
                // 验证数据有效性
                if (!validateBizRule(bizRule, sceneCode)) {
                    continue;
                }
                validCount++;
                
                // 转换为费用规则
                FeeRule feeRule = convertToFeeRule(bizRule, sceneCode, version);
                if (feeRule == null) {
                    continue;
                }
                
                // 检查重复性
                if (isDuplicate(feeRule, sceneCode)) {
                    duplicateCount++;
                    log.debug("跳过重复数据，serviceId={}, skuNo={}", 
                            bizRule.getServiceId(), bizRule.getSkuNo());
                    continue;
                }
                
                feeRuleList.add(feeRule);
                
            } catch (Exception e) {
                log.error("处理业务规则异常，serviceId={}, skuNo={}", 
                        bizRule.getServiceId(), bizRule.getSkuNo(), e);
            }
        }
        
        log.info("数据处理完成，sceneCode={}, 原始数据={}, 有效数据={}, 重复数据={}, 最终数据={}", 
                sceneCode.getCode(), processedCount, validCount, duplicateCount, feeRuleList.size());
        
        return feeRuleList;
    }
    
    @Override
    public boolean validateBizRule(BizRule bizRule, SceneCodeEnum sceneCode) {
        // 基本验证
        if (!dataValidator.validateBasic(bizRule)) {
            return false;
        }
        
        // 价格验证
        if (!dataValidator.validatePrice(bizRule)) {
            return false;
        }
        
        // 对于某些场景需要验证地区信息
        if (sceneCode == SceneCodeEnum.BARGAIN_PRICE_EVERYDAY) {
            if (!dataValidator.validateDivision(bizRule)) {
                return false;
            }
        }
        
        // 服务信息验证
        if (!dataValidator.validateService(bizRule)) {
            return false;
        }
        
        // SKU类型验证
        if (!dataValidator.validateSkuType(bizRule, sceneCode)) {
            return false;
        }
        
        return true;
    }
    
    @Override
    public FeeRule convertToFeeRule(BizRule bizRule, SceneCodeEnum sceneCode, String version) {
        try {
            // 查找对应的模板
            FeeTemplate template = templateResolver.resolveTemplate(bizRule, sceneCode);
            if (template == null) {
                log.warn("未找到对应的模板，serviceId={}, skuNo={}", 
                        bizRule.getServiceId(), bizRule.getSkuNo());
                return null;
            }
            
            // 转换为费用规则
            FeeRule feeRule = dataTransformer.transform(bizRule, sceneCode, version);
            if (feeRule == null) {
                log.warn("转换费用规则失败，serviceId={}, skuNo={}", 
                        bizRule.getServiceId(), bizRule.getSkuNo());
                return null;
            }
            
            // 设置模板相关信息
            feeRule.setTemplateId(template.getTemplateId());
            feeRule.setSort(template.getSort());
            
            // 如果没有计算规则数据，使用模板的
            if (feeRule.getCalculateRuleData() == null) {
                feeRule.setCalculateRuleData(template.getCalculateRuleData());
            }
            
            return feeRule;
            
        } catch (Exception e) {
            log.error("转换费用规则异常，serviceId={}, skuNo={}", 
                    bizRule.getServiceId(), bizRule.getSkuNo(), e);
            return null;
        }
    }
    
    @Override
    public boolean isDuplicate(FeeRule feeRule, SceneCodeEnum sceneCode) {
        return duplicateChecker.isDuplicate(feeRule, sceneCode);
    }
}
