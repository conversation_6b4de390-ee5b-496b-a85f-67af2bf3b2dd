package com.wanshifu.service.bigdata;

import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.response.bigdata.BizRule;

import java.util.List;

/**
 * 费用规则数据处理接口
 * 负责将从大数据获取的原始数据转换为费用规则对象，包括数据验证、转换、去重等业务逻辑
 * 
 * <AUTHOR> Assistant
 */
public interface FeeRuleDataProcessor {
    
    /**
     * 处理原始业务规则数据，转换为费用规则列表
     * 
     * @param rawData 原始业务规则数据
     * @param sceneCode 场景编码
     * @param version 当前版本号
     * @return 处理后的费用规则列表
     */
    List<FeeRule> processData(List<BizRule> rawData, SceneCodeEnum sceneCode, String version);
    
    /**
     * 验证单个业务规则数据的有效性
     * 
     * @param bizRule 业务规则数据
     * @param sceneCode 场景编码
     * @return true-数据有效；false-数据无效
     */
    boolean validateBizRule(BizRule bizRule, SceneCodeEnum sceneCode);
    
    /**
     * 将业务规则转换为费用规则
     * 
     * @param bizRule 业务规则数据
     * @param sceneCode 场景编码
     * @param version 当前版本号
     * @return 转换后的费用规则，如果转换失败返回null
     */
    FeeRule convertToFeeRule(BizRule bizRule, SceneCodeEnum sceneCode, String version);
    
    /**
     * 检查费用规则是否重复
     * 
     * @param feeRule 费用规则
     * @param sceneCode 场景编码
     * @return true-存在重复；false-不重复
     */
    boolean isDuplicate(FeeRule feeRule, SceneCodeEnum sceneCode);
}
