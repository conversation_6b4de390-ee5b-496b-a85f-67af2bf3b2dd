package com.wanshifu.service.bigdata;

import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.request.common.RangeReq;

/**
 * 场景处理策略接口
 * 不同场景的特殊处理逻辑，遵循开闭原则，便于扩展新场景
 * 
 * <AUTHOR> Assistant
 */
public interface SceneProcessingStrategy {
    
    /**
     * 判断是否支持该场景
     * 
     * @param sceneCode 场景编码
     * @return true-支持；false-不支持
     */
    boolean supports(SceneCodeEnum sceneCode);
    
    /**
     * 预处理场景特定逻辑
     * 
     * @param sceneCode 场景编码
     */
    void preProcess(String sceneCode);
    
    /**
     * 获取场景特定的ID范围
     * 
     * @param sceneCode 场景编码
     * @param version 版本号
     * @return ID范围，如果不需要特殊处理返回null
     */
    RangeReq getSceneSpecificIdRange(String sceneCode, String version);
    
    /**
     * 后处理场景特定逻辑
     * 
     * @param sceneCode 场景编码
     */
    void postProcess(String sceneCode);
    
    /**
     * 获取策略优先级
     * 
     * @return 优先级，数值越小优先级越高
     */
    int getPriority();
}
