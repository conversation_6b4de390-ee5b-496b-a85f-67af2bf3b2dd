package com.wanshifu.service.bigdata.impl;

import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.FeeTemplate;
import com.wanshifu.fee.center.domain.enums.FeeSkuTypeEnum;
import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.enums.TemplateStatusEnum;
import com.wanshifu.fee.center.domain.response.bigdata.BizRule;
import com.wanshifu.infrastructure.config.ApolloConfig;
import com.wanshifu.repository.FeeTemplateRepository;
import com.wanshifu.service.bigdata.TemplateResolver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static org.springframework.data.mongodb.core.query.Criteria.where;

/**
 * 模板解析器实现类
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class TemplateResolverImpl implements TemplateResolver {
    
    @Resource
    private FeeTemplateRepository feeTemplateRepository;
    
    @Resource
    private MongoTemplate mongoTemplate;
    
    @Override
    public FeeTemplate resolveTemplate(BizRule bizRule, SceneCodeEnum sceneCode) {
        // 对于特定场景，直接根据模板ID查找
        if (sceneCode == SceneCodeEnum.USER_ORDER_OFFER_GUIDE_PRICE || 
            sceneCode == SceneCodeEnum.USER_HISTORICAL_APPOINT_PRICE) {
            
            Long templateIdFromBigData = bizRule.getTemplateId();
            if (templateIdFromBigData == null || templateIdFromBigData <= 0) {
                log.warn("templateIdFromBigData为空或无效，templateId={}", templateIdFromBigData);
                return null;
            }
            
            return findTemplateById(templateIdFromBigData);
        }
        
        // 对于其他场景，根据SKU类型查找
        String skuType = bizRule.getSkuType();
        FeeSkuTypeEnum feeSkuTypeEnum = FeeSkuTypeEnum.fromCode(skuType);
        
        if (feeSkuTypeEnum == null) {
            log.warn("SKU类型无效，skuType={}", skuType);
            return null;
        }
        
        if (FeeSkuTypeEnum.STANDARD_SKU == feeSkuTypeEnum) {
            return resolveStandardSkuTemplate(bizRule, sceneCode);
        } else if (FeeSkuTypeEnum.CUSTOM_SKU == feeSkuTypeEnum) {
            return resolveCustomSkuTemplate(bizRule, sceneCode);
        } else {
            log.warn("不支持的SKU类型，skuType={}", skuType);
            return null;
        }
    }
    
    @Override
    public FeeTemplate resolveStandardSkuTemplate(BizRule bizRule, SceneCodeEnum sceneCode) {
        try {
            Criteria criteria = where("bizRule.skuType").is(FeeSkuTypeEnum.STANDARD_SKU.code)
                    .and(FeeTemplate.Fields.sceneCode).is(sceneCode.getCode())
                    .and("bizRule.serviceId").is(bizRule.getServiceId().toString())
                    .and("bizRule.skuNo").is(bizRule.getSkuNo())
                    .and(BaseDocument.Fields.del).is(false)
                    .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);
            
            FeeTemplate template = mongoTemplate.findOne(new Query(criteria), FeeTemplate.class);
            
            if (template == null) {
                log.warn("标准SKU未找到对应的模板，serviceId={}, skuNo={}", 
                        bizRule.getServiceId(), bizRule.getSkuNo());
            } else {
                log.debug("找到标准SKU模板，serviceId={}, skuNo={}, templateId={}", 
                        bizRule.getServiceId(), bizRule.getSkuNo(), template.getTemplateId());
            }
            
            return template;
            
        } catch (Exception e) {
            log.error("查找标准SKU模板异常，serviceId={}, skuNo={}", 
                    bizRule.getServiceId(), bizRule.getSkuNo(), e);
            return null;
        }
    }
    
    @Override
    public FeeTemplate resolveCustomSkuTemplate(BizRule bizRule, SceneCodeEnum sceneCode) {
        Long templateIdFromBigData = bizRule.getTemplateId();
        if (templateIdFromBigData == null) {
            log.warn("自定义SKU的templateIdFromBigData为空，serviceId={}, skuNo={}", 
                    bizRule.getServiceId(), bizRule.getSkuNo());
            return null;
        }
        
        try {
            String sourceTemplateId = findSourceTemplateId(bizRule, sceneCode, templateIdFromBigData);
            if (sourceTemplateId == null) {
                return null;
            }
            
            Criteria templateCriteria = where("templateId").is(Long.valueOf(sourceTemplateId))
                    .and(BaseDocument.Fields.del).is(false)
                    .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);
            
            FeeTemplate template = mongoTemplate.findOne(new Query(templateCriteria), FeeTemplate.class);
            
            if (template == null) {
                log.warn("自定义SKU未找到对应的模板，serviceId={}, skuNo={}, sourceTemplateId={}", 
                        bizRule.getServiceId(), bizRule.getSkuNo(), sourceTemplateId);
            } else {
                log.debug("找到自定义SKU模板，serviceId={}, skuNo={}, templateId={}", 
                        bizRule.getServiceId(), bizRule.getSkuNo(), template.getTemplateId());
            }
            
            return template;
            
        } catch (Exception e) {
            log.error("查找自定义SKU模板异常，serviceId={}, skuNo={}", 
                    bizRule.getServiceId(), bizRule.getSkuNo(), e);
            return null;
        }
    }
    
    @Override
    public FeeTemplate findTemplateById(Long templateId) {
        try {
            FeeTemplate template = feeTemplateRepository.findByTemplateId(templateId);
            if (template == null) {
                log.warn("根据ID未找到模板，templateId={}", templateId);
            }
            return template;
        } catch (Exception e) {
            log.error("根据ID查找模板异常，templateId={}", templateId, e);
            return null;
        }
    }
    
    private String findSourceTemplateId(BizRule bizRule, SceneCodeEnum sceneCode, Long templateIdFromBigData) {
        if (ApolloConfig.NEW_TEMPLATE_MAPPING_SWITCH) {
            return findSourceTemplateIdFromNewMapping(bizRule, sceneCode, templateIdFromBigData);
        } else {
            return findSourceTemplateIdFromOldMapping(bizRule, sceneCode, templateIdFromBigData);
        }
    }
    
    private String findSourceTemplateIdFromNewMapping(BizRule bizRule, SceneCodeEnum sceneCode, Long templateIdFromBigData) {
        // 使用新的模板映射逻辑
        // 这里简化实现，实际需要根据具体的映射逻辑来实现
        log.debug("使用新模板映射查找源模板ID");
        return null; // 简化实现
    }
    
    private String findSourceTemplateIdFromOldMapping(BizRule bizRule, SceneCodeEnum sceneCode, Long templateIdFromBigData) {
        // 使用旧的模板映射逻辑
        log.debug("使用旧模板映射查找源模板ID");
        return null; // 简化实现
    }
}
