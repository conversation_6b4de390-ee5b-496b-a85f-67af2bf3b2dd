package com.wanshifu.service.bigdata;

/**
 * 数据版本管理接口
 * 负责管理大数据同步的版本信息，包括版本检查、缓存管理、版本更新等功能
 * 
 * <AUTHOR> Assistant
 */
public interface DataVersionManager {
    
    /**
     * 检查版本是否发生变化
     * 
     * @param sceneCode 场景编码
     * @return true-版本已变化，需要同步；false-版本未变化，无需同步
     */
    boolean isVersionChanged(String sceneCode);
    
    /**
     * 获取当前缓存的版本号
     * 
     * @param sceneCode 场景编码
     * @return 当前版本号，如果不存在则返回null
     */
    String getCurrentVersion(String sceneCode);
    
    /**
     * 获取大数据的最新版本号
     * 
     * @param sceneCode 场景编码
     * @return 大数据的最新版本号
     */
    String getLatestVersionFromBigdata(String sceneCode);
    
    /**
     * 更新版本号到缓存
     * 
     * @param sceneCode 场景编码
     * @param version 新版本号
     */
    void updateCurrentVersion(String sceneCode, String version);
    
    /**
     * 保存上一个版本号
     * 
     * @param sceneCode 场景编码
     * @param version 版本号
     */
    void saveLastVersion(String sceneCode, String version);
    
    /**
     * 获取上一个版本号
     * 
     * @param sceneCode 场景编码
     * @return 上一个版本号
     */
    String getLastVersion(String sceneCode);
    
    /**
     * 清理版本缓存
     * 
     * @param sceneCode 场景编码
     */
    void clearVersionCache(String sceneCode);
}
