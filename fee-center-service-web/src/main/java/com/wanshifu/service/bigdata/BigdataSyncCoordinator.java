package com.wanshifu.service.bigdata;

/**
 * 大数据同步协调器接口
 * 负责协调整个大数据同步流程，是同步过程的总指挥
 * 
 * <AUTHOR> Assistant
 */
public interface BigdataSyncCoordinator {
    
    /**
     * 执行大数据同步
     * 
     * @param sceneCode 场景编码
     */
    void syncData(String sceneCode);
    
    /**
     * 检查同步前置条件
     * 
     * @param sceneCode 场景编码
     * @return true-满足条件；false-不满足条件
     */
    boolean checkPreconditions(String sceneCode);
    
    /**
     * 获取同步状态
     * 
     * @param sceneCode 场景编码
     * @return 同步状态描述
     */
    String getSyncStatus(String sceneCode);
    
    /**
     * 停止同步任务
     * 
     * @param sceneCode 场景编码
     */
    void stopSync(String sceneCode);
}
