package com.wanshifu.service.bigdata;

import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.request.common.RangeReq;
import com.wanshifu.fee.center.domain.response.bigdata.BizRule;
import com.wanshifu.fee.center.domain.response.bigdata.IdRange;

import java.util.List;

/**
 * 大数据数据获取接口
 * 负责从大数据平台获取原始数据，包括分页获取、ID范围查询等功能
 * 
 * <AUTHOR> Assistant
 */
public interface BigdataDataFetcher {
    
    /**
     * 分页获取大数据中的业务规则数据
     * 
     * @param range 数据范围请求
     * @param sceneCode 场景编码
     * @return 业务规则数据列表
     */
    List<BizRule> fetchBizRules(RangeReq range, SceneCodeEnum sceneCode);
    
    /**
     * 获取指定版本的ID范围
     * 
     * @param version 版本号
     * @return ID范围信息
     */
    IdRange getIdRange(String version);
    
    /**
     * 检查大数据连接是否正常
     * 
     * @return true-连接正常；false-连接异常
     */
    boolean isConnectionHealthy();
    
    /**
     * 获取大数据的时间戳信息
     * 
     * @param sceneCode 场景编码
     * @return 时间戳，如果获取失败返回null
     */
    Long getTimestamp(String sceneCode);
}
