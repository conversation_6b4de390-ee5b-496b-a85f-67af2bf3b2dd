package com.wanshifu.service.bigdata.strategy;

import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.request.common.RangeReq;
import com.wanshifu.service.bigdata.SceneProcessingStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 默认场景处理策略
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class DefaultSceneProcessingStrategy implements SceneProcessingStrategy {
    
    @Override
    public boolean supports(SceneCodeEnum sceneCode) {
        // 默认策略支持所有场景
        return true;
    }
    
    @Override
    public void preProcess(String sceneCode) {
        log.debug("默认场景预处理，sceneCode={}", sceneCode);
        // 默认不做特殊处理
    }
    
    @Override
    public RangeReq getSceneSpecificIdRange(String sceneCode, String version) {
        log.debug("默认场景获取ID范围，sceneCode={}, version={}", sceneCode, version);
        // 默认返回null，使用通用的ID范围
        return null;
    }
    
    @Override
    public void postProcess(String sceneCode) {
        log.debug("默认场景后处理，sceneCode={}", sceneCode);
        // 默认不做特殊处理
    }
    
    @Override
    public int getPriority() {
        // 默认策略优先级最低
        return Integer.MAX_VALUE;
    }
}
