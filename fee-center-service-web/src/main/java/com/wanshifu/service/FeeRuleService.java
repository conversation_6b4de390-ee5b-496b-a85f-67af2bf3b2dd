package com.wanshifu.service;

import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.document.FeeRuleDraft;
import com.wanshifu.fee.center.domain.request.*;
import com.wanshifu.fee.center.domain.request.feeRule.*;
import com.wanshifu.fee.center.domain.request.feeRule.master.*;
import com.wanshifu.fee.center.domain.response.*;
import com.wanshifu.fee.center.domain.response.feerule.BatchAddRulesResponse;
import com.wanshifu.framework.core.page.SimplePageInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface FeeRuleService {
    List<FeeRule> queryByCondition(FeeRuleQueryReq feeRuleQueryReq);

    List<FeeRule> queryMasterByCondition(FeeRuleQueryReq feeRuleQueryReq);

    List<FeeRule> batchQueryByCondition(FeeRuleBatchQueryReq feeRuleBatchQueryReq);

    List<FeeRule> queryGuidePrice(FeeRuleGuidePriceReq feeRuleGuidePriceReq);

    FeeRule save(FeeRule feeRule);

    List<FeeRule>  batchSave(List<FeeRule> feeRules);

    List<FeeRule>  batchSaveNew(List<FeeRule> feeRules);

    List<BatchAddRulesResponse>  batchSaveRules(List<FeeRule> feeRules);

    List<FeeRule> batchDel(List<Long> feeRuleIds);

    void batchDelRules(List<Long> feeRuleIds);

    FeeRule update(FeeRule feeRule);

    ApplyCalculateResp calculate(ApplyCalculateReq applyCalculateReq);

    ApplyOrderCalculateResp calculateOrder(ApplyOrderCalculateReq applyOrderCalculateReq);

    void batchDelete(FeeRuleBatchOperationReq req);

    SimplePageInfo<FeeRulePageResp> getFeeRulePage(FeeRulePageReq req);

    SimplePageInfo<FeeRuleServicePageResp> getFeeRuleServicePage(FeeRuleServicePageReq req, String collectionName);

    void completeDivision();

    void completeGroup();

    Set<Long> getServiceIdsByDivision(ServiceIdByDivisionIdsReq req);

    void completeServiceCategoryId(String sceneCode, String updateBy);

    List<ApplyOrderCalculateBatchResp> calculateOrderBatch(ApplyOrderCalculateBatchReq applyOrderCalculateBatchReq);

    void batchDeleteLogically(String sceneCode, String masterId, String bizTag);


    void correctTemplateId(String sceneCode, Set<String> bizTagSet);

    void correctStatus();

    List<QueryByBizIdResp> getByBizIdGroupByCondition(QueryByBizIdReq req);

    int deleteByCondition(DeleteByBizIdReq req);

    Map<String, AttributeValuePriceResp> getAttributeValuePriceByServiceId(Long serviceId, Long userId);

    SimplePageInfo<BargainPriceEverydayFeeRuleResp> getBargainPriceEverydayFeeRuleByServiceId(BargainPriceEverydayFeeRuleReq req);

    DistrictDetailResp getDistrictDetail(DistrictDetailReq req);

    DistrictDetailResp getPreGenerateDistrictDetail(DistrictDetailReq req);

    void preGenerateBargainPriceEverydayFeeRule(String sceneCode);

    List<AttributeValueResp> getAttributeValueByServiceId(String sceneCode, Long serviceId);

    void pullFeeRuleFromBigdata(String sceneCode);

    void pullCustomTemplateFromBigdata();

    List<PriceAndFeeNameResp> getPriceAndFeeName(PriceAndFeeNameReq req);

    void setGoodsCategoryAndServiceType(Map<String, String> feeBizRuleMap, String serviceId);

    void preGenerateLowestPrice();

    SimplePageInfo<LowestPriceResp> getLowestPriceByLevel1GoodsCategoryIdOrServiceIds(LowestPriceReq req);


    void handleDynamicPriceRule(FeeRule feeRule);

    void handleDynamicPriceRule(FeeRuleDraft feeRuleDraft);

    boolean applyOrderDetector(ApplyOrderCalculateReq applyOrderCalculateReq);

    FeeRuleBaseInfoForMasterResp queryBaseInfoForMaster(FeeRuleBaseInfoForMasterReq feeRuleBaseInfoForMasterReq);

    void createRecruitActivity(CreateRecruitActivityReq req);

    void deleteForMaster(RemoveForMasterReq req);

    void createRecruitMaster(CreateRecruitMasterReq req);

    void createRecruitMasterAsync(CreateRecruitMasterReq req);

    void createRecruitActivityMaster(CreateRecruitActivityMasterReq req);

    void createRecruitActivityMasterAsync(CreateRecruitActivityMasterReq req);

    List<FeeRule>  saveFeeRuleListNew(List<FeeRuleBatchAddReq.FeeRuleData> feeRuleDataList);

    List<BatchAddRulesResponse>  saveFeeRuleList(List<BatchAddRulesRequest.FeeRuleData> feeRuleDataList);

    void modifyRecruitMaster(ModifyRecruitMasterReq req);

    Page<FeeRule> queryPage(FeeRuleQueryPageReq req);

    List<GetRecruitActivityMasterCreateStatusResp> getRecruitActivityMasterCreateStatus(GetRecruitActivityMasterCreateStatusReq req);

    void modifyRecruitActivityMaster(ModifyRecruitActivityMasterReq req);

    void modifyRecruitActivityMasterAsync(ModifyRecruitActivityMasterReq req);

    void preHandleManualMaintenanceDataToCache(String sceneCode);

    void deleteRuleByMasterIdAsync(DeleteRuleByMasterIdRequest request);

    QueryByBizIdResp getQueryByBizIdResp(String sceneCode, Map<String, String> bizRule);

    Query getQueryByBizIdReq(QueryByBizIdReq req);

    Query getDeleteQueryByBizId(DeleteByBizIdReq req);

    void batchDeleteLogicallyDeletedFeeRules(String sceneCode);

    void createRecruitActivityAsync(CreateRecruitActivityReq req);

    void deleteRecruitMasterBatchAsync(List<DeleteRecruitMasterReq> reqList);
}
