package com.wanshifu.service;

import com.wanshifu.fee.center.domain.document.BizRuleMapping;
import com.wanshifu.fee.center.domain.request.BizRuleMappingApplyTypesReq;
import com.wanshifu.fee.center.domain.request.BizRuleMappingBatchQueryReq;
import com.wanshifu.fee.center.domain.request.BizRuleMappingQueryReq;
import com.wanshifu.fee.center.domain.request.mapping.BizRuleMappingBatchAddReq;
import com.wanshifu.fee.center.domain.request.mapping.CopyBizRuleMappingReq;
import com.wanshifu.fee.center.domain.request.mapping.QueryPageReq;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Set;

public interface BizRuleMappingService {
    Page<BizRuleMapping> queryByCondition(BizRuleMappingQueryReq bizRuleMappingQueryReq);

    Page<BizRuleMapping> batchQueryByCondition(BizRuleMappingBatchQueryReq bizRuleMappingBatchQueryReq);

    BizRuleMapping save(BizRuleMapping bizRuleMapping);

    void batchAdd(List<BizRuleMappingBatchAddReq> reqList);

    BizRuleMapping delete(Long skuMappingId);

    BizRuleMapping update(BizRuleMapping bizRuleMapping);

    Set<String> getServiceIdBySceneCode(String fromSceneCode, String toSceneCode);

    void setApplyTypes(BizRuleMappingApplyTypesReq req);

    Page<BizRuleMapping> queryPage(QueryPageReq req);

    BizRuleMapping queryByMappingId(Long mappingId);

    void copyBizRuleMapping(CopyBizRuleMappingReq req);

    BizRuleMapping findOneByTemplateId(Long templateId);

    void flushBizRuleMappingToTemplateMapping();

}
