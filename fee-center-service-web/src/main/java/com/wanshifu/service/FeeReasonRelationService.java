package com.wanshifu.service;


import com.wanshifu.fee.center.domain.document.FeeReasonRelation;
import com.wanshifu.fee.center.domain.request.FeeReasonRelationMaintainReq;
import com.wanshifu.fee.center.domain.request.FeeReasonRelationQueryReq;

import java.util.List;

public interface FeeReasonRelationService {
    List<FeeReasonRelation> query(FeeReasonRelationQueryReq feeReasonRelationQueryReq);
    List<FeeReasonRelation> maintain( FeeReasonRelationMaintainReq feeReasonRelationMaintainReq);
}
