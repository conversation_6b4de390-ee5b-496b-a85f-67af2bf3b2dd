package com.wanshifu.service;

import com.wanshifu.fee.center.domain.enums.AsyncTaskStatusEnum;
import com.wanshifu.fee.center.domain.enums.AsyncTaskTypeEnum;

public interface AsyncTasksInfoService {

    AsyncTaskStatusEnum getStatusByTaskId(String taskId, AsyncTaskTypeEnum taskType);

    void updateStatus(String taskId, AsyncTaskTypeEnum taskType, AsyncTaskStatusEnum status, String failReason);

    void createTask(String taskId, AsyncTaskTypeEnum taskType);
}
