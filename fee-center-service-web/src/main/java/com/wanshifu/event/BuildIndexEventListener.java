package com.wanshifu.event;

import com.wanshifu.repository.MongodbIndexCreator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@Component
public class BuildIndexEventListener {
    @Resource
    private MongodbIndexCreator mongodbIndexCreator;

    @Async
    @EventListener(BuildIndexEvent.class)
    public void handleBuildIndex(BuildIndexEvent event){
        if(!CollectionUtils.isEmpty(event.getObjectList())){
            mongodbIndexCreator.submitCreateTask(event.getObjectList());
        }else if (Objects.nonNull(event.getObject())){
            mongodbIndexCreator.submitCreateTask(event.getObject());
        }

    }
}
