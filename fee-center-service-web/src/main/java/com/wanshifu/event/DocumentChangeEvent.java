package com.wanshifu.event;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

@Setter
@Getter
public class DocumentChangeEvent extends ApplicationEvent {

    private String operation;

    private Object document;

    public DocumentChangeEvent(Object source, String operation, Object document) {
        super(source);
        this.operation = operation;
        this.document = document;
    }
}
