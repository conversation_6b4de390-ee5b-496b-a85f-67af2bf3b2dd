package com.wanshifu.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

@Getter
public class BuildIndexEvent extends ApplicationEvent {
    private Object object;
    private List<?> objectList;

    public BuildIndexEvent(List<?> source) {
        super(source);
        this.objectList = source;
    }

    public BuildIndexEvent(Object source) {
        super(source);
        this.object = source;
    }
}
