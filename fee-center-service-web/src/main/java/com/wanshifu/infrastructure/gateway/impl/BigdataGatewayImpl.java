package com.wanshifu.infrastructure.gateway.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.request.SkuIdRangeFromBigDataReq;
import com.wanshifu.fee.center.domain.request.common.RangeReq;
import com.wanshifu.fee.center.domain.response.bigdata.*;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.infrastructure.gateway.BigdataGateway;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Component
@Slf4j
public class BigdataGatewayImpl implements BigdataGateway {

    @Value("${wanshifu.bigdata-open-service.url}")
    private String bigDataOpenServiceUrl;
    @Value("${http.timeout.connect:3000}")
    private int timeout;

    // ================= API Path Constants =================
    private static final String API_GET_DATA = "/dataApi/getData/";
    private static final String API_GET_USER_DYNAMIC_PRICE = "/indexData/getUserDynamicPrice";

    private static final String API_GET_SERVICE_SKU_TEMP_STRE_DATA_INSERT_STATUS = "getServiceSkuTempStreDataInsertStatus";
    private static final String API_GET_TABLE_DATA_UPDATE_STATUS = "getTableDataUpdateStatus";
    private static final String API_GET_SERVICE_SKU_TEMPLATE_STREET_MAX_PRICE = "getServiceSkuTemplateStreetMaxPrice";
    private static final String API_GET_USER_EXPECT_PRICE = "getUsrExpectPrice";
    private static final String API_GET_USER_HISTORICAL_APPOINTED_PRICE = "getUsrHistoricalAppointedPrice";
    private static final String API_GET_MIN_MAX_ID_BY_VERSION = "getMinMaxIdByVersion";
    private static final String API_GET_ORDER_AVG_PUSH_DISTANCE = "getOrderAvgPushDistance";
    private static final String API_GET_REMOTE_DISTRICT_BY_STREET_ID = "getRemoteDistrictByStreetId";


    // ================= Request Body Constants =================
    private static final String EMPTY_JSON = "{}";
    private static final String ADS_DS_USR_EXPECT_PRICE_STAT_BODY = "{\"tableName\":\"ads_ds_usr_expect_price_stat\"}";
    private static final String ADS_DS_USR_HISTORICAL_APPOINTED_PRICE_BODY = "{\"tableName\":\"ads_ds_usr_historical_appointed_price\"}";


    private Map<SceneCodeEnum, String> timestampUrlMap;
    private Map<SceneCodeEnum, String> timestampBodyMap;
    private Map<SceneCodeEnum, String> skuFeeRuleUrlMap;

    @PostConstruct
    public void init() {
        String baseUrl = bigDataOpenServiceUrl + API_GET_DATA;

        timestampUrlMap = ImmutableMap.of(
                SceneCodeEnum.BARGAIN_PRICE_EVERYDAY, baseUrl + API_GET_SERVICE_SKU_TEMP_STRE_DATA_INSERT_STATUS,
                SceneCodeEnum.USER_ORDER_OFFER_GUIDE_PRICE, baseUrl + API_GET_TABLE_DATA_UPDATE_STATUS,
                SceneCodeEnum.USER_HISTORICAL_APPOINT_PRICE, baseUrl + API_GET_TABLE_DATA_UPDATE_STATUS
        );

        timestampBodyMap = ImmutableMap.of(
                SceneCodeEnum.BARGAIN_PRICE_EVERYDAY, EMPTY_JSON,
                SceneCodeEnum.USER_ORDER_OFFER_GUIDE_PRICE, ADS_DS_USR_EXPECT_PRICE_STAT_BODY,
                SceneCodeEnum.USER_HISTORICAL_APPOINT_PRICE, ADS_DS_USR_HISTORICAL_APPOINTED_PRICE_BODY
        );

        skuFeeRuleUrlMap = ImmutableMap.of(
                SceneCodeEnum.BARGAIN_PRICE_EVERYDAY, baseUrl + API_GET_SERVICE_SKU_TEMPLATE_STREET_MAX_PRICE,
                SceneCodeEnum.USER_ORDER_OFFER_GUIDE_PRICE, baseUrl + API_GET_USER_EXPECT_PRICE,
                SceneCodeEnum.USER_HISTORICAL_APPOINT_PRICE, baseUrl + API_GET_USER_HISTORICAL_APPOINTED_PRICE
        );
    }

    @Override
    public InsertTimestamp getTimestamp(String sceneCode) {
        SceneCodeEnum sceneCodeEnum = Optional.ofNullable(SceneCodeEnum.fromCode(sceneCode))
                .orElseThrow(() -> new BusException("无效的场景编码: " + sceneCode));

        String url = timestampUrlMap.get(sceneCodeEnum);
        String body = timestampBodyMap.get(sceneCodeEnum);

        if (url == null) {
            throw new BusException("该场景编码不支持获取时间戳: " + sceneCode);
        }

        return doPost(url, body, new TypeReference<BigDataResp<InsertTimestamp>>() {});
    }

    @Override
    public FeeRuleFromBigDataResp getSkuFeeRule(RangeReq req, SceneCodeEnum sceneCodeEnum) {
        if (sceneCodeEnum == null) {
            throw new BusException("场景类型不能为空");
        }
        String url = skuFeeRuleUrlMap.get(sceneCodeEnum);
        if (url == null) {
            throw new BusException(StrUtil.format("该场景不支持获取计费规则: {}-{}}", sceneCodeEnum.getCode(), sceneCodeEnum.getName()));
        }
        return doPost(url, req, FeeRuleFromBigDataResp.class);
    }

    @Override
    public IdRange getSkuIdRangeFromBigData(String version) {
        final String url = bigDataOpenServiceUrl + API_GET_DATA + API_GET_MIN_MAX_ID_BY_VERSION;
        SkuIdRangeFromBigDataReq req = new SkuIdRangeFromBigDataReq(version);
        return doPost(url, req, new TypeReference<BigDataResp<IdRange>>() {});
    }

    @Override
    public OrderAvgPushDistance getOrderAvgPushDistance(String orderNo) {
        final String url = bigDataOpenServiceUrl + API_GET_DATA + API_GET_ORDER_AVG_PUSH_DISTANCE;
        Map<String, Object> paramMap = Collections.singletonMap("orderNo", orderNo);
        return doGet(url, paramMap, new TypeReference<BigDataResp<OrderAvgPushDistance>>() {});
    }

    @Override
    public GetIndicatorValueResponse getIndicatorValue(GetIndicatorValueRequest request) {
        final String url = bigDataOpenServiceUrl + API_GET_USER_DYNAMIC_PRICE;
        return doPost(url, request, new TypeReference<BigDataResp<GetIndicatorValueResponse>>() {});
    }


    @Override
    public List<RemoteArea> getRemoteAreas(String streetIds) {
        final String url = bigDataOpenServiceUrl + API_GET_DATA + API_GET_REMOTE_DISTRICT_BY_STREET_ID;
        Map<String, Object> paramMap = Collections.singletonMap("streetIdList", streetIds);
        return doGet(url, paramMap, new TypeReference<BigDataResp<List<RemoteArea>>>() {});
    }

    /**
     * 执行POST请求的私有辅助方法，包含了完整的异常处理和响应校验
     */
    private <T> T doPost(String url, Object requestBody, TypeReference<BigDataResp<T>> typeReference) {
        String requestBodyString = (requestBody instanceof String) ? (String) requestBody : JSON.toJSONString(requestBody);
        try {
            String result = HttpUtil.post(url, requestBodyString, timeout);
            return parseResult(result, url, requestBodyString, typeReference);
        } catch (HttpException e) {
            log.error("调用大数据接口HTTP请求失败, URL: {}, 请求: {}", url, requestBodyString, e);
            throw new BusException("调用大数据接口失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("调用大数据接口失败, URL: {}, 请求: {}", url, requestBodyString, e);
            throw new BusException("调用大数据接口失败，请检查日志");
        }
    }
    
    /**
     * 执行POST请求的私有辅助方法（用于非泛型场景）
     */
    private <T> T doPost(String url, Object requestBody, Class<T> responseClass) {
        String requestBodyString = (requestBody instanceof String) ? (String) requestBody : JSON.toJSONString(requestBody);
        try {
            String result = HttpUtil.post(url, requestBodyString, timeout);
            // 对于非泛型的场景，我们假设它直接返回目标对象，或者也遵循BigDataResp格式
            // 这里我们先简单实现，如果需要也可以统一为BigDataResp
            if (StrUtil.isBlank(result)) {
                log.error("调用大数据接口失败，URL: {}, 请求: {}, 响应为空", url, requestBodyString);
                throw new BusException("调用大数据接口失败，响应为空");
            }
            return JSON.parseObject(result, responseClass);
        } catch (HttpException e) {
            log.error("调用大数据接口HTTP请求失败, URL: {}, 请求: {}", url, requestBodyString, e);
            throw new BusException("调用大数据接口失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("调用大数据接口失败, URL: {}, 请求: {}", url, requestBodyString, e);
            throw new BusException("调用大数据接口失败，请检查日志");
        }
    }

    /**
     * 执行GET请求的私有辅助方法
     */
    private <T> T doGet(String url, Map<String, Object> params, TypeReference<BigDataResp<T>> typeReference) {
        try {
            String result = HttpUtil.get(url, params, timeout);
            return parseResult(result, url, JSON.toJSONString(params), typeReference);
        } catch (HttpException e) {
            log.error("调用大数据接口HTTP GET请求失败, URL: {}, 参数: {}", url, params, e);
            throw new BusException("调用大数据接口失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("调用大数据接口GET请求失败, URL: {}, 参数: {}", url, params, e);
            throw new BusException("调用大数据接口失败，请检查日志");
        }
    }

    /**
     * 解析和校验响应结果
     */
    private <T> T parseResult(String result, String url, String request, TypeReference<BigDataResp<T>> typeReference) {
        if (StrUtil.isBlank(result)) {
            log.error("调用大数据接口响应为空, URL: {}, 请求: {}", url, request);
            throw new BusException("调用大数据接口失败，响应为空");
        }

        BigDataResp<T> response = JSON.parseObject(result, typeReference);

        if (response == null) {
            log.error("调用大数据接口响应解析为null, URL: {}, 请求: {}, 响应: {}", url, request, result);
            throw new BusException("调用大数据接口失败，响应解析失败");
        }

        // 检查大数据接口返回的业务状态码，0 通常表示成功
        if (response.isError()) {
            log.error("调用大数据接口业务失败, URL: {}, 请求: {}, 响应: {}", url, request, result);
            throw new BusException(StrUtil.format("调用大数据接口业务失败: {}", response.getErrMessage()));
        }

        return response.getData();
    }
}
