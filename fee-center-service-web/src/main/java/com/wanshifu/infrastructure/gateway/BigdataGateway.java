package com.wanshifu.infrastructure.gateway;

import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.request.common.RangeReq;
import com.wanshifu.fee.center.domain.response.bigdata.*;

import java.util.List;

public interface BigdataGateway {

    /**
     * 根据场景编码获取 timestamp
     * @param sceneCode 场景编码
     * @return timestamp
     */
    InsertTimestamp getTimestamp(String sceneCode);

    /**
     * 获取sku价格
     * @param req 请求参数
     * @param sceneCodeEnum 场景编码枚举
     * @return sku价格
     */
    FeeRuleFromBigDataResp getSkuFeeRule(RangeReq req, SceneCodeEnum sceneCodeEnum);

    /**
     * 获取skuId范围
     * @param version 版本
     * @return id范围
     */
    IdRange getSkuIdRangeFromBigData(String version);

    /**
     * 获取订单平均推单距离
     *
     * @param orderNo 订单号
     * @return 平均推单距离
     */
    OrderAvgPushDistance getOrderAvgPushDistance(String orderNo);

    /**
     * 获取指标值
     *
     * @param request 请求参数
     * @return 指标值
     */
    GetIndicatorValueResponse getIndicatorValue(GetIndicatorValueRequest request);

    /**
     * 获取偏远地区信息
     *
     * @param streetIds 街道ID，多个用英文逗号分割
     * @return 地区信息
     */
    List<RemoteArea> getRemoteAreas(String streetIds);
}
