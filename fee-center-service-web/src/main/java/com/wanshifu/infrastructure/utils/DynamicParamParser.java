package com.wanshifu.infrastructure.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.wanshifu.fee.center.domain.request.calculate.DynamicIndicatorParam;
import com.wanshifu.framework.utils.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

public class DynamicParamParser {

    private final Map<String, String> paramMap;

    public DynamicParamParser(List<DynamicIndicatorParam> params) {
        if (CollectionUtils.isEmpty(params)) {
            this.paramMap = new HashMap<>();
            return;
        }
        this.paramMap = params.stream()
                .collect(Collectors.toMap(
                        DynamicIndicatorParam::getKey,
                        DynamicIndicatorParam::getValue
                ));
    }

    public String getValue(String key) {
        return paramMap.get(key);
    }

    public Integer getIntValue(String key) {
        return Optional.ofNullable(paramMap.get(key))
                .map(Integer::parseInt)
                .orElse(null);
    }

    public Long getLongValue(String key) {
        return Optional.ofNullable(paramMap.get(key))
                .map(Long::parseLong)
                .orElse(null);
    }

    /**
     * 获取动态参数值，并转为LocalDateTime
     * 格式要求：yyyy-MM-dd HH:mm:ss
     *
     * @param key 参数键
     * @return 本地日期时间值
     */
    public LocalDateTime getLocalDateTimeValue(String key) {
        return Optional.ofNullable(paramMap.get(key))
                .map(value -> {
                    Date date = DateUtil.parse(value, DatePattern.NORM_DATETIME_PATTERN);
                    return DateUtil.toLocalDateTime(date);
                })
                .orElse(null);
    }
}
