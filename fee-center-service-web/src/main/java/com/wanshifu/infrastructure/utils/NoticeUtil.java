package com.wanshifu.infrastructure.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.framework.utils.HttpBuilder;
import com.wanshifu.infrastructure.config.ApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.slf4j.MDC;
import org.springframework.cloud.sleuth.Span;

import java.util.Date;
import java.util.HashMap;

@Slf4j
public class NoticeUtil {

    static final String newPricingWarningUrl = ApolloConfig.NEW_PRICING_WARNING_URL;

    public static void setNotice(String sceneCode,
                                 String serviceId,
                                 String serviceName,
                                 String exceptionMsg) {
        String warnMessage =
                        "\n    sceneCode：{} \n" +
                        "    serviceId：{} \n" +
                        "    serviceName：{} \n" +
                        "    详情：{}\n";
        sendTempMsg(newPricingWarningUrl,
                "调用计价接口异常",
                StrUtil.format(warnMessage,
                        sceneCode,
                        serviceId,
                        serviceName,
                        exceptionMsg)
                , exceptionMsg);
    }


    private static void sendTempMsg(String webhookUrl,
                                    String bussCode,
                                    String jsonParam,
                                    String exception) {
        String res = "";
        String requestData = "";
        if (StringUtils.isEmpty(webhookUrl)) {
            webhookUrl  = newPricingWarningUrl;
        }

        try {
            String errorMessage = new StringBuilder()
                    .append("【告警时间】 ").append(DateUtils.formatDateTime(new Date())).append("\n")
                    .append("【业务场景】 ").append( StringUtils.isNotEmpty(bussCode) ? bussCode : "未知" ).append("\n")
                    .append("【来源服务】 ").append(EnvUtil.getAppName()).append("\n")
                    .append("【参数信息】 ").append(jsonParam).append("\n")
                    .append("【异常原因】 ").append(exception).append("\n")
                    .append("【环境信息】 ").append(EnvUtil.getEnvName()).append("\n")
                    .append("【traceId】").append(MDC.get(Span.TRACE_ID_NAME)).append("\n")
                    .append("<at user_id=\"all\">所有人</at> ").toString();

            HashMap<String,Object> content = new HashMap();
            content.put("text", errorMessage);
            HashMap<String,Object> data = new HashMap();
            data.put("msg_type", "text");
            data.put("content", content);

            requestData = JSON.toJSONString(data);
            res = HttpBuilder.genericPost(webhookUrl)
                    .connectTimeOut(500)
                    .socketTimeout(500)
                    .entity(new StringEntity(requestData, ContentType.APPLICATION_JSON))
                    .build().executeToString();
            JSONObject jsonObject = JSON.parseObject(res);

            String statusCode = jsonObject.getString("StatusCode");
            if(StringUtils.isBlank(statusCode) || !statusCode.equals("0")){
                log.error("飞书预警请求参数: {} , 响应内容: {}, webhookUrl:{}", requestData, res, webhookUrl);
            }
        } catch (Exception e) {
            log.error("飞书预警失败: req: {}, res: {} , webhookUrl:{} ", requestData, res, webhookUrl , e);
        }
    }

}
