package com.wanshifu.infrastructure.utils;

import java.util.regex.Pattern;

public class PatternUtils {
    private final static String NEED_ESCAPE = "[*.?+$^][)(}{|\\/]";

    /**
     * 获取转义后的字符串
     * @return
     */
    public static String  toEscapeStr(String regex){
        return Pattern.quote(regex);
    }

    public static String toFuzzySearch(String content) {
        return ".*" + content + ".*";
    }
}
