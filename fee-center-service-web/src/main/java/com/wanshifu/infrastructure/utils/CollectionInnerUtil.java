package com.wanshifu.infrastructure.utils;

import com.wanshifu.framework.utils.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class CollectionInnerUtil {

    /**
     * 取交集
     * @param list1 列表1
     * @param list2 列表2
     * @return List<T> intersections
     * @param <T> 泛型
     */
    public static <T> Set<T> getIntersections(List<T> list1, List<T> list2) {
        if (CollectionUtils.isEmpty(list1) || CollectionUtils.isEmpty(list2)) {
            return null;
        }
        Set<T> set2 = new HashSet<>(list2); // 将list2转换为set以提高查找效率
        // 使用Stream API过滤出交集元素，并收集到一个新的Set中
        return list1.stream()
                .filter(set2::contains)
                .collect(Collectors.toSet());
    }
}
