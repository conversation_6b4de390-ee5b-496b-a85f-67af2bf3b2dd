package com.wanshifu.infrastructure.storage.impl;

import com.wanshifu.domain.storage.FeeTemplateStorage;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.FeeTemplate;
import com.wanshifu.fee.center.domain.enums.TemplateStatusEnum;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import static org.springframework.data.mongodb.core.query.Criteria.where;

@Repository
public class FeeTemplateStorageImpl implements FeeTemplateStorage {

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public FeeTemplate findOneBySceneCodeAndServiceId(String sceneCode, String serviceId) {
        Criteria criteria = where(FeeTemplate.Fields.sceneCode).is(sceneCode)
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                .and(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);
        return mongoTemplate.findOne(new Query(criteria), FeeTemplate.class);
    }
}
