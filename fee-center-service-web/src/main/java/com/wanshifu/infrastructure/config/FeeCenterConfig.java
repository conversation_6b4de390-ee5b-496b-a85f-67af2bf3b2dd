package com.wanshifu.infrastructure.config;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.ql.util.express.ExpressRunner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
@Slf4j
@EnableAsync
public class FeeCenterConfig {

    private  static final int THREAD_POOL_SIZE = Runtime.getRuntime().availableProcessors();

    @Bean
    public Executor mongodbTaskExecutor(){
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("fee-center-mongodb");
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(1024 * 8);
        executor.setKeepAliveSeconds(60);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        Executor wrapperedExecutor = TtlExecutors.getTtlExecutor(executor);
        log.info("【wanshifu spring boot component】 'async task pool' init successful!");

        return wrapperedExecutor;
    }

    @Bean
    public Executor batchTaskExecutor(){
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("fee-center-batchTaskExecutor");
        executor.setCorePoolSize(THREAD_POOL_SIZE * 2);
        executor.setMaxPoolSize(THREAD_POOL_SIZE * 20);
        executor.setQueueCapacity(1024 * 8);
        executor.setKeepAliveSeconds(60);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        Executor wrapperedExecutor = TtlExecutors.getTtlExecutor(executor);
        log.info("【wanshifu spring boot component】 'async task pool' init successful!");

        return wrapperedExecutor;
    }


    @Bean
    public Executor fetchBigdataTaskExecutor(){
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("fee-center-fetch-bigdata");
        executor.setCorePoolSize(4);
        executor.setMaxPoolSize(4);
        executor.setQueueCapacity(1);
        executor.setKeepAliveSeconds(60);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    @Bean
    public ExpressRunner expressRunner(){
        // 需要高精度运算，不需要全量日志
        return new ExpressRunner(true,false);
    }



}
