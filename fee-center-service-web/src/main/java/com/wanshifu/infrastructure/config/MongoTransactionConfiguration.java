package com.wanshifu.infrastructure.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.MongoDbFactory;

/**
 * @author: <PERSON>
 * @create: 2024-03-01 10:34
 * @description: 配置MongoDB事务管理器
 */
@Configuration
public class MongoTransactionConfiguration {

//    @Bean
//    public MongoTransactionManager mongoTransactionManager(MongoDbFactory factory) {
//        return new MongoTransactionManager(factory);
//    }

}
