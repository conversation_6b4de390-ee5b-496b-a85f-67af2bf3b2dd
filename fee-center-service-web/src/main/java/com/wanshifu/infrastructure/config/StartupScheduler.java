package com.wanshifu.infrastructure.config;

import com.wanshifu.fee.center.domain.document.master2c.DraftServePrice;
import com.wanshifu.fee.center.domain.document.master2c.ServePrice;
import com.wanshifu.fee.center.domain.enums.master2c.DraftServePriceStatusEnum;
import com.wanshifu.fee.center.domain.enums.master2c.ServePriceStatusEnum;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.repository.dao.DraftServePriceDao;
import com.wanshifu.repository.dao.ServePriceDao;
import com.wanshifu.service.master2c.ServePriceService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class StartupScheduler {

    private final ServePriceDao servePriceDao;
    private final ServePriceService servePriceService;

    private final DraftServePriceDao draftServePriceDao;

    @PostConstruct
    public void init() {
        // 查找“待生效”且 startTime 在未来的，那么要在startTime触发将状态变更新为“生效中”
        List<ServePrice> toActivateTasks = servePriceDao.findListByStatusAndNowBeforeStartTime(
                ServePriceStatusEnum.PENDING_ACTIVATION.getStatus());

        if (CollectionUtils.isNotEmpty(toActivateTasks)) {
            toActivateTasks.forEach(servePriceService::scheduleActive);
        }

        // 查找“待生效”且 startTime 在过去的，那么需立即将状态更新为“生效中”
        List<ServePrice> activeTasks = servePriceDao.findListByStatusAndNowAfterOrEqualStartTime(
                ServePriceStatusEnum.PENDING_ACTIVATION.getStatus());
        if (CollectionUtils.isNotEmpty(activeTasks)) {
            activeTasks.forEach(e -> servePriceService.active(e.getPriceId(), "系统"));
        }

        // 查找“待审核”且 startTime 在未来的，那么要在startTime触发将状态变更为“已驳回”
        List<ServePrice> autoRejectTasks = servePriceDao.findListByStatusAndNowBeforeStartTime(
                ServePriceStatusEnum.PENDING_REVIEW.getStatus());

        if (CollectionUtils.isNotEmpty(autoRejectTasks)) {
            toActivateTasks.forEach(servePriceService::scheduleReject);
        }

        // 查找“待审核”且 startTime 在过去的，那么需立即将状态更新为“已驳回”
        List<ServePrice> toAutoRejectTasks = servePriceDao.findListByStatusAndNowAfterOrEqualStartTime(
                ServePriceStatusEnum.PENDING_REVIEW.getStatus());

        if (CollectionUtils.isNotEmpty(toAutoRejectTasks)) {
            toAutoRejectTasks.forEach(e -> servePriceService.reject(e.getPriceId(), "系统", "超时未审核，系统自动驳回"));
        }

        //--------------草稿状态流转处理-----------------

        //查找“待审核”草稿且startTime在未来的，那么要在startTime触发时将状态变更为“取消”
        List<DraftServePrice> draftServePriceList = draftServePriceDao.findListByStatusAndNowBeforeStartTime(DraftServePriceStatusEnum.PENDING_REVIEW.getStatus());
        if (CollectionUtils.isNotEmpty(draftServePriceList)) {
            draftServePriceList.forEach(servePriceService::scheduleCancelDraft);
        }

        //查找“待审核”草稿且startTime在过去的，那么需立即将状态更新为“取消”
        List<DraftServePrice> draftServePriceList2 = draftServePriceDao.findListByStatusAndNowAfterOrEqualStartTime(DraftServePriceStatusEnum.PENDING_REVIEW.getStatus());
        if (CollectionUtils.isNotEmpty(draftServePriceList2)) {
            draftServePriceList2.forEach(e -> servePriceService.cancelDraft(e.getDraftId(), "超时自动取消"));
        }

        //查找“待生效”草稿且startTime在未来的，那么要在startTime触发时将状态变更为“上线”
        List<DraftServePrice> draftServePriceList3 = draftServePriceDao.findListByStatusAndNowBeforeStartTime(DraftServePriceStatusEnum.PENDING_ACTIVATION.getStatus());
        if (CollectionUtils.isNotEmpty(draftServePriceList3)) {
            draftServePriceList3.forEach(servePriceService::scheduleOnlineDraft);
        }

        //查找“待生效”草稿且startTime在过去的，那么需立即将状态更新为“上线”
        List<DraftServePrice> draftServePriceList4 = draftServePriceDao.findListByStatusAndNowAfterOrEqualStartTime(DraftServePriceStatusEnum.PENDING_ACTIVATION.getStatus());
        if (CollectionUtils.isNotEmpty(draftServePriceList4)) {
            draftServePriceList4.forEach(e -> servePriceService.onlineDraft(e.getDraftId(), e.getCreateBy()));

        }
    }
}
