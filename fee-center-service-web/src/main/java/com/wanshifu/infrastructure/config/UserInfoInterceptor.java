package com.wanshifu.infrastructure.config;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.wanshifu.framework.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
@Slf4j
public class UserInfoInterceptor implements HandlerInterceptor {

    public static TransmittableThreadLocal<String> userInfoLocal = new TransmittableThreadLocal<>();

    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o) {
        String userInfo = httpServletRequest.getHeader("user-info");

        log.info(">>>>>>>拦截到api相关请求头，请求用户：<<<<<<<<{}", userInfo);

        if(StringUtils.isNotEmpty(userInfo)){
            // 直接搂下来，放到ThreadLocal中 后续直接从中获取
            userInfoLocal.set(userInfo);
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) {

    }
}
