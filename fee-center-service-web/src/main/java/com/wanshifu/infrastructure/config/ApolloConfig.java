package com.wanshifu.infrastructure.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class ApolloConfig {

    /**
     * 通过服务id获取天天特价规则开关
     */
    public static boolean BARGAIN_PRICE_EVERYDAY_FEE_RULE_BY_SERVICE_ID_SWITCH;
    @Value("${fee.rule.getBargainPriceEverydayFeeRuleByServiceIdSwitch:true}")
    private void setBargainPriceEverydayFeeRuleByServiceIdSwitch(boolean bargainPriceEverydayFeeRuleByServiceIdSwitch) {
        BARGAIN_PRICE_EVERYDAY_FEE_RULE_BY_SERVICE_ID_SWITCH = bargainPriceEverydayFeeRuleByServiceIdSwitch;
    }

    /**
     * 计价异常通知，飞书url
     */
    public static String APPLY_ORDER_WARNING_URL;
    @Value("${feiShu-talk.apply-order-warning-url}")
    public void setFixedPricePublishOrderWarningUrl(String applyOrderWarningUrl) {
        APPLY_ORDER_WARNING_URL = applyOrderWarningUrl;
    }

    /**
     * 新标准计价接口异常通知，飞书url
     */
    public static String NEW_PRICING_WARNING_URL;
    @Value("${feiShu-talk.new-pricing-warning-url:https://open.feishu.cn/open-apis/bot/v2/hook/3227bada-f069-4c7f-80ad-49c1569eb377}")
    public void setNewPricingWarningUrl(String newPricingWarningUrl) {
        NEW_PRICING_WARNING_URL = newPricingWarningUrl;
    }

    /**
     * 是否启用重构模板映射
     */
    public static Boolean NEW_TEMPLATE_MAPPING_SWITCH;
    @Value("${new-template-mapping-switch:true}")
    public void setNewTemplateMappingSwitch(boolean newTemplateMappingSwitch) {
        NEW_TEMPLATE_MAPPING_SWITCH = newTemplateMappingSwitch;
    }

    /**
     * 是否启用动态算价（按服务）
     */
    public static Boolean SERVICE_DYNAMIC_FEE_RULE_SWITCH;
    @Value("${service.dynamic.fee.rule.switch:false}")
    public void setServiceDynamicFeeRuleSwitch(boolean serviceDynamicFeeRuleSwitch) {
        SERVICE_DYNAMIC_FEE_RULE_SWITCH = serviceDynamicFeeRuleSwitch;
    }
}
