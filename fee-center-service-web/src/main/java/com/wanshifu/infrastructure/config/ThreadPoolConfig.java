package com.wanshifu.infrastructure.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.*;

@Slf4j
@Configuration
@EnableAsync
public class ThreadPoolConfig {

    @Bean
    public ExecutorService autoAdjustThreadPool() {
        int corePoolSize = Math.max(4, Runtime.getRuntime().availableProcessors());
        int maxPoolSize = Math.max(corePoolSize, (int) Math.ceil(5.0 * Runtime.getRuntime().availableProcessors()));
        // 任务队列的最大容，超过这个值后，会根据拒绝策略拒绝。
        int queueCapacity = 100;

        // 使用 ArrayBlockingQueue 作为工作队列，它是一个有界队列。
        BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>(queueCapacity);

        // 自定义拒绝策略：当线程池和队列都满时，抛出 RejectedExecutionException。
        RejectedExecutionHandler rejectionHandler = (r, executor) -> {
            log.error("Task {} rejected from {}", r.toString(), executor.toString());
            throw new RejectedExecutionException("Task " + r.toString() + " rejected from " + executor.toString());
        };

        ThreadFactory threadFactory = new ThreadFactoryBuilder()
                .setNameFormat("auto-adjust-thread-pool-%d")
                .build();

        return new ThreadPoolExecutor(
                corePoolSize,
                maxPoolSize,
                60L, // 空闲线程存活时间（秒）
                TimeUnit.SECONDS, // 时间单位
                workQueue, // 工作队列
                threadFactory, // 线程工厂
                rejectionHandler // 拒绝策略
        );
    }

    // 日志处理线程池
    @Bean("logTaskExecutor")
    public ThreadPoolTaskExecutor logTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(5);
        executor.setKeepAliveSeconds(60);  // 空闲线程存活时间（秒）
        executor.setQueueCapacity(1000); // 允许大量日志堆积
        executor.setThreadNamePrefix("Log-Thread-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
        return executor;
    }
}
