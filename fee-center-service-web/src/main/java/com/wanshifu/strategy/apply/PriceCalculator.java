package com.wanshifu.strategy.apply;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.ql.util.express.DefaultContext;
import com.ql.util.express.ExpressRunner;
import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.common.enums.AttributeKeyEnum;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.biz.EnterpriseOrderOfferGuidePriceBizRule;
import com.wanshifu.fee.center.domain.biz.PlatformFixedPriceBizRule;
import com.wanshifu.fee.center.domain.constant.GlobalRedisKeyConstant;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.*;
import com.wanshifu.fee.center.domain.dto.*;
import com.wanshifu.fee.center.domain.enums.*;
import com.wanshifu.fee.center.domain.request.ApplyCalculateReq;
import com.wanshifu.fee.center.domain.request.SingleStandardPricingParam;
import com.wanshifu.fee.center.domain.request.calculate.StandardPricingRequest;
import com.wanshifu.fee.center.domain.request.calculate.StandardPricingResponse;
import com.wanshifu.fee.center.domain.response.ApplyCalculateResp;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.MapUtils;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.infrastructure.config.ApolloConfig;
import com.wanshifu.repository.SceneInfoRepository;
import com.wanshifu.service.DynamicFeeRuleService;
import com.wanshifu.service.StandardPricingLogService;
import com.wanshifu.strategy.extract.ServiceAttributeValueExtractor;
import com.wanshifu.strategy.extract.ValueExtractor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.core.ResolvableType;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.wanshifu.fee.center.domain.enums.ExpressParamEnum.*;


/**
 * @author: Chen Yong
 * @create: 2024-03-28 14:57
 * @description: 价格计算器。后续如有必要，可以将部分私有方法抽离，按策略调用
 */
@Component
@Slf4j
public class PriceCalculator {

    @Resource
    private AddressApi addressApi;
    @Resource
    private RedisHelper redisHelper;
    @Resource
    private SceneInfoRepository sceneInfoRepository;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private List<ValueExtractor<?>> valueExtractors;
    @Resource
    private ExpressRunner expressRunner;
    @Resource
    private ServiceAttributeValueExtractor serviceAttributeValueExtractor;
    @Resource
    private StandardPricingLogService standardPricingLogService;
    @Resource
    private DynamicFeeRuleService dynamicFeeRuleService;

    private final String sameSkuNoNeedRemoveFlag = "sameSkuNoNeedRemoveFlag";
    public StandardPricingResponse standardPricing(StandardPricingRequest request) {
        StandardPricingResponse standardPricingResponse = new StandardPricingResponse();
        standardPricingResponse.setTid(SnowFlakeGenerator.INSTANCE.generate());

        List<String> sceneCodeList = request.getSceneCodeList();
        List<ServiceDTO> serviceDtoList = request.getServiceDtoList();
        BigDecimal goodsNumber = extractOrderValue(serviceDtoList);

        StandardPricingLog pricingLog = getStandardPricingLog(request.getOrderBase(), standardPricingResponse.getTid());
        List<StandardPricingLog.ServiceResultLog> serviceResultLogList = new ArrayList<>();
        pricingLog.setSceneCodeList(sceneCodeList);
        pricingLog.setServiceDtoList(serviceDtoList);
        pricingLog.setServiceResultLogList(serviceResultLogList);

        /*
        历史逻辑：
        1、一次请求有多个场景，并且场景的优先级是根据场景编码在List中的顺序决定的，优先级按编码在列表中的顺序由高到低，优先级高的SceneCode的规则优先计算
        2、一个场景中可能有多个服务（对应业务是 一次下单多个一口价服务），也就是说一个场景下有的服务能成功算出价格，有的服务会失败，失败的则往下一个优先级的场景继续算价，依此类推，直到最后一个场景计算完成
        3、业务方只会去第一个场景的结果，所以需要将多个场景中算价成功的服务价格结果汇总的第一个场景中（服务结果信息应该保留真正成功算出结果的场景编码）
        4、所以返回结果的场景这一层的场景编码仅仅是第一次成功算价的，真正算价成功的场景编码还得看服务结果中的场景编码
         */
        List<StandardPricingResponse.ServiceResult> realServiceResultList = new ArrayList<>();
        for (ServiceDTO serviceDto : serviceDtoList) {
            for (String sceneCode : sceneCodeList) {
                if (serviceDto.getIsCalculated()) {
                    continue;
                }

                SingleStandardPricingParam param = new SingleStandardPricingParam();
                BeanUtils.copyProperties(request, param);
                param.setSceneCode(sceneCode);
                param.setServiceDto(serviceDto);
                param.setGoodsNumber(goodsNumber);

                String serviceId = serviceDto.getServiceId().toString();

                ServiceCalculateResult result;
                StandardPricingLog.ServiceResultLog serviceResultLog;
                try {
                    result = standardPricing(param);
                    if (ApolloConfig.SERVICE_DYNAMIC_FEE_RULE_SWITCH) {
                        // 只有配置了动态规则（按服务）的才需要处理
                        String sceneCods = redisHelper.get(GlobalRedisKeyConstant.SERVICE_DYNAMIC_FEE_RULE_SCENE_CODE);
                        if (StringUtils.isNotBlank(sceneCods)) {
                            boolean contained = Arrays.asList(StringUtils.split(sceneCods, ",")).contains(sceneCode);
                            if (contained) {
                                handleServiceDynamicPrice(request, result, param.getAddressInfo());
                            }
                        }
                    }
                    serviceDto.setIsCalculated(true);
                    result.setSuccess(true);
                    StandardPricingResponse.ServiceResult serviceResult = new StandardPricingResponse.ServiceResult();
                    BeanUtils.copyProperties(result, serviceResult);
                    serviceResult.setSceneCode(sceneCode);
                    serviceResult.setServiceId(serviceId);
                    serviceResult.setExternalServiceId(serviceDto.getExternalServiceId());
                    serviceResult.setIndex(serviceId);
                    realServiceResultList.add(serviceResult);

                    serviceResultLog = setPricingLog(result);
                    serviceResultLog.setSceneCode(sceneCode);
                    serviceResultLog.setIsSuccess(true);
//                    serviceResultLogList.add(serviceResultLog);
                } catch (Exception e) {
                    log.error("标准计价异常", e);
                    serviceResultLog = new StandardPricingLog.ServiceResultLog();
                    serviceResultLog.setSceneCode(sceneCode);
                    serviceResultLog.setServiceId(serviceId);
                    serviceResultLog.setErrorInfo(e.getMessage());
                    serviceResultLog.setIsSuccess(false);
                }
                serviceResultLogList.add(serviceResultLog);
            }

        }

        BigDecimal cost = realServiceResultList.stream().map(StandardPricingResponse.ServiceResult::getCost).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal costMax = realServiceResultList.stream().map(StandardPricingResponse.ServiceResult::getCostMax).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal dynamicCost = realServiceResultList.stream().map(StandardPricingResponse.ServiceResult::getDynamicFeeCost).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal dynamicCostMax = realServiceResultList.stream().map(StandardPricingResponse.ServiceResult::getDynamicFeeCostMax).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal basePrice = realServiceResultList.stream().map(StandardPricingResponse.ServiceResult::getBasePrice).filter(Objects::nonNull).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
        standardPricingResponse.setCost(cost);
        standardPricingResponse.setCostMax(costMax);
        standardPricingResponse.setDynamicFeeCost(dynamicCost);
        standardPricingResponse.setDynamicFeeCostMax(dynamicCostMax);
        standardPricingResponse.setBasePrice(basePrice);

        standardPricingResponse.setServiceResultList(realServiceResultList);
        if (realServiceResultList.size() == serviceDtoList.size()) {
            standardPricingResponse.setSuccess(true);
            pricingLog.setSuccess(true);
        } else {
            standardPricingResponse.setSuccess(false);
            pricingLog.setSuccess(false);
            // 计算 realServiceResultList中的serviceId与serviceDtoList中serviceId的差集
            Set<String> serviceIdSet = realServiceResultList.stream().map(StandardPricingResponse.ServiceResult::getServiceId).collect(Collectors.toSet());
            Set<Long> errorServiceIdSet = serviceDtoList.stream().map(ServiceDTO::getServiceId).filter(serviceId -> !serviceIdSet.contains(serviceId.toString())).collect(Collectors.toSet());
            standardPricingResponse.setErrorInfo(StrUtil.format("部分服务算价失败，失败的serviceId：{}", errorServiceIdSet));
        }

        pricingLog.setCost(cost);
        pricingLog.setCostMax(costMax);
        pricingLog.setDynamicFeeCost(dynamicCost);
        pricingLog.setDynamicFeeCostMax(dynamicCostMax);

        standardPricingLogService.saveAsync(pricingLog);

        return standardPricingResponse;
    }

    private void handleServiceDynamicPrice(StandardPricingRequest request, ServiceCalculateResult result, AddressInfo addressInfo) {
        if (result == null) {
            return;
        }

        String userId = Optional.ofNullable(request.getBizInfo()).map(BizInfo::getUserId).orElse(null);
        Long cityId = Optional.ofNullable(addressInfo).map(AddressInfo::getLv3DivisionId).orElse(null);

        BigDecimal dynamicFeeCost = dynamicFeeRuleService.getServiceDynamicFeeCost(
                result.getSceneCode(),
                request.getSubSceneCode(),
                result.getServiceId(),
                userId,
                cityId,
                result.getCost(),
                result.getDynamicFeeCost(),
                request.getDynamicIndicatorParamList());
        if (dynamicFeeCost != null) {
            dynamicFeeCost = dynamicFeeCost.setScale(2, RoundingMode.HALF_UP);
            result.setDynamicFeeCost(dynamicFeeCost);
        }
    }



    private StandardPricingLog.ServiceResultLog setPricingLog(ServiceCalculateResult result) {
        StandardPricingLog.ServiceResultLog serviceResultLog = new StandardPricingLog.ServiceResultLog();
        serviceResultLog.setServiceId(result.getServiceId());
        serviceResultLog.setCost(result.getCost());
        serviceResultLog.setDynamicFeeCost(result.getDynamicFeeCost());
//        List<FeeRule> feeRuleList = result.getFeeRuleList();
        List<CalculateResult> feeRuleList = result.getCalculateResultList();
        if (CollectionUtils.isNotEmpty(feeRuleList)) {
            List<StandardPricingLog.FeeRuleLog> feeRuleLogList = new ArrayList<>();
            serviceResultLog.setFeeRuleLogList(feeRuleLogList);
            for (CalculateResult feeRule : feeRuleList) {
                StandardPricingLog.FeeRuleLog feeRuleLog = new StandardPricingLog.FeeRuleLog();
                Map<String, String> bizRule = feeRule.getBizRule();
                feeRuleLog.setTemplateId(feeRule.getTemplateId());
                feeRuleLog.setFeeRuleId(feeRule.getFeeRuleId());
                feeRuleLog.setSkuNo(bizRule.get(CommonBizRule.Fields.skuNo));
                feeRuleLog.setSkuAttributePathName(bizRule.get(CommonBizRule.Fields.skuAttributePathName));
                feeRuleLog.setSkuNumberPathNo(bizRule.get(CommonBizRule.Fields.skuNumberPathNo));
                feeRuleLog.setSkuNumberName(bizRule.get(CommonBizRule.Fields.skuNumberName));
                feeRuleLog.setAttributeDisplayName(bizRule.get(CommonBizRule.Fields.attributeDisplayName));
                feeRuleLog.setFeeUnit(bizRule.get(CommonBizRule.Fields.feeUnit));
                feeRuleLog.setFeeName(bizRule.get(CommonBizRule.Fields.feeName));
                feeRuleLog.setSkuType(bizRule.get(CommonBizRule.Fields.skuType));
                feeRuleLog.setFeeTypeTag(bizRule.get(CommonBizRule.Fields.feeTypeTag));
                feeRuleLog.setExpress(feeRule.getCalculateRuleData().getExpress());
                feeRuleLog.setFeeTypeTagName(bizRule.get(CommonBizRule.Fields.feeTypeTagName));
                feeRuleLog.setPrice(bizRule.get(CommonBizRule.Fields.masterInputPrice));
                feeRuleLogList.add(feeRuleLog);
            }
        }


        return serviceResultLog;
    }

    private StandardPricingLog getStandardPricingLog(OrderBase orderBase, Long tid) {
        StandardPricingLog pricingLog = new StandardPricingLog();
        pricingLog.setTid(tid);
        if (orderBase != null) {
            pricingLog.setOrderId(orderBase.getOrderId());
            pricingLog.setOrderNo(orderBase.getOrderNo());
            pricingLog.setGlobalOrderTraceId(orderBase.getGlobalOrderTraceId());
        }
        return pricingLog;
    }


    private ServiceCalculateResult standardPricing(SingleStandardPricingParam param) {

        validateParameters(param);

        String sceneCode = param.getSceneCode();
        SceneInfo sceneInfo = getSceneInfo(sceneCode);

        validateBizInfo(param.getBizInfo(), sceneInfo.getBizIdType());

        completeAddressInfo(param, sceneInfo);

        ServiceDTO serviceDto = param.getServiceDto();
        String serviceId = serviceDto.getServiceId().toString();
        log.info("开始计算场景「{}」订单费用，serviceIds={}", sceneInfo.getSceneCode(), serviceId);


        /*
         * 映射需求思路：
         * 1、根据场景判断是否需要映射
         * 2、需要，则根据 场景+服务Id，带入sourceBizRule中，查找FeeTemplateMapping数据（需要保留映射关系，sourceBizRule的sceneCode+skuNo 作为key
         *    ，targetBizRule作为value，需要用source的skuNo替换target的，因为后面需要拿着skuNo去订单上下方中检查是否有【不反价属性】）
         * 3、获取上一步中的targetBizRule中的templateId数据，查询feeTemplate（templateId是feeTemplate表的唯一键）
         * 4、根据上一步的templateId，查询feeRule，需要加上地址等信息（参考已有查询条件），获取计费规则
         * 5、将feeRule中的serviceId（包括group）、skuNo、skuNumberPathNo 用source的替换掉，并且需要替换掉calculateRuleData中的 计价数量占位符
         * 6、后续的逻辑就是一样的了
         * 总结：映射的目的是拿到feeRule，而feeRule主要包含bizRule和calculateRuleData，然后订单上下文是原始的serviceId、skuNo、skuNumberPathNo，所以需要替换
         */
        List<FeeTemplate> feeTemplates = new ArrayList<>();
        List<FeeRule> feeRuleList = new ArrayList<>();
        String noMappingServiceId = null;
        boolean needFilterFeeRuleByDivisionType = true;

        // 业务侧传的优先，是否映射计价
        Boolean isMappingPricing = sceneInfo.getIsMappingPricing();
        isMappingPricing = isMappingPricing != null && isMappingPricing;
        Boolean isMappingPricingFromBiz = param.getIsMappingPricing();
        if (isMappingRequired(isMappingPricingFromBiz, isMappingPricing)) {
            // 查询映射
            String targetSceneCode = param.getTargetSceneCode();
            List<FeeTemplateMapping> mappings = getFeeTemplateMappingList(sceneCode, targetSceneCode, serviceId);

            if (CollectionUtils.isEmpty(mappings)) {
                noMappingServiceId = serviceId;
            } else {
                Map<Long, List<String>> templateIdWithServiceIdAndSkuNoMap = getTemplateIdWithServiceIdAndSkuNoMap(mappings);
                Set<SceneCodeAndServiceId> targetSceneCodeAndServiceIds = getTargetSceneCodeAndServiceIdsNew(mappings);

                addFeeTemplates(targetSceneCodeAndServiceIds, feeTemplates, serviceDto);
                addFeeRules(targetSceneCodeAndServiceIds, feeTemplates, feeRuleList, param, templateIdWithServiceIdAndSkuNoMap);

                if (!feeRuleList.isEmpty()) {
                    convertCustomToStandardSkuType(feeRuleList);
                    needFilterFeeRuleByDivisionType = false;
                }
            }
        } else {
            noMappingServiceId = serviceId;
        }

        if (StringUtils.isNotBlank(noMappingServiceId)) {
            // 如果映射不为空则需要转换映射
            // 查询计费模板
            List<FeeTemplate> originFeeTemplates = getFeeTemplates(noMappingServiceId, serviceDto, sceneCode);
            if (CollectionUtils.isNotEmpty(originFeeTemplates)) {
                feeTemplates.addAll(originFeeTemplates);
            }
            Criteria feeRuleCondition = getFeeRuleCondition(serviceId, sceneInfo, param.getAddressInfo(), param.getBizInfo());
            List<FeeRule> originFeeRuleList = feeRulesBy(feeRuleCondition);
            if (CollectionUtils.isNotEmpty(originFeeRuleList)) {
                feeRuleList.addAll(originFeeRuleList);
            }
        }

        completeGroup(feeRuleList);

        if (needFilterFeeRuleByDivisionType) {
            feeRuleList = filterFeeRuleByDivisionType(feeRuleList);
        }

        if (CollectionUtils.isEmpty(feeTemplates)) {
            throw new BusException(ExceptionEnum.WITHOUT_TEMPLATE.code,
                    StrUtil.format(ExceptionEnum.WITHOUT_TEMPLATE.msg, sceneCode, serviceId));
        }

        if (CollectionUtils.isEmpty(feeRuleList)) {
            throw new BusException(ExceptionEnum.WITHOUT_RULE.code,
                    StrUtil.format(ExceptionEnum.WITHOUT_RULE.msg, sceneCode, serviceId));
        }

        feeRuleList = removeDuplicateFeeRules(feeRuleList);

        final List<FeeTemplate> finalFeeTemplates = filterTemplate(feeTemplates, param.getBizInfo());
        if (CollectionUtils.isEmpty(finalFeeTemplates)) {
            throw new BusException(ExceptionEnum.WITHOUT_TEMPLATE.code,
                    StrUtil.format(ExceptionEnum.WITHOUT_TEMPLATE.msg, sceneCode, serviceId));
        }

        validateTemplate(finalFeeTemplates, feeRuleList, param, sceneInfo);

        // 去除不在template里面的规则
        feeRuleList.removeIf(rule -> !getValidTemplateIds(finalFeeTemplates).contains(rule.getTemplateId()));

        completeGroup(feeRuleList);

        return calculateFinalResult(feeRuleList, param, sceneInfo);
    }


    /**
     * 需求链接：<a href="https://jiqmwlmd0v.feishu.cn/wiki/DX9rwIBgTidUYAklLDgcr0YdnWf">支持商品型号定价（自定义商家sku）及计价</a>
     * @param feeTemplates 模板
     * @param bizInfo      业务信息
     */
    private List<FeeTemplate> filterTemplate(List<FeeTemplate> feeTemplates, BizInfo bizInfo) {

        if (CollectionUtils.isEmpty(feeTemplates)) {
            return feeTemplates;
        }

        /*
          1. 模版匹配，若存在自定义 SKU 的【服务费】模板，则不再匹配【标准】的服务费模板。
          PS：标明适用部分商家ID的自定义sku，需取订单的用户ID进行匹配；如果匹配到商家ID的自定义sku，优先用该自定义sku，否则继续匹配非自定义sku
         */
//        boolean hasServiceFeeCustomSku = feeTemplates.stream().anyMatch(e ->
//                FeeSkuTypeEnum.CUSTOM_SKU.code.equals(e.getBizRule().get(CommonBizRule.Fields.skuType))
//                        && FeeTypeTagEnum.SERVICE_FEE.code.equals(e.getBizRule().get(CommonBizRule.Fields.feeTypeTag)));
//        if (hasServiceFeeCustomSku) {
//            feeTemplates.removeIf(e -> FeeTypeTagEnum.SERVICE_FEE.code.equals(e.getBizRule().get(CommonBizRule.Fields.feeTypeTag))
//                    && FeeSkuTypeEnum.STANDARD_SKU.code.equals(e.getBizRule().get(CommonBizRule.Fields.skuType)));
//        }

        String userId = null;
        if (bizInfo != null) {
            userId = bizInfo.getUserId();
        }

        if (StringUtils.isNotBlank(userId)) {
            final String finalUserId = userId;
            boolean hasServiceFeeCustomSku = feeTemplates.stream().anyMatch(e -> {
                Map<String, String> bizRule = e.getBizRule();
                return FeeSkuTypeEnum.CUSTOM_SKU.code.equals(bizRule.get(CommonBizRule.Fields.skuType))
                        && FeeTypeTagEnum.SERVICE_FEE.code.equals(bizRule.get(CommonBizRule.Fields.feeTypeTag))
                        && finalUserId.equals(bizRule.get(CommonBizRule.Fields.customSkuUserId));
            });

            if (hasServiceFeeCustomSku) {
                feeTemplates.removeIf(e -> FeeTypeTagEnum.SERVICE_FEE.code.equals(e.getBizRule().get(CommonBizRule.Fields.feeTypeTag))
                        && FeeSkuTypeEnum.STANDARD_SKU.code.equals(e.getBizRule().get(CommonBizRule.Fields.skuType)));
            }

            feeTemplates = feeTemplates.stream().filter(e -> {
                Map<String, String> bizRule = e.getBizRule();
                String skuType = bizRule.get(CommonBizRule.Fields.skuType);
                String matchSkuType = bizRule.get(CommonBizRule.Fields.matchSkuType);
                if (FeeSkuTypeEnum.CUSTOM_SKU.code.equals(skuType) && MatchSkuTypeEnum.CURRENT_SKU.getCode().equals(matchSkuType)) {
                    String customSkuUserId = bizRule.get(CommonBizRule.Fields.customSkuUserId);
                    return finalUserId.equals(customSkuUserId);
                } else {
                    return true;
                }
            }).collect(Collectors.toList());

        } else {
            boolean hasServiceFeeCustomSku = feeTemplates.stream().anyMatch(e -> {
                Map<String, String> bizRule = e.getBizRule();
                return FeeSkuTypeEnum.CUSTOM_SKU.code.equals(bizRule.get(CommonBizRule.Fields.skuType))
                        && FeeTypeTagEnum.SERVICE_FEE.code.equals(bizRule.get(CommonBizRule.Fields.feeTypeTag))
                        && StringUtils.isBlank(bizRule.get(CommonBizRule.Fields.customSkuUserId));
            });

            if (hasServiceFeeCustomSku) {
                feeTemplates.removeIf(e -> FeeTypeTagEnum.SERVICE_FEE.code.equals(e.getBizRule().get(CommonBizRule.Fields.feeTypeTag))
                        && FeeSkuTypeEnum.STANDARD_SKU.code.equals(e.getBizRule().get(CommonBizRule.Fields.skuType)));
            }

            feeTemplates = feeTemplates.stream().filter(e -> {
                Map<String, String> bizRule = e.getBizRule();
                String skuType = bizRule.get(CommonBizRule.Fields.skuType);
                if (FeeSkuTypeEnum.CUSTOM_SKU.code.equals(skuType)) {
                    String customSkuUserId = bizRule.get(CommonBizRule.Fields.customSkuUserId);
                    return StringUtils.isBlank(customSkuUserId);
                } else {
                    return true;
                }
            }).collect(Collectors.toList());
        }
        return feeTemplates;
    }

    private void validateParameters(SingleStandardPricingParam param) {
        if (param == null || param.getSceneCode() == null || param.getServiceDto() == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
    }

    private boolean isMappingRequired(Boolean isMappingPricingFromBiz, Boolean isMappingPricing) {
        return (isMappingPricingFromBiz != null && isMappingPricingFromBiz) ||
                (isMappingPricingFromBiz == null && isMappingPricing);
    }

    private void addFeeTemplates(Set<SceneCodeAndServiceId> targetSceneCodeAndServiceIds, List<FeeTemplate> feeTemplates, ServiceDTO serviceDto) {
        List<FeeTemplate> mappingFeeTemplates = getFeeTemplateList(serviceDto, targetSceneCodeAndServiceIds);
        if (CollectionUtils.isNotEmpty(mappingFeeTemplates)) {
            feeTemplates.addAll(mappingFeeTemplates);
        }
    }

    private void addFeeRules(Set<SceneCodeAndServiceId> targetSceneCodeAndServiceIds, List<FeeTemplate> feeTemplates,
                             List<FeeRule> feeRuleList, SingleStandardPricingParam param,
                             Map<Long, List<String>> templateIdWithServiceIdAndSkuNoMap) {
        List<FeeRule> mappingFeeRuleList = getFeeRuleList(param, templateIdWithServiceIdAndSkuNoMap, targetSceneCodeAndServiceIds, feeTemplates);
        if (CollectionUtils.isNotEmpty(mappingFeeRuleList)) {
            feeRuleList.addAll(mappingFeeRuleList);
        }
    }

    private void convertCustomToStandardSkuType(List<FeeRule> feeRuleList) {
        feeRuleList.stream()
                .filter(e -> FeeSkuTypeEnum.CUSTOM_SKU.code.equals(e.getBizRule().get(CommonBizRule.Fields.skuType)))
                .forEach(e -> e.getBizRule().put(CommonBizRule.Fields.skuType, FeeSkuTypeEnum.STANDARD_SKU.code));
    }

    private void validateTemplate(List<FeeTemplate> feeTemplates, List<FeeRule> feeRuleList, SingleStandardPricingParam param, SceneInfo sceneInfo) {
        // 是否有“服务费”
        boolean hasServiceFee = feeTemplates.stream().anyMatch(e -> FeeTypeTagEnum.SERVICE_FEE.code.equals(e.getBizRule().get(CommonBizRule.Fields.feeTypeTag)));
        if (!hasServiceFee) {
            throw new BusException(ExceptionEnum.FEE_TEMPLATE_WITHOUT_SERVICE_FEE.code,
                    String.format("%s，serviceId=%s，sceneCode=%s", ExceptionEnum.FEE_TEMPLATE_WITHOUT_SERVICE_FEE.msg, param.getServiceDto().getServiceId(), sceneInfo.getSceneCode()));
        }

        // 完整性校验
        Set<Long> templateIds = feeTemplates.stream()
                .filter(template -> !ApplyFlagEnum.NO_APPLY_IF_HAVE.code.equals(template.getBizRule().get(CommonBizRule.Fields.applyFlag)))
                .map(FeeTemplate::getTemplateId).collect(Collectors.toSet());
        Set<Long> ruleTemplateIds = feeRuleList.stream().map(FeeRule::getTemplateId).collect(Collectors.toSet());

        Collection<?> subPositiveTemplateIds = CollectionUtils.subtract(templateIds, ruleTemplateIds);
        Collection<?> subNegativeTemplateIds = CollectionUtils.subtract(ruleTemplateIds, templateIds);

        if (CollectionUtils.isNotEmpty(subPositiveTemplateIds) || CollectionUtils.isNotEmpty(subNegativeTemplateIds)) {
            if (!Boolean.FALSE.equals(param.getNeedIntegrityValidation()) && !Boolean.FALSE.equals(sceneInfo.getNeedIntegrityValidation())) {
                // 若存在自定义 SKU 的模板，则不再校验模板与规则的数量是否相等(2025-06-20与产品经理远康商议)
                boolean hasCustomSku = feeTemplates.stream().anyMatch(template -> FeeSkuTypeEnum.CUSTOM_SKU.code.equals(template.getBizRule().get(CommonBizRule.Fields.skuType)));
                if (hasCustomSku) {
                    return;
                }

                throw new BusException(ExceptionEnum.INCOMPLETE_RULE.code,
                        StrUtil.format(ExceptionEnum.INCOMPLETE_RULE.msg, sceneInfo.getSceneCode(), param.getServiceDto().getServiceId()));
            } else {
                handleUnconfiguredTemplates(feeTemplates, subPositiveTemplateIds, param, sceneInfo);
            }
        }
    }

    private void handleUnconfiguredTemplates(List<FeeTemplate> feeTemplates, Collection<?> subPositiveTemplateIds, SingleStandardPricingParam param, SceneInfo sceneInfo) {
        List<FeeTemplate> templates = feeTemplates.stream()
                .filter(e -> subPositiveTemplateIds.contains(e.getTemplateId()))
                .filter(e -> FeeTypeTagEnum.SERVICE_FEE.code.equals(e.getBizRule().get(CommonBizRule.Fields.feeTypeTag)))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(templates)) {
            for (FeeTemplate template : templates) {
                String skuNo = template.getBizRule().get(CommonBizRule.Fields.skuNo);
                String skuValue = extractValue(param.getServiceDto().getAttributeDtoList(), skuNo);
                if (StringUtils.isNotBlank(skuValue)) {
                    throw new BusException(ExceptionEnum.ORDER_CONTEXT_EXIST_BUT_RULE_WITHOUT_SERVICE_FEE.code,
                            StrUtil.format(ExceptionEnum.ORDER_CONTEXT_EXIST_BUT_RULE_WITHOUT_SERVICE_FEE.msg, skuNo));
                }
            }
        }
    }

    private Set<Long> getValidTemplateIds(List<FeeTemplate> feeTemplates) {
        return feeTemplates.stream()
                .filter(template -> !ApplyFlagEnum.NO_APPLY_IF_HAVE.code.equals(template.getBizRule().get(CommonBizRule.Fields.applyFlag)))
                .map(FeeTemplate::getTemplateId).collect(Collectors.toSet());
    }

    private ServiceCalculateResult calculateFinalResult(List<FeeRule> feeRuleList, SingleStandardPricingParam param, SceneInfo sceneInfo) {
        Map<String, String> orderCache = new HashMap<>();
        orderCache.put(SceneInfo.Fields.amountType, sceneInfo.getAmountType());
        BigDecimal goodsNumber = param.getGoodsNumber();
        if (goodsNumber != null) {
            orderCache.put(ExpressParamEnum.ALL_SERVICE_AP_SKU_NUMBER.bizKey, goodsNumber.toString());
        }

        List<ApplyCalculateReq.CalculateRuleDataUnit> calculateRuleDataUnits = new ArrayList<>();
        Map<String, String> cache = new HashMap<>(orderCache);

        completeServiceAttribute(param.getServiceDto());

        for (FeeRule feeRule : feeRuleList) {
            Map<String, String> bizRule = feeRule.getBizRule();
            cache.putAll(bizRule);

            CalculateRuleData calculateRuleData = feeRule.getCalculateRuleData();
            String numberExpress = extractNumberExpress(calculateRuleData);

            if (StringUtils.isBlank(numberExpress)) {
                throw new BusException(ExceptionEnum.WITHOUT_SKU_NUMBER.code,
                        String.format("%s，serviceId=%s，sceneCode=%s",
                                ExceptionEnum.WITHOUT_SKU_NUMBER.msg, param.getServiceDto().getServiceId(), sceneInfo.getSceneCode()));
            }

            parseExpressionParamList(feeRule);

            List<Map<String, String>> expressionParamMapList = buildExpressionParamMapList(feeRule, param.getServiceDto(), cache);

            if (CollectionUtils.isEmpty(expressionParamMapList)) {
                continue;
            }

            ApplyCalculateReq.CalculateRuleDataUnit unit = buildCalculateRuleDataUnit(feeRule, expressionParamMapList);
            calculateRuleDataUnits.add(unit);
        }

        ApplyCalculateReq applyCalculateReq = new ApplyCalculateReq();
        applyCalculateReq.setCalculateRuleDataList(calculateRuleDataUnits);
        applyCalculateReq.setSceneCode(sceneInfo.getSceneCode());
        applyCalculateReq.setDynamicCalculateTime(param.getDynamicCalculateTime());

        // TODO 如果calculateRuleDataUnits没有数据，是否需要再往下走？
        ApplyCalculateResp applyCalculateResp = calculate(
                calculateRuleDataUnits,
                feeRuleList,
                orderCache,
                param.getTid(),
                param.getServiceDto(),
                sceneInfo.getSceneCode(),
                param.getDynamicCalculateTime());

        BigDecimal cost = applyCalculateResp.getCost();
        if (Objects.isNull(cost) || cost.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusException(ExceptionEnum.COST_LESS_OR_EQU_ZERO.code,
                    String.format("%s，serviceId=%s，sceneCode=%s",
                            ExceptionEnum.COST_LESS_OR_EQU_ZERO.msg, param.getServiceDto().getServiceId(), sceneInfo.getSceneCode()));
        }

        ServiceCalculateResult result = new ServiceCalculateResult();
        result.setServiceId(param.getServiceDto().getServiceId().toString());
        result.setCost(cost);
        result.setCostMax(applyCalculateResp.getCostMax());
        result.setDynamicFeeCost(applyCalculateResp.getDynamicFeeCost());
        result.setDynamicFeeCostMax(applyCalculateResp.getDynamicFeeCostMax());
        result.setCalculateResultList(applyCalculateResp.getCalculateResultList());
        result.setSceneCode(sceneInfo.getSceneCode());
        result.setBasePrice(getBasePrice(feeRuleList));

        return result;
    }

    private String extractNumberExpress(CalculateRuleData calculateRuleData) {
        ExpressInfo expressInfo = calculateRuleData.getExpressInfo();
        String numberExpress;
        if (expressInfo != null && CollectionUtils.isNotEmpty(expressInfo.getExpressInfoUnits())) {
            numberExpress = expressInfo.getExpressInfoUnits().get(0).getNumberExpress();
        } else {
            numberExpress = calculateRuleData.getExpress()
                    .replaceFirst("masterInputPrice", "")
                    .replaceFirst("\\*", "").trim();
        }
        return numberExpress;
    }

    private void parseExpressionParamList(FeeRule feeRule) {
        if ("1".equals(extractNumberExpress(feeRule.getCalculateRuleData()))) {
            List<String> expressionParamList = feeRule.getCalculateRuleData().getExpressionParamList();
            if (CollectionUtils.isNotEmpty(expressionParamList)) {
                expressionParamList = expressionParamList.stream()
                        .filter(StringUtils::isNotBlank)
                        .filter(e -> !feeRule.getBizRule().get(CommonBizRule.Fields.skuNumberPathNo).equals(e))
                        .collect(Collectors.toList());
                feeRule.getCalculateRuleData().setExpressionParamList(expressionParamList);
            }
        }
    }

    private List<Map<String, String>> buildExpressionParamMapList(FeeRule feeRule, ServiceDTO serviceDTO, Map<String, String> cache) {
        CalculateRuleData calculateRuleData = feeRule.getCalculateRuleData();
        String numberExpress = extractNumberExpress(calculateRuleData);

        if (StringUtils.isBlank(numberExpress)) {
            throw new BusException(ExceptionEnum.WITHOUT_SKU_NUMBER.code,
                    String.format("%s，serviceId=%s，sceneCode=%s",
                            ExceptionEnum.WITHOUT_SKU_NUMBER.msg, serviceDTO.getServiceId(), feeRule.getBizRule().get(CommonBizRule.Fields.sceneCode)));
        }

        Pattern pattern = Pattern.compile("AP\\d+");
        Matcher matcher = pattern.matcher(numberExpress);
        List<String> numberExpressParamList = new ArrayList<>();
        while (matcher.find()) {
            numberExpressParamList.add(matcher.group());
        }

        List<Map<String, String>> expressionParamMapList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(numberExpressParamList)) {
            List<Map<String, String>> skuNumberParamMapList = getSkuNumberParamMapList(serviceDTO.getAttributeDtoList(), numberExpressParamList);
            if (CollectionUtils.isNotEmpty(skuNumberParamMapList)) {
                for (Map<String, String> map : skuNumberParamMapList) {
                    cache.putAll(map);
                    Map<String, String> skuNumberParamMap = getSuiteValueExtractor(ServiceDTO.class).extract(serviceDTO, feeRule, cache);
                    if (!MapUtils.isEmpty(skuNumberParamMap)) {
                        expressionParamMapList.add(skuNumberParamMap);
                    }
                }
            }
        } else {
            List<AttributeDTO> attributeDTOS = serviceDTO.getAttributeDtoList();
            String skuNo = feeRule.getBizRule().get(CommonBizRule.Fields.skuNo);
            int count = countSkuNo(attributeDTOS, skuNo);
            if (count > 0) {
                for (int i = 0; i < count; i++) {
                    Map<String, String> paramMap = getSuiteValueExtractor(ServiceDTO.class).extract(serviceDTO, feeRule, cache);
                    if (MapUtils.isNotEmpty(paramMap)) {
                        expressionParamMapList.add(paramMap);
                    }
                }
            }
        }
        return expressionParamMapList;
    }

    private ApplyCalculateReq.CalculateRuleDataUnit buildCalculateRuleDataUnit(FeeRule feeRule, List<Map<String, String>> expressionParamMapList) {
        ApplyCalculateReq.CalculateRuleDataUnit unit = new ApplyCalculateReq.CalculateRuleDataUnit();
        CalculateRuleData calculateRuleData = feeRule.getCalculateRuleData();
        unit.setExpress(calculateRuleData.getExpress());
        unit.setExpressionParamList(calculateRuleData.getExpressionParamList());
        unit.setExpressionParamMapList(expressionParamMapList);
        unit.setGroup(feeRule.getGroup());
        unit.setFeeRuleId(feeRule.getFeeRuleId());
        unit.setTemplateId(feeRule.getTemplateId());
        return unit;
    }


    private List<FeeRule> removeDuplicateFeeRules(List<FeeRule> feeRuleList) {
        return feeRuleList.stream()
                .collect(
                        Collectors.collectingAndThen(
                                Collectors.toMap(
                                        FeeRule::getFeeRuleId,
                                        e -> e,
                                        (e1, e2) -> e1),
                                map -> new ArrayList<>(map.values())));
    }


    private SceneInfo getSceneInfo(String sceneCode) {
        SceneInfo sceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(sceneCode);
        if (sceneInfo == null) {
            throw new BusException(StrUtil.format("场景编码有误:{}", sceneCode));
        }
        return sceneInfo;
    }

    private void validateBizInfo(BizInfo bizInfo, String sceneBizIdType) {
        BizIdTypeEnum sceneBizIdTypeEnum = BizIdTypeEnum.fromCode(sceneBizIdType);
        if (sceneBizIdTypeEnum != null) {
            if (sceneBizIdTypeEnum.equals(BizIdTypeEnum.USER_ID) && (bizInfo == null || StringUtils.isBlank(bizInfo.getUserId()))) {
                throw new BusException("当场景的【业务ID类型=用户】时，用户id不能为空");
            }
        }
    }

    private void completeAddressInfo(SingleStandardPricingParam param, SceneInfo sceneInfo) {
        CoreAddressInfo coreAddressInfo = param.getCoreAddressInfo();
        if (coreAddressInfo == null) {
            return;
        }
        AddressInfo addressInfo = completeAddress(coreAddressInfo);
        // 此处的divisionType理论上应该由场景决定，但鉴于历史原因，有些场景没有配置divisionType，所以此处需要兼容
        addressInfo.setDivisionType(coreAddressInfo.getDivisionType());
        String divisionType = sceneInfo.getDivisionType();
        if (StringUtils.isNotBlank(divisionType)) {
            addressInfo.setDivisionType(divisionType);
        }

        addressInfo.setNeedSearchParent(coreAddressInfo.getNeedSearchParent());
        Boolean needSearchParent = sceneInfo.getNeedSearchParent();
        if (needSearchParent != null) {
            addressInfo.setNeedSearchParent(needSearchParent);
        }
        param.setAddressInfo(addressInfo);
    }


    private void completeGroup(List<FeeRule> feeRuleList) {
        feeRuleList.forEach(feeRule ->
                Optional.ofNullable(feeRule.getBizRule())
                        .map(rule -> rule.get(CommonBizRule.Fields.serviceId))
                        .ifPresent(feeRule::setGroup));
    }


    private Set<SceneCodeAndServiceId> getTargetSceneCodeAndServiceIdsNew(List<FeeTemplateMapping> mappings) {
        if (CollectionUtils.isEmpty(mappings)) {
            return Collections.emptySet();
        }
        Set<SceneCodeAndServiceId> sceneCodeAndServiceIds = new HashSet<>();
        for (FeeTemplateMapping mapping : mappings) {
            FeeTemplateMapping.TemplateInfo target = mapping.getTarget();
            SceneCodeAndServiceId sceneCodeAndServiceId = new SceneCodeAndServiceId(target.getSceneCode()
                    , target.getServiceId());
            sceneCodeAndServiceIds.add(sceneCodeAndServiceId);
        }
        return sceneCodeAndServiceIds;
    }


    private List<FeeTemplateMapping> getFeeTemplateMappingList(String sceneCode, String targetSceneCode, String serviceId) {
        Criteria mappingCriteria = Criteria.where(FeeTemplateMapping.Fields.source + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.sceneCode).is(sceneCode)
                .and(FeeTemplateMapping.Fields.applyTypes).is(ApplyTypeEnum.PRICING.getCode())
                .and(FeeTemplateMapping.Fields.source + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.serviceId).is(serviceId);
        if (StringUtils.isNotBlank(targetSceneCode)) {
            mappingCriteria.and(FeeTemplateMapping.Fields.target + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.sceneCode).is(targetSceneCode);
        }
        return feeTemplateMappingsBy(mappingCriteria);
    }


    private Map<Long, List<String>> getTemplateIdWithServiceIdAndSkuNoMap(List<FeeTemplateMapping> mappings) {
        Map<String, Long> serviceIdAndSkuNoWithTemplateIdMap = new HashMap<>();
        for (FeeTemplateMapping mapping : mappings) {
            FeeTemplateMapping.TemplateInfo source = mapping.getSource();
            FeeTemplateMapping.TemplateInfo target = mapping.getTarget();
            Long sourceTemplateId = source.getTemplateId();
            String sourceTemplateIdStr = sourceTemplateId == null ? "null" : sourceTemplateId.toString();
            // 因为当前查询已经通过sceneCode过滤了，所以不需要带上sceneCode
            // 结构：serviceId#skuNo#sourceTemplateId#toSkuNumberPathNo#fromSkuNumberPathNo
            serviceIdAndSkuNoWithTemplateIdMap.put(
                    source.getServiceId() + "#"
                            + source.getSkuNo() + "#"
                            + sourceTemplateIdStr + "#"
                            + source.getSkuNumberPathNo() + "#"
                            + target.getSkuNumberPathNo(),

                    target.getTemplateId());
        }
        Map<Long, List<String>> templateIdWithServiceIdAndSkuNoMap = new HashMap<>();
        for (Map.Entry<String, Long> entry : serviceIdAndSkuNoWithTemplateIdMap.entrySet()) {
            Long templateId = entry.getValue();
            List<String> serviceIdAndSkuNoList = templateIdWithServiceIdAndSkuNoMap.computeIfAbsent(templateId, k -> new ArrayList<>());
            serviceIdAndSkuNoList.add(entry.getKey());
        }
        return templateIdWithServiceIdAndSkuNoMap;
    }


    private List<FeeTemplate> getFeeTemplateList(ServiceDTO serviceDTO, Set<SceneCodeAndServiceId> sceneCodeAndServiceIds) {
        if (CollectionUtils.isEmpty(sceneCodeAndServiceIds)) {
            return Collections.emptyList();
        }
        List<Criteria> criteriaList = new ArrayList<>();
        for (SceneCodeAndServiceId sceneCodeAndServiceId : sceneCodeAndServiceIds) {
            String sceneCode = sceneCodeAndServiceId.getSceneCode();
            String serviceId = sceneCodeAndServiceId.getServiceId();
            Criteria criteria = new Criteria(FeeTemplate.Fields.sceneCode).is(sceneCode)
                    .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId);
            criteriaList.add(criteria);
        }
        Criteria feeTemplateCriteria = new Criteria().orOperator(criteriaList.toArray(new Criteria[0]))
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).ne(null);
        List<FeeTemplate> feeTemplates = feeTemplatesBy(feeTemplateCriteria);
        SceneCodeAndServiceId sceneCodeAndServiceId = sceneCodeAndServiceIds.iterator().next();
        if (CollectionUtils.isEmpty(feeTemplates)) {
            throw new BusException(ExceptionEnum.MAPPING_WITHOUT_TEMPLATE.code,
                    String.format("%s，serviceId=%s，sceneCode=%s",
                            ExceptionEnum.MAPPING_WITHOUT_TEMPLATE.msg,
                            sceneCodeAndServiceId.getServiceId(),
                            sceneCodeAndServiceId.getSceneCode()));
        }
        feeTemplates = checkApplyFlag(serviceDTO, feeTemplates);
        if (CollectionUtils.isEmpty(feeTemplates)) {
            throw new BusException(ExceptionEnum.MAPPING_WITHOUT_TEMPLATE_NO_APPLY.code,
                    String.format("%s，serviceId=%s，sceneCode=%s",
                            ExceptionEnum.MAPPING_WITHOUT_TEMPLATE_NO_APPLY.msg,
                            sceneCodeAndServiceId.getServiceId(),
                            sceneCodeAndServiceId.getSceneCode()));
        }
        String serviceId = serviceDTO.getServiceId().toString();
        feeTemplates.forEach(e -> e.setGroup(serviceId));
        feeTemplates.forEach(e -> e.getBizRule().put(CommonBizRule.Fields.serviceId, serviceId));
        return feeTemplates;
    }

    private List<FeeRule> getFeeRuleList(SingleStandardPricingParam param,
                                         Map<Long, List<String>> templateIdWithServiceIdAndSkuNoMap,
                                         Set<SceneCodeAndServiceId> fromSceneCodeAndServiceIds,
                                         List<FeeTemplate> feeTemplates) {
        List<FeeRule> feeRuleList;
        Criteria feeRuleCondition = getFeeRuleConditionByTemplateIds(param, fromSceneCodeAndServiceIds);
        feeRuleList = feeRulesBy(feeRuleCondition);
        if (CollectionUtils.isEmpty(feeRuleList)) {
            throw new BusException(ExceptionEnum.INCOMPLETE_RULE_MAPPING.code,
                    String.format("%s，serviceId=%s，sceneCode=%s",
                            ExceptionEnum.INCOMPLETE_RULE_MAPPING.msg,
                            param.getServiceDto().getServiceId(),
                            param.getSceneCode()));
        }
        feeRuleList = filterFeeRuleByDivisionType(feeRuleList);

        List<FeeRule> needSupplementFeeRuleList = new ArrayList<>();
        for (FeeRule feeRule : feeRuleList) {
            Long templateId = feeRule.getTemplateId();
            List<String> serviceIdAndSkuNoList = templateIdWithServiceIdAndSkuNoMap.get(templateId);
            if (CollectionUtils.isEmpty(serviceIdAndSkuNoList)) {
                continue;
            }
            for (int i = 0; i < serviceIdAndSkuNoList.size(); i++) {
                if (i == 0) {
                    replaceKeyAttributes(feeRule, serviceIdAndSkuNoList, i);
                } else {
                    feeRule.setCreateTime(null);
                    feeRule.setModifyTime(null);
                    FeeRule newFeeRule = new Gson().fromJson(JSON.toJSONString(feeRule), FeeRule.class);
                    replaceKeyAttributes(newFeeRule, serviceIdAndSkuNoList, i);
                    needSupplementFeeRuleList.add(newFeeRule);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(needSupplementFeeRuleList)) {
            feeRuleList.addAll(needSupplementFeeRuleList);
        }
        List<String> skuNoList = feeRuleList.stream().filter(e -> "true".equals(e.getBizRule().get(sameSkuNoNeedRemoveFlag)))
                .map(e -> e.getBizRule().get(CommonBizRule.Fields.skuNo)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(skuNoList)) {
            for (String skuNo : skuNoList) {
                List<FeeRule> needRemoveFeeRuleList = feeRuleList.stream().filter(feeRule -> skuNo.equals(feeRule.getBizRule().get(CommonBizRule.Fields.skuNo))
                        && feeRule.getBizRule().get(sameSkuNoNeedRemoveFlag) == null).collect(Collectors.toList());
                feeRuleList.removeAll(needRemoveFeeRuleList);
                Set<Long> needRemoveTemplateIds = needRemoveFeeRuleList.stream().map(FeeRule::getTemplateId).collect(Collectors.toSet());
                feeTemplates.removeIf(e -> needRemoveTemplateIds.contains(e.getTemplateId()));
            }
        }
        return feeRuleList;
    }

    private void replaceKeyAttributes(FeeRule feeRule, List<String> serviceIdAndSkuNoList, int i) {
        CalculateRuleData calculateRuleData = feeRule.getCalculateRuleData();
        // 结构：serviceId#skuNo#toTemplateId#toSkuNumberPathNo#fromSkuNumberPathNo
        String serviceIdAndSkuNo = serviceIdAndSkuNoList.get(i);
        String[] split = serviceIdAndSkuNo.split("#");
        String toServiceId = split[0];
        String toSkuNo = split[1];
        String toSkuNumberPathNo = split[3];
        String fromSkuNumberPathNo = split[4];
        Map<String, String> bizRule = feeRule.getBizRule();
        feeRule.setGroup(toServiceId);
        bizRule.put(CommonBizRule.Fields.serviceId, toServiceId);
        bizRule.put(CommonBizRule.Fields.group, toServiceId);
        bizRule.put(CommonBizRule.Fields.skuNo, toSkuNo);
        bizRule.put(CommonBizRule.Fields.skuNumberPathNo, toSkuNumberPathNo);
        bizRule.put(sameSkuNoNeedRemoveFlag, "true");
        String express = calculateRuleData.getExpress().replace(fromSkuNumberPathNo, toSkuNumberPathNo);
        calculateRuleData.setExpress(express);

        // 替换参数
        List<String> expressionParamList = calculateRuleData.getExpressionParamList();
        if (CollectionUtils.isNotEmpty(expressionParamList)) {
            for (int j = 0; j < expressionParamList.size(); j++) {
                String param = expressionParamList.get(j);
                if (fromSkuNumberPathNo.equals(param)) {
                    expressionParamList.set(j, toSkuNumberPathNo);
                }
            }
        }
        // 不应该在bizRule中冗余expression
        String bizRuleExpression = bizRule.get("expression");
        if (StringUtils.isNotBlank(bizRuleExpression)) {
            String expression = bizRuleExpression.replace(fromSkuNumberPathNo, toSkuNumberPathNo);
            bizRule.put("expression", expression);
        }
    }

    private AddressInfo completeAddress(CoreAddressInfo coreAddressInfo) {
        AddressInfo addressInfo = new AddressInfo();
        Long divisionId = coreAddressInfo.getDivisionId();
        String redisKey = this.getClass().getName() + ":completeAddress:divisionId=" + divisionId;
        String addressStr = redisHelper.get(redisKey);
        Address address;
        if (StringUtils.isNotBlank(addressStr)) {
            address = JSON.parseObject(addressStr, Address.class);
        } else {
            address = addressApi.getDivisionInfoByDivisionId(divisionId);
            if (address == null) {
                throw new BusException(StrUtil.format("divisionId={}有误", divisionId));
            }
            redisHelper.set(redisKey, JSON.toJSONString(address), 7 * 24 * 60 * 60);
        }

        addressInfo.setDivisionId(divisionId);
        Long lv1DivisionId = address.getLv1DivisionId();
        if (lv1DivisionId == null || lv1DivisionId.equals(0L)) {
            addressInfo.setLv1DivisionId(null);
        } else {
            addressInfo.setLv1DivisionId(lv1DivisionId);
            addressInfo.setLv1DivisionName(address.getLv1DivisionName());
        }

        Long lv2DivisionId = address.getLv2DivisionId();
        if (lv2DivisionId == null || lv2DivisionId.equals(0L)) {
            addressInfo.setLv2DivisionId(null);
        } else {
            addressInfo.setLv2DivisionId(lv2DivisionId);
            addressInfo.setLv2DivisionName(address.getLv2DivisionName());
        }

        Long lv3DivisionId = address.getLv3DivisionId();
        if (lv3DivisionId == null || lv3DivisionId.equals(0L)) {
            addressInfo.setLv3DivisionId(null);
        } else {
            addressInfo.setLv3DivisionId(lv3DivisionId);
            addressInfo.setLv3DivisionName(address.getLv3DivisionName());
        }

        Long lv4DivisionId = address.getLv4DivisionId();
        if (lv4DivisionId == null || lv4DivisionId.equals(0L)) {
            addressInfo.setLv4DivisionId(null);
        } else {
            addressInfo.setLv4DivisionId(lv4DivisionId);
            addressInfo.setLv4DivisionName(address.getLv4DivisionName());
        }

        Long lv5DivisionId = address.getLv5DivisionId();
        if (lv5DivisionId == null || lv5DivisionId.equals(0L)) {
            addressInfo.setLv5DivisionId(null);
        } else {
            addressInfo.setLv5DivisionId(lv5DivisionId);
            addressInfo.setLv5DivisionName(address.getLv5DivisionName());
        }
        return addressInfo;
    }

    private BigDecimal getBasePrice(List<FeeRule> feeRuleList) {
        return feeRuleList.stream()
                .map(f -> f.getBizRule().get(CommonBizRule.Fields.basePrice))
                .filter(Objects::nonNull)
                .map(BigDecimal::new)
                .max(BigDecimal::compareTo).orElse(null);
    }

    private List<FeeRule> filterFeeRuleByDivisionType(List<FeeRule> feeRuleList) {
        if (CollectionUtils.isEmpty(feeRuleList)) {
            return feeRuleList;
        }
        Map<String, FeeRule> ruleMap = new HashMap<>();
        for (FeeRule feeRule : feeRuleList) {
            Long templateId = feeRule.getTemplateId();
            String group = feeRule.getGroup();
            String key = templateId + "_" + group;
            if (!ruleMap.containsKey(key) || compareFeeRuleByDivisionType(feeRule, ruleMap.get(key)) > 0) {
                ruleMap.put(key, feeRule);
            }
        }
        return new ArrayList<>(ruleMap.values());
    }


    private int compareFeeRuleByDivisionType(FeeRule feeRule1, FeeRule feeRule2) {
        if (feeRule1 == null || feeRule2 == null) {
            return 0;
        }
        String divisionType1 = feeRule1.getBizRule().get(BizFieldEnum.DIVISION_TYPE.name);
        String divisionType2 = feeRule2.getBizRule().get(BizFieldEnum.DIVISION_TYPE.name);

        List<String> priorityList = Arrays.asList(
                DivisionTypeEnum.COUNTRY.code,
                DivisionTypeEnum.PROVINCE.code,
                DivisionTypeEnum.CITY.code,
                DivisionTypeEnum.DISTRICT.code,
                DivisionTypeEnum.STREET.code);

        return priorityList.indexOf(divisionType1) - priorityList.indexOf(divisionType2);
    }


    private List<Map<String, String>> getSkuNumberParamMapList(List<AttributeDTO> attributeDTOList, List<String> numberExpressParamList) {
        if (CollectionUtils.isEmpty(numberExpressParamList) || CollectionUtils.isEmpty(attributeDTOList)) {
            return null;
        }
        Map<String, List<String>> paramMap = new HashMap<>();
        for (String numberExpressParam : numberExpressParamList) {
            for (AttributeDTO attribute : attributeDTOList) {
                List<AttributeValueDTO> valueInfoList =  attribute.getAttributeValueDtoList();
                extractNumberValue(numberExpressParam, valueInfoList, paramMap);
            }
        }

        boolean validate = numberExpressParamValueMapIntegrityCheck(paramMap);
        if (!validate) {
            throw new BusException(ExceptionEnum.ORDER_CONTEXT_INCOMPLETE_SKU_NUMBER.code,
                    String.format("%s", ExceptionEnum.ORDER_CONTEXT_INCOMPLETE_SKU_NUMBER.msg));
        }

        return mapToList(paramMap);
    }

    private void extractNumberValue(String numberExpressParam, List<AttributeValueDTO> attributeValueDTOList, Map<String, List<String>> paramMap) {
        if (CollectionUtils.isNotEmpty(attributeValueDTOList)) {
            for (AttributeValueDTO serviceAttributeValue : attributeValueDTOList) {
                String attributePathNo = serviceAttributeValue.getAttributePathNo();
                if (numberExpressParam.equals(attributePathNo)) {
                    String value = serviceAttributeValue.getValue();
                    if (value != null) {
                        value = value.trim();
                    }
                    List<String> paramValueList = paramMap.get(numberExpressParam);
                    if (CollectionUtils.isEmpty(paramValueList)) {
                        paramValueList = new ArrayList<>();
                        paramMap.put(numberExpressParam, paramValueList);
                    }
                    paramValueList.add(value);
                }
                List<AttributeDTO> childAttributeList = serviceAttributeValue.getAttributeDtoList();
                if (CollectionUtils.isNotEmpty(childAttributeList)) {
                    for (AttributeDTO serviceAttribute : childAttributeList) {
                        List<AttributeValueDTO> valueChildList = serviceAttribute.getAttributeValueDtoList();
                        if (CollectionUtils.isNotEmpty(valueChildList)) {
                            extractNumberValue(numberExpressParam, valueChildList, paramMap);
                        }
                    }
                }
            }
        }
    }


    /**
     * 校验计价数量参数值完整性
     */
    private boolean numberExpressParamValueMapIntegrityCheck(Map<String, List<String>> numberExpressParamMapList) {

        if (numberExpressParamMapList.isEmpty()) {
            return true;  // 如果列表为空，则认为长度相等
        }

        int size = -1;  // 初始化参考长度为 -1

        for (List<String> stringList : numberExpressParamMapList.values()) {
            if (size == -1) {
                size = stringList.size();  // 第一次迭代时，将当前列表长度作为参考长度
            } else if (stringList.size() != size) {
                return false;  // 如果当前列表长度与参考长度不相等，则返回 false
            }
        }

        return true;  // 所有列表长度相等
    }


    private List<Map<String, String>> mapToList(Map<String, List<String>> inputMap) {
        List<Map<String, String>> outputList = new ArrayList<>();

        int maxSize = getMaxListSize(inputMap);

        for (int i = 0; i < maxSize; i++) {
            Map<String, String> map = new HashMap<>();

            for (Map.Entry<String, List<String>> entry : inputMap.entrySet()) {
                String key = entry.getKey();
                List<String> values = entry.getValue();

                if (i < values.size()) {
                    String value = values.get(i);
                    map.put(key, value);
                }
            }
            outputList.add(map);
        }
        return outputList;
    }


    private int getMaxListSize(Map<String, List<String>> map) {
        int maxSize = 0;

        for (List<String> values : map.values()) {
            maxSize = Math.max(maxSize, values.size());
        }

        return maxSize;
    }


    private Criteria getFeeRuleCondition(String serviceId,
                                         SceneInfo sceneInfo,
                                         AddressInfo addressInfo,
                                         BizInfo bizInfo) {
        Criteria feeRuleCondition = Criteria.where(FeeRule.Fields.sceneCode).is(sceneInfo.getSceneCode());
        // 用户信息
        String bizIdType = sceneInfo.getBizIdType();
        BizIdTypeEnum bizIdTypeEnum = BizIdTypeEnum.fromCode(bizIdType);
//        if (bizIdTypeEnum != null) {
//            if (bizIdTypeEnum.equals(BizIdTypeEnum.USER_ID)) {
//                bizInfo.setUserId(accountInfo.getAccountId().toString());
//            }
//        }
        feeRuleCondition.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + PlatformFixedPriceBizRule.Fields.feeTypeTag).ne(null);
        feeRuleCondition.and(FeeRule.Fields.bizRule + PunConstant.DOT + PlatformFixedPriceBizRule.Fields.serviceId).is(serviceId);

        // 一级国家 五级街道
        if (Objects.isNull(addressInfo)) {
            throw new BusException("addressInfo is null");
        }

        DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(addressInfo.getDivisionType());
        if (Objects.isNull(divisionTypeEnum)) {
            throw new BusException("addressInfo.divisionType设置错误");
        }
        // 只需要查省、市
        // 查出 国 省 市 区 然后根据是否需要回溯选择合适的级别
        List<Criteria> criteriaList = buildFeeRuleAddressCondition(addressInfo);

        feeRuleCondition.orOperator(criteriaList.toArray(new Criteria[0]));

        Criteria criteria = setConditionByBizInfo(bizInfo, feeRuleCondition, bizIdTypeEnum);
        if (criteria != null) {
            feeRuleCondition.andOperator(criteria);
        }

        return feeRuleCondition;
    }

    private Criteria getFeeRuleConditionByTemplateIds(SingleStandardPricingParam param, Set<SceneCodeAndServiceId> fromSceneCodeAndServiceIds) {
        Criteria feeRuleCondition = Criteria.where(FeeTemplate.Fields.bizRule + PunConstant.DOT + PlatformFixedPriceBizRule.Fields.feeTypeTag).ne(null);
        SceneInfo currentSceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(param.getSceneCode());
        // 用户信息
        String bizIdType = currentSceneInfo.getBizIdType();
        BizIdTypeEnum bizIdTypeEnum = BizIdTypeEnum.fromCode(bizIdType);
//        if (bizIdTypeEnum != null) {
//            if (bizIdTypeEnum.equals(BizIdTypeEnum.USER_ID)) {
//                feeRuleCondition.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + PlatformFixedPriceBizRule.Fields.userId).is(param.getAccountInfo().getAccountId().toString());
//            }
//        }
        List<Criteria> sceneCodeAndServiceIdCriteriaList = new ArrayList<>();
        for (SceneCodeAndServiceId sceneCodeAndServiceId : fromSceneCodeAndServiceIds) {
            String sceneCode = sceneCodeAndServiceId.getSceneCode();
            String serviceId = sceneCodeAndServiceId.getServiceId();
            Criteria criteria = new Criteria(FeeTemplate.Fields.sceneCode).is(sceneCode)
                    .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId);
            sceneCodeAndServiceIdCriteriaList.add(criteria);
        }
        Criteria sceneCodeAndServiceCriteria = new Criteria().orOperator(sceneCodeAndServiceIdCriteriaList.toArray(new Criteria[0]));

        AddressInfo addressInfo = param.getAddressInfo();
        // 一级国家 五级街道
        if (Objects.isNull(addressInfo)) {
            throw new BusException("addressInfo is null");
        }

        DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(addressInfo.getDivisionType());
        if (Objects.isNull(divisionTypeEnum)) {
            throw new BusException("addressInfo.divisionType设置错误");
        }
        // 只需要查省、市
        //
        // 查出 国 省 市 区 然后根据是否需要回溯选择合适的级别
        List<Criteria> criteriaList = buildFeeRuleAddressCondition(addressInfo);
        Criteria addressCriteria = new Criteria().orOperator(criteriaList.toArray(new Criteria[0]));



        BizInfo bizInfo = param.getBizInfo();
        Criteria bizInfoCriteria = setConditionByBizInfo(bizInfo, feeRuleCondition, bizIdTypeEnum);
        if (bizInfoCriteria != null) {
            feeRuleCondition.andOperator(addressCriteria, sceneCodeAndServiceCriteria, bizInfoCriteria);
        } else {
            feeRuleCondition.andOperator(addressCriteria, sceneCodeAndServiceCriteria);
        }
        return feeRuleCondition;
    }

    private Criteria setConditionByBizInfo(BizInfo bizInfo, Criteria feeRuleCondition, BizIdTypeEnum bizIdTypeEnum) {
        if (bizInfo != null) {
            String bizTag = bizInfo.getBizTag();
            String bizId = bizInfo.getBizId();
            String userId = bizInfo.getUserId();
            String masterId = bizInfo.getMasterId();

            if (StringUtils.isNotBlank(bizTag)) {
                feeRuleCondition.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizTag).is(bizTag);
            }

            // 由于bizId既有可能是userId，也有可能是masterId，所以才会有如此怪异的代码
            if ((StringUtils.isNotBlank(bizId) || StringUtils.isNotBlank(masterId)) && BizIdTypeEnum.MASTER_ID.equals(bizIdTypeEnum)) {
                String value = StringUtils.isNotBlank(bizId) ? bizId : masterId;
                return new Criteria().orOperator(new Criteria(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterId).is(value),
                        new Criteria(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizId).is(value));
            } else if ((StringUtils.isNotBlank(bizId) || StringUtils.isNotBlank(userId)) && BizIdTypeEnum.USER_ID.equals(bizIdTypeEnum)) {
                String value = StringUtils.isNotBlank(bizId) ? bizId : userId;
                return new Criteria().orOperator(new Criteria(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.userId).is(value),
                        new Criteria(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizId).is(value));
            }
        }
        return null;
    }


    private String extractValue(List<AttributeDTO> attributeDTOS, String key) {
        for (AttributeDTO serviceAttribute : attributeDTOS) {

            List<AttributeValueDTO> attributeValueDtoList = serviceAttribute.getAttributeValueDtoList();
            if (CollectionUtils.isEmpty(attributeValueDtoList)) {
                continue;
            }

            for (AttributeValueDTO serviceAttributeValue : attributeValueDtoList) {
                if (key.equals(serviceAttributeValue.getAttributePathNo())) {
                    // 将attributePathNo置空，这样可以提取出数组的所有值
//                    serviceAttributeValue.setAttributePathNo(null);
                    return serviceAttributeValue.getValue();
                }
                List<AttributeDTO> childList = serviceAttributeValue.getAttributeDtoList();
                if (CollectionUtils.isNotEmpty(childList)) {
                    String value = extractValue(childList, key);
                    if (com.wanshifu.framework.utils.StringUtils.isNotBlank(value)) {
//                        serviceAttributeValue.setAttributePathNo(null);
                        return value;
                    }
                }
            }
        }
        return null;
    }


    private int countSkuNo(List<AttributeDTO> serviceAttributes, String skuNo) {
        if (CollectionUtils.isEmpty(serviceAttributes) || StringUtils.isBlank(skuNo)) {
            return 0;
        }

        int count = 0;

        for (AttributeDTO serviceAttribute : serviceAttributes) {
            if (serviceAttribute != null) {
                List<AttributeValueDTO> childList = serviceAttribute.getAttributeValueDtoList();
                if (CollectionUtils.isNotEmpty(childList)) {
                    for (AttributeValueDTO serviceAttributeValue : childList) {
                        if (serviceAttributeValue != null) {
                            if (skuNo.equals(serviceAttributeValue.getAttributePathNo())) {
                                count++;
                            }
                            List<AttributeDTO> grandChildList = serviceAttributeValue.getAttributeDtoList();
                            if (CollectionUtils.isNotEmpty(grandChildList)) {
                                count += countSkuNo(grandChildList, skuNo); // 递归调用
                            }
                        }
                    }
                }
            }
        }

        return count;
    }


    protected List<Criteria> buildFeeRuleAddressCondition(AddressInfo addressInfo) {
        DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(addressInfo.getDivisionType());
        boolean needSearchParent = addressInfo.isNeedSearchParent();
        if (Objects.isNull(divisionTypeEnum)) {
            throw new BusException("addressInfo.divisionType设置错误");
        }
        Long province = addressInfo.getLv2DivisionId();
        // 部分特殊地区市区可以互换
        // 最新地址转换逻辑：①若无城市，则用区县代替；②若无区县，则用街道代替
        // 市
        Long city = Objects.isNull(addressInfo.getLv3DivisionId()) ? addressInfo.getLv4DivisionId() : addressInfo.getLv3DivisionId();
        // 区
//        Long district = Objects.isNull(addressInfo.getLv4DivisionId()) ? addressInfo.getLv3DivisionId() : addressInfo.getLv4DivisionId();
        Long district = Objects.isNull(addressInfo.getLv4DivisionId()) ? addressInfo.getLv5DivisionId() : addressInfo.getLv4DivisionId();
        // 街道
        List<Criteria> criteriaList = new ArrayList<>();
        Long street = addressInfo.getLv5DivisionId();
        if ((divisionTypeEnum == DivisionTypeEnum.STREET)) {
            if (Objects.nonNull(street)) {
                Criteria streetCondition = Criteria.where(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.divisionType).is(DivisionTypeEnum.STREET.code).and(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level4DivisionId).is(street.toString());
                criteriaList.add(streetCondition);
            }
        }

        if ((divisionTypeEnum == DivisionTypeEnum.DISTRICT) || (divisionTypeEnum == DivisionTypeEnum.STREET && needSearchParent)) {
            if (Objects.nonNull(district)) {
                Criteria districtCondition = Criteria.where(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.divisionType).is(DivisionTypeEnum.DISTRICT.code).and(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level3DivisionId).is(district.toString());
                criteriaList.add(districtCondition);
            }
        }

        if ((divisionTypeEnum == DivisionTypeEnum.CITY) || ((divisionTypeEnum == DivisionTypeEnum.STREET || divisionTypeEnum == DivisionTypeEnum.DISTRICT) && needSearchParent)) {
            if (Objects.nonNull(city)) {
                Criteria cityCondition = Criteria.where(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.divisionType).is(DivisionTypeEnum.CITY.code).and(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level2DivisionId).is(city.toString());
                criteriaList.add(cityCondition);
            }
        }

        if ((divisionTypeEnum == DivisionTypeEnum.PROVINCE) || ((divisionTypeEnum == DivisionTypeEnum.STREET || divisionTypeEnum == DivisionTypeEnum.DISTRICT || divisionTypeEnum == DivisionTypeEnum.CITY) && needSearchParent)) {
            if (Objects.nonNull(province)) {
                Criteria provinceCondition = Criteria.where(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.divisionType).is(DivisionTypeEnum.PROVINCE.code).and(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level1DivisionId).is(province.toString());
                criteriaList.add(provinceCondition);
            }
        }

        if ((divisionTypeEnum == DivisionTypeEnum.COUNTRY) || ((divisionTypeEnum == DivisionTypeEnum.STREET || divisionTypeEnum == DivisionTypeEnum.DISTRICT || divisionTypeEnum == DivisionTypeEnum.CITY || divisionTypeEnum == DivisionTypeEnum.PROVINCE) && needSearchParent)) {
            Criteria countryCondition = Criteria.where(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.divisionType).is(DivisionTypeEnum.COUNTRY.code);
            criteriaList.add(countryCondition);
        }

        if (CollectionUtils.isEmpty(criteriaList)) {
            log.error("构建地区条件错误，没有合适的地区, {}", addressInfo);
            throw new BusException("构建地区条件错误，没有合适的地区");
        }

        return criteriaList;
    }

    private final static String ATTRIBUTE_VALUE_MAX = "attributeValueMax";
    private final static String ATTRIBUTE_VALUE_MIN = "attributeValueMin";
    private final static String SKU_TYPE = "skuType";
    private final static String SKU_NO = "skuNo";

    protected List<FeeTemplate> checkApplyFlag(ServiceDTO serviceDTO, List<FeeTemplate> feeTemplates) {
        Map<String, List<FeeTemplate>> applyFlagMap = feeTemplates.stream()
                .filter(e -> e.getBizRule() != null
                        && e.getBizRule().get("applyFlag") != null
                        && e.getBizRule().get("applyFlag").equals(ApplyFlagEnum.NO_APPLY_IF_HAVE.code)).
                collect(Collectors.groupingBy(s -> s.getBizRule().get("skuNo")));


        // 检查是否存在不需要算费的 SKU
        if (!applyFlagMap.isEmpty()) {
            String serviceIdStr = serviceDTO.getServiceId().toString();
            for (List<FeeTemplate> templates : applyFlagMap.values()) {
                if (CollectionUtils.isNotEmpty(templates)) {
                    for (FeeTemplate template : templates) {
                        String serviceId = template.getBizRule().get(CommonBizRule.Fields.serviceId);
                        // 只判断同一服务的才有意义
                        if (!serviceIdStr.equals(serviceId)) {
                            continue;
                        }
                        String skuNo = template.getBizRule().get(SKU_NO);
                        boolean checkResult = checkTemplateSkuNo(serviceDTO.getAttributeDtoList(), template, skuNo);
                        if (checkResult) {
                            throw new BusException("存在无需算费的模板, 场景编码：" + template.getSceneCode());
                        } else {
                            feeTemplates = feeTemplates.stream().filter(e -> !e.getTemplateId().equals(template.getTemplateId())).collect(Collectors.toList());
                        }
                    }
                }
            }
        }
        return feeTemplates;
    }


    private boolean checkTemplateSkuNo(List<AttributeDTO> attributeInfoList, FeeTemplate feeRule, String skuNo) {
        for (AttributeDTO serviceAttribute : attributeInfoList) {
            List<AttributeValueDTO> valueList = serviceAttribute.getAttributeValueDtoList();
            if (CollectionUtils.isEmpty(valueList)) {
                continue;
            }
            for (AttributeValueDTO serviceAttributeValue : valueList) {

                if (skuNo.equals(serviceAttributeValue.getAttributePathNo())) {
                    String valueStr = serviceAttributeValue.getValue();

                    boolean isCustomSku = FeeSkuTypeEnum.CUSTOM_SKU.code.equals(feeRule.getBizRule().get(SKU_TYPE));
                    if (isCustomSku) {
                        String valueMaxStr = feeRule.getBizRule().get(ATTRIBUTE_VALUE_MAX);
                        String valueMinStr = feeRule.getBizRule().get(ATTRIBUTE_VALUE_MIN);
                        try {
                            BigDecimal valueMax = new BigDecimal(valueMaxStr);
                            BigDecimal valueMin = new BigDecimal(valueMinStr);
                            BigDecimal value = new BigDecimal(valueStr);
                            return value.compareTo(valueMax) <= 0 && value.compareTo(valueMin) >= 0;
                        } catch (Exception e) {
                            log.info("服务属性解析失败：无法转成数字 value={},max={},min={},e={}", valueStr, valueMaxStr, valueMinStr, e.toString());
                            return false;
                        }
                    }
                    return true;
                }
                List<AttributeDTO> serviceAttributeList = serviceAttributeValue.getAttributeDtoList();
                if (CollectionUtils.isNotEmpty(serviceAttributeList)) {
                    boolean checkSkuNo = checkTemplateSkuNo(serviceAttributeList, feeRule, skuNo);
                    if (checkSkuNo) {
                        return true;
                    }
                }
            }
        }
        return false;
    }


    protected List<FeeTemplate> feeTemplatesBy(Criteria criteria) {
        criteria.and(BaseDocument.Fields.status).is(0).and(BaseDocument.Fields.del).is(false);
        return mongoTemplate.find(Query.query(criteria), FeeTemplate.class);
    }

    protected List<FeeRule> feeRulesBy(Criteria criteria) {
        criteria.and(BaseDocument.Fields.status).is(0).and(BaseDocument.Fields.del).is(false);
        return mongoTemplate.find(Query.query(criteria), FeeRule.class);
    }


    protected List<FeeTemplateMapping> feeTemplateMappingsBy(Criteria criteria) {
        criteria.and(BaseDocument.Fields.status).is(MappingStatusEnum.PASS.code).and(BaseDocument.Fields.del).is(false);
        return mongoTemplate.find(Query.query(criteria), FeeTemplateMapping.class);
    }

    protected List<DynamicFeeRule> dynamicFeeRuleBy(Criteria criteria) {
        criteria.and(BaseDocument.Fields.status).is(DynamicFeeRuleStatusEnum.PASS.code).and(BaseDocument.Fields.del).is(false);
        return mongoTemplate.find(Query.query(criteria), DynamicFeeRule.class);
    }

    public List<FeeTemplate> getFeeTemplates(String serviceId, ServiceDTO serviceDTO, String sceneCode) {
        Criteria feeTemplateCondition = Criteria.where(FeeTemplate.Fields.sceneCode).is(sceneCode)
                .and(FeeTemplate.Fields.group).ne(null)
                .orOperator(
                        Criteria.where(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.feeTypeTag).ne(null),
                        Criteria.where(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.applyFlag).is(ApplyFlagEnum.NO_APPLY_IF_HAVE.code))
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.serviceId).is(serviceId);
        List<FeeTemplate> feeTemplates = feeTemplatesBy(feeTemplateCondition);
        if (CollectionUtils.isEmpty(feeTemplates)) {
            throw new BusException("模板为空");
        }

        feeTemplates = checkApplyFlag(serviceDTO, feeTemplates);
        return feeTemplates;
    }

    /**
     * 公开报价基础服务费需要补齐
     */
    private static final AttributeDTO BASE_SERVICE_FEE = JSON.parseObject("{\n" +
            "    \"attributeKey\": \"base_service_fee\",\n" +
            "    \"attributeId\": 45001776,\n" +
            "    \"attributeValueDtoList\": [\n" +
            "        {\n" +
            "            \"attributeValueId\": 48014958,\n" +
            "            \"attributePathNo\": \"AP48014959\",\n" +
            "            \"attributeDtoList\": null,\n" +
            "            \"value\": \"基础服务费默认值\"\n" +
            "        }\n" +
            "    ]\n" +
            "}", AttributeDTO.class);

    protected void completeServiceAttribute(ServiceDTO ServiceDTO) {
        boolean haveBaseServiceFee = ServiceDTO.getAttributeDtoList().stream().anyMatch(serviceAttribute -> serviceAttribute.getAttributeKey().equals("base_service_fee"));
        if (!haveBaseServiceFee) {
            log.info("补全基础服务元素");
            ServiceDTO.getAttributeDtoList().add(BASE_SERVICE_FEE);
        }
    }

    protected <T> ValueExtractor<T> getSuiteValueExtractor(Class<T> clazz) {
        ResolvableType paramType = ResolvableType.forClass(clazz);
        for (ValueExtractor<?> orderInfoConvertor : valueExtractors) {
            ResolvableType nowType = ResolvableType.forInstance(orderInfoConvertor);
            ResolvableType[] generics = nowType.as(ValueExtractor.class).getGenerics();
            if (Arrays.stream(generics).anyMatch(paramType::isAssignableFrom)) {
                return (ValueExtractor<T>) orderInfoConvertor;
            }
        }
        throw new BusException("未找到合适的取值器");
    }


    protected ApplyCalculateResp calculate(List<ApplyCalculateReq.CalculateRuleDataUnit> calculateRuleDataUnits,
                                           List<FeeRule> feeRules,
                                           Map<String, String> cache,
                                           Long tid,
                                           ServiceDTO serviceDTO,
                                           String sceneCode,
                                           Date dynamicCalculateTime) {

        Map<Long, FeeRule> feeRuleMap = feeRules.stream().collect(Collectors.toMap(FeeRule::getFeeRuleId, Function.identity(), (i, j) -> i));

        List<CalculateResult> calculateResultList = new ArrayList<>(calculateRuleDataUnits.size());
        // 应用规则
        Date now = new Date();
        boolean haveServiceFee = false;
        for (ApplyCalculateReq.CalculateRuleDataUnit calculateRuleDataUnit : calculateRuleDataUnits) {

            List<Map<String, String>> expressionParamMapList = calculateRuleDataUnit.getExpressionParamMapList();
            if (CollectionUtils.isEmpty(expressionParamMapList)) {
                expressionParamMapList = new ArrayList<>();
                expressionParamMapList.add(calculateRuleDataUnit.getExpressionParamMap());
            }
            for (Map<String, String> paramMap : expressionParamMapList) {
                CalculateResult calculateResult = new CalculateResult();

                Long calculateResultId = SnowFlakeGenerator.INSTANCE.generate();
                calculateResult.setCalculateResultId(calculateResultId);
                calculateResult.setSceneCode(sceneCode);
                calculateResult.setFeeRuleId(calculateRuleDataUnit.getFeeRuleId());
                calculateResult.setTemplateId(calculateRuleDataUnit.getTemplateId());
                calculateResult.setDel(false);
                calculateResult.setStatus(ResultStatusEnum.ACTIVE.code);
                calculateResult.setCreateTime(now);
                calculateResult.setModifyTime(now);
                calculateResult.setTid(tid);

                FeeRule feeRule = feeRuleMap.get(calculateResult.getFeeRuleId());
                Map<String, String> bizRule = feeRule.getBizRule();
                String feeTypeTag = bizRule.get(CommonBizRule.Fields.feeTypeTag);
                if (com.wanshifu.framework.utils.StringUtils.isNotBlank(feeTypeTag) && FeeTypeTagEnum.SERVICE_FEE.code.equals(feeTypeTag)) {
                    haveServiceFee = true;
                }
                calculateResult.setBizRule(bizRule);
                setSkuAttributePathId(bizRule);

                // 算费规则
                CalculateRuleData calculateRuleData = new CalculateRuleData();
                CalculateRuleData feeRuleCalculateRuleData = feeRule.getCalculateRuleData();
                BeanUtils.copyProperties(feeRuleCalculateRuleData, calculateRuleData);
                calculateRuleData.setExpressionParamMap(paramMap);
                calculateRuleData.setExpressionParamMapList(expressionParamMapList);
                calculateResult.setCalculateRuleData(calculateRuleData);
                // 算费
                BigDecimal cost = BigDecimal.ZERO;
                BigDecimal costMax = BigDecimal.ZERO;
                BigDecimal price = null;
                BigDecimal priceMax = null;
                // 编译 , 缓存;
                DefaultContext<String, Object> params = new DefaultContext<>();
                DefaultContext<String, Object> priceMaxParams = new DefaultContext<>();
                for (Map.Entry<String, String> stringStringEntry : paramMap.entrySet()) {
                    try {
                        BigDecimal bigDecimal = new BigDecimal(stringStringEntry.getValue());
                        params.put(stringStringEntry.getKey(), bigDecimal);
                        if ("masterInputPrice".equals(stringStringEntry.getKey())) {
                            price = bigDecimal;
                        }
                    } catch (Exception e) {
                        params.put(stringStringEntry.getKey(), stringStringEntry.getValue());
                    }
                }
                List<String> errorList = new ArrayList<>();
                try {
                    String masterInputPriceMax = bizRule.get(CommonBizRule.Fields.masterInputPriceMax);
                    String amountType = cache.get(SceneInfo.Fields.amountType);

                    if (Objects.isNull(feeRuleCalculateRuleData.getExpressInfo())) {
                        String str = expressRunner.execute(feeRuleCalculateRuleData.getExpress(), params, errorList, true, false, 1500).toString();
                        cost = new BigDecimal(str);
                        if (StringUtils.isNotBlank(masterInputPriceMax) && AmountTypeEnum.RANGE_PRICE.code.equals(amountType)) {
                            priceMaxParams.putAll(params);
                            // 用单价max替换单价，带入脚本计算出单价max的价格
                            priceMax = new BigDecimal(masterInputPriceMax);
                            priceMaxParams.put(CommonBizRule.Fields.masterInputPrice, priceMax);
                            String strMax = expressRunner.execute(feeRuleCalculateRuleData.getExpress(), priceMaxParams, errorList, true, false, 1500).toString();
                            costMax = new BigDecimal(strMax);
                        } else {
                            priceMax = price;
                            costMax = cost;
                        }
                    } else {
                        // 如果是复杂公式（即有各种条件的）则只直接返回
                        ExpressResultInfo resultInfo = (ExpressResultInfo) expressRunner.execute(feeRuleCalculateRuleData.getExpress(), params, errorList, true, false, 1500);
                        cost = resultInfo.getCost();
                        if (Objects.nonNull(calculateResult.getBizRule())) {
                            calculateResult.getBizRule().putIfAbsent("expressNumber", resultInfo.getNumber().toString());
                            calculateResult.getBizRule().putIfAbsent("expressPrice", resultInfo.getPrice().toString());
                            price = resultInfo.getPrice();
                        }

                        // 复杂公式。 计价场景的“金额类型”为“区间价”的时候，才需要计算 最大价
                        if (com.wanshifu.framework.utils.StringUtils.isNotBlank(masterInputPriceMax) && AmountTypeEnum.RANGE_PRICE.code.equals(amountType)) {
                            priceMaxParams.putAll(params);
                            // 用单价max替换单价，带入脚本计算出单价max的价格
                            priceMax = new BigDecimal(masterInputPriceMax);
                            priceMaxParams.put(CommonBizRule.Fields.masterInputPrice, priceMax);
                            ExpressResultInfo maxResultInfo = (ExpressResultInfo) expressRunner.execute(feeRuleCalculateRuleData.getExpress(), priceMaxParams, errorList, true, false, 1500);
                            if (maxResultInfo != null) {
                                costMax = maxResultInfo.getCost();
                            }
                        } else {
                            priceMax = price;
                            costMax = cost;
                        }
                    }

                    if (Objects.nonNull(price)) {
                        BigDecimal dynamicCost = getDynamicCost(serviceDTO, cache, feeRule, params, price, errorList, feeRuleCalculateRuleData, calculateResult, dynamicCalculateTime);
                        if (dynamicCost != null) {
                            calculateResult.setDynamicFeeCost(dynamicCost.setScale(2, RoundingMode.HALF_UP));
                        }
                    }
                    if (Objects.nonNull(priceMax) && !priceMaxParams.isEmpty()) {
                        BigDecimal dynamicCostMax = getDynamicCost(serviceDTO, cache, feeRule, priceMaxParams, priceMax, errorList, feeRuleCalculateRuleData, calculateResult, dynamicCalculateTime);
                        if (dynamicCostMax != null) {
                            calculateResult.setDynamicFeeCostMax(dynamicCostMax.setScale(2, RoundingMode.HALF_UP));
                        }
                    }
                } catch (Exception e) {
                    log.error("计算异常: tid={}, sceneCode={}, feeRuleId={} , log={} , express={}, params={}, ",
                            tid, feeRule.getSceneCode(), feeRule.getFeeRuleId(), e.getMessage(), feeRuleCalculateRuleData.getExpress(), params);
                    throw new BusException(ExceptionEnum.EXPRESSION_ERROR.code,
                            String.format("%s，serviceId=%s，templateId=%s，bizId=%s，sceneCode=%s",
                                    ExceptionEnum.EXPRESSION_ERROR.msg, serviceDTO.getServiceId(), feeRule.getTemplateId(),
                                    bizRule.get(CommonBizRule.Fields.bizId), feeRule.getSceneCode()));
                }

                if (CollectionUtils.isNotEmpty(errorList)) {
                    log.error("计算错误: tid:{}, sceneCode={}, feeRuleId={}  , errorList={}",
                            tid, feeRule.getSceneCode(), feeRule.getFeeRuleId(),  errorList);
                    throw new BusException("计算错误, errorList not empty");
                }
                if (cost != null) {
                    calculateResult.setCost(cost.setScale(2, RoundingMode.HALF_UP));
                }
                calculateResult.setCostMax(costMax.setScale(2, RoundingMode.HALF_UP));
                // 产品要求：如果计算结果<=0，则不返回该项明细
                if (BigDecimal.ZERO.compareTo(cost) < 0) {
                    calculateResultList.add(calculateResult);
                }
            }

        }

        if (!haveServiceFee) {
            throw new BusException("订单上下文没有【服务费】，serviceId=" + serviceDTO.getServiceId() + "，场景编码=" + sceneCode);
        }
        // 计算总价
        BigDecimal sumCost = BigDecimal.ZERO;
        BigDecimal sumCostMax = BigDecimal.ZERO;
        BigDecimal sumDynamicFeeCost = BigDecimal.ZERO;
        BigDecimal sumDynamicFeeCostMax = BigDecimal.ZERO;
        for (CalculateResult calculateResult : calculateResultList) {
            BigDecimal cost = calculateResult.getCost();
            BigDecimal costMax = calculateResult.getCostMax();
            BigDecimal dynamicFeeCost = calculateResult.getDynamicFeeCost();
            BigDecimal dynamicFeeCostMax = calculateResult.getDynamicFeeCostMax();

            sumCost = sumCost.add(calculateResult.getCost());
            sumCostMax = sumCostMax.add(Objects.nonNull(costMax) ? costMax : cost);
            sumDynamicFeeCost = sumDynamicFeeCost.add(Objects.nonNull(dynamicFeeCost) ? dynamicFeeCost : cost);
            sumDynamicFeeCostMax = sumDynamicFeeCostMax.add(Objects.nonNull(dynamicFeeCostMax) ? dynamicFeeCostMax : costMax);
        }
        ApplyCalculateResp applyCalculateResp = new ApplyCalculateResp();
        applyCalculateResp.setCalculateResultList(calculateResultList);
        applyCalculateResp.setCost(sumCost);
        applyCalculateResp.setCostMax(sumCostMax);
        applyCalculateResp.setDynamicFeeCost(sumDynamicFeeCost);
        applyCalculateResp.setDynamicFeeCostMax(sumDynamicFeeCostMax);
        applyCalculateResp.setTid(tid);
        return applyCalculateResp;
    }

    private BigDecimal getDynamicCost(ServiceDTO serviceDTO,
                                      Map<String, String> cache,
                                      FeeRule feeRule,
                                      DefaultContext<String, Object> params,
                                      BigDecimal price,
                                      List<String> errorList,
                                      CalculateRuleData feeRuleCalculateRuleData,
                                      CalculateResult calculateResult,
                                      Date dynamicCalculateTime) throws Exception {
        List<DynamicFeeRuleCalculateRuleData> dynamicRuleList = feeRule.getDynamicFeeRuleCalculateRuleDataList();
        if (CollectionUtils.isEmpty(dynamicRuleList)) {
            log.info("没有合适的调价规则");
            return null;
        } else {
            BigDecimal dynamicCost = null;
            for (DynamicFeeRuleCalculateRuleData ruleData : dynamicRuleList) {
                DynamicFeeRuleCalculateRuleData.MatchingRule matchingRule = ruleData.getMatchingRule();
                Date endTime = matchingRule.getEndTime();
                Date startTime = matchingRule.getStartTime();
                if (Objects.nonNull(startTime) && Objects.nonNull(endTime) && Objects.nonNull(dynamicCalculateTime)
                        && dynamicCalculateTime.compareTo(startTime) >= 0 && dynamicCalculateTime.compareTo(endTime) <= 0) {
                    // 计算调价费用结果
                    DynamicFeeRuleCalculateRuleData.CalculateData unitPrice = ruleData.getUnitPrice();
                    DynamicFeeRuleCalculateRuleData.CalculateData skuPrice = ruleData.getSkuPrice();
                    params.put(DynamicCalculateRuleData.ORIGIN_PRICE, price.doubleValue());
                    String adjustFee = expressRunner.execute(unitPrice.getExpress(), params, errorList, true, false, 1500).toString();
                    BigDecimal adjustFeePrice = new BigDecimal(adjustFee);
                    if (BigDecimal.ZERO.compareTo(adjustFeePrice) > 0) {
                        throw new BusException("调价后费用小于0");
                    }
                    params.put("masterInputPrice", adjustFeePrice);
                    if (Objects.isNull(skuPrice)) {
                        if (feeRuleCalculateRuleData.getExpressInfo() == null) {
                            String dynamicCostStr = expressRunner.execute(feeRuleCalculateRuleData.getExpress(), params, errorList, true, false, 1500).toString();
//                            calculateResult.setDynamicFeeCost(new BigDecimal(dynamicCostStr));
                            dynamicCost = new BigDecimal(dynamicCostStr);
                        } else {
                            ExpressResultInfo expressResultInfo = (ExpressResultInfo) expressRunner.execute(feeRuleCalculateRuleData.getExpress(), params, errorList, true, false, 1500);
//                            calculateResult.setDynamicFeeCost(expressResultInfo.getCost());
                            dynamicCost = expressResultInfo.getCost();
                        }
                    } else {
                        List<String> expressionParamList = skuPrice.getExpressionParamList();
                        if (CollectionUtils.isNotEmpty(expressionParamList)) {
                            for (String expressParam : expressionParamList) {
                                ExpressParamEnum expressParamEnum = ExpressParamEnum.fromCode(expressParam);
                                if (Objects.nonNull(expressParamEnum)) {
                                    String value = serviceAttributeValueExtractor.extract(serviceDTO, feeRule, cache, expressParamEnum);
                                    // ALL_SERVICE_AP_SKU_NUMBER与AP_SKU_PRICE需要作为数据参数 参与脚本计算
                                    if (ALL_SERVICE_AP_SKU_NUMBER.code.equals(expressParam) || AP_SKU_PRICE.code.equals(expressParam)) {
                                        params.put(expressParam, new BigDecimal(value));
                                    } else {
                                        params.put(expressParam, value);
                                    }
                                }
                            }
                            // 特殊处理，下方两个值需要取动态价
                            params.put(AP_SKU_PRICE.code, adjustFeePrice);
                            params.put(MASTER_INPUT_PRICE.code, adjustFeePrice);
                            ExpressResultInfo expressResultInfo = (ExpressResultInfo) expressRunner.execute(skuPrice.getExpress(), params, errorList, true, false, 1500);
//                            calculateResult.setDynamicFeeCost(expressResultInfo.getCost());
                            dynamicCost = expressResultInfo.getCost();
                        }
                    }
                    calculateResult.setDynamicFeeStrategyName(ruleData.getDynamicStrategyName());
                    calculateResult.setDynamicFeeRuleId(ruleData.getDynamicFeeRuleId());
                    log.info("开始计算调价费用——》dynamicFeeRule={} , params={} , adjustFee={}", ruleData, params, adjustFee);

                    // 只需处理命中的第一条即可
                    break;
                }
            }
            return dynamicCost;
        }
    }

    private void setSkuAttributePathId(Map<String, String> bizRule) {
        if (MapUtils.isNotEmpty(bizRule)) {
            String skuNo = bizRule.get(CommonBizRule.Fields.skuNo);
            if (com.wanshifu.framework.utils.StringUtils.isNotBlank(skuNo)) {
                bizRule.put("skuAttributePathId", skuNo.replace("AP", ""));
            }
        }
    }

    private BigDecimal extractOrderValue(List<ServiceDTO> serviceDtoList) {
        if (CollectionUtils.isEmpty(serviceDtoList)) {
            return null;
        }
        BigDecimal sumNumber = serviceDtoList.stream()
                .filter(serviceDTO -> CollectionUtils.isNotEmpty(serviceDTO.getAttributeDtoList()))
                .flatMap(serviceDTO -> serviceDTO.getAttributeDtoList().stream())
                .filter(attribute -> AttributeKeyEnum.goods_number.name().equals(attribute.getAttributeKey()))
                .filter(attribute -> CollectionUtils.isNotEmpty(attribute.getAttributeValueDtoList()))
                .flatMap(attribute -> attribute.getAttributeValueDtoList().stream())
                .filter(attributeValue -> StringUtils.isNotBlank(attributeValue.getValue()))
                .map(attributeValue -> new BigDecimal(attributeValue.getValue())).reduce(BigDecimal.ZERO, BigDecimal::add, BigDecimal::add);
        if (BigDecimal.ZERO.compareTo(sumNumber) < 0) {
            // 大于0 则放进去
            return sumNumber.setScale(2, RoundingMode.HALF_UP);
        } else {
            log.info("产品总数量不正常赋空null: num = {}", sumNumber);
            return null;
        }
    }
}
