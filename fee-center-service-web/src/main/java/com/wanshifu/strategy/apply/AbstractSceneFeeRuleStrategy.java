package com.wanshifu.strategy.apply;


import com.alibaba.fastjson.JSON;
import com.ql.util.express.DefaultContext;
import com.ql.util.express.ExpressRunner;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.biz.EnterpriseOrderOfferGuidePriceBizRule;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.*;
import com.wanshifu.fee.center.domain.dto.*;
import com.wanshifu.fee.center.domain.enums.*;
import com.wanshifu.fee.center.domain.request.ApplyCalculateReq;
import com.wanshifu.fee.center.domain.response.ApplyCalculateResp;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.MapUtils;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.strategy.extract.ServiceInfoValueExtractor;
import com.wanshifu.strategy.extract.ValueExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.core.ResolvableType;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.wanshifu.fee.center.domain.enums.ExpressParamEnum.*;

@Slf4j
public abstract class AbstractSceneFeeRuleStrategy implements SceneFeeRuleStrategy {

    @Resource
    private List<ValueExtractor<?>> valueExtractors;

    @Resource
    protected MongoTemplate mongoTemplate;

    @Resource
    private ExpressRunner expressRunner;

    @Resource
    protected ServiceInfoValueExtractor serviceInfoValueExtractor;


    private final static String ATTRIBUTE_VALUE_MAX = "attributeValueMax";
    private final static String ATTRIBUTE_VALUE_MIN = "attributeValueMin";
    private final static String SKU_TYPE = "skuType";
    private final static String SKU_NO = "skuNo";


    protected List<FeeTemplate> feeTemplatesBy(Criteria criteria) {
        criteria.and(BaseDocument.Fields.status).is(0).and(BaseDocument.Fields.del).is(false);
        return mongoTemplate.find(Query.query(criteria), FeeTemplate.class);
    }

    protected List<FeeRule> feeRulesBy(Criteria criteria) {
        criteria.and(BaseDocument.Fields.status).is(0).and(BaseDocument.Fields.del).is(false);
        return mongoTemplate.find(Query.query(criteria), FeeRule.class);
    }

    protected List<BizRuleMapping> bizRuleMappingBy(Criteria criteria) {
        criteria.and(BaseDocument.Fields.status).is(0).and(BaseDocument.Fields.del).is(false);
        return mongoTemplate.find(Query.query(criteria), BizRuleMapping.class);
    }

    protected List<FeeTemplateMapping> feeTemplateMappingsBy(Criteria criteria) {
        criteria.and(BaseDocument.Fields.status).is(MappingStatusEnum.PASS.code).and(BaseDocument.Fields.del).is(false);
        return mongoTemplate.find(Query.query(criteria), FeeTemplateMapping.class);
    }

    protected List<DynamicFeeRule> dynamicFeeRuleBy(Criteria criteria) {
        criteria.and(BaseDocument.Fields.status).is(DynamicFeeRuleStatusEnum.PASS.code).and(BaseDocument.Fields.del).is(false);
        return mongoTemplate.find(Query.query(criteria), DynamicFeeRule.class);
    }

    @SuppressWarnings("all")
    protected <T> ValueExtractor<T> getSuiteValueExtractor(Class<T> clazz) {
        ResolvableType paramType = ResolvableType.forClass(clazz);
        for (ValueExtractor<?> orderInfoConvertor : valueExtractors) {
            ResolvableType nowType = ResolvableType.forInstance(orderInfoConvertor);
            ResolvableType[] generics = nowType.as(ValueExtractor.class).getGenerics();
            if (Arrays.stream(generics).anyMatch(paramType::isAssignableFrom)) {
                return (ValueExtractor<T>) orderInfoConvertor;
            }
        }
        throw new BusException("未找到合适的取值器");
    }


//    protected ApplyCalculateResp calculate(ApplyCalculateReq applyCalculateReq, List<FeeRule> feeRules, List<DynamicFeeRule> dynamicFeeRules, Long tid, DivisionTypeEnum divisionTypeEnum) {
    protected ApplyCalculateResp calculate(ApplyCalculateReq applyCalculateReq, List<FeeRule> feeRules, Map<String, String> cache, Long tid, Long serviceId) {

        Map<Long, FeeRule> feeRuleMap = feeRules.stream().collect(Collectors.toMap(FeeRule::getFeeRuleId, Function.identity(), (i, j) -> i));

        List<CalculateResult> calculateResultList = new ArrayList<>(applyCalculateReq.getCalculateRuleDataList().size());
        // 应用规则
        Date now = new Date();
        boolean haveServiceFee = false;
        for (ApplyCalculateReq.CalculateRuleDataUnit calculateRuleDataUnit : applyCalculateReq.getCalculateRuleDataList()) {

            List<Map<String, String>> expressionParamMapList = calculateRuleDataUnit.getExpressionParamMapList();
            if (CollectionUtils.isEmpty(expressionParamMapList)) {
                expressionParamMapList = new ArrayList<>();
                expressionParamMapList.add(calculateRuleDataUnit.getExpressionParamMap());
            }
            for (Map<String, String> paramMap : expressionParamMapList) {
                CalculateResult calculateResult = new CalculateResult();

                Long calculateResultId = SnowFlakeGenerator.INSTANCE.generate();
                calculateResult.setCalculateResultId(calculateResultId);
                calculateResult.setSceneCode(applyCalculateReq.getSceneCode());
                calculateResult.setFeeRuleId(calculateRuleDataUnit.getFeeRuleId());
                calculateResult.setTemplateId(calculateRuleDataUnit.getTemplateId());
                calculateResult.setDel(false);
                calculateResult.setStatus(ResultStatusEnum.ACTIVE.code);
                calculateResult.setCreateTime(now);
                calculateResult.setModifyTime(now);
                calculateResult.setTid(tid);

                FeeRule feeRule = feeRuleMap.get(calculateResult.getFeeRuleId());
                Map<String, String> bizRule = feeRule.getBizRule();
                String feeTypeTag = bizRule.get(CommonBizRule.Fields.feeTypeTag);
                if (StringUtils.isNotBlank(feeTypeTag) && FeeTypeTagEnum.SERVICE_FEE.code.equals(feeTypeTag)) {
                    haveServiceFee = true;
                }
                calculateResult.setBizRule(bizRule);
                setSkuAttributePathId(bizRule);

                // 算费规则
                CalculateRuleData calculateRuleData = new CalculateRuleData();
                CalculateRuleData feeRuleCalculateRuleData = feeRule.getCalculateRuleData();
                BeanUtils.copyProperties(feeRuleCalculateRuleData, calculateRuleData);
                calculateRuleData.setExpressionParamMap(paramMap);
                calculateRuleData.setExpressionParamMapList(expressionParamMapList);
                calculateResult.setCalculateRuleData(calculateRuleData);
                // 算费
                BigDecimal cost;
                BigDecimal costMax = BigDecimal.ZERO;
                BigDecimal price = null;
                BigDecimal priceMax;
                // 编译 , 缓存;
                DefaultContext<String, Object> params = new DefaultContext<>();
                DefaultContext<String, Object> priceMaxParams = new DefaultContext<>();
                for (Map.Entry<String, String> stringStringEntry : paramMap.entrySet()) {
                    try {
                        BigDecimal bigDecimal = new BigDecimal(stringStringEntry.getValue());
                        params.put(stringStringEntry.getKey(), bigDecimal);
                        if ("masterInputPrice".equals(stringStringEntry.getKey())) {
                            price = bigDecimal;
                        }
                    } catch (Exception e) {
                        params.put(stringStringEntry.getKey(), stringStringEntry.getValue());
                    }
                }
                List<String> errorList = new ArrayList<>();
                try {
                    String masterInputPriceMax = bizRule.get(CommonBizRule.Fields.masterInputPriceMax);
                    String amountType = cache.get(SceneInfo.Fields.amountType);

                    if (Objects.isNull(feeRuleCalculateRuleData.getExpressInfo())) {
                        String str = expressRunner.execute(feeRuleCalculateRuleData.getExpress(), params, errorList, true, false, 1500).toString();
                        cost = new BigDecimal(str);

                        // 计价场景的“金额类型”为“区间价”的时候，才需要计算 最大价
                        if (StringUtils.isNotBlank(masterInputPriceMax) && AmountTypeEnum.RANGE_PRICE.code.equals(amountType)) {
                            priceMaxParams.putAll(params);
                            // 用单价max替换单价，带入脚本计算出单价max的价格
                            priceMax = new BigDecimal(masterInputPriceMax);
                            priceMaxParams.put(CommonBizRule.Fields.masterInputPrice, priceMax);
                            String strMax = expressRunner.execute(feeRuleCalculateRuleData.getExpress(), priceMaxParams, errorList, true, false, 1500).toString();
                            costMax = new BigDecimal(strMax);
                        } else {
                            priceMax = price;
                            costMax = cost;
                        }
                    } else {
                        // 如果是复杂公式（即有各种条件的）则只直接返回
                        ExpressResultInfo resultInfo = (ExpressResultInfo) expressRunner.execute(feeRuleCalculateRuleData.getExpress(), params, errorList, true, false, 1500);
                        cost = resultInfo.getCost();
                        if (Objects.nonNull(calculateResult.getBizRule())) {
                            calculateResult.getBizRule().putIfAbsent("expressNumber", resultInfo.getNumber().toString());
                            calculateResult.getBizRule().putIfAbsent("expressPrice", resultInfo.getPrice().toString());
                            price = resultInfo.getPrice();
                        }

                        // 计价场景的“金额类型”为“区间价”的时候，才需要计算 最大价
                        if (StringUtils.isNotBlank(masterInputPriceMax) && AmountTypeEnum.RANGE_PRICE.code.equals(amountType)) {
                            priceMaxParams.putAll(params);
                            // 用单价max替换单价，带入脚本计算出单价max的价格
                            priceMax = new BigDecimal(masterInputPriceMax);
                            priceMaxParams.put(CommonBizRule.Fields.masterInputPrice, priceMax);
                            ExpressResultInfo maxResultInfo = (ExpressResultInfo) expressRunner.execute(feeRuleCalculateRuleData.getExpress(), priceMaxParams, errorList, true, false, 1500);
                            if (maxResultInfo != null) {
                                costMax = maxResultInfo.getCost();
                            }
                        } else {
                            priceMax = price;
                            costMax = cost;
                        }
                    }

                    if (Objects.nonNull(price)) {
                        BigDecimal dynamicCost = getDynamicCost(applyCalculateReq, cache, feeRule, params, price, errorList, feeRuleCalculateRuleData, calculateResult);
                        if (dynamicCost != null) {
                            calculateResult.setDynamicFeeCost(dynamicCost.setScale(2, RoundingMode.HALF_UP));
                        }
                    }
                    if (Objects.nonNull(priceMax) && !priceMaxParams.isEmpty()) {
                        BigDecimal dynamicCostMax = getDynamicCost(applyCalculateReq, cache, feeRule, priceMaxParams, priceMax, errorList, feeRuleCalculateRuleData, calculateResult);
                        if (dynamicCostMax != null) {
                            calculateResult.setDynamicFeeCostMax(dynamicCostMax.setScale(2, RoundingMode.HALF_UP));
                        }
                    }
                } catch (Exception e) {
//                    log.error("计算异常: tid={} , log={} , express={}, params={}, ", tid, e.getMessage(), feeRuleCalculateRuleData.getExpress(), params);
                    log.error("计算异常，异常栈：{}", e.toString());
                    log.error("计算异常: tid={}, sceneCode={}, feeRuleId={} , log={} , express={}, params={}, ",
                            tid, feeRule.getSceneCode(), feeRule.getFeeRuleId(), e.getMessage(), feeRuleCalculateRuleData.getExpress(), params);
//                    throw new BusException("计算异常", e);
                    throw new BusException(ExceptionEnum.EXPRESSION_ERROR.code,
                            String.format("%s，serviceId=%s，templateId=%s，bizId=%s，sceneCode=%s",
                                    ExceptionEnum.EXPRESSION_ERROR.msg, serviceId, feeRule.getTemplateId(),
                                    bizRule.get(CommonBizRule.Fields.bizId), feeRule.getSceneCode()));
                }

                if (CollectionUtils.isNotEmpty(errorList)) {
                    log.error("计算错误: tid:{}, sceneCode={}, feeRuleId={}  , errorList={}",
                            tid, feeRule.getSceneCode(), feeRule.getFeeRuleId(),  errorList);
                    throw new BusException("计算错误, errorList not empty");
                }
                if (cost != null) {
                    calculateResult.setCost(cost.setScale(2, RoundingMode.HALF_UP));
                }
                calculateResult.setCostMax(costMax.setScale(2, RoundingMode.HALF_UP));
                // 产品要求：如果计算结果<=0，则不返回该项明细
                if (BigDecimal.ZERO.compareTo(cost) < 0) {
                    calculateResultList.add(calculateResult);
                }
            }

        }

        if (!haveServiceFee) {
            throw new BusException("订单上下文没有【服务费】，serviceId=" + serviceId + "，场景编码=" + applyCalculateReq.getSceneCode());
        }
        // 计算总价
        BigDecimal sumCost = BigDecimal.ZERO;
        BigDecimal sumCostMax = BigDecimal.ZERO;
        BigDecimal sumDynamicFeeCost = BigDecimal.ZERO;
        BigDecimal sumDynamicFeeCostMax = BigDecimal.ZERO;
        for (CalculateResult calculateResult : calculateResultList) {
            BigDecimal cost = calculateResult.getCost();
            BigDecimal costMax = calculateResult.getCostMax();
            BigDecimal dynamicFeeCost = calculateResult.getDynamicFeeCost();
            BigDecimal dynamicFeeCostMax = calculateResult.getDynamicFeeCostMax();

            sumCost = sumCost.add(cost);
            sumCostMax = sumCostMax.add(Objects.nonNull(costMax) ? costMax : cost);
            sumDynamicFeeCost = sumDynamicFeeCost.add(Objects.nonNull(dynamicFeeCost) ? dynamicFeeCost : cost);
            sumDynamicFeeCostMax = sumDynamicFeeCostMax.add(Objects.nonNull(dynamicFeeCostMax) ? dynamicFeeCostMax : costMax);
        }
        ApplyCalculateResp applyCalculateResp = new ApplyCalculateResp();
        applyCalculateResp.setCalculateResultList(calculateResultList);
        applyCalculateResp.setCost(sumCost);
        applyCalculateResp.setCostMax(sumCostMax);
        applyCalculateResp.setDynamicFeeCost(sumDynamicFeeCost);
        applyCalculateResp.setDynamicFeeCostMax(sumDynamicFeeCostMax);
        applyCalculateResp.setTid(tid);
        return applyCalculateResp;
    }

    private BigDecimal getDynamicCost(ApplyCalculateReq applyCalculateReq, Map<String, String> cache, FeeRule feeRule, DefaultContext<String, Object> params, BigDecimal price, List<String> errorList, CalculateRuleData feeRuleCalculateRuleData, CalculateResult calculateResult) throws Exception {
        List<DynamicFeeRuleCalculateRuleData> dynamicRuleList = feeRule.getDynamicFeeRuleCalculateRuleDataList();
        if (CollectionUtils.isEmpty(dynamicRuleList)) {
            log.info("没有合适的调价规则");
            return null;
        } else {
            BigDecimal dynamicCost = null;
            Date dynamicCalculateTime = applyCalculateReq.getDynamicCalculateTime();
            for (DynamicFeeRuleCalculateRuleData ruleData : dynamicRuleList) {
                DynamicFeeRuleCalculateRuleData.MatchingRule matchingRule = ruleData.getMatchingRule();
                Date endTime = matchingRule.getEndTime();
                Date startTime = matchingRule.getStartTime();
                if (Objects.nonNull(startTime) && Objects.nonNull(endTime) && Objects.nonNull(dynamicCalculateTime)
                        && dynamicCalculateTime.compareTo(startTime) >= 0 && dynamicCalculateTime.compareTo(endTime) <= 0) {
                    // 计算调价费用结果
                    DynamicFeeRuleCalculateRuleData.CalculateData unitPrice = ruleData.getUnitPrice();
                    DynamicFeeRuleCalculateRuleData.CalculateData skuPrice = ruleData.getSkuPrice();
                    params.put(DynamicCalculateRuleData.ORIGIN_PRICE, price.doubleValue());
                    String adjustFee = expressRunner.execute(unitPrice.getExpress(), params, errorList, true, false, 1500).toString();
                    BigDecimal adjustFeePrice = new BigDecimal(adjustFee);
                    if (BigDecimal.ZERO.compareTo(adjustFeePrice) > 0) {
                        throw new BusException("调价后费用小于0");
                    }
                    params.put("masterInputPrice", adjustFeePrice);
                    if (Objects.isNull(skuPrice)) {
                        if (feeRuleCalculateRuleData.getExpressInfo() == null) {
                            String dynamicCostStr = expressRunner.execute(feeRuleCalculateRuleData.getExpress(), params, errorList, true, false, 1500).toString();
//                            calculateResult.setDynamicFeeCost(new BigDecimal(dynamicCostStr));
                            dynamicCost = new BigDecimal(dynamicCostStr);
                        } else {
                            ExpressResultInfo expressResultInfo = (ExpressResultInfo) expressRunner.execute(feeRuleCalculateRuleData.getExpress(), params, errorList, true, false, 1500);
//                            calculateResult.setDynamicFeeCost(expressResultInfo.getCost());
                            dynamicCost = expressResultInfo.getCost();
                        }
                    } else {
                        List<String> expressionParamList = skuPrice.getExpressionParamList();
                        if (CollectionUtils.isNotEmpty(expressionParamList)) {
                            for (String expressParam : expressionParamList) {
                                ExpressParamEnum expressParamEnum = ExpressParamEnum.fromCode(expressParam);
                                if (Objects.nonNull(expressParamEnum)) {
                                    String value = serviceInfoValueExtractor.extract(applyCalculateReq.getServiceInfo(), feeRule, cache, expressParamEnum);
                                    // ALL_SERVICE_AP_SKU_NUMBER与AP_SKU_PRICE需要作为数据参数 参与脚本计算
                                    if (ALL_SERVICE_AP_SKU_NUMBER.code.equals(expressParam) || AP_SKU_PRICE.code.equals(expressParam)) {
                                        params.put(expressParam, new BigDecimal(value));
                                    } else {
                                        params.put(expressParam, value);
                                    }
                                }
                            }
                            // 特殊处理，下方两个值需要取动态价
                            params.put(AP_SKU_PRICE.code, adjustFeePrice);
                            params.put(MASTER_INPUT_PRICE.code, adjustFeePrice);
                            ExpressResultInfo expressResultInfo = (ExpressResultInfo) expressRunner.execute(skuPrice.getExpress(), params, errorList, true, false, 1500);
//                            calculateResult.setDynamicFeeCost(expressResultInfo.getCost());
                            dynamicCost = expressResultInfo.getCost();
                        }
                    }
                    calculateResult.setDynamicFeeStrategyName(ruleData.getDynamicStrategyName());
                    calculateResult.setDynamicFeeRuleId(ruleData.getDynamicFeeRuleId());
                    log.info("开始计算调价费用——》dynamicFeeRule={} , params={} , adjustFee={}", ruleData, params, adjustFee);

                    // 只需处理命中的第一条即可
                    break;
                }
            }
            return dynamicCost;
        }
    }

    private void setSkuAttributePathId(Map<String, String> bizRule) {
        if (MapUtils.isNotEmpty(bizRule)) {
            String skuNo = bizRule.get("skuNo");
            if (StringUtils.isNotBlank(skuNo)) {
                bizRule.put("skuAttributePathId", skuNo.replace("AP", ""));
            }
        }
    }

    protected List<Criteria> buildDynamicFeeRuleAddressCondition(AddressInfo addressInfo) {
        DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(addressInfo.getDivisionType());
        if (Objects.isNull(divisionTypeEnum)) {
            throw new BusException("addressInfo.divisionType设置错误");
        }
        Long province = addressInfo.getLv2DivisionId();
        // 部分特殊地区市区可以互换
        // 市
        Long city = Objects.isNull(addressInfo.getLv3DivisionId()) ? addressInfo.getLv4DivisionId() : addressInfo.getLv3DivisionId();
        // 区
        Long district = Objects.isNull(addressInfo.getLv4DivisionId()) ? addressInfo.getLv3DivisionId() : addressInfo.getLv4DivisionId();
        // 街道
        List<Criteria> criteriaList = new ArrayList<>();
        Long street = addressInfo.getLv5DivisionId();
        if ((divisionTypeEnum == DivisionTypeEnum.STREET)) {
            if (Objects.nonNull(street)) {
                Criteria streetCondition = Criteria.where(DynamicFeeRule.Fields.divisionType).is(DivisionTypeEnum.STREET.code).and(DynamicFeeRule.Fields.divisionIds).is(street);
                criteriaList.add(streetCondition);
            }
        }

        if ((divisionTypeEnum == DivisionTypeEnum.DISTRICT) || divisionTypeEnum == DivisionTypeEnum.STREET) {
            if (Objects.nonNull(district)) {
                Criteria districtCondition = Criteria.where(DynamicFeeRule.Fields.divisionType).is(DivisionTypeEnum.DISTRICT.code).and(DynamicFeeRule.Fields.divisionIds).is(district);
                criteriaList.add(districtCondition);
            }
        }


        if ((divisionTypeEnum == DivisionTypeEnum.CITY) || divisionTypeEnum == DivisionTypeEnum.STREET || divisionTypeEnum == DivisionTypeEnum.DISTRICT) {
            if (Objects.nonNull(city)) {
                Criteria cityCondition = Criteria.where(DynamicFeeRule.Fields.divisionType).is(DivisionTypeEnum.CITY.code).and(DynamicFeeRule.Fields.divisionIds).in(city);
                criteriaList.add(cityCondition);
            }
        }

        if ((divisionTypeEnum == DivisionTypeEnum.PROVINCE) || divisionTypeEnum == DivisionTypeEnum.STREET || divisionTypeEnum == DivisionTypeEnum.DISTRICT || divisionTypeEnum == DivisionTypeEnum.CITY) {
            if (Objects.nonNull(province)) {
                Criteria provinceCondition = Criteria.where(DynamicFeeRule.Fields.divisionType).is(DivisionTypeEnum.PROVINCE.code).and(DynamicFeeRule.Fields.divisionIds).in(province);
                criteriaList.add(provinceCondition);
            }
        }

        if ((divisionTypeEnum == DivisionTypeEnum.COUNTRY) || divisionTypeEnum == DivisionTypeEnum.STREET || divisionTypeEnum == DivisionTypeEnum.DISTRICT || divisionTypeEnum == DivisionTypeEnum.CITY || divisionTypeEnum == DivisionTypeEnum.PROVINCE) {
            Criteria countryCondition = Criteria.where(DynamicFeeRule.Fields.divisionType).is(DivisionTypeEnum.COUNTRY.code);
            criteriaList.add(countryCondition);
        }

        if (CollectionUtils.isEmpty(criteriaList)) {
            log.error("构建地区条件错误，没有合适的地区, {}", addressInfo);
            throw new BusException("构建地区条件错误，没有合适的地区");
        }

        return criteriaList;
    }

    protected List<Criteria> buildFeeRuleAddressCondition(AddressInfo addressInfo) {
        DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(addressInfo.getDivisionType());
        boolean needSearchParent = addressInfo.isNeedSearchParent();
        if (Objects.isNull(divisionTypeEnum)) {
            throw new BusException("addressInfo.divisionType设置错误");
        }
        Long province = addressInfo.getLv2DivisionId();
        // 部分特殊地区市区可以互换
        // 市
        Long city = Objects.isNull(addressInfo.getLv3DivisionId()) ? addressInfo.getLv4DivisionId() : addressInfo.getLv3DivisionId();
        // 区
//        Long district = Objects.isNull(addressInfo.getLv4DivisionId()) ? addressInfo.getLv3DivisionId() : addressInfo.getLv4DivisionId();
        // 保持统一
        Long district = Objects.isNull(addressInfo.getLv4DivisionId()) ? addressInfo.getLv5DivisionId() : addressInfo.getLv4DivisionId();
        // 街道
        List<Criteria> criteriaList = new ArrayList<>();
        Long street = addressInfo.getLv5DivisionId();
        if ((divisionTypeEnum == DivisionTypeEnum.STREET)) {
            if (Objects.nonNull(street)) {
                Criteria streetCondition = Criteria.where(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.divisionType).is(DivisionTypeEnum.STREET.code).and(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level4DivisionId).is(street.toString());
                criteriaList.add(streetCondition);
            }
        }

        if ((divisionTypeEnum == DivisionTypeEnum.DISTRICT) || (divisionTypeEnum == DivisionTypeEnum.STREET && needSearchParent)) {
            if (Objects.nonNull(district)) {
                Criteria districtCondition = Criteria.where(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.divisionType).is(DivisionTypeEnum.DISTRICT.code).and(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level3DivisionId).is(district.toString());
                criteriaList.add(districtCondition);
            }
        }


        if ((divisionTypeEnum == DivisionTypeEnum.CITY) || ((divisionTypeEnum == DivisionTypeEnum.STREET || divisionTypeEnum == DivisionTypeEnum.DISTRICT) && needSearchParent)) {
            if (Objects.nonNull(city)) {
                Criteria cityCondition = Criteria.where(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.divisionType).is(DivisionTypeEnum.CITY.code).and(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level2DivisionId).is(city.toString());
                criteriaList.add(cityCondition);
            }
        }

        if ((divisionTypeEnum == DivisionTypeEnum.PROVINCE) || ((divisionTypeEnum == DivisionTypeEnum.STREET || divisionTypeEnum == DivisionTypeEnum.DISTRICT || divisionTypeEnum == DivisionTypeEnum.CITY) && needSearchParent)) {
            if (Objects.nonNull(province)) {
                Criteria provinceCondition = Criteria.where(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.divisionType).is(DivisionTypeEnum.PROVINCE.code).and(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level1DivisionId).is(province.toString());
                criteriaList.add(provinceCondition);
            }
        }

        if ((divisionTypeEnum == DivisionTypeEnum.COUNTRY) || ((divisionTypeEnum == DivisionTypeEnum.STREET || divisionTypeEnum == DivisionTypeEnum.DISTRICT || divisionTypeEnum == DivisionTypeEnum.CITY || divisionTypeEnum == DivisionTypeEnum.PROVINCE) && needSearchParent)) {
            Criteria countryCondition = Criteria.where(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.divisionType).is(DivisionTypeEnum.COUNTRY.code);
            criteriaList.add(countryCondition);
        }

        if (CollectionUtils.isEmpty(criteriaList)) {
            log.error("构建地区条件错误，没有合适的地区, {}", addressInfo);
            throw new BusException("构建地区条件错误，没有合适的地区");
        }

        return criteriaList;
    }

    private DynamicFeeRule filterDynamicFeeRule(List<DynamicFeeRule> dynamicFeeRules, DivisionTypeEnum divisionTypeEnum, FeeRule feeRule) {
        if (CollectionUtils.isEmpty(dynamicFeeRules)) {
            return null;
        }
        // 每个serviceId逐级上移查找
        // 首先必须要对服务与tag类型
        List<DynamicFeeRule> dynamicFeeRuleList = dynamicFeeRules.stream()
                .filter(dynamicFeeRule ->
                        dynamicFeeRule.getServiceDataList().stream().anyMatch(serviceData -> serviceData.getServiceId().equals(Long.parseLong(feeRule.getBizRule().get("serviceId")))
                                && dynamicFeeRule.getFeeTypeTag().equals(feeRule.getBizRule().get("feeTypeTag"))) )
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dynamicFeeRuleList)) {
            return null;
        }

        // 先用户再地区
        List<DynamicFeeRule> userDynamicFeeRuleList = dynamicFeeRuleList.stream().filter(dynamicFeeRule -> CollectionUtils.isNotEmpty(dynamicFeeRule.getUserIds())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(userDynamicFeeRuleList)) {
            // 地区过滤
            // FIXME 为了修复NEP 临时改的，后续需要确认
            if (divisionTypeEnum != null) {
                return findDynamicFeeRule(userDynamicFeeRuleList, divisionTypeEnum);
            }
            return userDynamicFeeRuleList.get(0);
        }
        if (divisionTypeEnum != null) {
            return findDynamicFeeRule(dynamicFeeRuleList, divisionTypeEnum);
        }
        return dynamicFeeRuleList.get(0);
    }

    private DynamicFeeRule findDynamicFeeRule(List<DynamicFeeRule> dynamicFeeRules, DivisionTypeEnum divisionTypeEnum) {
        if (Objects.isNull(divisionTypeEnum)) {
            return null;
        }
        if (dynamicFeeRules.stream().anyMatch(feeRule -> feeRule.getDivisionType().equals(divisionTypeEnum.code))) {
            return dynamicFeeRules.stream().filter(feeRule -> feeRule.getDivisionType().equals(divisionTypeEnum.code)).findFirst().orElse(null);
        }

        return findDynamicFeeRule(dynamicFeeRules, divisionTypeEnum.parent);
    }


    protected List<FeeRule> filterFeeRule(List<FeeRule> feeRuleList, AddressInfo addressInfo) {
        Map<String, List<FeeRule>> serviceIdFeeRuleMap = feeRuleList.stream().collect(Collectors.groupingBy(feeRule -> feeRule.getBizRule().get("serviceId")));

        // 每个serviceId逐级上移查找
        DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(addressInfo.getDivisionType());
        boolean needSearchParent = addressInfo.isNeedSearchParent();

        List<FeeRule> result = new ArrayList<>();

        serviceIdFeeRuleMap.forEach((key, value) -> {
            List<FeeRule> feeRule = findFeeRule(value, divisionTypeEnum, needSearchParent);
            result.addAll(feeRule);
        });

        return result;
    }

    private List<FeeRule> findFeeRule(List<FeeRule> feeRuleList, DivisionTypeEnum divisionTypeEnum, boolean needSearchParent) {
        if (Objects.isNull(divisionTypeEnum)) {
            return Collections.emptyList();
        }
        if (feeRuleList.stream().anyMatch(feeRule -> feeRule.getBizRule().get("divisionType").equals(divisionTypeEnum.code))) {
            return feeRuleList.stream().filter(feeRule -> feeRule.getBizRule().get("divisionType").equals(divisionTypeEnum.code)).collect(Collectors.toList());
        }

        if (needSearchParent) {
            return findFeeRule(feeRuleList, divisionTypeEnum.parent, true);
        }
        return Collections.emptyList();
    }


    /**
     * 公开报价基础服务费需要补齐
     */
    private static final ServiceAttribute BASE_SERVICE_FEE = JSON.parseObject("{\n" +
            "    \"attributeKey\": \"base_service_fee\",\n" +
            "    \"attributeId\": 45001776,\n" +
            "    \"childList\": [\n" +
            "        {\n" +
            "            \"attributeValueId\": 48014958,\n" +
            "            \"attributePathNo\": \"**********\",\n" +
            "            \"childList\": null,\n" +
            "            \"value\": \"基础服务费默认值\"\n" +
            "        }\n" +
            "    ]\n" +
            "}", ServiceAttribute.class);

    protected void completeServiceAttribute(CalculateServiceInfo calculateServiceInfo) {
        boolean haveBaseServiceFee = calculateServiceInfo.getRootAttributeDetailList().stream().anyMatch(serviceAttribute -> serviceAttribute.getAttributeKey().equals("base_service_fee"));
        if (!haveBaseServiceFee) {
            log.info("补全基础服务元素");
            calculateServiceInfo.getRootAttributeDetailList().add(BASE_SERVICE_FEE);
        }
    }

    public List<FeeTemplate> getFeeTemplates(Set<String> serviceIds, List<CalculateServiceInfo> serviceInfos, String sceneCode) {
        Criteria feeTemplateCondition = Criteria.where(FeeTemplate.Fields.sceneCode).is(sceneCode)
                .and(FeeTemplate.Fields.group).ne(null)
                .orOperator(
                        Criteria.where(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.feeTypeTag).ne(null),
                        Criteria.where(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.applyFlag).is(ApplyFlagEnum.NO_APPLY_IF_HAVE.code))
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.serviceId).in(serviceIds);
        List<FeeTemplate> feeTemplates = feeTemplatesBy(feeTemplateCondition);
        if (CollectionUtils.isEmpty(feeTemplates)) {
            throw new BusException("模板为空");
        }

        feeTemplates = checkApplyFlag(serviceInfos, feeTemplates);
        return feeTemplates;
    }

    protected List<FeeTemplate> checkApplyFlag(List<CalculateServiceInfo> serviceInfos, List<FeeTemplate> feeTemplates) {
        Map<String, List<FeeTemplate>> applyFlagMap = feeTemplates.stream()
                .filter(e -> e.getBizRule() != null
                        && e.getBizRule().get("applyFlag") != null
                        && e.getBizRule().get("applyFlag").equals(ApplyFlagEnum.NO_APPLY_IF_HAVE.code)).
                collect(Collectors.groupingBy(s -> s.getBizRule().get("skuNo")));


        // 检查是否存在不需要算费的 SKU
        if (!applyFlagMap.isEmpty()) {
            for (CalculateServiceInfo serviceInfo : serviceInfos) {
                String serviceIdStr = serviceInfo.getServiceId().toString();
                for (List<FeeTemplate> templates : applyFlagMap.values()) {
                    if (CollectionUtils.isNotEmpty(templates)) {
                        for (FeeTemplate template : templates) {
                            String serviceId = template.getBizRule().get(CommonBizRule.Fields.serviceId);
                            // 只判断同一服务的才有意义
                            if (!serviceIdStr.equals(serviceId)) {
                                continue;
                            }
                            String skuNo = template.getBizRule().get(SKU_NO);
                            boolean checkResult = checkTemplateSkuNo(serviceInfo.getRootAttributeDetailList(), template, skuNo);
                            if (checkResult) {
                                throw new BusException("存在无需算费的模板");
                            } else {
                                feeTemplates = feeTemplates.stream().filter(e -> !e.getTemplateId().equals(template.getTemplateId())).collect(Collectors.toList());
                            }
                        }
                    }
                }
            }
        }
        return feeTemplates;
    }


    private boolean checkTemplateSkuNo(List<ServiceAttribute> serviceAttributes, FeeTemplate feeRule, String skuNo) {
        for (ServiceAttribute serviceAttribute : serviceAttributes) {
            List<ServiceAttributeValue> valueList = serviceAttribute.getChildList();
            if (CollectionUtils.isEmpty(valueList)) {
                continue;
            }
            for (ServiceAttributeValue serviceAttributeValue : valueList) {

                if (skuNo.equals(serviceAttributeValue.getAttributePathNo())) {
                    String valueStr = serviceAttributeValue.getValue();

                    boolean isCustomSku = FeeSkuTypeEnum.CUSTOM_SKU.code.equals(feeRule.getBizRule().get(SKU_TYPE));
                    if (isCustomSku) {
                        String valueMaxStr = feeRule.getBizRule().get(ATTRIBUTE_VALUE_MAX);
                        String valueMinStr = feeRule.getBizRule().get(ATTRIBUTE_VALUE_MIN);
                        try {
                            BigDecimal valueMax = new BigDecimal(valueMaxStr);
                            BigDecimal valueMin = new BigDecimal(valueMinStr);
                            BigDecimal value = new BigDecimal(valueStr);
                            return value.compareTo(valueMax) <= 0 && value.compareTo(valueMin) >= 0;
                        } catch (Exception e) {
                            log.info("服务属性解析失败：无法转成数字 value={},max={},min={},e={}", valueStr, valueMaxStr, valueMinStr, e.toString());
                            return false;
                        }
                    }
                    return true;
                }
                List<ServiceAttribute> childList = serviceAttributeValue.getChildList();
                if(CollectionUtils.isNotEmpty(childList)){
                    boolean checkSkuNo = checkTemplateSkuNo(childList, feeRule, skuNo);
                    if(checkSkuNo){
                        return true;
                    }
                }
            }
        }
        return false;
    }

}
