package com.wanshifu.strategy.apply;

import com.wanshifu.fee.center.domain.enums.AdjustPriceTypeEnum;
import com.wanshifu.fee.center.domain.enums.DynamicSymbolEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PriceAdjuster {

    private AdjustPriceTypeEnum adjustPriceTypeEnum;
    private DynamicSymbolEnum dynamicSymbolEnum;
    private double adjustValue;
    private double basePrice;

    public double getAdjustPrice() {
        Double deltaPrice = adjustPriceTypeEnum.getDeltaPrice(basePrice, adjustValue);
        return dynamicSymbolEnum.getAdjustPrice(deltaPrice);
    }
}
