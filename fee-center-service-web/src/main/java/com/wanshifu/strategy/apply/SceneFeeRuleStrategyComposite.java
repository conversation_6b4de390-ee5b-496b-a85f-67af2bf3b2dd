package com.wanshifu.strategy.apply;

import cn.hutool.core.util.NumberUtil;
import com.wanshifu.common.enums.AttributeKeyEnum;
import com.wanshifu.domain.dto.ApplyOrderCalculateDTO;
import com.wanshifu.fee.center.domain.document.CalculateResult;
import com.wanshifu.fee.center.domain.document.SceneInfo;
import com.wanshifu.fee.center.domain.dto.CalculateServiceInfo;
import com.wanshifu.fee.center.domain.enums.AmountTypeEnum;
import com.wanshifu.fee.center.domain.response.ApplyOrderCalculateResp;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.MapUtils;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.repository.CalculateResultRepository;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@Slf4j
public class SceneFeeRuleStrategyComposite implements SceneFeeRuleStrategy {

    @Resource
    private CalculateResultRepository calculateResultRepository;
    @Resource
    private SceneFeeRuleStrategy commonSceneFeeRuleStrategy;
    @Value("${needClearUnrelatedFields:true}")
    private Boolean needClearUnrelatedFields;

    @Override
    public ApplyOrderCalculateResp apply(ApplyOrderCalculateDTO applyOrderCalculateDTO) {
        ApplyOrderCalculateResp applyOrderCalculateResp = applyOrderCalculateDTO.getApplyOrderCalculateResp();
        if (Objects.isNull(applyOrderCalculateResp)) {
            applyOrderCalculateResp = new ApplyOrderCalculateResp();
            applyOrderCalculateResp.setTid(SnowFlakeGenerator.INSTANCE.generate());
            applyOrderCalculateDTO.setApplyOrderCalculateResp(applyOrderCalculateResp);
            log.info("applyOrderCalculateResp instance init tid = {}", applyOrderCalculateResp.getTid());
        }

        List<SceneInfo> sceneInfoList = applyOrderCalculateDTO.getSceneInfoList();
        if (CollectionUtils.isEmpty(sceneInfoList)) {
            throw new BusException("算费失败, 场景信息为空");
        }

        BigDecimal goodsNumber = extractOrderValue(applyOrderCalculateDTO);
        applyOrderCalculateDTO.setGoodsNumber(goodsNumber);

        Boolean mergeByPriority = applyOrderCalculateDTO.getMergeByPriority();
        List<ApplyOrderCalculateResp.SceneResult> resultList = applyOrderCalculateResp.getSceneResultList();
        if (mergeByPriority) {
            List<String> sceneCodeList = applyOrderCalculateDTO.getSceneCode();
            // 按照优先级排序
            sceneInfoList.sort(Comparator.comparing(e -> sceneCodeList.indexOf(e.getSceneCode())));
            List<CalculateServiceInfo> serviceInfos = applyOrderCalculateDTO.getServiceInfos();
            ApplyOrderCalculateResp resp = null;
            ApplyOrderCalculateResp errorResp = null;

            for (SceneInfo sceneInfo : sceneInfoList) {
                String sceneCode = sceneInfo.getSceneCode();
                for (CalculateServiceInfo serviceInfo : serviceInfos) {
                    if (serviceInfo.getIsCalculated()) {
                        continue;
                    }
                    try {
                        List<CalculateServiceInfo> calculateServiceInfos = new ArrayList<>();
                        calculateServiceInfos.add(serviceInfo);
                        applyOrderCalculateDTO.setServiceInfos(calculateServiceInfos);
                        applyOrderCalculateDTO.setCurrentSceneInfo(sceneInfo);
                        applyOrderCalculateDTO.setApplyOrderCalculateResp(null);
                        ApplyOrderCalculateResp applyResp = commonSceneFeeRuleStrategy.apply(applyOrderCalculateDTO);
                        if (resp == null) {
                            resp = applyResp;
                            ApplyOrderCalculateResp.SceneResult sceneResult = applyResp.getSceneResultList().get(0);
                            resp.setCost(sceneResult.getCost());
                            resp.setCostMax(sceneResult.getCostMax());
                            resp.setDynamicFeeCost(sceneResult.getDynamicFeeCost());
                            resp.setDynamicFeeCostMax(sceneResult.getDynamicFeeCostMax());
                        } else {
                            ApplyOrderCalculateResp calculateResp = applyOrderCalculateDTO.getApplyOrderCalculateResp();
                            // 因为会统一到一个场景，所以只需要取第一个即可
                            ApplyOrderCalculateResp.SceneResult sceneResult = resp.getSceneResultList().get(0);
                            ApplyOrderCalculateResp.SceneResult sceneResultAdd = calculateResp.getSceneResultList().get(0);

                            BigDecimal addCost = sceneResultAdd.getCost();
                            BigDecimal addDynamicFeeCost = sceneResultAdd.getDynamicFeeCost();
                            resp.setCost(resp.getCost().add(addCost));
                            resp.setDynamicFeeCost(resp.getDynamicFeeCost().add(addDynamicFeeCost));
                            BigDecimal cost = sceneResult.getCost();
                            sceneResult.setCost(NumberUtil.add(cost, addCost));

                            BigDecimal dynamicFeeCost = sceneResult.getDynamicFeeCost();
                            sceneResult.setDynamicFeeCost(NumberUtil.add(dynamicFeeCost, addDynamicFeeCost));

                            if (AmountTypeEnum.RANGE_PRICE.code.equals(sceneInfo.getAmountType())) {
                                BigDecimal costMax = sceneResult.getCostMax();
                                BigDecimal addCostMax = sceneResultAdd.getCostMax();
                                costMax = costMax != null ? costMax : cost;
                                addCostMax = addCostMax != null ? addCostMax : addCost;
                                sceneResult.setCostMax(NumberUtil.add(costMax, addCostMax));

                                BigDecimal dynamicFeeCostMax = sceneResult.getDynamicFeeCostMax();
                                BigDecimal addDynamicFeeCostMax = sceneResultAdd.getDynamicFeeCostMax();
                                dynamicFeeCostMax = dynamicFeeCostMax != null ? dynamicFeeCostMax : dynamicFeeCost;
                                addDynamicFeeCostMax = addDynamicFeeCostMax != null ? addDynamicFeeCostMax : addDynamicFeeCost;
                                sceneResult.setDynamicFeeCostMax(NumberUtil.add(dynamicFeeCostMax, addDynamicFeeCostMax));
                            }


                            BigDecimal basePrice = Optional.ofNullable(sceneResult.getBasePrice())
                                    .orElse(BigDecimal.ZERO)
                                    .max(Optional.ofNullable(sceneResultAdd.getBasePrice()).orElse(BigDecimal.ZERO));
                            sceneResult.setBasePrice(basePrice);

                            List<ApplyOrderCalculateResp.ServiceResult> serviceResultList = sceneResult.getServiceResultList();
                            List<ApplyOrderCalculateResp.ServiceResult> serviceResultListAdd = sceneResultAdd.getServiceResultList();
                            if (CollectionUtils.isNotEmpty(serviceResultListAdd)) {
                                serviceResultList = Stream.concat(serviceResultList.stream(), serviceResultListAdd.stream())
                                        .collect(Collectors.toList());
                            } else if (serviceResultList == null) {
                                serviceResultList = new ArrayList<>(serviceResultListAdd != null ? serviceResultListAdd : Collections.emptyList());
                            }
                            sceneResult.setServiceResultList(serviceResultList);

                            List<CalculateResult> calculateResultList = sceneResult.getCalculateResultList();
                            List<CalculateResult> calculateResultListAdd = sceneResultAdd.getCalculateResultList();
                            if (CollectionUtils.isNotEmpty(calculateResultListAdd)) {
                                calculateResultList = Stream.concat(calculateResultList.stream(), calculateResultListAdd.stream())
                                        .collect(Collectors.toList());
                            } else if (calculateResultList == null) {
                                calculateResultList = new ArrayList<>(calculateResultListAdd != null ? calculateResultListAdd : Collections.emptyList());
                            }
                            sceneResult.setCalculateResultList(calculateResultList);
                        }
                        serviceInfo.setIsCalculated(true);
//                        applyOrderCalculateResp = resp;
                        log.info("apply {} complete", sceneCode);
                    } catch (Exception e) {
                        // 构建返回
                        List<ApplyOrderCalculateResp.SceneResult> sceneResultList = resultList;
                        if (CollectionUtils.isEmpty(sceneResultList)) {
                            sceneResultList = new ArrayList<>();
                            applyOrderCalculateResp.setSceneResultList(sceneResultList);
                        }
                        // 只计算当前场景的
                        ApplyOrderCalculateResp.SceneResult sceneResult = new ApplyOrderCalculateResp.SceneResult();
                        sceneResult.setSceneCode(sceneCode);
                        sceneResult.setCost(BigDecimal.ZERO);
                        sceneResult.setSuccess(false);
                        sceneResult.setErrorInfo(e.getMessage() == null ? "exception的message为null" : e.getMessage());
                        sceneResultList.add(sceneResult);
                        if (errorResp == null) {
                            errorResp = new ApplyOrderCalculateResp();
                            errorResp.setSceneResultList(sceneResultList);
                        } else {
                            errorResp.getSceneResultList().addAll(sceneResultList);
                        }
                        if (!(e instanceof BusException)) {
//                            StackTraceElement[] stackTrace = e.getStackTrace();
//                            if (stackTrace != null) {
//                                for (StackTraceElement stackTraceElement : stackTrace) {
//                                    log.error("多场景，NPE位置：{}，场景：{}", stackTraceElement.getClassName() + ":" + stackTraceElement.getMethodName() + ":" + stackTraceElement.getLineNumber(), sceneCode);
//                                }
//                            }
                            StringWriter sw = new StringWriter();
                            PrintWriter pw = new PrintWriter(sw);
                            e.printStackTrace(pw);
                            log.error("多场景，异常堆栈：\n{}", sw);
                        }
                        log.error("apply {} error 多场景, {}", sceneCode, e.toString());
                    }
                }
            }
            applyOrderCalculateResp = resp;


            // 需要判断是否所有服务都计算出了结果
            List<CalculateServiceInfo> serviceInfoList = serviceInfos.stream().filter(s -> !s.getIsCalculated()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(serviceInfoList)) {
//                throw new BusException("算费失败, 有服务未计算出结果, serviceId=" + serviceInfoList.stream().map(CalculateServiceInfo::getServiceId).collect(Collectors.toList()));
                if (errorResp != null) {
                    List<String> errorInfoList = errorResp.getSceneResultList().stream().map(ApplyOrderCalculateResp.SceneResult::getErrorInfo).filter(StringUtil::isNotBlank).collect(Collectors.toList());
//                    throw new BusException("算费失败, 有服务未计算出结果, 详情：" + JSON.toJSONString(errorResp, SerializerFeature.DisableCircularReferenceDetect));
                    throw new BusException("算费失败, 有服务未计算出结果, 详情：" + errorInfoList);
                } else {
                    throw new BusException("算费失败, 有服务未计算出结果, serviceId=" + serviceInfoList.stream().map(CalculateServiceInfo::getServiceId).collect(Collectors.toList()));
                }
            }

        } else {
            for (SceneInfo sceneInfo : sceneInfoList) {
                String sceneCode = sceneInfo.getSceneCode();
                try {
                    applyOrderCalculateDTO.setCurrentSceneInfo(sceneInfo);
                    commonSceneFeeRuleStrategy.apply(applyOrderCalculateDTO);
                    log.info("apply {} complete", sceneCode);
                } catch (Exception e) {
                    // 构建返回
                    if (CollectionUtils.isEmpty(resultList)) {
                        resultList = new ArrayList<>();
                        applyOrderCalculateResp.setSceneResultList(resultList);
                    }
                    // 只计算当前场景的
                    ApplyOrderCalculateResp.SceneResult sceneResult = new ApplyOrderCalculateResp.SceneResult();
                    sceneResult.setSceneCode(sceneCode);
                    sceneResult.setCost(BigDecimal.ZERO);
                    sceneResult.setSuccess(false);
                    sceneResult.setErrorInfo(e.getMessage() == null ? "exception的message为null" : e.getMessage());
                    resultList.add(sceneResult);
                    if (!(e instanceof BusException)) {
//                        StackTraceElement[] stackTrace = e.getStackTrace();
//                        if (stackTrace != null) {
//                            for (StackTraceElement stackTraceElement : stackTrace) {
//                                log.error("单场景, NPE位置：{}，场景：{}", stackTraceElement.getClassName() + ":" + stackTraceElement.getMethodName() + ":" + stackTraceElement.getLineNumber(), sceneCode);
//                            }
//                        }
                        StringWriter sw = new StringWriter();
                        PrintWriter pw = new PrintWriter(sw);
                        e.printStackTrace(pw);
                        log.error("单场景，异常堆栈：\n{}", sw);
                    }
                    log.error("apply {} error 单场景, {}", sceneCode, e.toString());
                }
            }
        }

        // 保存到库
        log.info("计算完成——准备入库,tid={}", applyOrderCalculateResp.getTid());
        if (CollectionUtils.isEmpty(sceneInfoList)) {
            throw new BusException("算费失败,所有场景算费结果为空");
        }
        saveInfo(applyOrderCalculateDTO);
        log.info("计算完成——已入库,tid={}", applyOrderCalculateResp.getTid());

        // 将业务端不关心的字段置空，尽量减小响应大小，避免大对象
//        if (needClearUnrelatedFields) {
//            clearUnrelatedFields(applyOrderCalculateResp);
//        }
        return applyOrderCalculateResp;
    }


    private void clearUnrelatedFields(ApplyOrderCalculateResp applyOrderCalculateResp) {
        if (Objects.nonNull(applyOrderCalculateResp)) {
            List<ApplyOrderCalculateResp.SceneResult> sceneResultList = applyOrderCalculateResp.getSceneResultList();
            if (CollectionUtils.isNotEmpty(sceneResultList)) {
                for (ApplyOrderCalculateResp.SceneResult sceneResult : sceneResultList) {
                    sceneResult.setCalculateResultList(null);
                    List<ApplyOrderCalculateResp.ServiceResult> serviceResultList = sceneResult.getServiceResultList();
                    if (CollectionUtils.isNotEmpty(serviceResultList)) {
                        for (ApplyOrderCalculateResp.ServiceResult serviceResult : serviceResultList) {
                            List<CalculateResult> calculateResultList = serviceResult.getCalculateResultList();
                            if (CollectionUtils.isNotEmpty(calculateResultList)) {
                                calculateResultList.forEach(calculateResult -> {
                                    calculateResult.setCalculateRuleData(null);
                                    calculateResult.setDynamicCalculateRuleData(null);
                                });
                            }
                        }
                    }
                }
            }
        }
    }


    private void saveInfo(ApplyOrderCalculateDTO applyOrderCalculateDTO) {
        // 获取全部的结果，存库
        // bizRule
        Map<String, String> bizRule = new HashMap<>();
        if (Objects.nonNull(applyOrderCalculateDTO.getOrderBase())) {
            if (Objects.nonNull(applyOrderCalculateDTO.getOrderBase().getOrderId())) {
                bizRule.put("orderId", applyOrderCalculateDTO.getOrderBase().getOrderId().toString());
            }
            if (Objects.nonNull(applyOrderCalculateDTO.getOrderBase().getGlobalOrderTraceId())) {
                bizRule.put("globalOrderTraceId", applyOrderCalculateDTO.getOrderBase().getGlobalOrderTraceId().toString());
            }
        }

        ApplyOrderCalculateResp applyOrderCalculateResp = applyOrderCalculateDTO.getApplyOrderCalculateResp();
        List<ApplyOrderCalculateResp.SceneResult> sceneResultList = applyOrderCalculateResp.getSceneResultList();
        if (CollectionUtils.isEmpty(sceneResultList)) {
            return;
        }
        // 只需要获取成功的结果
        sceneResultList = sceneResultList.stream().filter(ApplyOrderCalculateResp.SceneResult::isSuccess).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(sceneResultList)) {
            BigDecimal cost = sceneResultList.stream().map(ApplyOrderCalculateResp.SceneResult::getCost).reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal costMax = sceneResultList.stream().map(ApplyOrderCalculateResp.SceneResult::getCostMax).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal dynamicCost = sceneResultList.stream().map(sceneResult -> Objects.nonNull(sceneResult.getDynamicFeeCost()) ? sceneResult.getDynamicFeeCost() : sceneResult.getCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
//            BigDecimal dynamicCostMax = sceneResultList.stream().map(sceneResult -> Objects.nonNull(sceneResult.getDynamicFeeCostMax()) ? sceneResult.getDynamicFeeCostMax() : sceneResult.getCostMax()).reduce(BigDecimal.ZERO, BigDecimal::add);
            applyOrderCalculateResp.setCost(cost);
//            applyOrderCalculateResp.setCostMax(costMax);
            applyOrderCalculateResp.setDynamicFeeCost(dynamicCost);
//            applyOrderCalculateResp.setDynamicFeeCostMax(dynamicCostMax);
            List<CalculateResult> calculateResults = sceneResultList.stream().filter(sceneResult -> CollectionUtils.isNotEmpty(sceneResult.getCalculateResultList())).flatMap(sceneResult -> sceneResult.getCalculateResultList().stream()).collect(Collectors.toList());
            List<CalculateResult> calculateResultList = new ArrayList<>(calculateResults);
            List<CalculateResult> serviceResults = sceneResultList.stream().filter(sceneResult -> CollectionUtils.isNotEmpty(sceneResult.getServiceResultList())).flatMap(sceneResult -> sceneResult.getServiceResultList().stream()).filter(serviceResult -> CollectionUtils.isNotEmpty(serviceResult.getCalculateResultList())).flatMap(serviceResult -> serviceResult.getCalculateResultList().stream()).collect(Collectors.toList());
            calculateResultList.addAll(serviceResults);
            // bizRule
            for (CalculateResult calculateResult : calculateResultList) {
                if (MapUtils.isEmpty(calculateResult.getBizRule())) {
                    calculateResult.setBizRule(bizRule);
                } else {
                    calculateResult.getBizRule().putAll(bizRule);
                }
            }
            calculateResultRepository.save(calculateResultList);
        }
    }

    /**
     * 通用sku属性提取，典型的是灯具套装安装一口价，在某个sku上面有一个折扣价，需要通过订单商品总数作为条件
     */
//    private void extractOrderValue(ApplyOrderCalculateDTO applyOrderCalculateDTO, Map<String, String> orderCache) {
    private BigDecimal extractOrderValue(ApplyOrderCalculateDTO applyOrderCalculateDTO) {
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(applyOrderCalculateDTO.getServiceInfos())) {
            BigDecimal sumNumber = applyOrderCalculateDTO.getServiceInfos().stream()
                    .filter(serviceInfo -> org.apache.commons.collections.CollectionUtils.isNotEmpty(serviceInfo.getRootAttributeDetailList()))
                    .flatMap(serviceInfo -> serviceInfo.getRootAttributeDetailList().stream())
                    .filter(attribute -> AttributeKeyEnum.goods_number.name().equals(attribute.getAttributeKey()))
                    .filter(attribute -> org.apache.commons.collections.CollectionUtils.isNotEmpty(attribute.getChildList()))
                    .flatMap(attribute -> attribute.getChildList().stream())
                    .filter(attributeValue -> StringUtils.isNotBlank(attributeValue.getValue()))
                    .map(attributeValue -> new BigDecimal(attributeValue.getValue())).reduce(BigDecimal.ZERO, BigDecimal::add, BigDecimal::add);
            if (BigDecimal.ZERO.compareTo(sumNumber) < 0) {
                // 大于0 则放进去
//                orderCache.put(ExpressParamEnum.ALL_SERVICE_AP_SKU_NUMBER.bizKey, sumNumber.setScale(2, RoundingMode.HALF_UP).toString());
                return sumNumber.setScale(2, RoundingMode.HALF_UP);
            } else {
                log.info("产品总数量不正常赋空null: num = {}", sumNumber);
                return null;
            }
        }
        return null;
    }
}
