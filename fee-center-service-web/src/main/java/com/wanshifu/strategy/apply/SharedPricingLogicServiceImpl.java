package com.wanshifu.strategy.apply;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: <PERSON> Yong
 * @create: 2025-07-04 11:05
 * @description: 共享计价逻辑服务, 用于新旧标准计价接口共用。
 * 理论上不应该出现这样的代码，但短时间内无法完全去掉旧标准计价接口，又不应该在新旧标准计价接口写重复的代码，故抽象出该类
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SharedPricingLogicServiceImpl implements SharedPricingLogicService {




}
