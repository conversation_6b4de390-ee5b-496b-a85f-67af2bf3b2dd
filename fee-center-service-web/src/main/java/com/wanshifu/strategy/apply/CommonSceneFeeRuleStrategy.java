package com.wanshifu.strategy.apply;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.domain.dto.ApplyOrderCalculateDTO;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.biz.PlatformFixedPriceBizRule;
import com.wanshifu.fee.center.domain.constant.Constant;
import com.wanshifu.fee.center.domain.constant.GlobalRedisKeyConstant;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.*;
import com.wanshifu.fee.center.domain.dto.*;
import com.wanshifu.fee.center.domain.enums.*;
import com.wanshifu.fee.center.domain.request.ApplyCalculateReq;
import com.wanshifu.fee.center.domain.request.calculate.DynamicIndicatorParam;
import com.wanshifu.fee.center.domain.response.ApplyCalculateResp;
import com.wanshifu.fee.center.domain.response.ApplyOrderCalculateResp;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.MapUtils;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.infrastructure.config.ApolloConfig;
import com.wanshifu.service.DynamicFeeRuleService;
import com.wanshifu.strategy.extract.ValueExtractor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * @author: Chen Yong
 * @create: 2024-03-28 14:57
 * @description: 通用处理策略
 */
@Component
@Slf4j
public class CommonSceneFeeRuleStrategy extends AbstractSceneFeeRuleStrategy {

    @Resource
    private AddressApi addressApi;
    @Resource
    private RedisHelper redisHelper;
    @Resource
    private DynamicFeeRuleService dynamicFeeRuleService;

    @Value("${base-service-fee-switch:true}")
    private Boolean baseServiceFeeSwitch;


    private final String sameSkuNoNeedRemoveFlag = "sameSkuNoNeedRemoveFlag";

    @Override
    public String getSceneCode() {
        return "common";
    }

    @Override
    public ApplyOrderCalculateResp apply(ApplyOrderCalculateDTO applyOrderCalculateDTO) {
        SceneInfo currentSceneInfo = applyOrderCalculateDTO.getCurrentSceneInfo();
        AddressInfo addressInfo = applyOrderCalculateDTO.getAddressInfo();
//        DivisionTypeEnum divisionTypeEnum = null;
        if (addressInfo != null) {
//            divisionTypeEnum = DivisionTypeEnum.fromCode(addressInfo.getDivisionType());
            completeAddress(addressInfo);

            String divisionType = currentSceneInfo.getDivisionType();
            if (StringUtils.isNotBlank(divisionType)) {
                addressInfo.setDivisionType(divisionType);
            }

            Boolean needSearchParent = currentSceneInfo.getNeedSearchParent();
            if (needSearchParent != null) {
                addressInfo.setNeedSearchParent(needSearchParent);
            }
        }
        final String sceneCode = currentSceneInfo.getSceneCode();
        String bizIdType = currentSceneInfo.getBizIdType();
        BizIdTypeEnum bizIdTypeEnum = BizIdTypeEnum.fromCode(bizIdType);
        AccountInfo from = applyOrderCalculateDTO.getFrom();
        if (bizIdTypeEnum != null) {
            if (bizIdTypeEnum.equals(BizIdTypeEnum.USER_ID) && (from == null || from.getAccountId() == null)) {
                throw new BusException("下单人信息为空");
            }
        }
        Boolean isMappingPricing = currentSceneInfo.getIsMappingPricing();
        isMappingPricing = isMappingPricing != null && isMappingPricing;

        ApplyOrderCalculateResp applyOrderCalculateResp = applyOrderCalculateDTO.getApplyOrderCalculateResp();

        if (Objects.isNull(applyOrderCalculateResp)) {
            applyOrderCalculateResp = new ApplyOrderCalculateResp();
            applyOrderCalculateResp.setTid(SnowFlakeGenerator.INSTANCE.generate());
            applyOrderCalculateDTO.setApplyOrderCalculateResp(applyOrderCalculateResp);
        }

        List<CalculateServiceInfo> serviceInfos = applyOrderCalculateDTO.getServiceInfos();
        Set<String> serviceIds = serviceInfos.stream().map(CalculateServiceInfo::getServiceId).map(Objects::toString).collect(Collectors.toSet());
        log.info("开始计算场景「{}」订单费用，serviceIds={}", sceneCode, serviceIds);

        // 当前是按单个服务算价，所以只取第一个即可
        String serviceId1st = serviceIds.iterator().next();

        /*
         * 映射需求思路：
         * 1、根据场景判断是否需要映射
         * 2、需要，则根据 场景+服务Id，带入toBizRule中，查找bizRuleMapping数据（需要保留映射关系，toBizRule的sceneCode+skuNo 作为key
         *    ，fromBizRule作为value，需要用to的skuNo替换from的，因为后面需要拿着skuNo去订单上下方中检查是否有【不反价属性】）
         * 3、获取上一步中的fromBizRule中的templateId数据，查询feeTemplate（templateId是feeTemplate表的唯一键）
         * 4、根据上一步的templateId，查询feeRule，需要加上地址等信息（参考已有查询条件），获取计费规则
         * 5、将feeRule中的serviceId（包括group）、skuNo、skuNumberPathNo 用to的替换调，并且需要替换掉calculateRuleData中的 计价数量占位符
         * 6、后续的逻辑就是一样的了
         * 总结：映射的目的是拿到feeRule，而feeRule主要包含bizRule和calculateRuleData，然后订单上下文是原始的serviceId、skuNo、skuNumberPathNo，所以需要替换
         * 综上所述，此乃偷梁换柱之法是也
         */
        List<FeeTemplate> feeTemplates = new ArrayList<>();
        List<FeeRule> feeRuleList = new ArrayList<>();
        Set<String> noMappingServiceIdSet;
        boolean needFilterFeeRuleByDivisionType = true;
        // 业务侧传的优先，是否映射计价
        Boolean isMappingPricingFromBiz = applyOrderCalculateDTO.getIsMappingPricing();
        if ((isMappingPricingFromBiz != null && isMappingPricingFromBiz) || (isMappingPricing && isMappingPricingFromBiz == null)) {
            // 查询映射
            String fromSceneCode = applyOrderCalculateDTO.getFromSceneCode();

            if (ApolloConfig.NEW_TEMPLATE_MAPPING_SWITCH) {
                List<FeeTemplateMapping> mappings = getFeeTemplateMappingList(sceneCode, fromSceneCode, serviceIds);
                if (CollectionUtils.isEmpty(mappings)) {
                    noMappingServiceIdSet = new HashSet<>(serviceIds);
                } else {
                    Set<String> foundServiceIds = mappings.stream().map(e -> e.getSource().getServiceId()).collect(Collectors.toSet());
                    noMappingServiceIdSet = serviceIds.stream().filter(e -> !foundServiceIds.contains(e)).collect(Collectors.toSet());
                    Map<Long, List<String>> templateIdWithServiceIdAndSkuNoMap = getTemplateIdWithServiceIdAndSkuNoMapNew(mappings);
                    // 从映射中获取templateId
                    Set<SceneCodeAndServiceId> fromSceneCodeAndServiceIds = getSceneCodeAndServiceIdsNew(mappings);
                    // 查询feeTemplate
                    List<FeeTemplate> mappingFeeTemplates = getFeeTemplateList(serviceInfos, fromSceneCodeAndServiceIds);
                    if (CollectionUtils.isNotEmpty(mappingFeeTemplates)) {
                        feeTemplates.addAll(mappingFeeTemplates);
                    }
                    // 查询feeRule
                    List<FeeRule> mappingFeeRuleList = getFeeRuleList(applyOrderCalculateDTO, templateIdWithServiceIdAndSkuNoMap, fromSceneCodeAndServiceIds, feeTemplates);
                    if (CollectionUtils.isNotEmpty(mappingFeeRuleList)) {
                        // FIXME 2024年6月5日(天天特价项目) 与远康确认，临时方案，将自定义强制改为标准
                        mappingFeeRuleList.stream()
                                .filter(e -> FeeSkuTypeEnum.CUSTOM_SKU.code.equals(e.getBizRule().get(CommonBizRule.Fields.skuType)))
                                .forEach(e -> e.getBizRule().put(CommonBizRule.Fields.skuType, FeeSkuTypeEnum.STANDARD_SKU.code));
                        feeRuleList.addAll(mappingFeeRuleList);
                        needFilterFeeRuleByDivisionType = false;
                    }
                }
            } else {
                List<BizRuleMapping> bizRuleMappings = getBizRuleMappingList(sceneCode, fromSceneCode, serviceIds);
                if (CollectionUtils.isEmpty(bizRuleMappings)) {
                    noMappingServiceIdSet = new HashSet<>(serviceIds);
                } else {
                    Set<String> foundServiceIds = bizRuleMappings.stream().map(e -> e.getToBizRule().get(CommonBizRule.Fields.serviceId)).collect(Collectors.toSet());
                    noMappingServiceIdSet = serviceIds.stream().filter(e -> !foundServiceIds.contains(e)).collect(Collectors.toSet());
                    Map<Long, List<String>> templateIdWithServiceIdAndSkuNoMap = getTemplateIdWithServiceIdAndSkuNoMap(bizRuleMappings);
                    // 从映射中获取templateId
                    Set<SceneCodeAndServiceId> fromSceneCodeAndServiceIds = getSceneCodeAndServiceIds(bizRuleMappings);
                    // 查询feeTemplate
                    List<FeeTemplate> mappingFeeTemplates = getFeeTemplateList(serviceInfos, fromSceneCodeAndServiceIds);
                    if (CollectionUtils.isNotEmpty(mappingFeeTemplates)) {
                        feeTemplates.addAll(mappingFeeTemplates);
                    }
                    // 查询feeRule
                    List<FeeRule> mappingFeeRuleList = getFeeRuleList(applyOrderCalculateDTO, templateIdWithServiceIdAndSkuNoMap, fromSceneCodeAndServiceIds, feeTemplates);
                    if (CollectionUtils.isNotEmpty(mappingFeeRuleList)) {
                        // FIXME 2024年6月5日(天天特价项目) 与远康确认，临时方案，将自定义强制改为标准
                        mappingFeeRuleList.stream()
                                .filter(e -> FeeSkuTypeEnum.CUSTOM_SKU.code.equals(e.getBizRule().get(CommonBizRule.Fields.skuType)))
                                .forEach(e -> e.getBizRule().put(CommonBizRule.Fields.skuType, FeeSkuTypeEnum.STANDARD_SKU.code));
                        feeRuleList.addAll(mappingFeeRuleList);
                        needFilterFeeRuleByDivisionType = false;
                    }
                }
            }


        } else {
            noMappingServiceIdSet = serviceIds;
        }

        if (CollectionUtils.isNotEmpty(noMappingServiceIdSet)) {
            // 如果映射不为空则需要转换映射
            // 查询计费模板
            List<FeeTemplate> originFeeTemplates = getFeeTemplates(noMappingServiceIdSet, serviceInfos, sceneCode);
            if (CollectionUtils.isNotEmpty(originFeeTemplates)) {
                feeTemplates.addAll(originFeeTemplates);
            }
            if (CollectionUtils.isEmpty(feeTemplates)) {
                throw new BusException(ExceptionEnum.WITHOUT_TEMPLATE.code,
                        StrUtil.format(ExceptionEnum.WITHOUT_TEMPLATE.msg, sceneCode, serviceId1st));
            }
            Criteria feeRuleCondition = getFeeRuleCondition(applyOrderCalculateDTO);
            List<FeeRule> originFeeRuleList = feeRulesBy(feeRuleCondition);
            if (CollectionUtils.isNotEmpty(originFeeRuleList)) {
                feeRuleList.addAll(originFeeRuleList);
            }
        }

        Map<String, List<FeeTemplate>> feeTemplateMap = feeTemplates.stream().collect(Collectors.groupingBy(FeeTemplate::getGroup));

        completeGroup(feeRuleList);

        if (needFilterFeeRuleByDivisionType) {
            feeRuleList = filterFeeRuleByDivisionType(feeRuleList);
        }
        if (CollectionUtils.isEmpty(feeRuleList)) {
            throw new BusException(ExceptionEnum.WITHOUT_RULE.code,
                    StrUtil.format(ExceptionEnum.WITHOUT_RULE.msg, sceneCode, serviceId1st));
        }

        feeRuleList = feeRuleList.stream()
                .collect(
                        Collectors.collectingAndThen(
                                Collectors.toMap(
                                        FeeRule::getFeeRuleId,
                                        e -> e,
                                        (e1, e2) -> e1),
                                map -> new ArrayList<>(map.values())));

        Map<String, List<FeeRule>> feeRuleMap = feeRuleList.stream().collect(Collectors.groupingBy(FeeRule::getGroup));

        // 取差集校验完整性
        if (CollectionUtils.isNotEmpty(CollectionUtils.subtract(feeTemplateMap.keySet(), feeRuleMap.keySet()))) {
            throw new BusException(ExceptionEnum.INCOMPLETE_RULE.code,
                    StrUtil.format(ExceptionEnum.INCOMPLETE_RULE.msg, sceneCode, serviceId1st));
        }

        // 2024年6月7日  与远康确认，将补全服务基础服务费属性前置
        // 补全服务基础服务费属性
//        serviceInfos.forEach(this::completeServiceAttribute);

        // templateId完整性校验与过滤
        feeTemplateMap.forEach((key, value) -> {
            Set<Long> templateIds = value.stream().filter(e -> e.getBizRule().get("applyFlag") == null || !e.getBizRule().get("applyFlag").equals(ApplyFlagEnum.NO_APPLY_IF_HAVE.code)).map(FeeTemplate::getTemplateId).collect(Collectors.toSet());

            List<FeeRule> ruleList = feeRuleMap.get(key);
            Set<Long> realTemplateIds = ruleList.stream().map(FeeRule::getTemplateId).collect(Collectors.toSet());
            // 差集
            Collection<?> unConfigTemplateIds = CollectionUtils.subtract(templateIds, realTemplateIds);
            Collection<?> unConfigTemplateIds2 = CollectionUtils.subtract(realTemplateIds, templateIds);
            Boolean integrityValidation = applyOrderCalculateDTO.getNeedIntegrityValidation();
            Boolean needIntegrityValidation = currentSceneInfo.getNeedIntegrityValidation();
            if (CollectionUtils.isNotEmpty(unConfigTemplateIds) || CollectionUtils.isNotEmpty(unConfigTemplateIds2)) {
                // 二者任一为假，都不需要校验完整性，考虑到integrityValidation、needIntegrityValidation可能为null，故与False比较再取反
                if (!Boolean.FALSE.equals(integrityValidation) && !Boolean.FALSE.equals(needIntegrityValidation)) {
//                    throw new BusException(key + "计费规则配置不完整；unConfigTemplateIds=" + JSON.toJSONString(unConfigTemplateIds, SerializerFeature.DisableCircularReferenceDetect));
                    throw new BusException(ExceptionEnum.INCOMPLETE_RULE.code,
                            StrUtil.format(ExceptionEnum.INCOMPLETE_RULE.msg, sceneCode, serviceId1st));
                } else {
                    // 不校验完整性，但需要判断unConfigTemplateIds中的skuNo在 订单上下文 与 模板中 是否存在【服务费】的属性（排除AP48014959），
                    // 如果存在则不需要算费

                    // 2024年6月7日 根远康确认，改为 不排除AP48014959
                    List<FeeTemplate> templates;
                    if (baseServiceFeeSwitch) {
                        templates = feeTemplates.stream().filter(e -> unConfigTemplateIds.contains(e.getTemplateId()))
                                .filter(e -> !Constant.BASE_SERVICE_FEE_SKU_NO.equals(e.getBizRule().get(CommonBizRule.Fields.skuNo)))
                                .filter(e -> FeeTypeTagEnum.SERVICE_FEE.code.equals(e.getBizRule().get(CommonBizRule.Fields.feeTypeTag))).collect(Collectors.toList());

                    } else {
                        templates = feeTemplates.stream().filter(e -> unConfigTemplateIds.contains(e.getTemplateId()))
//                                .filter(e -> !"AP48014959".equals(e.getBizRule().get(CommonBizRule.Fields.skuNo)))
                                .filter(e -> FeeTypeTagEnum.SERVICE_FEE.code.equals(e.getBizRule().get(CommonBizRule.Fields.feeTypeTag))).collect(Collectors.toList());

                    }
                    if (CollectionUtils.isNotEmpty(templates)) {
                        for (FeeTemplate template : templates) {
                            String skuNo = template.getBizRule().get(CommonBizRule.Fields.skuNo);
                            for (CalculateServiceInfo serviceInfo : serviceInfos) {
                                String skuValue = extractValue(serviceInfo.getRootAttributeDetailList(), skuNo);
                                if (StringUtils.isNotBlank(skuValue)) {
                                    // 订单上下文存在「服务费」类型的skuNo，且没有该skuNo的计价规则中，故无需计价
//                                    throw new BusException(String.format("订单上下文存在serviceId=%s「服务费」类型的skuNo=%s，且没有该skuNo的计价规则中，故无需计价", serviceInfo.getServiceId(), skuNo));
                                    throw new BusException(ExceptionEnum.ORDER_CONTEXT_EXIST_BUT_RULE_WITHOUT_SERVICE_FEE.code,
                                            StrUtil.format(ExceptionEnum.ORDER_CONTEXT_EXIST_BUT_RULE_WITHOUT_SERVICE_FEE.msg, skuNo));
                                }
                            }
                        }
                    }
                }
            }

            // 去除不在template里面的规则
            ruleList.removeIf(rule -> !templateIds.contains(rule.getTemplateId()));
        });

        completeGroup(feeRuleList);

        // 提取sku，构建算费元素
        List<ApplyOrderCalculateResp.ServiceResult> serviceResultList = new ArrayList<>();

        // 订单维度的指标缓存
        Map<String, String> orderCache = new HashMap<>();
        orderCache.put(SceneInfo.Fields.amountType, currentSceneInfo.getAmountType());
        BigDecimal goodsNumber = applyOrderCalculateDTO.getGoodsNumber();
        if (goodsNumber != null) {
            orderCache.put(ExpressParamEnum.ALL_SERVICE_AP_SKU_NUMBER.bizKey, goodsNumber.toString());
        }
//        extractOrderValue(applyOrderCalculateDTO, orderCache);


        for (CalculateServiceInfo serviceInfo : serviceInfos) {
            Long serviceId = serviceInfo.getServiceId();
            List<FeeRule> feeRules = feeRuleMap.getOrDefault(serviceId.toString(), Collections.emptyList());
            List<ApplyCalculateReq.CalculateRuleDataUnit> calculateRuleDataUnits = new ArrayList<>();
            // 通过skuNo查找属性
            Map<String, String> cache = new HashMap<>(orderCache);

            // 补全服务基础服务费属性
            completeServiceAttribute(serviceInfo);

            for (FeeRule feeRule : feeRules) {
                Map<String, String> bizRule = feeRule.getBizRule();
                log.info("serviceId={}, skuNo={}, skuNumberPathNo={}", serviceId, bizRule.get(CommonBizRule.Fields.skuNo), bizRule.get(CommonBizRule.Fields.skuNumberPathNo));
                cache.putAll(bizRule);

                ValueExtractor<CalculateServiceInfo> suiteValueExtractor = getSuiteValueExtractor(CalculateServiceInfo.class);

                CalculateRuleData calculateRuleData = feeRule.getCalculateRuleData();
                ExpressInfo expressInfo = calculateRuleData.getExpressInfo();
                // 解析计价公式中的变量（即AP开头的）
                String numberExpress;
                if (expressInfo != null && CollectionUtils.isNotEmpty(expressInfo.getExpressInfoUnits())) {
                    numberExpress = expressInfo.getExpressInfoUnits().get(0).getNumberExpress();
                } else {
                    numberExpress = calculateRuleData.getExpress()
                            .replaceFirst("masterInputPrice", "")
                            .replaceFirst("\\*", "").trim();
                }

                if (StringUtils.isBlank(numberExpress)) {
                    throw new BusException(ExceptionEnum.WITHOUT_SKU_NUMBER.code,
                            String.format("%s，serviceId=%s，sceneCode=%s",
                                    ExceptionEnum.WITHOUT_SKU_NUMBER.msg, serviceId1st, sceneCode));
                }

                // FIXME 临时解决方案。长期解决方案应该是在计价规则修复，不应该在这里处理，应该是导入规则的时候或生成模板的时候有问题
                if ("1".equals(numberExpress)) {
                    List<String> expressionParamList = feeRule.getCalculateRuleData().getExpressionParamList();
                    if (CollectionUtils.isNotEmpty(expressionParamList)) {
                        expressionParamList = expressionParamList.stream().filter(StringUtils::isNotBlank).filter(e -> !e.equals(bizRule.get(CommonBizRule.Fields.skuNumberPathNo))).collect(Collectors.toList());
                        feeRule.getCalculateRuleData().setExpressionParamList(expressionParamList);
                    }
                }

                Pattern pattern = Pattern.compile("AP\\d+");
                Matcher matcher = pattern.matcher(numberExpress);
                List<String> numberExpressParamList = new ArrayList<>();
                if (matcher.find()) {
                    numberExpressParamList.add(matcher.group());
                }

                List<Map<String, String>> expressionParamMapList = new ArrayList<>();
                Map<String, String> paramMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(numberExpressParamList)) {
                    List<Map<String, String>> skuNumberParamMapList = getSkuNumberParamMapList(serviceInfo.getRootAttributeDetailList(), numberExpressParamList);
                    if (CollectionUtils.isNotEmpty(skuNumberParamMapList)) {
                        for (Map<String, String> map : skuNumberParamMapList) {
                            cache.putAll(map);
                            Map<String, String> skuNumberParamMap = suiteValueExtractor.extract(serviceInfo, feeRule, cache);
                            if (!MapUtils.isEmpty(skuNumberParamMap)) {
                                expressionParamMapList.add(skuNumberParamMap);
                            }
                        }
                    }
                } else {
                    // 先从订单上下文中获取 skuNo 重复出现的次数（主要应用于门窗 有数组属性的场景）
                    List<ServiceAttribute> rootAttributeDetailList = serviceInfo.getRootAttributeDetailList();
                    String skuNo = bizRule.get(CommonBizRule.Fields.skuNo);
                    int count = countSkuNo(rootAttributeDetailList, skuNo);
                    if (count > 0) {
                        for (int i = 0; i < count; i++) {
                            paramMap = suiteValueExtractor.extract(serviceInfo, feeRule, cache);
                            if (MapUtils.isNotEmpty(paramMap)) {
                                expressionParamMapList.add(paramMap);
                            }
                        }
                    }
                }

                if (MapUtils.isEmpty(paramMap) && CollectionUtils.isEmpty(expressionParamMapList)) {
                    // 跳过这个规则
                    continue;
                }

                // 添加算费单元
                ApplyCalculateReq.CalculateRuleDataUnit unit = new ApplyCalculateReq.CalculateRuleDataUnit();
                // 构建原子
                unit.setExpress(calculateRuleData.getExpress());
                unit.setExpressionParamList(calculateRuleData.getExpressionParamList());
//                unit.setExpressionParamMap(paramMap);
                unit.setExpressionParamMapList(expressionParamMapList);
                unit.setGroup(feeRule.getGroup());
                unit.setFeeRuleId(feeRule.getFeeRuleId());
                unit.setTemplateId(feeRule.getTemplateId());

                calculateRuleDataUnits.add(unit);
            }

            ApplyOrderCalculateResp.ServiceResult serviceResult = new ApplyOrderCalculateResp.ServiceResult();

            ApplyCalculateReq applyCalculateReq = new ApplyCalculateReq();
            applyCalculateReq.setCalculateRuleDataList(calculateRuleDataUnits);
            applyCalculateReq.setSceneCode(sceneCode);
            applyCalculateReq.setDynamicCalculateTime(applyOrderCalculateDTO.getDynamicCalculateTime());
            ApplyCalculateResp applyCalculateResp = calculate(applyCalculateReq, feeRuleList, orderCache, applyOrderCalculateResp.getTid(), serviceId);
            BigDecimal cost = applyCalculateResp.getCost();
            if (Objects.isNull(cost) || cost.compareTo(BigDecimal.ZERO) <= 0){
//                throw new BusException("当前服务（serviceId：" + serviceId + "）上下文找不到可用的规则，故无法计价");
                throw new BusException(ExceptionEnum.COST_LESS_OR_EQU_ZERO.code,
                        String.format("%s，serviceId=%s，sceneCode=%s",
                                ExceptionEnum.COST_LESS_OR_EQU_ZERO.msg, serviceId1st, sceneCode));
            }
            serviceResult.setIndex(serviceInfo.getIndex());
            serviceResult.setCost(cost);
            serviceResult.setDynamicFeeCost(applyCalculateResp.getDynamicFeeCost());

            if (AmountTypeEnum.RANGE_PRICE.code.equals(currentSceneInfo.getAmountType())) {
                serviceResult.setCostMax(applyCalculateResp.getCostMax());
                serviceResult.setDynamicFeeCostMax(applyCalculateResp.getDynamicFeeCostMax());
            }

            serviceResult.setCalculateResultList(applyCalculateResp.getCalculateResultList());
            serviceResult.setSceneCode(sceneCode);
            serviceResult.setIndex(serviceId.toString());

            if (ApolloConfig.SERVICE_DYNAMIC_FEE_RULE_SWITCH) {
                // 只有配置了动态规则（按服务）的才需要处理
                String sceneCods = redisHelper.get(GlobalRedisKeyConstant.SERVICE_DYNAMIC_FEE_RULE_SCENE_CODE);
                if (StringUtils.isNotBlank(sceneCods)) {
                    boolean contained = Arrays.asList(StringUtils.split(sceneCods, ",")).contains(sceneCode);
                    if (contained) {
                        handleServiceDynamicPrice(applyOrderCalculateDTO.getDynamicIndicatorParamList(),
                                serviceResult,
                                from,
                                addressInfo,
                                applyOrderCalculateDTO.getSubSceneCode());
                    }
                }
            }


            serviceResultList.add(serviceResult);
        }

        // 构建返回
        List<ApplyOrderCalculateResp.SceneResult> sceneResultList = applyOrderCalculateResp.getSceneResultList();
        if (CollectionUtils.isEmpty(sceneResultList)) {
            sceneResultList = new ArrayList<>();
            applyOrderCalculateResp.setSceneResultList(sceneResultList);
        }

        BigDecimal cost = serviceResultList.stream().map(ApplyOrderCalculateResp.ServiceResult::getCost).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal dynamicCost = serviceResultList.stream().map(serviceResult -> Objects.nonNull(serviceResult.getDynamicFeeCost()) ? serviceResult.getDynamicFeeCost() : serviceResult.getCost()).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 只计算当前场景的
        ApplyOrderCalculateResp.SceneResult sceneResult = new ApplyOrderCalculateResp.SceneResult();
        sceneResult.setSceneCode(sceneCode);
        sceneResult.setCost(cost);
        sceneResult.setDynamicFeeCost(dynamicCost);

        if (AmountTypeEnum.RANGE_PRICE.code.equals(currentSceneInfo.getAmountType())) {
            BigDecimal costMax = serviceResultList.stream()
                    .map(ApplyOrderCalculateResp.ServiceResult::getCostMax)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal dynamicCostMax = serviceResultList.stream()
                    .map(serviceResult -> Objects.nonNull(serviceResult.getDynamicFeeCostMax()) ? serviceResult.getDynamicFeeCostMax() : serviceResult.getCostMax())
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            sceneResult.setCostMax(costMax);
            sceneResult.setDynamicFeeCostMax(dynamicCostMax);
         }

        sceneResult.setBasePrice(getBasePrice(feeRuleList));
        sceneResult.setServiceResultList(serviceResultList);

        sceneResultList.add(sceneResult);

        return applyOrderCalculateResp;
    }

    private void handleServiceDynamicPrice(List<DynamicIndicatorParam> indicatorParamList,
                                           ApplyOrderCalculateResp.ServiceResult serviceResult,
                                           AccountInfo accountInfo,
                                           AddressInfo addressInfo,
                                           String subSceneCode) {
        if (serviceResult == null) {
            return;
        }

        String userId = Optional.ofNullable(accountInfo).map(AccountInfo::getAccountId).map(Object::toString).orElse(null);
        Long cityId = Optional.ofNullable(addressInfo).map(AddressInfo::getLv3DivisionId).orElse(null);

        BigDecimal dynamicFeeCost = dynamicFeeRuleService.getServiceDynamicFeeCost(
                serviceResult.getSceneCode(),
                subSceneCode,
                serviceResult.getIndex(),
                userId,
                cityId,
                serviceResult.getCost(),
                serviceResult.getDynamicFeeCost(),
                indicatorParamList);
        if (dynamicFeeCost != null) {
            dynamicFeeCost = dynamicFeeCost.setScale(2, RoundingMode.HALF_UP);
            serviceResult.setDynamicFeeCost(dynamicFeeCost);
        }
    }


    private void completeGroup(List<FeeRule> feeRuleList) {
        feeRuleList.forEach(feeRule ->
                Optional.ofNullable(feeRule.getBizRule())
                        .map(rule -> rule.get(CommonBizRule.Fields.serviceId))
                        .ifPresent(feeRule::setGroup));
    }

    private Set<SceneCodeAndServiceId> getSceneCodeAndServiceIds(List<BizRuleMapping> bizRuleMappings) {
        if (CollectionUtils.isEmpty(bizRuleMappings)) {
            return Collections.emptySet();
        }
        Set<SceneCodeAndServiceId> sceneCodeAndServiceIds = new HashSet<>();
        for (BizRuleMapping mapping : bizRuleMappings) {
            Map<String, String> fromBizRule = mapping.getFromBizRule();
            SceneCodeAndServiceId sceneCodeAndServiceId = new SceneCodeAndServiceId(fromBizRule.get(FeeRule.Fields.sceneCode)
                    , fromBizRule.get(CommonBizRule.Fields.serviceId));
            sceneCodeAndServiceIds.add(sceneCodeAndServiceId);
        }
        return sceneCodeAndServiceIds;
    }


    private Set<SceneCodeAndServiceId> getSceneCodeAndServiceIdsNew(List<FeeTemplateMapping> mappings) {
        if (CollectionUtils.isEmpty(mappings)) {
            return Collections.emptySet();
        }
        Set<SceneCodeAndServiceId> sceneCodeAndServiceIds = new HashSet<>();
        for (FeeTemplateMapping mapping : mappings) {
            FeeTemplateMapping.TemplateInfo target = mapping.getTarget();
            SceneCodeAndServiceId sceneCodeAndServiceId = new SceneCodeAndServiceId(target.getSceneCode()
                    , target.getServiceId());
            sceneCodeAndServiceIds.add(sceneCodeAndServiceId);
        }
        return sceneCodeAndServiceIds;
    }


    private List<BizRuleMapping> getBizRuleMappingList(String sceneCode, String fromSceneCode, Set<String> serviceIds) {
        Criteria mappingCriteria = Criteria.where(BizRuleMapping.Fields.sceneCode).is(sceneCode)
                .and(BizRuleMapping.Fields.applyTypes).is(ApplyTypeEnum.PRICING.getCode())
                .and(BizRuleMapping.Fields.toBizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).in(serviceIds);
        if (StringUtils.isNotBlank(fromSceneCode)) {
            mappingCriteria.and(BizRuleMapping.Fields.fromBizRule + PunConstant.DOT +  BizRuleMapping.Fields.sceneCode).is(fromSceneCode);
        }
        List<BizRuleMapping> bizRuleMappings = bizRuleMappingBy(mappingCriteria);
        return bizRuleMappings;
    }

    private List<FeeTemplateMapping> getFeeTemplateMappingList(String sourceSceneCode, String targetSceneCode, Set<String> serviceIds) {
        Criteria mappingCriteria = Criteria.where(FeeTemplateMapping.Fields.source + PunConstant.DOT +  FeeTemplateMapping.TemplateInfo.Fields.sceneCode).is(sourceSceneCode)
                .and(FeeTemplateMapping.Fields.applyTypes).is(ApplyTypeEnum.PRICING.getCode())
                .and(FeeTemplateMapping.Fields.source + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.serviceId).in(serviceIds);
        if (StringUtils.isNotBlank(targetSceneCode)) {
            mappingCriteria.and(FeeTemplateMapping.Fields.target + PunConstant.DOT + FeeTemplateMapping.TemplateInfo.Fields.sceneCode).is(targetSceneCode);
        }
        return feeTemplateMappingsBy(mappingCriteria);
    }


    private Map<Long, List<String>> getTemplateIdWithServiceIdAndSkuNoMap(List<BizRuleMapping> bizRuleMappings) {
        Map<String, Long> serviceIdAndSkuNoWithTemplateIdMap = new HashMap<>();
        for (BizRuleMapping mapping : bizRuleMappings) {
            Map<String, String> toBizRule = mapping.getToBizRule();
            Map<String, String> fromBizRule = mapping.getFromBizRule();
            Long templateId = mapping.getTemplateId();
            String templateIdStr = templateId == null ? "null" : templateId.toString();
            // 因为当前查询已经通过sceneCode过滤了，所以不需要带上sceneCode
            // 结构：serviceId#skuNo#toTemplateId#toSkuNumberPathNo#fromSkuNumberPathNo
            serviceIdAndSkuNoWithTemplateIdMap.put(
                    toBizRule.get(CommonBizRule.Fields.serviceId) + "#"
                            + toBizRule.get(CommonBizRule.Fields.skuNo) + "#"
                            + templateIdStr + "#"
                            + toBizRule.get(CommonBizRule.Fields.skuNumberPathNo) + "#"
                            + fromBizRule.get(CommonBizRule.Fields.skuNumberPathNo),
                    Long.valueOf(fromBizRule.get(CommonBizRule.Fields.templateId)));
        }
        Map<Long, List<String>> templateIdWithServiceIdAndSkuNoMap = new HashMap<>();
        for (Map.Entry<String, Long> entry : serviceIdAndSkuNoWithTemplateIdMap.entrySet()) {
            Long templateId = entry.getValue();
            List<String> serviceIdAndSkuNoList = templateIdWithServiceIdAndSkuNoMap.computeIfAbsent(templateId, k -> new ArrayList<>());
            serviceIdAndSkuNoList.add(entry.getKey());
        }
        return templateIdWithServiceIdAndSkuNoMap;
    }


    private Map<Long, List<String>> getTemplateIdWithServiceIdAndSkuNoMapNew(List<FeeTemplateMapping> mappings) {
        Map<String, Long> serviceIdAndSkuNoWithTemplateIdMap = new HashMap<>();
        for (FeeTemplateMapping mapping : mappings) {
            FeeTemplateMapping.TemplateInfo source = mapping.getSource();
            FeeTemplateMapping.TemplateInfo target = mapping.getTarget();
            Long sourceTemplateId = source.getTemplateId();
            String sourceTemplateIdStr = sourceTemplateId == null ? "null" : sourceTemplateId.toString();
            // 因为当前查询已经通过sceneCode过滤了，所以不需要带上sceneCode
            // 结构：serviceId#skuNo#sourceTemplateId#toSkuNumberPathNo#fromSkuNumberPathNo
            serviceIdAndSkuNoWithTemplateIdMap.put(
                    source.getServiceId() + "#"
                    + source.getSkuNo() + "#"
                    + sourceTemplateIdStr + "#"
                    + source.getSkuNumberPathNo() + "#"
                    + target.getSkuNumberPathNo(),

                    target.getTemplateId());
        }
        Map<Long, List<String>> templateIdWithServiceIdAndSkuNoMap = new HashMap<>();
        for (Map.Entry<String, Long> entry : serviceIdAndSkuNoWithTemplateIdMap.entrySet()) {
            Long templateId = entry.getValue();
            List<String> serviceIdAndSkuNoList = templateIdWithServiceIdAndSkuNoMap.computeIfAbsent(templateId, k -> new ArrayList<>());
            serviceIdAndSkuNoList.add(entry.getKey());
        }
        return templateIdWithServiceIdAndSkuNoMap;
    }


    private List<FeeTemplate> getFeeTemplateList(List<CalculateServiceInfo> serviceInfos, Set<SceneCodeAndServiceId> sceneCodeAndServiceIds) {
        if (CollectionUtils.isEmpty(sceneCodeAndServiceIds)) {
            return Collections.emptyList();
        }
        List<Criteria> criteriaList = new ArrayList<>();
        for (SceneCodeAndServiceId sceneCodeAndServiceId : sceneCodeAndServiceIds) {
            String sceneCode = sceneCodeAndServiceId.getSceneCode();
            String serviceId = sceneCodeAndServiceId.getServiceId();
            Criteria criteria = new Criteria(FeeTemplate.Fields.sceneCode).is(sceneCode)
                    .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId);
            criteriaList.add(criteria);
        }
        Criteria feeTemplateCriteria = new Criteria().orOperator(criteriaList.toArray(new Criteria[0]))
                .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.feeTypeTag).ne(null);
        List<FeeTemplate> feeTemplates = feeTemplatesBy(feeTemplateCriteria);
        SceneCodeAndServiceId sceneCodeAndServiceId = sceneCodeAndServiceIds.iterator().next();
        if (CollectionUtils.isEmpty(feeTemplates)) {
            throw new BusException(ExceptionEnum.MAPPING_WITHOUT_TEMPLATE.code,
                    String.format("%s，serviceId=%s，sceneCode=%s",
                            ExceptionEnum.MAPPING_WITHOUT_TEMPLATE.msg,
                            sceneCodeAndServiceId.getServiceId(),
                            sceneCodeAndServiceId.getSceneCode()));
        }
        feeTemplates = checkApplyFlag(serviceInfos, feeTemplates);
        if (CollectionUtils.isEmpty(feeTemplates)) {
            throw new BusException(ExceptionEnum.MAPPING_WITHOUT_TEMPLATE_NO_APPLY.code,
                    String.format("%s，serviceId=%s，sceneCode=%s",
                            ExceptionEnum.MAPPING_WITHOUT_TEMPLATE_NO_APPLY.msg,
                            sceneCodeAndServiceId.getServiceId(),
                            sceneCodeAndServiceId.getSceneCode()));
        }
        String serviceId = serviceInfos.get(0).getServiceId().toString();
        feeTemplates.forEach(e -> e.setGroup(serviceId));
        feeTemplates.forEach(e -> e.getBizRule().put(CommonBizRule.Fields.serviceId, serviceId));
        return feeTemplates;
    }

    private List<FeeRule> getFeeRuleList(ApplyOrderCalculateDTO applyOrderCalculateDTO,
                                         Map<Long, List<String>> templateIdWithServiceIdAndSkuNoMap,
                                         Set<SceneCodeAndServiceId> fromSceneCodeAndServiceIds,
                                         List<FeeTemplate> feeTemplates) {
        List<FeeRule> feeRuleList;
        Criteria feeRuleCondition = getFeeRuleConditionByTemplateIds(applyOrderCalculateDTO, fromSceneCodeAndServiceIds);
        feeRuleList = feeRulesBy(feeRuleCondition);
        if (CollectionUtils.isEmpty(feeRuleList)) {
            throw new BusException(ExceptionEnum.INCOMPLETE_RULE_MAPPING.code,
                    String.format("%s，serviceId=%s，sceneCode=%s",
                            ExceptionEnum.INCOMPLETE_RULE_MAPPING.msg,
                            applyOrderCalculateDTO.getServiceInfos().get(0).getServiceId(),
                            applyOrderCalculateDTO.getCurrentSceneInfo().getSceneCode()));
        }
        feeRuleList = filterFeeRuleByDivisionType(feeRuleList);

        List<FeeRule> needSupplementFeeRuleList = new ArrayList<>();
        for (FeeRule feeRule : feeRuleList) {
            Long templateId = feeRule.getTemplateId();
            List<String> serviceIdAndSkuNoList = templateIdWithServiceIdAndSkuNoMap.get(templateId);
            if (CollectionUtils.isEmpty(serviceIdAndSkuNoList)) {
                continue;
            }
            for (int i = 0; i < serviceIdAndSkuNoList.size(); i++) {
                if (i == 0) {
                    replaceKeyAttributes(feeRule, serviceIdAndSkuNoList, i);
                } else {
                    feeRule.setCreateTime(null);
                    feeRule.setModifyTime(null);
                    FeeRule newFeeRule = new Gson().fromJson(JSON.toJSONString(feeRule), FeeRule.class);
                    replaceKeyAttributes(newFeeRule, serviceIdAndSkuNoList, i);
                    needSupplementFeeRuleList.add(newFeeRule);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(needSupplementFeeRuleList)) {
            feeRuleList.addAll(needSupplementFeeRuleList);
        }
        List<String> skuNoList = feeRuleList.stream().filter(e -> "true".equals(e.getBizRule().get(sameSkuNoNeedRemoveFlag)))
                .map(e -> e.getBizRule().get(CommonBizRule.Fields.skuNo)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(skuNoList)) {
            for (String skuNo : skuNoList) {
                List<FeeRule> needRemoveFeeRuleList = feeRuleList.stream().filter(feeRule -> skuNo.equals(feeRule.getBizRule().get(CommonBizRule.Fields.skuNo))
                        && feeRule.getBizRule().get(sameSkuNoNeedRemoveFlag) == null).collect(Collectors.toList());
                feeRuleList.removeAll(needRemoveFeeRuleList);
                Set<Long> needRemoveTemplateIds = needRemoveFeeRuleList.stream().map(FeeRule::getTemplateId).collect(Collectors.toSet());
                feeTemplates.removeIf(e -> needRemoveTemplateIds.contains(e.getTemplateId()));
            }
        }
        return feeRuleList;
    }

    private void replaceKeyAttributes(FeeRule feeRule, List<String> serviceIdAndSkuNoList, int i) {
        CalculateRuleData calculateRuleData = feeRule.getCalculateRuleData();
        // 结构：serviceId#skuNo#toTemplateId#toSkuNumberPathNo#fromSkuNumberPathNo
        String serviceIdAndSkuNo = serviceIdAndSkuNoList.get(i);
        String[] split = serviceIdAndSkuNo.split("#");
        String toServiceId = split[0];
        String toSkuNo = split[1];
        String toSkuNumberPathNo = split[3];
        String fromSkuNumberPathNo = split[4];
        Map<String, String> bizRule = feeRule.getBizRule();
        feeRule.setGroup(toServiceId);
        bizRule.put(CommonBizRule.Fields.serviceId, toServiceId);
        bizRule.put(CommonBizRule.Fields.group, toServiceId);
        bizRule.put(CommonBizRule.Fields.skuNo, toSkuNo);
        bizRule.put(CommonBizRule.Fields.skuNumberPathNo, toSkuNumberPathNo);
        bizRule.put(sameSkuNoNeedRemoveFlag, "true");
        String express = calculateRuleData.getExpress().replace(fromSkuNumberPathNo, toSkuNumberPathNo);
        calculateRuleData.setExpress(express);

        // 替换参数
        List<String> expressionParamList = calculateRuleData.getExpressionParamList();
        if (CollectionUtils.isNotEmpty(expressionParamList)) {
            for (int j = 0; j < expressionParamList.size(); j++) {
                String param = expressionParamList.get(j);
                if (fromSkuNumberPathNo.equals(param)) {
                    expressionParamList.set(j, toSkuNumberPathNo);
                }
            }
        }
        // 不应该在bizRule中冗余expression
        String bizRuleExpression = bizRule.get("expression");
        if (StringUtils.isNotBlank(bizRuleExpression)) {
            String expression = bizRuleExpression.replace(fromSkuNumberPathNo, toSkuNumberPathNo);
            bizRule.put("expression", expression);
        }
    }

    private void completeAddress(AddressInfo addressInfo) {
        Long divisionId = addressInfo.getDivisionId();
        String redisKey = this.getClass().getName() + ":completeAddress:divisionId=" + divisionId;
        String addressStr = redisHelper.get(redisKey);
        Address address;
        if (StringUtils.isNotBlank(addressStr)) {
            address = JSON.parseObject(addressStr, Address.class);
        } else {
            address = addressApi.getDivisionInfoByDivisionId(divisionId);
            if (address == null) {
                throw new BusException("divisionId有误");
            }
            redisHelper.set(redisKey, JSON.toJSONString(address), 7 * 24 * 60 * 60);
        }

        Long lv1DivisionId = address.getLv1DivisionId();
        if (lv1DivisionId == null || lv1DivisionId.equals(0L)) {
            addressInfo.setLv1DivisionId(null);
        } else {
            addressInfo.setLv1DivisionId(lv1DivisionId);
            addressInfo.setLv1DivisionName(address.getLv1DivisionName());
        }

        Long lv2DivisionId = address.getLv2DivisionId();
        if (lv2DivisionId == null || lv2DivisionId.equals(0L)) {
            addressInfo.setLv2DivisionId(null);
        } else {
            addressInfo.setLv2DivisionId(lv2DivisionId);
            addressInfo.setLv2DivisionName(address.getLv2DivisionName());
        }

        Long lv3DivisionId = address.getLv3DivisionId();
        if (lv3DivisionId == null || lv3DivisionId.equals(0L)) {
            addressInfo.setLv3DivisionId(null);
        } else {
            addressInfo.setLv3DivisionId(lv3DivisionId);
            addressInfo.setLv3DivisionName(address.getLv3DivisionName());
        }

        Long lv4DivisionId = address.getLv4DivisionId();
        if (lv4DivisionId == null || lv4DivisionId.equals(0L)) {
            addressInfo.setLv4DivisionId(null);
        } else {
            addressInfo.setLv4DivisionId(lv4DivisionId);
            addressInfo.setLv4DivisionName(address.getLv4DivisionName());
        }

        Long lv5DivisionId = address.getLv5DivisionId();
        if (lv5DivisionId == null || lv5DivisionId.equals(0L)) {
            addressInfo.setLv5DivisionId(null);
        } else {
            addressInfo.setLv5DivisionId(lv5DivisionId);
            addressInfo.setLv5DivisionName(address.getLv5DivisionName());
        }
//        addressInfo.setLv1DivisionId(lv1DivisionId);
//        addressInfo.setLv2DivisionId(address.getLv2DivisionId());
//        addressInfo.setLv3DivisionId(address.getLv3DivisionId());
//        addressInfo.setLv4DivisionId(address.getLv4DivisionId());
//        addressInfo.setLv5DivisionId(address.getLv5DivisionId());
    }

    private BigDecimal getBasePrice(List<FeeRule> feeRuleList) {
        return feeRuleList.stream()
                .map(f -> f.getBizRule().get(CommonBizRule.Fields.basePrice))
                .filter(Objects::nonNull)
                .map(BigDecimal::new)
                .max(BigDecimal::compareTo).orElse(null);
    }

    private List<FeeRule> filterFeeRuleByDivisionType(List<FeeRule> feeRuleList) {
        if (CollectionUtils.isEmpty(feeRuleList)) {
            return feeRuleList;
        }
        Map<String, FeeRule> ruleMap = new HashMap<>();
        for (FeeRule feeRule : feeRuleList) {
            Long templateId = feeRule.getTemplateId();
            String group = feeRule.getGroup();
            String key = templateId + "_" + group;
            if (!ruleMap.containsKey(key) || compareFeeRuleByDivisionType(feeRule, ruleMap.get(key)) > 0) {
                ruleMap.put(key, feeRule);
            }
        }
        return new ArrayList<>(ruleMap.values());
    }


    private int compareFeeRuleByDivisionType(FeeRule feeRule1, FeeRule feeRule2) {
        if (feeRule1 == null || feeRule2 == null) {
            return 0;
        }
        String divisionType1 = feeRule1.getBizRule().get(BizFieldEnum.DIVISION_TYPE.name);
        String divisionType2 = feeRule2.getBizRule().get(BizFieldEnum.DIVISION_TYPE.name);

        List<String> priorityList = Arrays.asList(
                DivisionTypeEnum.COUNTRY.code,
                DivisionTypeEnum.PROVINCE.code,
                DivisionTypeEnum.CITY.code,
                DivisionTypeEnum.DISTRICT.code,
                DivisionTypeEnum.STREET.code);

        return priorityList.indexOf(divisionType1) - priorityList.indexOf(divisionType2);
    }


    private List<Map<String, String>> getSkuNumberParamMapList(List<ServiceAttribute> serviceAttributeList, List<String> numberExpressParamList) {
        if (CollectionUtils.isEmpty(numberExpressParamList) || CollectionUtils.isEmpty(serviceAttributeList)) {
            return null;
        }
        Map<String, List<String>> paramMap = new HashMap<>();
        for (String numberExpressParam : numberExpressParamList) {
            for (ServiceAttribute attribute : serviceAttributeList) {
                List<ServiceAttributeValue> childList = attribute.getChildList();
                extractNumberValue(numberExpressParam, childList, paramMap);
            }
        }

        boolean validate = numberExpressParamValueMapIntegrityCheck(paramMap);
        if (!validate) {
            throw new BusException(ExceptionEnum.ORDER_CONTEXT_INCOMPLETE_SKU_NUMBER.code,
                    String.format("%s",ExceptionEnum.ORDER_CONTEXT_INCOMPLETE_SKU_NUMBER.msg));
        }

        return mapToList(paramMap);
    }

    private void extractNumberValue(String numberExpressParam, List<ServiceAttributeValue> childList, Map<String, List<String>> paramMap) {
        if (CollectionUtils.isNotEmpty(childList)) {
            for (ServiceAttributeValue serviceAttributeValue : childList) {
                String attributePathNo = serviceAttributeValue.getAttributePathNo();
                if (numberExpressParam.equals(attributePathNo)) {
                    String value = serviceAttributeValue.getValue();
                    List<String> paramValueList = paramMap.get(numberExpressParam);
                    if (CollectionUtils.isEmpty(paramValueList)) {
                        paramValueList = new ArrayList<>();
                        paramMap.put(numberExpressParam, paramValueList);
                    }
                    paramValueList.add(value);
                }
                List<ServiceAttribute> childAttributeList = serviceAttributeValue.getChildList();
                if (CollectionUtils.isNotEmpty(childAttributeList)) {
                    for (ServiceAttribute serviceAttribute : childAttributeList) {
                        List<ServiceAttributeValue> valueChildList = serviceAttribute.getChildList();
                        if (CollectionUtils.isNotEmpty(valueChildList)) {
                            extractNumberValue(numberExpressParam, valueChildList, paramMap);
                        }
                    }
                }
            }
        }
    }


    /**
     * 校验计价数量参数值完整性
     *
     * @param numberExpressParamMapList
     * @return
     */
    private boolean numberExpressParamValueMapIntegrityCheck(Map<String, List<String>> numberExpressParamMapList) {

        if (numberExpressParamMapList.isEmpty()) {
            return true;  // 如果列表为空，则认为长度相等
        }

        int size = -1;  // 初始化参考长度为 -1

        for (List<String> stringList : numberExpressParamMapList.values()) {
            if (size == -1) {
                size = stringList.size();  // 第一次迭代时，将当前列表长度作为参考长度
            } else if (stringList.size() != size) {
                return false;  // 如果当前列表长度与参考长度不相等，则返回 false
            }
        }

        return true;  // 所有列表长度相等
    }


    private List<Map<String, String>> mapToList(Map<String, List<String>> inputMap) {
        List<Map<String, String>> outputList = new ArrayList<>();

        int maxSize = getMaxListSize(inputMap);

        for (int i = 0; i < maxSize; i++) {
            Map<String, String> map = new HashMap<>();

            for (Map.Entry<String, List<String>> entry : inputMap.entrySet()) {
                String key = entry.getKey();
                List<String> values = entry.getValue();

                if (i < values.size()) {
                    String value = values.get(i);
                    map.put(key, value);
                }
            }
            outputList.add(map);
        }
        return outputList;
    }


    private static int getMaxListSize(Map<String, List<String>> map) {
        int maxSize = 0;

        for (List<String> values : map.values()) {
            maxSize = Math.max(maxSize, values.size());
        }

        return maxSize;
    }


    private Criteria getFeeRuleCondition(ApplyOrderCalculateDTO applyOrderCalculateReq) {
        Set<String> serviceIds = applyOrderCalculateReq.getServiceInfos().stream().map(CalculateServiceInfo::getServiceId).map(Objects::toString).collect(Collectors.toSet());
        SceneInfo currentSceneInfo = applyOrderCalculateReq.getCurrentSceneInfo();
        Criteria feeRuleCondition = Criteria.where(FeeRule.Fields.sceneCode).is(currentSceneInfo.getSceneCode());
        // 用户信息
        String bizIdType = currentSceneInfo.getBizIdType();
        BizIdTypeEnum bizIdTypeEnum = BizIdTypeEnum.fromCode(bizIdType);
        if (bizIdTypeEnum != null) {
            if (bizIdTypeEnum.equals(BizIdTypeEnum.USER_ID)) {
                feeRuleCondition.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + PlatformFixedPriceBizRule.Fields.userId).is(applyOrderCalculateReq.getFrom().getAccountId().toString());
            } else if(bizIdTypeEnum.equals(BizIdTypeEnum.MASTER_ID)) {
//                feeRuleCondition.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + PlatformFixedPriceBizRule.Fields.masterId).is(applyOrderCalculateReq.getBizRule().get(CommonBizRule.Fields.masterId).toString());
            }
        }
        feeRuleCondition.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + PlatformFixedPriceBizRule.Fields.feeTypeTag).ne(null);
        feeRuleCondition.and(FeeRule.Fields.bizRule + PunConstant.DOT + PlatformFixedPriceBizRule.Fields.serviceId).in(serviceIds);

        AddressInfo addressInfo = applyOrderCalculateReq.getAddressInfo();
        // 一级国家 五级街道
        if (Objects.isNull(addressInfo)) {
            throw new BusException("addressInfo is null");
        }

        DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(addressInfo.getDivisionType());
        if (Objects.isNull(divisionTypeEnum)) {
            throw new BusException("addressInfo.divisionType设置错误");
        }
        // 只需要查省、市
        //
        // 查出 国 省 市 区 然后根据是否需要回溯选择合适的级别
        List<Criteria> criteriaList = buildFeeRuleAddressCondition(addressInfo);

        feeRuleCondition.orOperator(criteriaList.toArray(new Criteria[0]));

        Map<String, String> bizRule = applyOrderCalculateReq.getBizRule();
        // 与下方逻辑重复，故注释掉
//        if (bizRule != null && bizRule.get(CommonBizRule.Fields.bizTag) != null) {
//            feeRuleCondition.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizTag).is(bizRule.get(CommonBizRule.Fields.bizTag));
//        }
        if (MapUtils.isNotEmpty(bizRule)) {
            bizRule.forEach((key, value) -> {
                if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value)) {
                    feeRuleCondition.and(FeeRule.Fields.bizRule + PunConstant.DOT + key).is(value);
                }
            });
        }

        return feeRuleCondition;
    }

    private Criteria getFeeRuleConditionByTemplateIds(ApplyOrderCalculateDTO applyOrderCalculateReq, Set<SceneCodeAndServiceId> fromSceneCodeAndServiceIds) {
        Criteria feeRuleCondition = Criteria.where(FeeTemplate.Fields.bizRule + PunConstant.DOT + PlatformFixedPriceBizRule.Fields.feeTypeTag).ne(null);
        SceneInfo currentSceneInfo = applyOrderCalculateReq.getCurrentSceneInfo();
        // 用户信息
        String bizIdType = currentSceneInfo.getBizIdType();
        BizIdTypeEnum bizIdTypeEnum = BizIdTypeEnum.fromCode(bizIdType);
        if (bizIdTypeEnum != null) {
            if (bizIdTypeEnum.equals(BizIdTypeEnum.USER_ID)) {
                feeRuleCondition.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + PlatformFixedPriceBizRule.Fields.userId).is(applyOrderCalculateReq.getFrom().getAccountId().toString());
            }
        }
        List<Criteria> sceneCodeAndServiceIdCriteriaList = new ArrayList<>();
        for (SceneCodeAndServiceId sceneCodeAndServiceId : fromSceneCodeAndServiceIds) {
            String sceneCode = sceneCodeAndServiceId.getSceneCode();
            String serviceId = sceneCodeAndServiceId.getServiceId();
            Criteria criteria = new Criteria(FeeTemplate.Fields.sceneCode).is(sceneCode)
                    .and(FeeTemplate.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId);
            sceneCodeAndServiceIdCriteriaList.add(criteria);
        }
        Criteria sceneCodeAndServiceCriteria = new Criteria().orOperator(sceneCodeAndServiceIdCriteriaList.toArray(new Criteria[0]));

        AddressInfo addressInfo = applyOrderCalculateReq.getAddressInfo();
        // 一级国家 五级街道
        if (Objects.isNull(addressInfo)) {
            throw new BusException("addressInfo is null");
        }

        DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(addressInfo.getDivisionType());
        if (Objects.isNull(divisionTypeEnum)) {
            throw new BusException("addressInfo.divisionType设置错误");
        }
        // 只需要查省、市
        //
        // 查出 国 省 市 区 然后根据是否需要回溯选择合适的级别
        List<Criteria> criteriaList = buildFeeRuleAddressCondition(addressInfo);
        Criteria addressCriteria = new Criteria().orOperator(criteriaList.toArray(new Criteria[0]));

        feeRuleCondition.andOperator(addressCriteria, sceneCodeAndServiceCriteria);

        Map<String, String> bizRule = applyOrderCalculateReq.getBizRule();
        // 与下方逻辑重复，故注释掉
//        if (bizRule != null && bizRule.get(CommonBizRule.Fields.bizTag) != null) {
//            feeRuleCondition.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizTag).is(bizRule.get(CommonBizRule.Fields.bizTag));
//        }
        if (MapUtils.isNotEmpty(bizRule)) {
            bizRule.forEach((key, value) -> {
                if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value)) {
                    // 历史问题，bizId、masterId、userId混用
                    if (CommonBizRule.Fields.bizId.equals(key) && bizIdTypeEnum != null) {
                        switch (bizIdTypeEnum) {
                            case MASTER_ID:
                                feeRuleCondition.orOperator(new Criteria(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.masterId).is(value),
                                        new Criteria(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizId).is(value));
                                break;
                            case USER_ID:
                                feeRuleCondition.orOperator(new Criteria(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.userId).is(value),
                                        new Criteria(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizId).is(value));
                                break;
                        }
                    } else {
                        feeRuleCondition.and(FeeRule.Fields.bizRule + PunConstant.DOT + key).is(value);
                    }
                }
            });
        }

        return feeRuleCondition;
    }


    private String extractValue(List<ServiceAttribute> serviceAttributes, String key) {
        for (ServiceAttribute serviceAttribute : serviceAttributes) {
            for (ServiceAttributeValue serviceAttributeValue : serviceAttribute.getChildList()) {

                if (key.equals(serviceAttributeValue.getAttributePathNo())) {
                    // 将attributePathNo置空，这样可以提取出数组的所有值
//                    serviceAttributeValue.setAttributePathNo(null);
                    return serviceAttributeValue.getValue();
                }
                List<ServiceAttribute> childList = serviceAttributeValue.getChildList();
                if (CollectionUtils.isNotEmpty(childList)) {
                    String value = extractValue(childList, key);
                    if (com.wanshifu.framework.utils.StringUtils.isNotBlank(value)) {
//                        serviceAttributeValue.setAttributePathNo(null);
                        return value;
                    }
                }
            }
        }
        return null;
    }


    private int countSkuNo(List<ServiceAttribute> serviceAttributes, String skuNo) {
        if (CollectionUtils.isEmpty(serviceAttributes) || StringUtils.isBlank(skuNo)) {
            return 0;
        }

        int count = 0;

        for (ServiceAttribute serviceAttribute : serviceAttributes) {
            if (serviceAttribute != null) {
                List<ServiceAttributeValue> childList = serviceAttribute.getChildList();
                if (CollectionUtils.isNotEmpty(childList)) {
                    for (ServiceAttributeValue serviceAttributeValue : childList) {
                        if (serviceAttributeValue != null ) {
                            if (skuNo.equals(serviceAttributeValue.getAttributePathNo())) {
                                count++;
                            }
                            List<ServiceAttribute> grandChildList = serviceAttributeValue.getChildList();
                            if (CollectionUtils.isNotEmpty(grandChildList)) {
                                count += countSkuNo(grandChildList, skuNo); // 递归调用
                            }
                        }
                    }
                }
            }
        }

        return count;
    }


}
