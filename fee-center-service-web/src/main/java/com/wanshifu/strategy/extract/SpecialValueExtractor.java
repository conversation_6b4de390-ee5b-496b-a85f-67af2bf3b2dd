package com.wanshifu.strategy.extract;

import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.dto.CalculateServiceInfo;
import com.wanshifu.fee.center.domain.dto.ServiceAttribute;
import com.wanshifu.fee.center.domain.dto.ServiceAttributeValue;
import com.wanshifu.fee.center.domain.enums.ExpressParamEnum;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * ExpressParamEnum 属性值提取
 */
@Slf4j
public abstract class SpecialValueExtractor<T> implements ValueExtractor<T> {


    public String extract(CalculateServiceInfo source, FeeRule feeRule, Map<String, String> cache, ExpressParamEnum paramEnum) {
        if (paramEnum == ExpressParamEnum.AP_SKU_PRICE) {
            String apSkuPrice = feeRule.getBizRule().get(ExpressParamEnum.AP_SKU_PRICE.bizKey);
            if (StringUtils.isNotBlank(apSkuPrice)) {
                return apSkuPrice;
            }
            return feeRule.getBizRule().get(ExpressParamEnum.MASTER_INPUT_PRICE.bizKey);
        }

        if (paramEnum == ExpressParamEnum.FEE_TYPE_TAG) {
            return feeRule.getBizRule().get(ExpressParamEnum.FEE_TYPE_TAG.bizKey);
        }

        if (paramEnum == ExpressParamEnum.AP_SKU_NUMBER) {
            String ap = feeRule.getBizRule().get(ExpressParamEnum.AP_SKU_NUMBER.bizKey);
            String value = cache.get(ap);
            if (StringUtils.isNotBlank(value)) {
                return value;
            } else if ("1".equals(ap)) {
                return "1";
            }
            return extractValue(source.getRootAttributeDetailList(), ap);
        }

        if (paramEnum == ExpressParamEnum.AP_SKU_NO) {
            String ap = feeRule.getBizRule().get(ExpressParamEnum.AP_SKU_NO.bizKey);
            String value = cache.get(ap);
            if (StringUtils.isNotBlank(value)) {
                return value;
            }
            return extractValue(source.getRootAttributeDetailList(), ap);
        }

        if (paramEnum == ExpressParamEnum.ALL_SERVICE_AP_SKU_NUMBER) {
            // 返回数量只之和
            return cache.get(ExpressParamEnum.ALL_SERVICE_AP_SKU_NUMBER.bizKey);
        }


        return null;
    }

    // key == getAttributePathNo
    private String extractValue(List<ServiceAttribute> serviceAttributes, String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        for (ServiceAttribute serviceAttribute : serviceAttributes) {
            for (ServiceAttributeValue serviceAttributeValue : serviceAttribute.getChildList()) {

                if (key.equals(serviceAttributeValue.getAttributePathNo())) {
                    return serviceAttributeValue.getValue();
                }
                List<ServiceAttribute> childList = serviceAttributeValue.getChildList();
                if (CollectionUtils.isNotEmpty(childList)) {
                    String value = extractValue(childList, key);
                    if (StringUtils.isNotBlank(value)) {
                        return value;
                    }
                }
            }
        }
        return null;
    }
}
