package com.wanshifu.strategy.extract;

import com.wanshifu.api.utils.MapUtils;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.dto.*;
import com.wanshifu.fee.center.domain.enums.CustomSkuValueTypeEnum;
import com.wanshifu.fee.center.domain.enums.ExpressParamEnum;
import com.wanshifu.fee.center.domain.enums.FeeSkuTypeEnum;
import com.wanshifu.fee.center.domain.enums.MatchSkuTypeEnum;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.utils.StrKit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class ServiceAttributeValueExtractor extends SpecialValueExtractor<ServiceDTO> {

//    private final static String SKU_NO = "skuNo";
//    private final static String ATTRIBUTE_VALUE_MAX = "attributeValueMax";
//    private final static String ATTRIBUTE_VALUE_MIN = "attributeValueMin";
    private final static String SKU_TYPE = "skuType";

    public Map<String, String> extract(ServiceDTO serviceDTO, FeeRule feeRule, Map<String, String> cache) {
        Map<String, String> bizRule = feeRule.getBizRule();
        String skuNo = bizRule.get(CommonBizRule.Fields.skuNo);
        Map<String, String> result = new HashMap<>();
        List<AttributeDTO> attributeDTOS = serviceDTO.getAttributeDtoList();
        boolean checkSkuNo = checkSkuNo(attributeDTOS, bizRule, skuNo);
        if (checkSkuNo) {
            // 满足准入条件，取值
            log.info("满足准入条件  feeRuleId={} skuNo={}", feeRule.getFeeRuleId(), skuNo);
            List<String> expressionParamList = feeRule.getCalculateRuleData().getExpressionParamList();
            for (String key : expressionParamList) {
                if (Objects.nonNull(cache.get(key))) {
                    result.put(key, cache.get(key));
                    continue;
                }
                // 如果是特殊的先检查特殊
                ExpressParamEnum expressParamEnum = ExpressParamEnum.fromCode(key);
                if (Objects.nonNull(expressParamEnum)) {
                    String extractValue = extract(serviceDTO, feeRule, cache, expressParamEnum);
                    cache.put(key, StrKit.trimSafely(extractValue));
                    result.put(key, StrKit.trimSafely(extractValue));
                    continue;
                }

                String extractValue = extractValue(attributeDTOS, key);

                // 非特殊的，不缓存
                result.put(key, StrKit.trimSafely(extractValue));
            }
        }

        return result;
    }


    public String extract(ServiceDTO source, FeeRule feeRule, Map<String, String> cache, ExpressParamEnum paramEnum) {
        if (paramEnum == ExpressParamEnum.AP_SKU_PRICE) {
            String apSkuPrice = feeRule.getBizRule().get(ExpressParamEnum.AP_SKU_PRICE.bizKey);
            if (StringUtils.isNotBlank(apSkuPrice)) {
                return apSkuPrice;
            }
            return feeRule.getBizRule().get(ExpressParamEnum.MASTER_INPUT_PRICE.bizKey);
        }

        if (paramEnum == ExpressParamEnum.FEE_TYPE_TAG) {
            return feeRule.getBizRule().get(ExpressParamEnum.FEE_TYPE_TAG.bizKey);
        }

        if (paramEnum == ExpressParamEnum.AP_SKU_NUMBER) {
            String ap = feeRule.getBizRule().get(ExpressParamEnum.AP_SKU_NUMBER.bizKey);
            String value = cache.get(ap);
            if (StringUtils.isNotBlank(value)) {
                return value;
            } else if ("1".equals(ap)) {
                return "1";
            }
            return extractValue(source.getAttributeDtoList(), ap);
        }

        if (paramEnum == ExpressParamEnum.AP_SKU_NO) {
            String ap = feeRule.getBizRule().get(ExpressParamEnum.AP_SKU_NO.bizKey);
            String value = cache.get(ap);
            if (StringUtils.isNotBlank(value)) {
                return value;
            }
            return extractValue(source.getAttributeDtoList(), ap);
        }

        if (paramEnum == ExpressParamEnum.ALL_SERVICE_AP_SKU_NUMBER) {
            // 返回数量只之和
            return cache.get(ExpressParamEnum.ALL_SERVICE_AP_SKU_NUMBER.bizKey);
        }


        return null;
    }

    private boolean checkSkuNo(List<AttributeDTO> serviceAttributes, Map<String, String> bizRule, String skuNo) {
        if (CollectionUtils.isEmpty(serviceAttributes) || MapUtils.isEmpty(bizRule)) {
            return false;
        }
        // 先判断是否为自定义sku
        String matchSkuType = bizRule.get(CommonBizRule.Fields.matchSkuType);
        String valueMaxStr = StrKit.trimSafely(bizRule.get(CommonBizRule.Fields.attributeValueMax));
        String valueMinStr = StrKit.trimSafely(bizRule.get(CommonBizRule.Fields.attributeValueMin));
        String attributeValueFixed = StrKit.trimSafely(bizRule.get(CommonBizRule.Fields.attributeValueFixed));
        String skuType = StrKit.trimSafely(bizRule.get(CommonBizRule.Fields.skuType));
        String customSkuValueType = StrKit.trimSafely(bizRule.get(CommonBizRule.Fields.customSkuValueType));
        if (MatchSkuTypeEnum.OTHER_SKU.getCode().equals(matchSkuType)) {
            String originSkuNo = bizRule.get(CommonBizRule.Fields.skuNo);
            String matchSkuNo = bizRule.get(CommonBizRule.Fields.matchSkuNo);
            boolean hitOriginSkuNo = checkHitSkuNo(serviceAttributes, originSkuNo);
            boolean hitMatchSkuNo = checkHitSkuNo(serviceAttributes, matchSkuNo);
            // 如果没有都命中，则直接返回false
            if (!(hitOriginSkuNo && hitMatchSkuNo)) {
                return false;
            } else {
                // 如果没有最小值和最大值，则表示一定会命中该条自定义sku，可直接返回true
                if (CustomSkuValueTypeEnum.RANGE.getCode().equals(customSkuValueType)) {
                    if (StringUtils.isBlank(valueMinStr) && StringUtils.isBlank(valueMaxStr)) {
                        return true;
                    }
                } else if (CustomSkuValueTypeEnum.FIXED.getCode().equals(customSkuValueType)) {
                    if (StringUtils.isBlank(attributeValueFixed)) {
                        return true;
                    }
                }
            }
        }
        for (AttributeDTO serviceAttribute : serviceAttributes) {
            if (serviceAttribute == null || CollectionUtils.isEmpty(serviceAttribute.getAttributeValueDtoList())) {
                continue;
            }
            for (AttributeValueDTO serviceAttributeValue : serviceAttribute.getAttributeValueDtoList()) {
                if (skuNo.equals(serviceAttributeValue.getAttributePathNo())) {
                    String valueStr = StrKit.trimSafely(serviceAttributeValue.getValue());
                    boolean isCustomSku = FeeSkuTypeEnum.CUSTOM_SKU.code.equals(skuType);
                    // 新逻辑，不一定为自定义sku才需要对比值范围
//                    String valueMaxStr = StrKit.trimSafely(bizRule.get(ATTRIBUTE_VALUE_MAX));
//                    String valueMinStr = StrKit.trimSafely(bizRule.get(ATTRIBUTE_VALUE_MIN));
                    if (isCustomSku) {
                        try {
                            if (CustomSkuValueTypeEnum.RANGE.getCode().equals(customSkuValueType)) {
                                return compareValue(valueMaxStr, valueMinStr, valueStr);
                            } else if (CustomSkuValueTypeEnum.FIXED.getCode().equals(customSkuValueType)) {
                                return attributeValueFixed.equals(valueStr);
                            }
                        } catch (Exception e) {
                            log.info("服务属性解析失败：无法转成数字 value={},max={},min={},e={}", valueStr, valueMaxStr, valueMinStr, e);
                            return false;
                        }
                    }
                    try {
                        if (StringUtils.isNotBlank(valueMinStr) && StringUtils.isNotBlank(valueMaxStr)) {
                            if (valueMinStr.equals(valueMaxStr)) {
                                return valueStr.equals(valueMinStr);
                            } else {
                                return compareValue(valueMaxStr, valueMinStr, valueStr);
                            }
                        }
                    } catch (Exception e) {
                        log.info("服务属性解析失败：无法转成数字 value={},max={},min={},e={}", valueStr, valueMaxStr, valueMinStr, e);
                        return false;
                    }
                    return true;
                }
                List<AttributeDTO> childList = serviceAttributeValue.getAttributeDtoList();
                if(CollectionUtils.isNotEmpty(childList)){
                    boolean checkSkuNo = checkSkuNo(childList, bizRule, skuNo);
                    if(checkSkuNo){
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private boolean compareValue(String valueMaxStr, String valueMinStr, String valueStr) {
        if (StringUtils.isBlank(valueStr) || StringUtils.isBlank(valueMaxStr) || StringUtils.isBlank(valueMinStr)) {
            return false;
        }
        BigDecimal valueMax = new BigDecimal(valueMaxStr);
        BigDecimal valueMin = new BigDecimal(valueMinStr);
        BigDecimal value = new BigDecimal(valueStr);
        return value.compareTo(valueMax) <= 0 && value.compareTo(valueMin) > 0;
    }


    private boolean checkHitSkuNo(List<AttributeDTO> serviceAttributes, String skuNo) {
        if (StringUtils.isBlank(skuNo) || CollectionUtils.isEmpty(serviceAttributes)) {
            return false;
        }
        for (AttributeDTO serviceAttribute : serviceAttributes) {
            for (AttributeValueDTO serviceAttributeValue : serviceAttribute.getAttributeValueDtoList()) {
                if (skuNo.equals(serviceAttributeValue.getAttributePathNo())) {
                    return true;
                }
                List<AttributeDTO> childList = serviceAttributeValue.getAttributeDtoList();
                if (CollectionUtils.isNotEmpty(childList)) {
                    boolean checkSkuNo = checkHitSkuNo(childList, skuNo);
                    if (checkSkuNo) {
                        return true;
                    }
                }
            }
        }
        return false;
    }


    // key == getAttributePathNo
    private String extractValue(List<AttributeDTO> serviceAttributes, String key) {
        for (AttributeDTO serviceAttribute : serviceAttributes) {
            List<AttributeValueDTO> attributeValueDtoList = serviceAttribute.getAttributeValueDtoList();
            if (CollectionUtils.isEmpty(attributeValueDtoList)) {
                continue;
            }

            for (AttributeValueDTO serviceAttributeValue : attributeValueDtoList) {
                if (key.equals(serviceAttributeValue.getAttributePathNo()) && !serviceAttributeValue.getIsExtracted()) {
                    // 将attributePathNo置空，这样可以提取出数组的所有值
                    serviceAttributeValue.setIsExtracted(true);
                    return serviceAttributeValue.getValue();
                }
                List<AttributeDTO> childList = serviceAttributeValue.getAttributeDtoList();
                if (CollectionUtils.isNotEmpty(childList)) {
                    String value = extractValue(childList, key);
                    if (StringUtils.isNotBlank(value)) {
                        return value;
                    }
                }
            }
        }
        return null;
    }


}
