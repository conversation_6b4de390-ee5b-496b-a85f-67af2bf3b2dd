package com.wanshifu.strategy.extract;

import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.dto.CalculateServiceInfo;
import com.wanshifu.fee.center.domain.dto.ServiceAttribute;
import com.wanshifu.fee.center.domain.dto.ServiceAttributeValue;
import com.wanshifu.fee.center.domain.enums.ExpressParamEnum;
import com.wanshifu.fee.center.domain.enums.FeeSkuTypeEnum;
import com.wanshifu.fee.center.domain.enums.MatchSkuTypeEnum;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.utils.StrKit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class ServiceInfoValueExtractor extends SpecialValueExtractor<CalculateServiceInfo> {

    private final static String SKU_NO = "skuNo";
    private final static String ATTRIBUTE_VALUE_MAX = "attributeValueMax";
    private final static String ATTRIBUTE_VALUE_MIN = "attributeValueMin";
    private final static String SKU_TYPE = "skuType";

    public Map<String, String> extract(CalculateServiceInfo serviceInfo, FeeRule feeRule, Map<String, String> cache) {
        String skuNo = feeRule.getBizRule().get(SKU_NO);
        Map<String, String> result = new HashMap<>();
        List<ServiceAttribute> rootAttributeDetailList = serviceInfo.getRootAttributeDetailList();
        boolean checkSkuNo = checkSkuNo(rootAttributeDetailList, feeRule, skuNo);
        if (checkSkuNo) {
            // 满足准入条件，取值
            log.info("满足准入条件  feeRuleId={} skuNo={}", feeRule.getFeeRuleId(), skuNo);
            List<String> expressionParamList = feeRule.getCalculateRuleData().getExpressionParamList();
            for (String key : expressionParamList) {
                if (Objects.nonNull(cache.get(key))) {
                    result.put(key, cache.get(key));
                    continue;
                }
                // 如果是特殊的先检查特殊
                ExpressParamEnum expressParamEnum = ExpressParamEnum.fromCode(key);
                if (Objects.nonNull(expressParamEnum)) {
                    String extractValue = extract(serviceInfo, feeRule, cache, expressParamEnum);
                    // 说明没有提取到特殊值
//                    if (StringUtils.isBlank(extractValue)) {
//                        return Collections.emptyMap();
//                    }
                    cache.put(key, extractValue);
                    result.put(key, extractValue);
                    continue;
                }

                String extractValue = extractValue(rootAttributeDetailList, key);
//                if (StringUtils.isBlank(extractValue)) {
//                    return Collections.emptyMap();
//                }
                // 非特殊的，不缓存
//                cache.put(key, extractValue);
                result.put(key, extractValue);
            }
        }

        return result;
    }


    // cunzai
    private boolean checkSkuNo(List<ServiceAttribute> serviceAttributes, FeeRule feeRule, String skuNo) {
        // 先判断是否为自定义sku
        Map<String, String> bizRule = feeRule.getBizRule();
        String matchSkuType = bizRule.get(CommonBizRule.Fields.matchSkuType);
        if (MatchSkuTypeEnum.OTHER_SKU.getCode().equals(matchSkuType)) {
            String originSkuNo = bizRule.get(CommonBizRule.Fields.skuNo);
            String matchSkuNo = bizRule.get(CommonBizRule.Fields.matchSkuNo);
            boolean hitOriginSkuNo = checkSkuNo(serviceAttributes, originSkuNo);
            boolean hitMatchSkuNo = checkSkuNo(serviceAttributes, matchSkuNo);
            // 如果没有都命中，则直接返回false
            if (!(hitOriginSkuNo && hitMatchSkuNo)) {
                return false;
            } else {
                // 如果没有最小值和最大值，则表示一定会命中该条自定义sku，可直接返回true
                String max = StrKit.trimSafely(bizRule.get(ATTRIBUTE_VALUE_MAX));
                String min = StrKit.trimSafely(bizRule.get(ATTRIBUTE_VALUE_MIN));
                if (StringUtils.isBlank(max) && StringUtils.isBlank(min)) {
                    return true;
                }
            }
        }
        for (ServiceAttribute serviceAttribute : serviceAttributes) {
            for (ServiceAttributeValue serviceAttributeValue : serviceAttribute.getChildList()) {

                if (skuNo.equals(serviceAttributeValue.getAttributePathNo())) {
                    String valueStr = StrKit.trimSafely(serviceAttributeValue.getValue());
                    boolean isCustomSku = FeeSkuTypeEnum.CUSTOM_SKU.code.equals(bizRule.get(SKU_TYPE));
                    // 新逻辑，不一定为自定义sku才需要对比值范围
                    String valueMaxStr = StrKit.trimSafely(bizRule.get(ATTRIBUTE_VALUE_MAX));
                    String valueMinStr = StrKit.trimSafely(bizRule.get(ATTRIBUTE_VALUE_MIN));
                    if (isCustomSku) {
                        try {
                            return compareValue(valueMaxStr, valueMinStr, valueStr);
                        } catch (Exception e) {
                            log.info("服务属性解析失败：无法转成数字 value={},max={},min={},e={}", valueStr, valueMaxStr, valueMinStr, e);
                            return false;
                        }
                    }
                    try {
                        if (StringUtils.isNotBlank(valueMinStr) && StringUtils.isNotBlank(valueMaxStr)) {
                            if (valueMinStr.equals(valueMaxStr)) {
                                return valueStr.equals(valueMinStr);
                            } else {
                                return compareValue(valueMaxStr, valueMinStr, valueStr);
                            }
                        }
                    } catch (Exception e) {
                        log.info("服务属性解析失败：无法转成数字 value={},max={},min={},e={}", valueStr, valueMaxStr, valueMinStr, e);
                        return false;
                    }
                    return true;
                }
                List<ServiceAttribute> childList = serviceAttributeValue.getChildList();
                if(CollectionUtils.isNotEmpty(childList)){
                    boolean checkSkuNo = checkSkuNo(childList, feeRule, skuNo);
                    if(checkSkuNo){
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private boolean compareValue(String valueMaxStr, String valueMinStr, String valueStr) {
        if (StringUtils.isBlank(valueStr) || StringUtils.isBlank(valueMaxStr) || StringUtils.isBlank(valueMinStr)) {
            return false;
        }
        BigDecimal valueMax = new BigDecimal(valueMaxStr);
        BigDecimal valueMin = new BigDecimal(valueMinStr);
        BigDecimal value = new BigDecimal(valueStr);
        return value.compareTo(valueMax) <= 0 && value.compareTo(valueMin) > 0;
    }


    private boolean checkSkuNo(List<ServiceAttribute> serviceAttributes, String skuNo) {
        if (StringUtils.isBlank(skuNo) || CollectionUtils.isEmpty(serviceAttributes)) {
            return false;
        }
        for (ServiceAttribute serviceAttribute : serviceAttributes) {
            for (ServiceAttributeValue serviceAttributeValue : serviceAttribute.getChildList()) {
                if (skuNo.equals(serviceAttributeValue.getAttributePathNo())) {
                    return true;
                }
                List<ServiceAttribute> childList = serviceAttributeValue.getChildList();
                if (CollectionUtils.isNotEmpty(childList)) {
                    boolean checkSkuNo = checkSkuNo(childList, skuNo);
                    if (checkSkuNo) {
                        return true;
                    }
                }
            }
        }
        return false;
    }


    // key == getAttributePathNo
    private String extractValue(List<ServiceAttribute> serviceAttributes, String key) {
        for (ServiceAttribute serviceAttribute : serviceAttributes) {
            for (ServiceAttributeValue serviceAttributeValue : serviceAttribute.getChildList()) {

                if (key.equals(serviceAttributeValue.getAttributePathNo()) && !serviceAttributeValue.getIsExtracted()) {
                    // 将attributePathNo置空，这样可以提取出数组的所有值
//                    serviceAttributeValue.setAttributePathNo(null);
                    serviceAttributeValue.setIsExtracted(true);
                    return StrKit.trimSafely(serviceAttributeValue.getValue());
                }
                List<ServiceAttribute> childList = serviceAttributeValue.getChildList();
                if (CollectionUtils.isNotEmpty(childList)) {
                    String value = extractValue(childList, key);
                    if (StringUtils.isNotBlank(value)) {
//                        serviceAttributeValue.setAttributePathNo(null);
                        return StrKit.trimSafely(value);
                    }
//                    return extractValue(childList, key);
                }
            }
        }
        return null;
    }

}
