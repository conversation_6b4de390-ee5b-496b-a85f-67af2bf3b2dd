package com.wanshifu.strategy.rule;

import com.wanshifu.domain.dto.ApplyOrderCalculateDTO;
import com.wanshifu.fee.center.domain.biz.EnterpriseOrderOfferGuidePriceBizRule;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.dto.AddressInfo;
import com.wanshifu.fee.center.domain.dto.CalculateServiceInfo;
import com.wanshifu.fee.center.domain.enums.DivisionTypeEnum;
import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.framework.core.BusException;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: <PERSON>
 * @create: 2024-03-28 16:49
 * @description: enterprise_order_offer_guide_price 总包报价参考价 只计算商品价格
 */
@Service
public class EnterpriseOrderOfferGuidePriceScene implements IFeeRuleConditionHandler {

    @Resource
    private CommonFeeRuleConditionHandler commonFeeRuleConditionHandler;

    @Override
    public boolean isSupport(String sceneCode) {
        return SceneCodeEnum.ENTERPRISE_ORDER_OFFER_GUIDE_PRICE.getCode().equals(sceneCode);
    }

    @Override
    public Criteria getFeeRuleCondition(ApplyOrderCalculateDTO applyOrderCalculateReq) {
        Set<String> serviceIds = applyOrderCalculateReq.getServiceInfos().stream().map(CalculateServiceInfo::getServiceId).map(Objects::toString).collect(Collectors.toSet());

        Criteria feeRuleCondition = Criteria.where(FeeRule.Fields.sceneCode).is(SceneCodeEnum.ENTERPRISE_ORDER_OFFER_GUIDE_PRICE.getCode());
        feeRuleCondition.and(FeeRule.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.feeTypeTag).ne(null);
        feeRuleCondition.and(FeeRule.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.serviceId).in(serviceIds);

        AddressInfo addressInfo = applyOrderCalculateReq.getAddressInfo();
        // 一级国家 五级街道
        if (Objects.isNull(addressInfo)) {
            throw new BusException("addressInfo is null");
        }

        DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(addressInfo.getDivisionType());
        if (Objects.isNull(divisionTypeEnum)) {
            throw new BusException("addressInfo.divisionType设置错误");
        }
        // 只需要查省、市
        // 查出 国 省 市 区 然后根据是否需要回溯选择合适的级别
        List<Criteria> criteriaList = commonFeeRuleConditionHandler.buildFeeRuleAddressCondition(addressInfo);

        feeRuleCondition.orOperator(criteriaList.toArray(new Criteria[0]));
        return feeRuleCondition;
    }
}
