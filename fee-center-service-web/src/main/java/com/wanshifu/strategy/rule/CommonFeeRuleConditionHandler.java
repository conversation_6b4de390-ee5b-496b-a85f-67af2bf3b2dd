package com.wanshifu.strategy.rule;

import com.wanshifu.domain.dto.ApplyOrderCalculateDTO;
import com.wanshifu.fee.center.domain.biz.EnterpriseOrderOfferGuidePriceBizRule;
import com.wanshifu.fee.center.domain.biz.PlatformFixedPriceBizRule;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.document.FeeTemplate;
import com.wanshifu.fee.center.domain.dto.AddressInfo;
import com.wanshifu.fee.center.domain.dto.CalculateServiceInfo;
import com.wanshifu.fee.center.domain.enums.BizIdTypeEnum;
import com.wanshifu.fee.center.domain.enums.DivisionTypeEnum;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: Chen Yong
 * @create: 2024-03-28 17:11
 * @description: fee rule处理
 */
@Slf4j
@Service
public class CommonFeeRuleConditionHandler implements IFeeRuleConditionHandler{

    @Override
    public boolean isSupport(String sceneCode) {
        return false;
    }

    protected List<Criteria> buildFeeRuleAddressCondition(AddressInfo addressInfo) {
        DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(addressInfo.getDivisionType());
        boolean needSearchParent = addressInfo.isNeedSearchParent();
        if (Objects.isNull(divisionTypeEnum)) {
            throw new BusException("addressInfo.divisionType设置错误");
        }
        Long province = addressInfo.getLv2DivisionId();
        // 部分特殊地区市区可以互换
        // 市
        Long city = Objects.isNull(addressInfo.getLv3DivisionId()) ? addressInfo.getLv4DivisionId() : addressInfo.getLv3DivisionId();
        // 区
//        Long district = Objects.isNull(addressInfo.getLv4DivisionId()) ? addressInfo.getLv3DivisionId() : addressInfo.getLv4DivisionId();
        // 保持统一
        Long district = Objects.isNull(addressInfo.getLv4DivisionId()) ? addressInfo.getLv5DivisionId() : addressInfo.getLv4DivisionId();
        // 街道
        List<Criteria> criteriaList = new ArrayList<>();
        Long street = addressInfo.getLv5DivisionId();
        if ((divisionTypeEnum == DivisionTypeEnum.STREET)) {
            if (Objects.nonNull(street)) {
                Criteria streetCondition = Criteria.where(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.divisionType).is(DivisionTypeEnum.STREET.code).and(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level4DivisionId).is(street.toString());
                criteriaList.add(streetCondition);
            }
        }

        if ((divisionTypeEnum == DivisionTypeEnum.DISTRICT) || (divisionTypeEnum == DivisionTypeEnum.STREET && needSearchParent)) {
            if (Objects.nonNull(district)) {
                Criteria districtCondition = Criteria.where(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.divisionType).is(DivisionTypeEnum.DISTRICT.code).and(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level3DivisionId).is(district.toString());
                criteriaList.add(districtCondition);
            }
        }


        if ((divisionTypeEnum == DivisionTypeEnum.CITY) || ((divisionTypeEnum == DivisionTypeEnum.STREET || divisionTypeEnum == DivisionTypeEnum.DISTRICT) && needSearchParent)) {
            if (Objects.nonNull(city)) {
                Criteria cityCondition = Criteria.where(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.divisionType).is(DivisionTypeEnum.CITY.code).and(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level2DivisionId).is(city.toString());
                criteriaList.add(cityCondition);
            }
        }

        if ((divisionTypeEnum == DivisionTypeEnum.PROVINCE) || ((divisionTypeEnum == DivisionTypeEnum.STREET || divisionTypeEnum == DivisionTypeEnum.DISTRICT || divisionTypeEnum == DivisionTypeEnum.CITY) && needSearchParent)) {
            if (Objects.nonNull(province)) {
                Criteria provinceCondition = Criteria.where(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.divisionType).is(DivisionTypeEnum.PROVINCE.code).and(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level1DivisionId).is(province.toString());
                criteriaList.add(provinceCondition);
            }
        }

        if ((divisionTypeEnum == DivisionTypeEnum.COUNTRY) || ((divisionTypeEnum == DivisionTypeEnum.STREET || divisionTypeEnum == DivisionTypeEnum.DISTRICT || divisionTypeEnum == DivisionTypeEnum.CITY || divisionTypeEnum == DivisionTypeEnum.PROVINCE) && needSearchParent)) {
            Criteria countryCondition = Criteria.where(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.divisionType).is(DivisionTypeEnum.COUNTRY.code);
            criteriaList.add(countryCondition);
        }

        if (CollectionUtils.isEmpty(criteriaList)) {
            throw new BusException("构建地区条件错误，没有合适的地区，addressInfo=" + addressInfo);
        }

        return criteriaList;
    }


    public Criteria getFeeRuleCondition(ApplyOrderCalculateDTO applyOrderCalculateReq) {
        Set<String> serviceIds = applyOrderCalculateReq.getServiceInfos().stream().map(CalculateServiceInfo::getServiceId).map(Objects::toString).collect(Collectors.toSet());

        Criteria feeRuleCondition = Criteria.where(FeeRule.Fields.sceneCode).is(applyOrderCalculateReq.getCurrentSceneInfo().getSceneCode());
        // 用户信息
        String bizIdType = applyOrderCalculateReq.getSceneInfoList().get(0).getBizIdType();
        BizIdTypeEnum bizIdTypeEnum = BizIdTypeEnum.fromCode(bizIdType);
        if (bizIdTypeEnum != null) {
            if (bizIdTypeEnum.equals(BizIdTypeEnum.USER_ID)) {
                feeRuleCondition.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + PlatformFixedPriceBizRule.Fields.userId).is(applyOrderCalculateReq.getFrom().getAccountId().toString());
            } else if(bizIdTypeEnum.equals(BizIdTypeEnum.MASTER_ID)) {
                feeRuleCondition.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + PlatformFixedPriceBizRule.Fields.masterId).is(applyOrderCalculateReq.getTo().getAccountId().toString());
            }
        }
        feeRuleCondition.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + PlatformFixedPriceBizRule.Fields.feeTypeTag).ne(null);
        feeRuleCondition.and(FeeRule.Fields.bizRule + PunConstant.DOT + PlatformFixedPriceBizRule.Fields.serviceId).in(serviceIds);

        AddressInfo addressInfo = applyOrderCalculateReq.getAddressInfo();
        // 一级国家 五级街道
        if (Objects.isNull(addressInfo)) {
            throw new BusException("addressInfo is null");
        }

        DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(addressInfo.getDivisionType());
        if (Objects.isNull(divisionTypeEnum)) {
            throw new BusException("addressInfo.divisionType设置错误");
        }
        // 只需要查省、市
        //
        // 查出 国 省 市 区 然后根据是否需要回溯选择合适的级别
        List<Criteria> criteriaList = buildFeeRuleAddressCondition(addressInfo);

        feeRuleCondition.orOperator(criteriaList.toArray(new Criteria[0]));

        Map<String, String> bizRule = applyOrderCalculateReq.getBizRule();
        // 与下方逻辑重复，故注释掉
//        if (bizRule != null && bizRule.get(CommonBizRule.Fields.bizTag) != null) {
//            feeRuleCondition.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.bizTag).is(bizRule.get(CommonBizRule.Fields.bizTag));
//        }
        if (MapUtils.isNotEmpty(bizRule)) {
            bizRule.forEach((key, value) -> {
                if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value)) {
                    feeRuleCondition.and(FeeRule.Fields.bizRule + PunConstant.DOT + key).is(value);
                }
            });
        }


        return feeRuleCondition;
    }
}
