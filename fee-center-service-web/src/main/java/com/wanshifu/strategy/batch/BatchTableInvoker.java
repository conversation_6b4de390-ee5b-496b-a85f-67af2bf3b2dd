package com.wanshifu.strategy.batch;

import com.google.common.base.Strings;
import com.wanshifu.domain.request.BatchTaskGenerateReq;
import com.wanshifu.fee.center.domain.document.BatchTaskInfo;
import com.wanshifu.fee.center.domain.document.SceneInfo;
import com.wanshifu.framework.core.BusException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class BatchTableInvoker {

    @Autowired
    private List<BatchTableHandler> batchTableHandlers;

    @Resource
    private BatchUpdateSkuByServiceHandler batchUpdateSkuByServiceHandler;

    private BatchTableHandler getBatchTableHandler(SceneInfo sceneInfo) {
        for (BatchTableHandler batchTableHandler : batchTableHandlers) {
            if (batchTableHandler.isSupport(sceneInfo)) {
                return batchTableHandler;
            }
        }
        throw new BusException("未找到对应的handler:" + sceneInfo.getSceneCode());
    }

    public byte[] generate(SceneInfo sceneInfo, BatchTaskGenerateReq req) {
        String taskType = req.getTaskType();
        if (!Strings.isNullOrEmpty(taskType)
                && "batchUpdateSkuByService".equals(taskType)) {
            return batchUpdateSkuByServiceHandler.generateTemplateTable(sceneInfo, req);
        } else {
            BatchTableHandler batchTableHandler = getBatchTableHandler(sceneInfo);
            return batchTableHandler.generateTemplateTable(sceneInfo, req);
        }

    }

    public void handle(SceneInfo sceneInfo, BatchTaskInfo batchTaskInfo) {
        BatchTableHandler batchTableHandler = getBatchTableHandler(sceneInfo);
        batchTableHandler.handleBatchTask(sceneInfo, batchTaskInfo);
    }
}
