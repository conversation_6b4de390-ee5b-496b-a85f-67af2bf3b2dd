package com.wanshifu.strategy.batch;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.mongodb.WriteResult;
import com.wanshifu.api.FileUploadUtils;
import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.bean.FileUploadResp;
import com.wanshifu.domain.dto.table.AutoReceiveOrderGuidePriceTable;
import com.wanshifu.domain.request.BatchTaskGenerateReq;
import com.wanshifu.fee.center.domain.biz.AutoReceiveOrderGuidePriceBizRule;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.*;
import com.wanshifu.fee.center.domain.enums.*;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.HttpBuilder;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.repository.BatchTaskInfoRepository;
import com.wanshifu.repository.FeeRuleDraftRepository;
import com.wanshifu.service.FormulaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Component
@Slf4j
@Order(BatchTableHandler.DEFAULT_ORDER)
public class AutoReceiveOrderGuidePriceBatchTableHandler implements BatchTableHandler {

    @javax.annotation.Resource
    private MongoTemplate mongoTemplate;

    @javax.annotation.Resource
    private BatchTaskInfoRepository batchTaskInfoRepository;

    @javax.annotation.Resource
    private FeeRuleDraftRepository feeRuleDraftRepository;

    @javax.annotation.Resource
    private Executor batchTaskExecutor;

    @javax.annotation.Resource
    private AddressApi addressApi;

    @Resource
    private FormulaService formulaService;

    // 创建一个 DivisionTypeEnum 到错误消息的映射
    public static final Map<DivisionTypeEnum, String> errorMessageMap = new HashMap<>();

    static {
        errorMessageMap.put(DivisionTypeEnum.PROVINCE, "省份为空");
        errorMessageMap.put(DivisionTypeEnum.CITY, "省份或者城市为空");
        errorMessageMap.put(DivisionTypeEnum.DISTRICT, "省份、城市或者区县为空");
        errorMessageMap.put(DivisionTypeEnum.STREET, "省份、城市、区县或者街道为空");
    }


    @Override
    public boolean isSupport(SceneInfo sceneInfo) {
        return "auto_receive_order_guide_price".equals(sceneInfo.getSceneCode());
    }

    @Override
    public byte[] generateTemplateTable(SceneInfo sceneInfo, BatchTaskGenerateReq req) {
        String sceneCode = req.getSceneCode();
        Set<String> serviceIds = req.getServiceIds();
        // 模板库里面查询模板数据
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        criteria.and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);
        criteria.and(FeeTemplate.Fields.sceneCode).is(sceneCode);
        // 费用类型不为空
        criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.feeTypeTagName).ne(null);
        criteria.and(FeeTemplate.Fields.group).ne(null);
        criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.serviceId).in(serviceIds);
        Query query = Query.query(criteria);
        List<FeeTemplate> feeTemplates = mongoTemplate.find(query, FeeTemplate.class);
        if (CollectionUtils.isEmpty(feeTemplates)) {
            throw new BusException("无模板信息");
        }
        // 生成表格
        List<AutoReceiveOrderGuidePriceTable> tableInfoList = new ArrayList<>(feeTemplates.size());
        // 保证同一个服务在一组
        Map<String, List<FeeTemplate>> collect = feeTemplates.stream().collect(Collectors.groupingBy(FeeTemplate::getGroup));

        for (Map.Entry<String, List<FeeTemplate>> stringListEntry : collect.entrySet()) {
            List<FeeTemplate> feeTemplateList = stringListEntry.getValue();
            List<FeeTemplate> templates = feeTemplateList.stream().sorted(Comparator.comparing(FeeTemplate::getSort)).collect(Collectors.toList());
            // 排序
            for (FeeTemplate feeTemplate : templates) {
                AutoReceiveOrderGuidePriceTable autoReceiveOrderGuidePriceTable = new AutoReceiveOrderGuidePriceTable();
                autoReceiveOrderGuidePriceTable.setServiceId(feeTemplate.getBizRule().get(AutoReceiveOrderGuidePriceBizRule.Fields.serviceId));
                autoReceiveOrderGuidePriceTable.setServiceName(feeTemplate.getBizRule().get(AutoReceiveOrderGuidePriceBizRule.Fields.serviceName));
                autoReceiveOrderGuidePriceTable.setSkuNo(feeTemplate.getBizRule().get(CommonBizRule.Fields.skuNo));
                autoReceiveOrderGuidePriceTable.setTemplateId(feeTemplate.getTemplateId().toString());
                autoReceiveOrderGuidePriceTable.setAttributeDisplayName(feeTemplate.getBizRule().get(AutoReceiveOrderGuidePriceBizRule.Fields.attributeDisplayName));
                tableInfoList.add(autoReceiveOrderGuidePriceTable);
            }
        }
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        EasyExcel.write(byteArrayOutputStream, AutoReceiveOrderGuidePriceTable.class).excludeColumnFieldNames(Collections.singletonList("error")).sheet(sceneInfo.getSceneName()).doWrite(tableInfoList);


        return byteArrayOutputStream.toByteArray();
    }

    @Override
    public void handleBatchTask(SceneInfo sceneInfo, BatchTaskInfo batchTaskInfo) {
        try {
            log.info("开始处理任务：task={}", batchTaskInfo);
            // 下载文件
            String fileUrl = batchTaskInfo.getFileUrl();
            InputStream content = HttpBuilder.genericGet(fileUrl).build().execute().getEntity().getContent();
            log.info("文件下载完成：taskId={},fileUrl={}", batchTaskInfo.getBatchTaskId(), fileUrl);
            List<AutoReceiveOrderGuidePriceTable> tableInfoList = EasyExcel.read(content).sheet(sceneInfo.getSceneName()).head(AutoReceiveOrderGuidePriceTable.class).doReadSync();
            log.info("文件解析完成：taskId={}", batchTaskInfo.getBatchTaskId());

            if (CollectionUtils.isEmpty(tableInfoList)) {
                throw new BusException("文件为空");
            }

            int hour = LocalTime.now().getHour();
            if (hour >= 7 && hour < 21 && tableInfoList.size() > 10000) {
                log.error("导入时间在7-21点之间，且条数大于1万条，超过阈值，导入失败！");
                throw new BusException("导入时间在7-21点之间，且条数大于1万条，超过阈值，导入失败！");
            } else if (tableInfoList.size() > 30000) {
                log.error("导入条目过大，大于3w. batchTaskId={}", batchTaskInfo.getBatchTaskId());
                throw new BusException(batchTaskInfo.getBatchTaskId() + "条目大于3w");
            }

            // 对于上传数据来说，其批次唯一性由bizId + 服务ID + 地区决定
            batchTaskInfo.setCount(tableInfoList.size());

            // 异常数据
            List<AutoReceiveOrderGuidePriceTable> errorInfo = new ArrayList<>();

            String divisionType = batchTaskInfo.getDivisionType();
            DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(divisionType);
//  TODO 产品说本期没有bizId校验
//            tableInfoList = validateBizId(tableInfoList,batchTaskInfo,errorInfo);

            tableInfoList = validateDivisionType(tableInfoList, errorInfo, divisionType, divisionTypeEnum);

            Assert.notNull(divisionTypeEnum, "divisionTypeEnum is null");

            tableInfoList = validateDivision(tableInfoList, errorInfo, divisionTypeEnum);

            tableInfoList = validatePrice(tableInfoList, errorInfo);

            tableInfoList = validateTemplateIdAndServiceId(tableInfoList, errorInfo);

            List<String> haveTagList = new ArrayList<>();
            // key = serviceId + 地区 + 自定义维度（bizTag）
            Map<String, List<AutoReceiveOrderGuidePriceTable>> serviceMap = toMap(divisionTypeEnum, haveTagList,tableInfoList);

            Set<String> serviceIds = tableInfoList.stream().map(AutoReceiveOrderGuidePriceTable::getServiceId).collect(Collectors.toSet());
            // 模板库里面查询模板数据
            Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
            criteria.and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);
            criteria.and(FeeTemplate.Fields.sceneCode).is(sceneInfo.getSceneCode());
            criteria.and(FeeTemplate.Fields.group).ne(null);
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.serviceId).in(serviceIds);
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.feeTypeTagName).ne(null);
            Query query = Query.query(criteria);
            List<FeeTemplate> feeTemplates = mongoTemplate.find(query, FeeTemplate.class);
            Map<String, List<FeeTemplate>> templateMap = feeTemplates.stream().collect(Collectors.groupingBy(FeeTemplate::getGroup));


            Map<String, Address> addressesCache = new HashMap<>();

            // 数据完整性校验
            for (Map.Entry<String, List<AutoReceiveOrderGuidePriceTable>> entry : serviceMap.entrySet()) {
                String key = entry.getKey();
                String[] split = key.split("#");
                String serviceId = split[0];
                String bizTag = null;
                boolean haveTag = haveTagList.contains(key);
                if(haveTag){
                    bizTag = split[split.length - 1];
                }

                List<FeeTemplate> thisFeeTemplates = templateMap.get(serviceId);
                List<AutoReceiveOrderGuidePriceTable> value = entry.getValue();
                if (CollectionUtils.isEmpty(thisFeeTemplates)) {
                    for (AutoReceiveOrderGuidePriceTable autoReceiveOrderGuidePriceTable : value) {
                        autoReceiveOrderGuidePriceTable.setError(serviceId + "该服务模板不存在");
                    }
                    errorInfo.addAll(value);
                    continue;
                }

                // 重复templateId
                Map<String, List<AutoReceiveOrderGuidePriceTable>> repeatTemplate = value.stream().collect(Collectors.groupingBy(AutoReceiveOrderGuidePriceTable::getTemplateId));
                Map.Entry<String, List<AutoReceiveOrderGuidePriceTable>> reEntry = repeatTemplate.entrySet().stream().filter(repeatEntry -> repeatEntry.getValue().size() > 1).findAny().orElse(null);
                if (Objects.nonNull(reEntry)) {
                    for (AutoReceiveOrderGuidePriceTable autoReceiveOrderGuidePriceTable : value) {
                        StringBuilder builder = new StringBuilder();
                        if (Objects.nonNull(batchTaskInfo.getBizId())) {
                            builder.append(batchTaskInfo.getBizId()).append("+");
                        }
                        builder.append(key).append("+").append(reEntry.getKey()).append("重复");
                        autoReceiveOrderGuidePriceTable.setError(builder.toString());
                    }
                    errorInfo.addAll(reEntry.getValue());
                    continue;
                }


                Set<Long> templateIds = value.stream().map(AutoReceiveOrderGuidePriceTable::getTemplateId).map(Long::valueOf).collect(Collectors.toSet());
                Set<Long> realTemplateIds = thisFeeTemplates.stream().map(FeeTemplate::getTemplateId).collect(Collectors.toSet());

                // 取差集，找出在库中没有的数据与缺失的数据
                Collection<?> subtract = CollectionUtils.subtract(templateIds, realTemplateIds);
                if (CollectionUtils.isNotEmpty(subtract)) {
                    for (AutoReceiveOrderGuidePriceTable autoReceiveOrderGuidePriceTable : value) {
                        autoReceiveOrderGuidePriceTable.setError("定价ID " + subtract.stream().filter(Objects::nonNull).map(Object::toString).collect(Collectors.joining(",")) + "在库中不存在");
                    }
                    errorInfo.addAll(value);
                    continue;
                }
//                else {
//                    subtract = CollectionUtils.subtract(realTemplateIds, templateIds);
//                    if (CollectionUtils.isNotEmpty(subtract)) {
//                        for (AutoReceiveOrderGuidePriceTable autoReceiveOrderGuidePriceTable : value) {
//                            autoReceiveOrderGuidePriceTable.setError("缺失定价ID" + subtract.stream().filter(Objects::nonNull).map(Object::toString).collect(Collectors.joining(",")) + "的数据");
//                        }
//                        errorInfo.addAll(value);
//                        continue;
//                    }
//                }

//                Collection<?> intersection = CollectionUtils.intersection(templateIds, realTemplateIds);

                // 对不上
//                if (intersection.size() != realTemplateIds.size()) {
//                    for (AutoReceiveOrderGuidePriceTable autoReceiveOrderGuidePriceTable : value) {
//                        autoReceiveOrderGuidePriceTable.setError(serviceId + "数据不完整");
//                    }
//                    errorInfo.addAll(value);
//                    continue;
//                }

                List<Address> addresses = new ArrayList<>();
                // 校验地区
                // 非国家级别需要校验地址
                if (DivisionTypeEnum.COUNTRY != divisionTypeEnum) {
                    // 如果是街道需要分割，如果是其他直接用,拼接
                    // 如果是街道需要分割，如果是其他直接用,拼接
                    if (DivisionTypeEnum.STREET == divisionTypeEnum) {
                        // 默认事倒数第一个
                        String street = null;
                        boolean haveBizId = StringUtils.isNotBlank(sceneInfo.getBizIdType()) && !BizIdTypeEnum.EMPTY.code.equals(sceneInfo.getBizIdType());
                        // 同时有tag和id是倒数第三个
                        if (haveBizId && haveTag) {
                            // 不然就是倒数第二个
                            street = split[split.length - 3];
                        }else if (haveBizId || haveTag) {
                            // 只有其中一个则是倒数第二个
                            street = split[split.length - 2];
                        }else {
                            street  = split[split.length - 1];
                        }

                        String[] streets = street.split("[,，]");
                        String shouldFindAddress = Arrays.stream(split).skip(1).limit(3).collect(Collectors.joining(","));
                        for (String s : streets) {
                            String streetStr = shouldFindAddress + "," + s;
                            validateAddress(errorInfo, addressesCache, value, addresses, streetStr);
                        }
                    } else {
                        int limit = split.length - 1;
                        if (StringUtils.isNotBlank(sceneInfo.getBizIdType()) && !BizIdTypeEnum.EMPTY.code.equals(sceneInfo.getBizIdType())) {
                            limit--;
                        }
                        if(haveTagList.contains(key)){
                            limit--;
                        }
                        String shouldFindAddress = Arrays.stream(split).skip(1).limit(limit).collect(Collectors.joining(","));
                         validateAddress(errorInfo, addressesCache, value, addresses, shouldFindAddress);
                    }
                }


                // 查询库中草稿数据，如果有则删除
                // 开始删除旧草稿
                Update update = Update.update(BaseDocument.Fields.del, true);
                // FIXME 此处删除与插入需要做成事务, 但是公司spring现版本不支持
                if (DivisionTypeEnum.COUNTRY == divisionTypeEnum) {
                    // 模板库里面查询模板数据
                    Criteria findDraftCriteria = Criteria.where(BaseDocument.Fields.del).is(false);
                    findDraftCriteria.and(FeeRuleDraft.Fields.sceneCode).is(sceneInfo.getSceneCode());
                    // 国家级别 bizId + serviceId
                    if (StringUtils.isNotBlank(batchTaskInfo.getBizId())) {
                        findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.masterId).is(batchTaskInfo.getBizId());
                    }

                    if (haveTag) {
                        findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.bizTag).is(bizTag);
                    }

                    findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.divisionType).is(divisionType);
                    findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.serviceId).is(serviceId);
                    Query findDraftQuery = Query.query(findDraftCriteria);
                    String criteriaStr = JSON.toJSONString(findDraftCriteria);
                    log.info("开始删除旧草稿：query={}", criteriaStr);
                    WriteResult writeResult = mongoTemplate.updateMulti(findDraftQuery, update, FeeRuleDraft.class);
                    log.info("旧草稿删除完成：query={} , result={}", criteriaStr, writeResult);
                    // 开始构建插入草稿数据
                    List<FeeRuleDraft> feeRuleDrafts = new ArrayList<>();
                    for (FeeTemplate thisFeeTemplate : thisFeeTemplates) {
                        // 找到用户填写的数据
                        Long templateId = thisFeeTemplate.getTemplateId();
                        AutoReceiveOrderGuidePriceTable thisAutoReceiveOrderGuidePriceTable = value.stream().filter(autoReceiveOrderGuidePriceTable -> autoReceiveOrderGuidePriceTable.getTemplateId().equals(templateId.toString())).findFirst().orElse(null);
                        if (Objects.isNull(thisAutoReceiveOrderGuidePriceTable)) {
                            continue;
                        }
//                        Assert.notNull(thisAutoReceiveOrderGuidePriceTable, "thisAutoReceiveOrderGuidePriceTable is null");
                        // 存储参数

                        Date date = new Date();
                        FeeRuleDraft feeRuleDraft = new FeeRuleDraft();
                        feeRuleDraft.setFeeRuleDraftId(SnowFlakeGenerator.INSTANCE.generate());
                        feeRuleDraft.setBizRule(thisFeeTemplate.getBizRule());
                        feeRuleDraft.setGroup(thisFeeTemplate.getGroup());
                        feeRuleDraft.setTemplateId(thisFeeTemplate.getTemplateId());
                        CalculateRuleData calculateRuleData = thisFeeTemplate.getCalculateRuleData();
                        feeRuleDraft.setCalculateRuleData(calculateRuleData);

                        feeRuleDraft.setTemplateVersion(thisFeeTemplate.getTemplateVersion());
                        feeRuleDraft.setSceneName(sceneInfo.getSceneName());
                        feeRuleDraft.setSceneCode(sceneInfo.getSceneCode());
                        feeRuleDraft.setCreateTime(date);
                        feeRuleDraft.setModifyTime(date);
                        feeRuleDraft.setDel(false);
                        feeRuleDraft.setStatus(DraftStatusEnum.AUDIT.code);
                        feeRuleDraft.setFeeName(thisFeeTemplate.getBizRule().get(AutoReceiveOrderGuidePriceBizRule.Fields.feeName));

                        if (haveTag){
                            feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.bizTag,bizTag);
                        }
                        String bizId = batchTaskInfo.getBizId();
                        feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.basePrice, thisAutoReceiveOrderGuidePriceTable.getBasePrice());
                        feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.divisionType, divisionType);
//                        feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.masterId, bizId);
                        feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.bizId, bizId);
                        if (StringUtils.isNotBlank(sceneInfo.getBizIdType())) {
                            feeRuleDraft.getBizRule().put("bizType", sceneInfo.getBizIdType());
                        }
//                        feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.bizType, BizIdTypeEnum.MASTER_ID.code);
                        feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.masterInputPrice, thisAutoReceiveOrderGuidePriceTable.getMasterInputPrice());
                        feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.divisionTypeName, divisionTypeEnum.name);
                        feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.batchTaskId, batchTaskInfo.getBatchTaskId().toString());
                        // 该场景不支持导入 业务ID
                        CalculateRuleData calculateRule = formulaService.handleFormulas(calculateRuleData, divisionType, null, null);
                        feeRuleDraft.setCalculateRuleData(calculateRule);
                        feeRuleDrafts.add(feeRuleDraft);
                    }
                    // 草稿保存
                    log.info("草稿保存：info={}", feeRuleDrafts);
                    feeRuleDraftRepository.save(feeRuleDrafts);
                    log.info("草稿保存完成：info={}", feeRuleDrafts);
                } else {
                    // 非国家则需要逐地址删除与插入
                    for (Address address : addresses) {
                        // 我们库中是从省开始的，而地址库是从国家开始的
                        // 模板库里面查询模板数据
                        Criteria findDraftCriteria = Criteria.where(BaseDocument.Fields.del).is(false);
                        findDraftCriteria.and(FeeRuleDraft.Fields.sceneCode).is(sceneInfo.getSceneCode());
                        // 地区级别 bizId + serviceId + 地区id
                        if (StringUtils.isNotBlank(batchTaskInfo.getBizId())) {
                            findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.masterId).is(batchTaskInfo.getBizId());
                        }
                        if (haveTag) {
                            findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.bizTag).is(bizTag);
                        }

                        findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.divisionType).is(divisionType);
                        findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.serviceId).is(serviceId);

                        if (divisionTypeEnum == DivisionTypeEnum.PROVINCE) {
                            findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.level1DivisionId).is(address.getLv2DivisionId().toString());
                        }
                        if (divisionTypeEnum == DivisionTypeEnum.CITY) {
                            findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.level1DivisionId).is(address.getLv2DivisionId().toString());
                            findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.level2DivisionId).is(address.getLv3DivisionId().toString());
                        }
                        if (divisionTypeEnum == DivisionTypeEnum.DISTRICT) {
                            findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.level1DivisionId).is(address.getLv2DivisionId().toString());
                            if (address.getLv3DivisionId() != 0) {
                                findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.level2DivisionId).is(address.getLv3DivisionId().toString());
                            } else {
                                findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.level2DivisionId).is(address.getLv4DivisionId().toString());
                            }
                            findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.level3DivisionId).is(address.getLv4DivisionId().toString());
                        }
                        if (divisionTypeEnum == DivisionTypeEnum.STREET) {
                            findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.level1DivisionId).is(address.getLv2DivisionId().toString());
                            if (address.getLv3DivisionId() != 0) {
                                findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.level2DivisionId).is(address.getLv3DivisionId().toString());
                            } else {
                                findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.level2DivisionId).is(address.getLv4DivisionId().toString());
                            }

                            if (address.getLv4DivisionId() != 0) {
                                findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.level3DivisionId).is(address.getLv4DivisionId().toString());
                            } else {
                                findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.level3DivisionId).is(address.getLv5DivisionId().toString());
                            }

                            findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.level4DivisionId).is(address.getLv5DivisionId().toString());
                        }
                        Query findDraftQuery = Query.query(findDraftCriteria);
                        String criteriaStr = JSON.toJSONString(findDraftCriteria);
                        log.info("开始删除旧草稿：query={}", criteriaStr);
                        WriteResult writeResult = mongoTemplate.updateMulti(findDraftQuery, update, FeeRuleDraft.class);
                        log.info("旧草稿删除完成：query={} , result={}", criteriaStr, writeResult);
                        // 开始构建插入草稿数据
                        List<FeeRuleDraft> feeRuleDrafts = new ArrayList<>();
                        for (FeeTemplate thisFeeTemplate : thisFeeTemplates) {
                            // 找到用户填写的数据
                            Long templateId = thisFeeTemplate.getTemplateId();
                            AutoReceiveOrderGuidePriceTable thisAutoReceiveOrderGuidePriceTable = value.stream().filter(autoReceiveOrderGuidePriceTable -> autoReceiveOrderGuidePriceTable.getTemplateId().equals(templateId.toString())).findFirst().orElse(null);
                            if (thisAutoReceiveOrderGuidePriceTable == null) {
                                continue;
                            }
//                            Assert.notNull(thisAutoReceiveOrderGuidePriceTable, "thisAutoReceiveOrderGuidePriceTable is null");
                            // 存储参数

                            Date date = new Date();
                            FeeRuleDraft feeRuleDraft = new FeeRuleDraft();
                            feeRuleDraft.setFeeRuleDraftId(SnowFlakeGenerator.INSTANCE.generate());
                            feeRuleDraft.setBizRule(thisFeeTemplate.getBizRule());
                            feeRuleDraft.setGroup(thisFeeTemplate.getGroup());
                            feeRuleDraft.setTemplateId(thisFeeTemplate.getTemplateId());
                            CalculateRuleData calculateRuleData = thisFeeTemplate.getCalculateRuleData();
                            feeRuleDraft.setCalculateRuleData(calculateRuleData);
                            feeRuleDraft.setTemplateVersion(thisFeeTemplate.getTemplateVersion());
                            feeRuleDraft.setSceneName(sceneInfo.getSceneName());
                            feeRuleDraft.setSceneCode(sceneInfo.getSceneCode());
                            feeRuleDraft.setCreateTime(date);
                            feeRuleDraft.setModifyTime(date);
                            feeRuleDraft.setDel(false);
                            feeRuleDraft.setStatus(DraftStatusEnum.AUDIT.code);
                            feeRuleDraft.setFeeName(thisFeeTemplate.getBizRule().get(AutoReceiveOrderGuidePriceBizRule.Fields.feeName));
                            feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.basePrice, thisAutoReceiveOrderGuidePriceTable.getBasePrice());
                            feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.divisionType, divisionType);
//                            feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.masterId, batchTaskInfo.getBizId());
                            feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.bizId, batchTaskInfo.getBizId());
//                            feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.bizType, BizIdTypeEnum.MASTER_ID.code);
                            if (StringUtils.isNotBlank(sceneInfo.getBizIdType())) {
                                feeRuleDraft.getBizRule().put("bizType", sceneInfo.getBizIdType());
                            }
                            feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.masterInputPrice, thisAutoReceiveOrderGuidePriceTable.getMasterInputPrice());
                            feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.divisionTypeName, divisionTypeEnum.name);
                            feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.batchTaskId, batchTaskInfo.getBatchTaskId().toString());

                            String divisionId = null;

                            if (haveTag){
                                feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.bizTag,bizTag);
                            }

                            if (divisionTypeEnum == DivisionTypeEnum.PROVINCE) {
                                feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.level1DivisionId, address.getLv2DivisionId().toString());
                                feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.province, address.getLv2DivisionName());
                                divisionId = address.getLv2DivisionId().toString();
                            }

                            if (divisionTypeEnum == DivisionTypeEnum.CITY) {
                                feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.level1DivisionId, address.getLv2DivisionId().toString());
                                feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.province, address.getLv2DivisionName());
                                feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.level2DivisionId, address.getLv3DivisionId().toString());
                                feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.city, address.getLv3DivisionName());
                                divisionId = address.getLv3DivisionId().toString();
                            }

                            if (divisionTypeEnum == DivisionTypeEnum.DISTRICT) {
                                feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.level1DivisionId, address.getLv2DivisionId().toString());
                                feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.province, address.getLv2DivisionName());

                                if (address.getLv3DivisionId() != 0) {
                                    feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.level2DivisionId, address.getLv3DivisionId().toString());
                                    feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.city, address.getLv3DivisionName());
                                } else {
                                    feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.level2DivisionId, address.getLv4DivisionId().toString());
                                    feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.city, address.getLv4DivisionName());
                                }

                                feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.level3DivisionId, address.getLv4DivisionId().toString());
                                feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.district, address.getLv4DivisionName());
                                divisionId = address.getLv4DivisionId().toString();
                            }

                            if (divisionTypeEnum == DivisionTypeEnum.STREET) {
                                feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.level1DivisionId, address.getLv2DivisionId().toString());
                                feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.province, address.getLv2DivisionName());
                                if (address.getLv3DivisionId() != 0) {
                                    feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.level2DivisionId, address.getLv3DivisionId().toString());
                                    feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.city, address.getLv3DivisionName());
                                } else {
                                    feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.level2DivisionId, address.getLv4DivisionId().toString());
                                    feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.city, address.getLv4DivisionName());
                                }
                                if (address.getLv4DivisionId() != 0) {
                                    feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.level3DivisionId, address.getLv4DivisionId().toString());
                                    feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.district, address.getLv4DivisionName());
                                } else {
                                    feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.level3DivisionId, address.getLv5DivisionId().toString());
                                    feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.district, address.getLv5DivisionName());
                                }

                                feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.level4DivisionId, address.getLv5DivisionId().toString());
                                feeRuleDraft.getBizRule().put(AutoReceiveOrderGuidePriceBizRule.Fields.street, address.getLv5DivisionName());
                                divisionId = address.getLv5DivisionId().toString();
                            }
                            // 该场景不支持导入业务ID
                            CalculateRuleData calculateRule = formulaService.handleFormulas(calculateRuleData, divisionType, divisionId, null);
                            feeRuleDraft.setCalculateRuleData(calculateRule);

                            feeRuleDrafts.add(feeRuleDraft);
                        }
                        // 草稿保存
                        log.info("草稿保存：info={}", feeRuleDrafts);
                        feeRuleDraftRepository.save(feeRuleDrafts);
                        log.info("草稿保存完成：info={}", feeRuleDrafts);
                    }
                }
            }

            // 回写任务状态
            batchTaskInfo.setErrorCount(0);
            batchTaskInfo.setStatus(BatchTaskStatusEnum.SUCCESS.code);
            // 有错误
            if (CollectionUtils.isNotEmpty(errorInfo)) {
                batchTaskInfo.setErrorCount(errorInfo.size());
                batchTaskInfo.setStatus(BatchTaskStatusEnum.FAIL.code);
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                EasyExcel.write(byteArrayOutputStream, AutoReceiveOrderGuidePriceTable.class).sheet(sceneInfo.getSceneName()).doWrite(errorInfo);
                FileUploadResp upload = FileUploadUtils.upload(byteArrayOutputStream.toByteArray(), batchTaskInfo.getFileName().substring(batchTaskInfo.getFileName().lastIndexOf(PunConstant.DOT)));
                if (!upload.isSuccess()) {
                    log.error("上传失败 upload={}", upload);
                } else {
                    batchTaskInfo.setErrorFileUrl(upload.getData().getFileUrl());
                }
            }
        } catch (Exception e) {
            log.error("处理任务异常", e);
            if (batchTaskInfo.getStatus() == BatchTaskStatusEnum.IMPORT.code) {
                log.error("未到终态发生错误：task={}", batchTaskInfo);
                batchTaskInfo.setStatus(BatchTaskStatusEnum.FAIL.code);
                batchTaskInfo.setErrorFileUrl(batchTaskInfo.getFileUrl());
            }
        } finally {
            batchTaskInfoRepository.save(batchTaskInfo);
        }

    }


    private boolean validateAddress(List<AutoReceiveOrderGuidePriceTable> errorInfo, Map<String, Address> addressesCache, List<AutoReceiveOrderGuidePriceTable> value, List<Address> addresses, String streetStr) {
        Address divisionByFullName = findAddress(addressesCache, streetStr);
        if (Objects.isNull(divisionByFullName)) {
            for (AutoReceiveOrderGuidePriceTable autoReceiveOrderGuidePriceTable : value) {
                autoReceiveOrderGuidePriceTable.setError(streetStr + "地址错误，库中无数据");
            }
            errorInfo.addAll(value);
            return true;
        }
        addresses.add(divisionByFullName);
        return false;
    }

    private Address findAddress(Map<String, Address> addressesCache, String address) {
        Address divisionByFullName = addressesCache.get(address);
        if (Objects.isNull(divisionByFullName)) {
            divisionByFullName = addressApi.getDivisionByFullName(address);
            if (Objects.nonNull(divisionByFullName)) {
                addressesCache.put(address, divisionByFullName);
            }
        }
        return divisionByFullName;
    }

    private List<AutoReceiveOrderGuidePriceTable> validatePrice(List<AutoReceiveOrderGuidePriceTable> tableInfoList, List<AutoReceiveOrderGuidePriceTable> errorInfo) {
        // 校验单价/起步价
        tableInfoList = tableInfoList.stream().peek(autoReceiveOrderGuidePriceTable -> {
            if (StringUtils.isBlank(autoReceiveOrderGuidePriceTable.getMasterInputPrice())) {
                autoReceiveOrderGuidePriceTable.setError("单价为空");
                errorInfo.add(autoReceiveOrderGuidePriceTable);
                return;
            }
            try {
                BigDecimal bigDecimal = new BigDecimal(autoReceiveOrderGuidePriceTable.getMasterInputPrice());
                // 数字范围
                if (bigDecimal.compareTo(BigDecimal.ONE) < 0.01 || bigDecimal.compareTo(BigDecimal.valueOf(9999)) > 0) {
                    autoReceiveOrderGuidePriceTable.setError("单价不在0.01-9999范围内");
                    errorInfo.add(autoReceiveOrderGuidePriceTable);
                    return;
                }
                String masterInputPriceMaxStr = autoReceiveOrderGuidePriceTable.getMasterInputPriceMax();
                if (StringUtils.isNotBlank(masterInputPriceMaxStr)) {
                    BigDecimal masterInputPriceMax = new BigDecimal(masterInputPriceMaxStr);
                    if (masterInputPriceMax.compareTo(new BigDecimal("0.01")) < 0 || masterInputPriceMax.compareTo(BigDecimal.valueOf(9999)) > 0) {
                        autoReceiveOrderGuidePriceTable.setError("单价max不能小于单价");
                        errorInfo.add(autoReceiveOrderGuidePriceTable);
                        return;
                    }
                    if (masterInputPriceMax.compareTo(bigDecimal) <= 0) {
                        autoReceiveOrderGuidePriceTable.setError("单价max必须大于单价");
                        errorInfo.add(autoReceiveOrderGuidePriceTable);
                        return;
                    }
                }
            } catch (NumberFormatException e) {
                autoReceiveOrderGuidePriceTable.setError("单价不为数字");
                errorInfo.add(autoReceiveOrderGuidePriceTable);
                return;
            }
            if (StringUtils.isNotBlank(autoReceiveOrderGuidePriceTable.getBasePrice())) {
                try {
                    BigDecimal bigDecimal = new BigDecimal(autoReceiveOrderGuidePriceTable.getBasePrice());
                    // 数字范围
                    if (bigDecimal.compareTo(BigDecimal.ONE) < 0 || bigDecimal.compareTo(BigDecimal.valueOf(999)) > 0) {
                        autoReceiveOrderGuidePriceTable.setError("起步价不在1-999范围内");
                        errorInfo.add(autoReceiveOrderGuidePriceTable);
                    }
                } catch (NumberFormatException e) {
                    autoReceiveOrderGuidePriceTable.setError("起步价不为数字");
                    errorInfo.add(autoReceiveOrderGuidePriceTable);
                }
            }
        }).filter(autoReceiveOrderGuidePriceTable -> StringUtils.isBlank(autoReceiveOrderGuidePriceTable.getError())).collect(Collectors.toList());
        return tableInfoList;
    }

    private List<AutoReceiveOrderGuidePriceTable> validateTemplateIdAndServiceId(List<AutoReceiveOrderGuidePriceTable> tableInfoList, List<AutoReceiveOrderGuidePriceTable> errorInfo) {
        // 校验单价/起步价
        tableInfoList = tableInfoList.stream().peek(autoReceiveOrderGuidePriceTable -> {
            if (StringUtils.isBlank(autoReceiveOrderGuidePriceTable.getTemplateId())) {
                autoReceiveOrderGuidePriceTable.setError("定价ID为空");
                errorInfo.add(autoReceiveOrderGuidePriceTable);
                return;
            }
            try {
                Long templateId = Long.valueOf(autoReceiveOrderGuidePriceTable.getTemplateId());
            } catch (NumberFormatException e) {
                autoReceiveOrderGuidePriceTable.setError("定价ID格式不正确");
                errorInfo.add(autoReceiveOrderGuidePriceTable);
                return;
            }
            if (StringUtils.isBlank(autoReceiveOrderGuidePriceTable.getServiceId())) {
                autoReceiveOrderGuidePriceTable.setError("服务ID为空");
                errorInfo.add(autoReceiveOrderGuidePriceTable);
                return;
            }
            try {
                Long serviceId = Long.valueOf(autoReceiveOrderGuidePriceTable.getServiceId());
            } catch (NumberFormatException e) {
                autoReceiveOrderGuidePriceTable.setError("服务ID格式不正确");
                errorInfo.add(autoReceiveOrderGuidePriceTable);
                return;
            }
        }).filter(autoReceiveOrderGuidePriceTable -> StringUtils.isBlank(autoReceiveOrderGuidePriceTable.getError())).collect(Collectors.toList());
        return tableInfoList;
    }

    private List<AutoReceiveOrderGuidePriceTable> validateDivision(List<AutoReceiveOrderGuidePriceTable> tableInfoList, List<AutoReceiveOrderGuidePriceTable> errorInfo, DivisionTypeEnum divisionTypeEnum) {
        // 地区

        if (divisionTypeEnum != DivisionTypeEnum.COUNTRY) {
            tableInfoList = tableInfoList.stream().peek(autoReceiveOrderGuidePriceTable -> {
                String errorMessage = errorMessageMap.get(divisionTypeEnum);

                if (errorMessage != null) {
                    // 处理特殊情况，东莞没有区一级，海南（直辖市）没有市一级
                    if (StringUtils.isBlank(autoReceiveOrderGuidePriceTable.getProvince())) {
                        autoReceiveOrderGuidePriceTable.setError(errorMessage);
                        errorInfo.add(autoReceiveOrderGuidePriceTable);
                    }

                    if (divisionTypeEnum == DivisionTypeEnum.CITY && StringUtils.isBlank(autoReceiveOrderGuidePriceTable.getCity())) {
                        autoReceiveOrderGuidePriceTable.setError(errorMessage);
                        errorInfo.add(autoReceiveOrderGuidePriceTable);
                    }

                    if (divisionTypeEnum == DivisionTypeEnum.DISTRICT && StringUtils.isBlank(autoReceiveOrderGuidePriceTable.getDistrict())) {
                        // 市可为空
                        autoReceiveOrderGuidePriceTable.setError(errorMessage);
                        errorInfo.add(autoReceiveOrderGuidePriceTable);
                    }

                    if (divisionTypeEnum == DivisionTypeEnum.STREET && ((StringUtils.isBlank(autoReceiveOrderGuidePriceTable.getCity()) && StringUtils.isBlank(autoReceiveOrderGuidePriceTable.getDistrict())) || StringUtils.isBlank(autoReceiveOrderGuidePriceTable.getStreet()))) {
                        // 区或者市可为空，但是不能同时为空
                        autoReceiveOrderGuidePriceTable.setError(errorMessage);
                        errorInfo.add(autoReceiveOrderGuidePriceTable);
                    }


                }
            }).filter(autoReceiveOrderGuidePriceTable -> StringUtils.isBlank(autoReceiveOrderGuidePriceTable.getError())).collect(Collectors.toList());
        }

        return tableInfoList;
    }


    private List<AutoReceiveOrderGuidePriceTable> validateDivisionType(List<AutoReceiveOrderGuidePriceTable> tableInfoList, List<AutoReceiveOrderGuidePriceTable> errorInfo, String divisionType, DivisionTypeEnum divisionTypeEnum) {
        // 全部错误
        if (Objects.isNull(divisionTypeEnum)) {
            tableInfoList = tableInfoList.stream().peek(autoReceiveOrderGuidePriceTable -> {
                autoReceiveOrderGuidePriceTable.setError(divisionType + "地区类型不存在");
                errorInfo.add(autoReceiveOrderGuidePriceTable);
            }).filter(autoReceiveOrderGuidePriceTable -> StringUtils.isBlank(autoReceiveOrderGuidePriceTable.getError())).collect(Collectors.toList());
        }
        return tableInfoList;
    }

    private List<AutoReceiveOrderGuidePriceTable> validateBizId(List<AutoReceiveOrderGuidePriceTable> tableInfoList, BatchTaskInfo batchTaskInfo, List<AutoReceiveOrderGuidePriceTable> errorInfo) {
        // 全部错误
        if (StringUtils.isNotBlank(batchTaskInfo.getBizId())) {
            return tableInfoList.stream().peek(autoReceiveOrderGuidePriceTable -> {
                autoReceiveOrderGuidePriceTable.setError(BizIdTypeEnum.MASTER_ID.code + "未填写");
                errorInfo.add(autoReceiveOrderGuidePriceTable);
            }).filter(autoReceiveOrderGuidePriceTable -> StringUtils.isBlank(autoReceiveOrderGuidePriceTable.getError())).collect(Collectors.toList());
        }
        return tableInfoList;
    }



    private Map<String, List<AutoReceiveOrderGuidePriceTable>> toMap(DivisionTypeEnum divisionTypeEnum,List<String> haveTagList, List<AutoReceiveOrderGuidePriceTable> tableInfoList) {
        if (Objects.isNull(divisionTypeEnum)) {
            throw new BusException("区域类型（地区维度）错误");
        }

        return tableInfoList.stream().collect(Collectors.groupingBy(table -> {
            StringBuilder key = new StringBuilder(table.getServiceId());
            if (divisionTypeEnum != DivisionTypeEnum.COUNTRY) {
                key.append("#").append(table.getProvince().trim());
                if (divisionTypeEnum != DivisionTypeEnum.PROVINCE) {
                    key.append("#").append(StringUtils.isNotBlank(table.getCity()) ? table.getCity().trim() : "");
                    if (divisionTypeEnum != DivisionTypeEnum.CITY) {
                        key.append("#").append(StringUtils.isNotBlank(table.getDistrict()) ? table.getDistrict().trim() : "");
                        if (divisionTypeEnum == DivisionTypeEnum.STREET) {
                            key.append("#").append(table.getStreet().trim());
                        }
                    }
                }
            }

            if(StringUtils.isNotBlank(table.getBizTag())){
                key.append("#").append(table.getBizTag());
                haveTagList.add(key.toString());
            }

            return key.toString();
        }));
    }

}
