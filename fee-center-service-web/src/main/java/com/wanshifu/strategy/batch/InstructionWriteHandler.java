package com.wanshifu.strategy.batch;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.context.RowWriteHandlerContext;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

public class InstructionWriteHandler implements RowWriteHandler {
    private final String instruction;
    private final int columnCount;

    public InstructionWriteHandler(String instruction, int columnCount) {
        this.instruction = instruction;
        this.columnCount = columnCount;
    }

    @Override
    public void afterRowCreate(RowWriteHandlerContext context) {
        Sheet sheet = context.getWriteSheetHolder().getSheet();
        // 只在还没有任何合并区域时才写
        if (sheet.getNumMergedRegions() == 0) {
            Workbook wb = sheet.getWorkbook();

            // 1. 创建第 0 行
            Row row = sheet.createRow(0);

            // 2. 写说明文字到 A1
            Cell cell = row.createCell(0);
            cell.setCellValue(instruction);

            // 3. 合并 A1 到 对应末列
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, columnCount - 1));

            // 4. 设置自动换行样式
            CellStyle wrapStyle = wb.createCellStyle();
            wrapStyle.setWrapText(true);
            cell.setCellStyle(wrapStyle);

            // 5. 根据换行数撑开行高
            // 默认行高（磅）
            float defaultRowHeight = sheet.getDefaultRowHeightInPoints();
            // 换行数 = instruction 中的 '\n' 个数 + 1
            int lines = instruction.split("\n", -1).length;
            // 新行高 = 默认行高 * 行数
            row.setHeightInPoints(defaultRowHeight * lines);

        }
    }
}