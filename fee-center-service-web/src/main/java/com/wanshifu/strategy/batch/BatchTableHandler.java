package com.wanshifu.strategy.batch;

import com.wanshifu.domain.request.BatchTaskGenerateReq;
import com.wanshifu.fee.center.domain.document.BatchTaskInfo;
import com.wanshifu.fee.center.domain.document.SceneInfo;

public interface BatchTableHandler {
    int DEFAULT_ORDER = 1;

    boolean isSupport(SceneInfo sceneInfo);

    byte[] generateTemplateTable(SceneInfo sceneInfo, BatchTaskGenerateReq req);

    void handleBatchTask(SceneInfo sceneInfo, BatchTaskInfo batchTaskInfo);
}
