package com.wanshifu.strategy.batch;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.wanshifu.adapter.api.backend.AttributePathSnapshotApi;
import com.wanshifu.adapter.dto.attribute.path.AttributePathNoWithWhiteAccountIdReq;
import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.domain.dto.table.BaseTable;
import com.wanshifu.domain.dto.table.BatchUpdateSkuByServiceTable;
import com.wanshifu.domain.request.BatchTaskGenerateReq;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.FeeTemplate;
import com.wanshifu.fee.center.domain.document.SceneInfo;
import com.wanshifu.fee.center.domain.enums.BizIdTypeEnum;
import com.wanshifu.fee.center.domain.enums.DivisionTypeEnum;
import com.wanshifu.fee.center.domain.enums.TemplateStatusEnum;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.repository.BatchTaskInfoRepository;
import com.wanshifu.repository.FeeRuleDraftRepository;
import com.wanshifu.repository.FeeRuleRepository;
import com.wanshifu.repository.SceneInfoRepository;
import com.wanshifu.service.FeeRuleService;
import com.wanshifu.service.FormulaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 批量按服务自定义sku下载模版处理类
 * @date 2025/7/17 16:05
 */
@Component
@Slf4j
public class BatchUpdateSkuByServiceHandler {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private BatchTaskInfoRepository batchTaskInfoRepository;
    @Resource
    private SceneInfoRepository sceneInfoRepository;
    @Resource
    private FeeRuleDraftRepository feeRuleDraftRepository;
    @Resource
    private FeeRuleRepository feeRuleRepository;

    @Resource
    private FormulaService formulaService;

    @Resource
    private FeeRuleService feeRuleService;

    @Resource
    private AddressApi addressApi;

    @Resource
    private AttributePathSnapshotApi attributePathSnapshotApi;

    /**
     * 管控开始时间
     */
    @Value("${controlStartTime:7}")
    private int controlStartTime;

    /**
     * 管控结束时间
     */
    @Value("${controlEndTime:21}")
    private int controlEndTime;

    /**
     * 管控期间导入文件最大行数
     */
    @Value("${controlFileMaxRowSize:20500}")
    private int controlFileMaxRowSize;

    // 创建一个 DivisionTypeEnum 到错误消息的映射
    public static final Map<DivisionTypeEnum, String> errorMessageMap = new HashMap<>();

    static {
        errorMessageMap.put(DivisionTypeEnum.PROVINCE, "省份为空");
        errorMessageMap.put(DivisionTypeEnum.CITY, "省份或者城市为空");
        errorMessageMap.put(DivisionTypeEnum.DISTRICT, "省份、城市或者区县为空");
        errorMessageMap.put(DivisionTypeEnum.STREET, "省份、城市、区县或者街道为空");
    }


    public byte[] generateTemplateTable(SceneInfo sceneInfo, BatchTaskGenerateReq req) {
        String sceneCode = req.getSceneCode();
        Boolean showWhitelistAttributesAndStatus = req.getShowWhitelistAttributesAndStatus();
        showWhitelistAttributesAndStatus = showWhitelistAttributesAndStatus != null && showWhitelistAttributesAndStatus;
        Set<String> serviceIds = req.getServiceIds();
        // 模板库里面查询模板数据
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        criteria.and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);
        criteria.and(FeeTemplate.Fields.sceneCode).is(sceneCode);
        // 费用类型不为空
        criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + "feeTypeTagName").ne(null);
        criteria.and("group").ne(null);
        criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + "serviceId").in(serviceIds);
        Query query = Query.query(criteria);
        List<FeeTemplate> feeTemplates = mongoTemplate.find(query, FeeTemplate.class);
        if (CollectionUtils.isEmpty(feeTemplates)) {
            throw new BusException("无模板信息");
        }
        if (feeTemplates.size() > 30000) {
            throw new BusException("模板数量超过30000，请缩小导出范围");
        }
        // 生成表格
        List<BatchUpdateSkuByServiceTable> tableInfoList = new ArrayList<>(feeTemplates.size());
        // 保证同一个服务在一组
        Map<String, List<FeeTemplate>> collect = feeTemplates.stream().collect(Collectors.groupingBy(FeeTemplate::getGroup));

        for (Map.Entry<String, List<FeeTemplate>> stringListEntry : collect.entrySet()) {
            List<FeeTemplate> feeTemplateList = stringListEntry.getValue();
            String serviceId = stringListEntry.getKey();
            List<FeeTemplate> templates = feeTemplateList.stream().sorted(Comparator.comparing(FeeTemplate::getSort)).collect(Collectors.toList());

            Map<String, Set<Long>> skuNoWithAccountIdMap = null;
            if (showWhitelistAttributesAndStatus) {
                Set<String> skuNos = feeTemplateList.stream().map(f -> f.getBizRule().get(CommonBizRule.Fields.skuNo)).collect(Collectors.toSet());
                AttributePathNoWithWhiteAccountIdReq accountIdReq = new AttributePathNoWithWhiteAccountIdReq();
                accountIdReq.setServiceId(Long.valueOf(serviceId));
                accountIdReq.setAttributePathNos(skuNos);
                skuNoWithAccountIdMap = attributePathSnapshotApi.getAttributePathNoWithWhiteAccountId(accountIdReq);
            }
            // 排序
            for (FeeTemplate feeTemplate : templates) {
                BatchUpdateSkuByServiceTable batchUpdateSkuByServiceTable = new BatchUpdateSkuByServiceTable();
                batchUpdateSkuByServiceTable.setServiceId(feeTemplate.getBizRule().get("serviceId"));
                batchUpdateSkuByServiceTable.setServiceName(feeTemplate.getBizRule().get("serviceName"));
                String skuNo = feeTemplate.getBizRule().get(CommonBizRule.Fields.skuNo);
                batchUpdateSkuByServiceTable.setSkuNo(skuNo);
                batchUpdateSkuByServiceTable.setTemplateId(feeTemplate.getTemplateId().toString());
                batchUpdateSkuByServiceTable.setAttributeDisplayName(feeTemplate.getBizRule().get("attributeDisplayName"));
                if (showWhitelistAttributesAndStatus && skuNoWithAccountIdMap != null) {
                    Set<Long> skuNoSet = skuNoWithAccountIdMap.get(skuNo);
                    if (CollectionUtils.isNotEmpty(skuNoSet)) {
                        batchUpdateSkuByServiceTable.setWhiteListAttributesAccountIds(skuNoSet.stream().sorted().map(String::valueOf).collect(Collectors.joining("、")));
                    }
                }
                batchUpdateSkuByServiceTable.setCustomizeSkuUserId(feeTemplate.getBizRule().get(CommonBizRule.Fields.customSkuUserId));
                tableInfoList.add(batchUpdateSkuByServiceTable);
            }
        }

        // 1. 构造说明文字
        String instruction = "说明：\n" +
                "商家ID：选填；\n" +
                "自定义sku名称：必填，不能超过200个字；\n" +
                "若 SKU 定义为用户输入范围（如 “一体 0-60cm”），填写最小最大值；若为具体（如商品型号），则填写具体值。\n" +
                "最小值：当“value值类型”选择“范围”时必填，且为数值；\n" +
                "最大值：当“value值类型”选择“范围”时必填，且为数值，须大于最小值；\n" +
                "具体值：当“value值类型”选择“具体值”时必填。\n" +
                "商家ID、最大值、最小值、具体值组合不能重复，且多条范围值之间不能有值交叉。";

        // 2. 排除列列表（保持你原来的逻辑）
        List<String> excludeColumnFieldNames = new ArrayList<>();
        excludeColumnFieldNames.add(BatchUpdateSkuByServiceTable.Fields.error);
        if (StringUtils.isBlank(sceneInfo.getBizIdType())
                || BizIdTypeEnum.EMPTY.code.equals(sceneInfo.getBizIdType())) {
            excludeColumnFieldNames.add(BatchUpdateSkuByServiceTable.Fields.customSkuUserId);
        }

        // 3. 用反射 + 注解过滤真实要写出的列数
        Field[] allFields = BatchUpdateSkuByServiceTable.class.getDeclaredFields();
        int columnCount = (int) Arrays.stream(allFields)
                // 必须有 @ExcelProperty
                .filter(f -> f.isAnnotationPresent(ExcelProperty.class))
                // 且不在排除列表里
                .filter(f -> !excludeColumnFieldNames.contains(f.getName()))
                .count();

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

        try (ExcelWriter writer = EasyExcel.write(byteArrayOutputStream, BatchUpdateSkuByServiceTable.class)
                .excludeColumnFieldNames(excludeColumnFieldNames)
                .registerWriteHandler(new InstructionWriteHandler(instruction, columnCount))
                .build()) {

            // sheet 名称用场景名称
            WriteSheet sheet = EasyExcel.writerSheet(sceneInfo.getSceneName())
                    .relativeHeadRowIndex(1)
                    .build();
            writer.write(tableInfoList, sheet);
            writer.finish();
        }

        return byteArrayOutputStream.toByteArray();
    }
}
