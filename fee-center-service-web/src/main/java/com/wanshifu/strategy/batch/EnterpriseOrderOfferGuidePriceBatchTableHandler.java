package com.wanshifu.strategy.batch;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.mongodb.WriteResult;
import com.wanshifu.api.FileUploadUtils;
import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.bean.FileUploadResp;
import com.wanshifu.domain.dto.table.EnterpriseOrderOfferGuidePriceTable;
import com.wanshifu.domain.request.BatchTaskGenerateReq;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.biz.EnterpriseOrderOfferGuidePriceBizRule;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.*;
import com.wanshifu.fee.center.domain.enums.*;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.HttpBuilder;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.repository.BatchTaskInfoRepository;
import com.wanshifu.repository.FeeRuleDraftRepository;
import com.wanshifu.service.FormulaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Component
@Slf4j
@Order(BatchTableHandler.DEFAULT_ORDER)
public class EnterpriseOrderOfferGuidePriceBatchTableHandler implements BatchTableHandler {

    @javax.annotation.Resource
    private MongoTemplate mongoTemplate;

    @javax.annotation.Resource
    private BatchTaskInfoRepository batchTaskInfoRepository;

    @javax.annotation.Resource
    private FeeRuleDraftRepository feeRuleDraftRepository;

    @javax.annotation.Resource
    private Executor batchTaskExecutor;

    @javax.annotation.Resource
    private AddressApi addressApi;

    @Resource
    private FormulaService formulaService;

    // 创建一个 DivisionTypeEnum 到错误消息的映射
    public static final Map<DivisionTypeEnum, String> errorMessageMap = new HashMap<>();

    static {
        errorMessageMap.put(DivisionTypeEnum.PROVINCE, "省份为空");
        errorMessageMap.put(DivisionTypeEnum.CITY, "省份或者城市为空");
        errorMessageMap.put(DivisionTypeEnum.DISTRICT, "省份、城市或者区县为空");
        errorMessageMap.put(DivisionTypeEnum.STREET, "省份、城市、区县或者街道为空");
    }


    @Override
    public boolean isSupport(SceneInfo sceneInfo) {
        return "enterprise_order_offer_guide_price".equals(sceneInfo.getSceneCode());
    }

    @Override
    public byte[] generateTemplateTable(SceneInfo sceneInfo, BatchTaskGenerateReq req) {
        String sceneCode = req.getSceneCode();
        Set<String> serviceIds = req.getServiceIds();
        // 模板库里面查询模板数据
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        criteria.and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);
        criteria.and(FeeTemplate.Fields.sceneCode).is(sceneCode);
        // 费用类型不为空
        criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.feeTypeTagName).ne(null);
        criteria.and(FeeTemplate.Fields.group).ne(null);
        criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.serviceId).in(serviceIds);
        Query query = Query.query(criteria);
        List<FeeTemplate> feeTemplates = mongoTemplate.find(query, FeeTemplate.class);
        if (CollectionUtils.isEmpty(feeTemplates)) {
            throw new BusException("无模板信息");
        }
        // 生成表格
        List<EnterpriseOrderOfferGuidePriceTable> tableInfoList = new ArrayList<>(feeTemplates.size());
        // 保证同一个服务在一组
        Map<String, List<FeeTemplate>> collect = feeTemplates.stream().collect(Collectors.groupingBy(FeeTemplate::getGroup));

        for (Map.Entry<String, List<FeeTemplate>> stringListEntry : collect.entrySet()) {
            List<FeeTemplate> feeTemplateList = stringListEntry.getValue();
            List<FeeTemplate> templates = feeTemplateList.stream().sorted(Comparator.comparing(FeeTemplate::getSort)).collect(Collectors.toList());
            // 排序
            for (FeeTemplate feeTemplate : templates) {
                EnterpriseOrderOfferGuidePriceTable enterpriseOrderOfferGuidePriceTable = new EnterpriseOrderOfferGuidePriceTable();
                enterpriseOrderOfferGuidePriceTable.setServiceId(feeTemplate.getBizRule().get(EnterpriseOrderOfferGuidePriceBizRule.Fields.serviceId));
                enterpriseOrderOfferGuidePriceTable.setServiceName(feeTemplate.getBizRule().get(EnterpriseOrderOfferGuidePriceBizRule.Fields.serviceName));
                enterpriseOrderOfferGuidePriceTable.setSkuNo(feeTemplate.getBizRule().get(CommonBizRule.Fields.skuNo));
                enterpriseOrderOfferGuidePriceTable.setTemplateId(feeTemplate.getTemplateId().toString());
                enterpriseOrderOfferGuidePriceTable.setAttributeDisplayName(feeTemplate.getBizRule().get(EnterpriseOrderOfferGuidePriceBizRule.Fields.attributeDisplayName));
                tableInfoList.add(enterpriseOrderOfferGuidePriceTable);
            }
        }
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        EasyExcel.write(byteArrayOutputStream, EnterpriseOrderOfferGuidePriceTable.class).excludeColumnFieldNames(Collections.singletonList("error")).sheet(sceneInfo.getSceneName()).doWrite(tableInfoList);


        return byteArrayOutputStream.toByteArray();
    }

    @Override
    public void handleBatchTask(SceneInfo sceneInfo, BatchTaskInfo batchTaskInfo) {
        try {
            log.info("开始处理任务：task={}", batchTaskInfo);
            // 下载文件
            String fileUrl = batchTaskInfo.getFileUrl();
            InputStream content = HttpBuilder.genericGet(fileUrl).build().execute().getEntity().getContent();
            log.info("文件下载完成：taskId={},fileUrl={}", batchTaskInfo.getBatchTaskId(), fileUrl);
            List<EnterpriseOrderOfferGuidePriceTable> tableInfoList = EasyExcel.read(content).sheet(sceneInfo.getSceneName()).head(EnterpriseOrderOfferGuidePriceTable.class).doReadSync();
            log.info("文件解析完成：taskId={}", batchTaskInfo.getBatchTaskId());

            if (CollectionUtils.isEmpty(tableInfoList)) {
                throw new BusException("文件为空");
            }

            int hour = LocalTime.now().getHour();
            if (hour >= 7 && hour < 21 && tableInfoList.size() > 10000) {
                log.error("导入时间在7-21点之间，且条数大于1万条，超过阈值，导入失败！");
                throw new BusException("导入时间在7-21点之间，且条数大于1万条，超过阈值，导入失败！");
            } else if (tableInfoList.size() > 30000) {
                log.error("导入条目过大，大于3w. batchTaskId={}", batchTaskInfo.getBatchTaskId());
                throw new BusException(batchTaskInfo.getBatchTaskId() + "条目大于3w");
            }

            // 对于上传数据来说，其批次唯一性由bizId + 服务ID + 地区决定
            batchTaskInfo.setCount(tableInfoList.size());

            // 异常数据
            List<EnterpriseOrderOfferGuidePriceTable> errorInfo = new ArrayList<>();

            String divisionType = batchTaskInfo.getDivisionType();
            DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(divisionType);
//  TODO 产品说本期没有bizId校验
//            tableInfoList = validateBizId(tableInfoList,batchTaskInfo,errorInfo);

            tableInfoList = validateDivisionType(tableInfoList, errorInfo, divisionType, divisionTypeEnum);

            Assert.notNull(divisionTypeEnum, "divisionTypeEnum is null");

            tableInfoList = validateDivision(tableInfoList, errorInfo, divisionTypeEnum);

            tableInfoList = validatePrice(tableInfoList, errorInfo);

            tableInfoList = validateTemplateIdAndServiceId(tableInfoList, errorInfo);

            List<String> haveTagList = new ArrayList<>();
            // key = serviceId + 地区
            Map<String, List<EnterpriseOrderOfferGuidePriceTable>> serviceMap = toMap(divisionTypeEnum, haveTagList,tableInfoList);

            Set<String> serviceIds = tableInfoList.stream().map(EnterpriseOrderOfferGuidePriceTable::getServiceId).collect(Collectors.toSet());
            // 模板库里面查询模板数据
            Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
            criteria.and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);
            criteria.and(FeeTemplate.Fields.sceneCode).is(sceneInfo.getSceneCode());
            criteria.and(FeeTemplate.Fields.group).ne(null);
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.serviceId).in(serviceIds);
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.feeTypeTagName).ne(null);
            Query query = Query.query(criteria);
            List<FeeTemplate> feeTemplates = mongoTemplate.find(query, FeeTemplate.class);
            Map<String, List<FeeTemplate>> templateMap = feeTemplates.stream().collect(Collectors.groupingBy(FeeTemplate::getGroup));


            Map<String, Address> addressesCache = new HashMap<>();

            // 数据完整性校验
            for (Map.Entry<String, List<EnterpriseOrderOfferGuidePriceTable>> entry : serviceMap.entrySet()) {
                String key = entry.getKey();
                String[] split = key.split("#");
                String serviceId = split[0];
                String bizTag = null;
                boolean haveTag = haveTagList.contains(key);
                if(haveTag){
                    bizTag = split[split.length - 1];
                }

                List<FeeTemplate> thisFeeTemplates = templateMap.get(serviceId);
                List<EnterpriseOrderOfferGuidePriceTable> value = entry.getValue();
                if (CollectionUtils.isEmpty(thisFeeTemplates)) {
                    for (EnterpriseOrderOfferGuidePriceTable EnterpriseOrderOfferGuidePriceTable : value) {
                        EnterpriseOrderOfferGuidePriceTable.setError(serviceId + "该服务模板不存在");
                    }
                    errorInfo.addAll(value);
                    continue;
                }

                // 重复templateId
                Map<String, List<EnterpriseOrderOfferGuidePriceTable>> repeatTemplate = value.stream().collect(Collectors.groupingBy(EnterpriseOrderOfferGuidePriceTable::getTemplateId));
                Map.Entry<String, List<EnterpriseOrderOfferGuidePriceTable>> reEntry = repeatTemplate.entrySet().stream().filter(repeatEntry -> repeatEntry.getValue().size() > 1).findAny().orElse(null);
                if (Objects.nonNull(reEntry)) {
                    for (EnterpriseOrderOfferGuidePriceTable EnterpriseOrderOfferGuidePriceTable : value) {
                        StringBuilder builder = new StringBuilder();
                        if (Objects.nonNull(batchTaskInfo.getBizId())) {
                            builder.append(batchTaskInfo.getBizId()).append("+");
                        }
                        builder.append(key).append("+").append(reEntry.getKey()).append("重复");
                        EnterpriseOrderOfferGuidePriceTable.setError(builder.toString());
                    }
                    errorInfo.addAll(reEntry.getValue());
                    continue;
                }


                Set<Long> templateIds = value.stream().map(EnterpriseOrderOfferGuidePriceTable::getTemplateId).map(Long::valueOf).collect(Collectors.toSet());
                Set<Long> realTemplateIds = thisFeeTemplates.stream().map(FeeTemplate::getTemplateId).collect(Collectors.toSet());

                // 取差集，找出在库中没有的数据与缺失的数据
                Collection<?> subtract = CollectionUtils.subtract(templateIds, realTemplateIds);
                if (CollectionUtils.isNotEmpty(subtract)) {
                    for (EnterpriseOrderOfferGuidePriceTable EnterpriseOrderOfferGuidePriceTable : value) {
                        EnterpriseOrderOfferGuidePriceTable.setError("定价ID " + subtract.stream().filter(Objects::nonNull).map(Object::toString).collect(Collectors.joining(",")) + "在库中不存在");
                    }
                    errorInfo.addAll(value);
                    continue;
                }
//                else {
//                    subtract = CollectionUtils.subtract(realTemplateIds, templateIds);
//                    if (CollectionUtils.isNotEmpty(subtract)) {
//                        for (EnterpriseOrderOfferGuidePriceTable EnterpriseOrderOfferGuidePriceTable : value) {
//                            EnterpriseOrderOfferGuidePriceTable.setError("缺失定价ID" + subtract.stream().filter(Objects::nonNull).map(Object::toString).collect(Collectors.joining(",")) + "的数据");
//                        }
//                        errorInfo.addAll(value);
//                        continue;
//                    }
//                }

//                Collection<?> intersection = CollectionUtils.intersection(templateIds, realTemplateIds);

                // 对不上
//                if (intersection.size() != realTemplateIds.size()) {
//                    for (EnterpriseOrderOfferGuidePriceTable EnterpriseOrderOfferGuidePriceTable : value) {
//                        EnterpriseOrderOfferGuidePriceTable.setError(serviceId + "数据不完整");
//                    }
//                    errorInfo.addAll(value);
//                    continue;
//                }

                List<Address> addresses = new ArrayList<>();
                // 校验地区
                // 非国家级别需要校验地址
                if (DivisionTypeEnum.COUNTRY != divisionTypeEnum) {
                    // 如果是街道需要分割，如果是其他直接用,拼接
                    // 如果是街道需要分割，如果是其他直接用,拼接
                    if (DivisionTypeEnum.STREET == divisionTypeEnum) {
                        // 默认事倒数第一个
                        String street = null;
                        boolean haveBizId = StringUtils.isNotBlank(sceneInfo.getBizIdType()) && !BizIdTypeEnum.EMPTY.code.equals(sceneInfo.getBizIdType());
                        // 同时有tag和id是倒数第三个
                        if (haveBizId && haveTag) {
                            // 不然就是倒数第二个
                            street = split[split.length - 3];
                        }else if (haveBizId || haveTag) {
                            // 只有其中一个则是倒数第二个
                            street = split[split.length - 2];
                        }else {
                            street  = split[split.length - 1];
                        }

                        String[] streets = street.split("[,，]");
                        String shouldFindAddress = Arrays.stream(split).skip(1).limit(3).collect(Collectors.joining(","));
                        for (String s : streets) {
                            String streetStr = shouldFindAddress + "," + s;
                            validateAddress(errorInfo, addressesCache, value, addresses, streetStr);
                        }
                    } else {
                        int limit = split.length - 1;
                        if (StringUtils.isNotBlank(sceneInfo.getBizIdType()) && !BizIdTypeEnum.EMPTY.code.equals(sceneInfo.getBizIdType())) {
                            limit--;
                        }
                        if(haveTagList.contains(key)){
                            limit--;
                        }
                        String shouldFindAddress = Arrays.stream(split).skip(1).limit(limit).collect(Collectors.joining(","));
                        validateAddress(errorInfo, addressesCache, value, addresses, shouldFindAddress);
                    }
                }


                // 查询库中草稿数据，如果有则删除
                // 开始删除旧草稿
                Update update = Update.update(BaseDocument.Fields.del, true);
                // FIXME 此处删除与插入需要做成事务, 但是公司spring现版本不支持
                if (DivisionTypeEnum.COUNTRY == divisionTypeEnum) {
                    // 模板库里面查询模板数据
                    Criteria findDraftCriteria = Criteria.where(BaseDocument.Fields.del).is(false);
                    findDraftCriteria.and(FeeRuleDraft.Fields.sceneCode).is(sceneInfo.getSceneCode());
                    // 国家级别 bizId + serviceId
                    if (StringUtils.isNotBlank(batchTaskInfo.getBizId())) {
                        findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.masterId).is(batchTaskInfo.getBizId());
                    }

                    if (haveTag) {
                        findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.bizTag).is(bizTag);
                    }

                    findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.divisionType).is(divisionType);
                    findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.serviceId).is(serviceId);
                    Query findDraftQuery = Query.query(findDraftCriteria);
                    String criteriaStr = JSON.toJSONString(findDraftCriteria);
                    log.info("开始删除旧草稿：query={}", criteriaStr);
                    WriteResult writeResult = mongoTemplate.updateMulti(findDraftQuery, update, FeeRuleDraft.class);
                    log.info("旧草稿删除完成：query={} , result={}", criteriaStr, writeResult);
                    // 开始构建插入草稿数据
                    List<FeeRuleDraft> feeRuleDrafts = new ArrayList<>();
                    for (FeeTemplate thisFeeTemplate : thisFeeTemplates) {
                        // 找到用户填写的数据
                        Long templateId = thisFeeTemplate.getTemplateId();
                        EnterpriseOrderOfferGuidePriceTable thisEnterpriseOrderOfferGuidePriceTable = value.stream().filter(EnterpriseOrderOfferGuidePriceTable -> EnterpriseOrderOfferGuidePriceTable.getTemplateId().equals(templateId.toString())).findFirst().orElse(null);
                        if (thisEnterpriseOrderOfferGuidePriceTable == null) {
                            continue;
                        }
                        Assert.notNull(thisEnterpriseOrderOfferGuidePriceTable, "thisEnterpriseOrderOfferGuidePriceTable is null");
                        // 存储参数

                        Date date = new Date();
                        FeeRuleDraft feeRuleDraft = new FeeRuleDraft();
                        feeRuleDraft.setFeeRuleDraftId(SnowFlakeGenerator.INSTANCE.generate());
                        feeRuleDraft.setBizRule(thisFeeTemplate.getBizRule());
                        feeRuleDraft.setGroup(thisFeeTemplate.getGroup());
                        feeRuleDraft.setTemplateId(thisFeeTemplate.getTemplateId());
                        CalculateRuleData calculateRuleData = thisFeeTemplate.getCalculateRuleData();
//                        feeRuleDraft.setCalculateRuleData(calculateRuleData);
                        feeRuleDraft.setTemplateVersion(thisFeeTemplate.getTemplateVersion());
                        feeRuleDraft.setSceneName(sceneInfo.getSceneName());
                        feeRuleDraft.setSceneCode(sceneInfo.getSceneCode());
                        feeRuleDraft.setCreateTime(date);
                        feeRuleDraft.setModifyTime(date);
                        feeRuleDraft.setDel(false);
                        feeRuleDraft.setStatus(DraftStatusEnum.AUDIT.code);
                        feeRuleDraft.setFeeName(thisFeeTemplate.getBizRule().get(EnterpriseOrderOfferGuidePriceBizRule.Fields.feeName));
                        String bizId = batchTaskInfo.getBizId();
                        feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.basePrice, thisEnterpriseOrderOfferGuidePriceTable.getBasePrice());
                        feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.divisionType, divisionType);
//                        feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.masterId, bizId);
                        feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.bizId, bizId);
//                        feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.bizType, BizIdTypeEnum.MASTER_ID.code);

                        if (StringUtils.isNotBlank(sceneInfo.getBizIdType())) {
                            feeRuleDraft.getBizRule().put("bizType", sceneInfo.getBizIdType());
                        }

                        if(haveTag){
                            feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.bizTag, bizTag);
                        }

                        feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.masterInputPrice, thisEnterpriseOrderOfferGuidePriceTable.getMasterInputPrice());
                        feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.divisionTypeName, divisionTypeEnum.name);
                        feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.batchTaskId, batchTaskInfo.getBatchTaskId().toString());
                        CalculateRuleData calculateRule = formulaService.handleFormulas(calculateRuleData, divisionType, null, null);
                        feeRuleDraft.setCalculateRuleData(calculateRule);
                        feeRuleDrafts.add(feeRuleDraft);
                    }
                    // 草稿保存
                    log.info("草稿保存：info={}", feeRuleDrafts);
                    feeRuleDraftRepository.save(feeRuleDrafts);
                    log.info("草稿保存完成：info={}", feeRuleDrafts);
                } else {
                    // 非国家则需要逐地址删除与插入
                    for (Address address : addresses) {
                        // 我们库中是从省开始的，而地址库是从国家开始的
                        // 模板库里面查询模板数据
                        Criteria findDraftCriteria = Criteria.where(BaseDocument.Fields.del).is(false);
                        findDraftCriteria.and(FeeRuleDraft.Fields.sceneCode).is(sceneInfo.getSceneCode());
                        // 地区级别 bizId + serviceId + 地区id
                        if (StringUtils.isNotBlank(batchTaskInfo.getBizId())) {
                            findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.masterId).is(batchTaskInfo.getBizId());
                        }

                        if (haveTag) {
                            findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.bizTag).is(bizTag);
                        }

                        findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.divisionType).is(divisionType);
                        findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.serviceId).is(serviceId);

                        if (divisionTypeEnum == DivisionTypeEnum.PROVINCE) {
                            findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level1DivisionId).is(address.getLv2DivisionId().toString());
                        }
                        if (divisionTypeEnum == DivisionTypeEnum.CITY) {
                            findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level1DivisionId).is(address.getLv2DivisionId().toString());
                            findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level2DivisionId).is(address.getLv3DivisionId().toString());
                        }
                        if (divisionTypeEnum == DivisionTypeEnum.DISTRICT) {
                            findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level1DivisionId).is(address.getLv2DivisionId().toString());
                            if (address.getLv3DivisionId() != 0) {
                                findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level2DivisionId).is(address.getLv3DivisionId().toString());
                            } else {
                                findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level2DivisionId).is(address.getLv4DivisionId().toString());
                            }
                            findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level3DivisionId).is(address.getLv4DivisionId().toString());
                        }
                        if (divisionTypeEnum == DivisionTypeEnum.STREET) {
                            findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level1DivisionId).is(address.getLv2DivisionId().toString());
                            if (address.getLv3DivisionId() != 0) {
                                findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level2DivisionId).is(address.getLv3DivisionId().toString());
                            } else {
                                findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level2DivisionId).is(address.getLv4DivisionId().toString());
                            }

                            if (address.getLv4DivisionId() != 0) {
                                findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level3DivisionId).is(address.getLv4DivisionId().toString());
                            } else {
                                findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level3DivisionId).is(address.getLv5DivisionId().toString());
                            }

                            findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + EnterpriseOrderOfferGuidePriceBizRule.Fields.level4DivisionId).is(address.getLv5DivisionId().toString());
                        }
                        Query findDraftQuery = Query.query(findDraftCriteria);
                        String criteriaStr = JSON.toJSONString(findDraftCriteria);
                        log.info("开始删除旧草稿：query={}", criteriaStr);
                        WriteResult writeResult = mongoTemplate.updateMulti(findDraftQuery, update, FeeRuleDraft.class);
                        log.info("旧草稿删除完成：query={} , result={}", criteriaStr, writeResult);
                        // 开始构建插入草稿数据
                        List<FeeRuleDraft> feeRuleDrafts = new ArrayList<>();
                        for (FeeTemplate thisFeeTemplate : thisFeeTemplates) {
                            // 找到用户填写的数据
                            Long templateId = thisFeeTemplate.getTemplateId();
                            EnterpriseOrderOfferGuidePriceTable thisEnterpriseOrderOfferGuidePriceTable = value.stream().filter(EnterpriseOrderOfferGuidePriceTable -> EnterpriseOrderOfferGuidePriceTable.getTemplateId().equals(templateId.toString())).findFirst().orElse(null);
                            if (thisEnterpriseOrderOfferGuidePriceTable == null) {
                                continue;
                            }
//                            Assert.notNull(thisEnterpriseOrderOfferGuidePriceTable, "enterprise_order_offer_guide_price table is null");
                            // 存储参数

                            Date date = new Date();
                            FeeRuleDraft feeRuleDraft = new FeeRuleDraft();
                            feeRuleDraft.setFeeRuleDraftId(SnowFlakeGenerator.INSTANCE.generate());
                            feeRuleDraft.setBizRule(thisFeeTemplate.getBizRule());
                            feeRuleDraft.setGroup(thisFeeTemplate.getGroup());
                            feeRuleDraft.setTemplateId(thisFeeTemplate.getTemplateId());
                            CalculateRuleData calculateRuleData = thisFeeTemplate.getCalculateRuleData();
                            feeRuleDraft.setCalculateRuleData(calculateRuleData);
                            feeRuleDraft.setTemplateVersion(thisFeeTemplate.getTemplateVersion());
                            feeRuleDraft.setSceneName(sceneInfo.getSceneName());
                            feeRuleDraft.setSceneCode(sceneInfo.getSceneCode());
                            feeRuleDraft.setCreateTime(date);
                            feeRuleDraft.setModifyTime(date);
                            feeRuleDraft.setDel(false);
                            feeRuleDraft.setStatus(DraftStatusEnum.AUDIT.code);
                            feeRuleDraft.setFeeName(thisFeeTemplate.getBizRule().get(EnterpriseOrderOfferGuidePriceBizRule.Fields.feeName));
                            feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.basePrice, thisEnterpriseOrderOfferGuidePriceTable.getBasePrice());
                            feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.divisionType, divisionType);
//                            feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.masterId, batchTaskInfo.getBizId());
                            feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.bizId, batchTaskInfo.getBizId());
//                            feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.bizType, BizIdTypeEnum.MASTER_ID.code);
                            if (StringUtils.isNotBlank(sceneInfo.getBizIdType())) {
                                feeRuleDraft.getBizRule().put("bizType", sceneInfo.getBizIdType());
                            }
                            feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.masterInputPrice, thisEnterpriseOrderOfferGuidePriceTable.getMasterInputPrice());
                            feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.divisionTypeName, divisionTypeEnum.name);
                            feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.batchTaskId, batchTaskInfo.getBatchTaskId().toString());

                            if(haveTag){
                                feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.bizTag, bizTag);
                            }

                            String divisionId = null;

                            if (divisionTypeEnum == DivisionTypeEnum.PROVINCE) {
                                feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.level1DivisionId, address.getLv2DivisionId().toString());
                                feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.province, address.getLv2DivisionName());
                                divisionId = address.getLv2DivisionId().toString();
                            }

                            if (divisionTypeEnum == DivisionTypeEnum.CITY) {
                                feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.level1DivisionId, address.getLv2DivisionId().toString());
                                feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.province, address.getLv2DivisionName());
                                feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.level2DivisionId, address.getLv3DivisionId().toString());
                                feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.city, address.getLv3DivisionName());
                                divisionId = address.getLv3DivisionId().toString();
                            }

                            if (divisionTypeEnum == DivisionTypeEnum.DISTRICT) {
                                feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.level1DivisionId, address.getLv2DivisionId().toString());
                                feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.province, address.getLv2DivisionName());

                                if (address.getLv3DivisionId() != 0) {
                                    feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.level2DivisionId, address.getLv3DivisionId().toString());
                                    feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.city, address.getLv3DivisionName());
                                } else {
                                    feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.level2DivisionId, address.getLv4DivisionId().toString());
                                    feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.city, address.getLv4DivisionName());
                                }

                                feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.level3DivisionId, address.getLv4DivisionId().toString());
                                feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.district, address.getLv4DivisionName());
                                divisionId = address.getLv4DivisionId().toString();
                            }

                            if (divisionTypeEnum == DivisionTypeEnum.STREET) {
                                feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.level1DivisionId, address.getLv2DivisionId().toString());
                                feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.province, address.getLv2DivisionName());
                                if (address.getLv3DivisionId() != 0) {
                                    feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.level2DivisionId, address.getLv3DivisionId().toString());
                                    feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.city, address.getLv3DivisionName());
                                } else {
                                    feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.level2DivisionId, address.getLv4DivisionId().toString());
                                    feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.city, address.getLv4DivisionName());
                                }
                                if (address.getLv4DivisionId() != 0) {
                                    feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.level3DivisionId, address.getLv4DivisionId().toString());
                                    feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.district, address.getLv4DivisionName());
                                } else {
                                    feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.level3DivisionId, address.getLv5DivisionId().toString());
                                    feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.district, address.getLv5DivisionName());
                                }

                                feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.level4DivisionId, address.getLv5DivisionId().toString());
                                feeRuleDraft.getBizRule().put(EnterpriseOrderOfferGuidePriceBizRule.Fields.street, address.getLv5DivisionName());
                                divisionId = address.getLv5DivisionId().toString();
                            }
                            CalculateRuleData calculateRule = formulaService.handleFormulas(calculateRuleData, divisionType, divisionId, null);
                            feeRuleDraft.setCalculateRuleData(calculateRule);
                            feeRuleDrafts.add(feeRuleDraft);
                        }
                        // 草稿保存
                        log.info("草稿保存：info={}", feeRuleDrafts);
                        feeRuleDraftRepository.save(feeRuleDrafts);
                        log.info("草稿保存完成：info={}", feeRuleDrafts);
                    }
                }
            }

            // 回写任务状态
            batchTaskInfo.setErrorCount(0);
            batchTaskInfo.setStatus(BatchTaskStatusEnum.SUCCESS.code);
            // 有错误
            if (CollectionUtils.isNotEmpty(errorInfo)) {
                batchTaskInfo.setErrorCount(errorInfo.size());
                batchTaskInfo.setStatus(BatchTaskStatusEnum.FAIL.code);
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                EasyExcel.write(byteArrayOutputStream, EnterpriseOrderOfferGuidePriceTable.class).sheet(sceneInfo.getSceneName()).doWrite(errorInfo);
                FileUploadResp upload = FileUploadUtils.upload(byteArrayOutputStream.toByteArray(), batchTaskInfo.getFileName().substring(batchTaskInfo.getFileName().lastIndexOf(PunConstant.DOT)));
                if (!upload.isSuccess()) {
                    log.error("上传失败 upload={}", upload);
                } else {
                    batchTaskInfo.setErrorFileUrl(upload.getData().getFileUrl());
                }
            }
        } catch (Exception e) {
            log.error("处理任务异常", e);
            if (batchTaskInfo.getStatus() == BatchTaskStatusEnum.IMPORT.code) {
                log.error("未到终态发生错误：task={}", batchTaskInfo);
                batchTaskInfo.setStatus(BatchTaskStatusEnum.FAIL.code);
                batchTaskInfo.setErrorFileUrl(batchTaskInfo.getFileUrl());
            }
        } finally {
            batchTaskInfoRepository.save(batchTaskInfo);
        }

    }


    private boolean validateAddress(List<EnterpriseOrderOfferGuidePriceTable> errorInfo, Map<String, Address> addressesCache, List<EnterpriseOrderOfferGuidePriceTable> value, List<Address> addresses, String streetStr) {
        Address divisionByFullName = findAddress(addressesCache, streetStr);
        if (Objects.isNull(divisionByFullName)) {
            for (EnterpriseOrderOfferGuidePriceTable EnterpriseOrderOfferGuidePriceTable : value) {
                EnterpriseOrderOfferGuidePriceTable.setError(streetStr + "地址错误，库中无数据");
            }
            errorInfo.addAll(value);
            return true;
        }
        addresses.add(divisionByFullName);
        return false;
    }

    private Address findAddress(Map<String, Address> addressesCache, String address) {
        Address divisionByFullName = addressesCache.get(address);
        if (Objects.isNull(divisionByFullName)) {
            divisionByFullName = addressApi.getDivisionByFullName(address);
            if (Objects.nonNull(divisionByFullName)) {
                addressesCache.put(address, divisionByFullName);
            }
        }
        return divisionByFullName;
    }

    private List<EnterpriseOrderOfferGuidePriceTable> validatePrice(List<EnterpriseOrderOfferGuidePriceTable> tableInfoList, List<EnterpriseOrderOfferGuidePriceTable> errorInfo) {
        // 校验单价/起步价
        tableInfoList = tableInfoList.stream().peek(EnterpriseOrderOfferGuidePriceTable -> {
            if (StringUtils.isBlank(EnterpriseOrderOfferGuidePriceTable.getMasterInputPrice())) {
                EnterpriseOrderOfferGuidePriceTable.setError("单价为空");
                errorInfo.add(EnterpriseOrderOfferGuidePriceTable);
                return;
            }
            try {
                BigDecimal bigDecimal = new BigDecimal(EnterpriseOrderOfferGuidePriceTable.getMasterInputPrice());
                // 数字范围
                if (bigDecimal.compareTo(BigDecimal.ONE) < 0.01 || bigDecimal.compareTo(BigDecimal.valueOf(9999)) > 0) {
                    EnterpriseOrderOfferGuidePriceTable.setError("单价不在0.01-9999范围内");
                    errorInfo.add(EnterpriseOrderOfferGuidePriceTable);
                    return;
                }
                String masterInputPriceMaxStr = EnterpriseOrderOfferGuidePriceTable.getMasterInputPriceMax();
                if (StringUtils.isNotBlank(masterInputPriceMaxStr)) {
                    BigDecimal masterInputPriceMax = new BigDecimal(masterInputPriceMaxStr);
                    if (masterInputPriceMax.compareTo(new BigDecimal("0.01")) < 0 || masterInputPriceMax.compareTo(BigDecimal.valueOf(9999)) > 0) {
                        EnterpriseOrderOfferGuidePriceTable.setError("单价max不能小于单价");
                        errorInfo.add(EnterpriseOrderOfferGuidePriceTable);
                        return;
                    }
                    if (masterInputPriceMax.compareTo(bigDecimal) <= 0) {
                        EnterpriseOrderOfferGuidePriceTable.setError("单价max必须大于单价");
                        errorInfo.add(EnterpriseOrderOfferGuidePriceTable);
                        return;
                    }
                }
            } catch (NumberFormatException e) {
                EnterpriseOrderOfferGuidePriceTable.setError("单价不为数字");
                errorInfo.add(EnterpriseOrderOfferGuidePriceTable);
                return;
            }
            if (StringUtils.isNotBlank(EnterpriseOrderOfferGuidePriceTable.getBasePrice())) {
                try {
                    BigDecimal bigDecimal = new BigDecimal(EnterpriseOrderOfferGuidePriceTable.getBasePrice());
                    // 数字范围
                    if (bigDecimal.compareTo(BigDecimal.ONE) < 0 || bigDecimal.compareTo(BigDecimal.valueOf(999)) > 0) {
                        EnterpriseOrderOfferGuidePriceTable.setError("起步价不在1-999范围内");
                        errorInfo.add(EnterpriseOrderOfferGuidePriceTable);
                    }
                } catch (NumberFormatException e) {
                    EnterpriseOrderOfferGuidePriceTable.setError("起步价不为数字");
                    errorInfo.add(EnterpriseOrderOfferGuidePriceTable);
                }
            }
        }).filter(EnterpriseOrderOfferGuidePriceTable -> StringUtils.isBlank(EnterpriseOrderOfferGuidePriceTable.getError())).collect(Collectors.toList());
        return tableInfoList;
    }

    private List<EnterpriseOrderOfferGuidePriceTable> validateTemplateIdAndServiceId(List<EnterpriseOrderOfferGuidePriceTable> tableInfoList, List<EnterpriseOrderOfferGuidePriceTable> errorInfo) {
        // 校验单价/起步价
        tableInfoList = tableInfoList.stream().peek(EnterpriseOrderOfferGuidePriceTable -> {
            if (StringUtils.isBlank(EnterpriseOrderOfferGuidePriceTable.getTemplateId())) {
                EnterpriseOrderOfferGuidePriceTable.setError("定价ID为空");
                errorInfo.add(EnterpriseOrderOfferGuidePriceTable);
                return;
            }
            try {
                Long templateId = Long.valueOf(EnterpriseOrderOfferGuidePriceTable.getTemplateId());
            } catch (NumberFormatException e) {
                EnterpriseOrderOfferGuidePriceTable.setError("定价ID格式不正确");
                errorInfo.add(EnterpriseOrderOfferGuidePriceTable);
                return;
            }
            if (StringUtils.isBlank(EnterpriseOrderOfferGuidePriceTable.getServiceId())) {
                EnterpriseOrderOfferGuidePriceTable.setError("服务ID为空");
                errorInfo.add(EnterpriseOrderOfferGuidePriceTable);
                return;
            }
            try {
                Long serviceId = Long.valueOf(EnterpriseOrderOfferGuidePriceTable.getServiceId());
            } catch (NumberFormatException e) {
                EnterpriseOrderOfferGuidePriceTable.setError("服务ID格式不正确");
                errorInfo.add(EnterpriseOrderOfferGuidePriceTable);
                return;
            }
        }).filter(EnterpriseOrderOfferGuidePriceTable -> StringUtils.isBlank(EnterpriseOrderOfferGuidePriceTable.getError())).collect(Collectors.toList());
        return tableInfoList;
    }

    private List<EnterpriseOrderOfferGuidePriceTable> validateDivision(List<EnterpriseOrderOfferGuidePriceTable> tableInfoList, List<EnterpriseOrderOfferGuidePriceTable> errorInfo, DivisionTypeEnum divisionTypeEnum) {
        // 地区

        if (divisionTypeEnum != DivisionTypeEnum.COUNTRY) {
            tableInfoList = tableInfoList.stream().peek(EnterpriseOrderOfferGuidePriceTable -> {
                String errorMessage = errorMessageMap.get(divisionTypeEnum);

                if (errorMessage != null) {
                    // 处理特殊情况，东莞没有区一级，海南（直辖市）没有市一级
                    if (StringUtils.isBlank(EnterpriseOrderOfferGuidePriceTable.getProvince())) {
                        EnterpriseOrderOfferGuidePriceTable.setError(errorMessage);
                        errorInfo.add(EnterpriseOrderOfferGuidePriceTable);
                    }

                    if (divisionTypeEnum == DivisionTypeEnum.CITY && StringUtils.isBlank(EnterpriseOrderOfferGuidePriceTable.getCity())) {
                        EnterpriseOrderOfferGuidePriceTable.setError(errorMessage);
                        errorInfo.add(EnterpriseOrderOfferGuidePriceTable);
                    }

                    if (divisionTypeEnum == DivisionTypeEnum.DISTRICT && StringUtils.isBlank(EnterpriseOrderOfferGuidePriceTable.getDistrict())) {
                        // 市可为空
                        EnterpriseOrderOfferGuidePriceTable.setError(errorMessage);
                        errorInfo.add(EnterpriseOrderOfferGuidePriceTable);
                    }

                    if (divisionTypeEnum == DivisionTypeEnum.STREET && ((StringUtils.isBlank(EnterpriseOrderOfferGuidePriceTable.getCity()) && StringUtils.isBlank(EnterpriseOrderOfferGuidePriceTable.getDistrict())) || StringUtils.isBlank(EnterpriseOrderOfferGuidePriceTable.getStreet()))) {
                        // 区或者市可为空，但是不能同时为空
                        EnterpriseOrderOfferGuidePriceTable.setError(errorMessage);
                        errorInfo.add(EnterpriseOrderOfferGuidePriceTable);
                    }


                }
            }).filter(EnterpriseOrderOfferGuidePriceTable -> StringUtils.isBlank(EnterpriseOrderOfferGuidePriceTable.getError())).collect(Collectors.toList());
        }

        return tableInfoList;
    }


    private List<EnterpriseOrderOfferGuidePriceTable> validateDivisionType(List<EnterpriseOrderOfferGuidePriceTable> tableInfoList, List<EnterpriseOrderOfferGuidePriceTable> errorInfo, String divisionType, DivisionTypeEnum divisionTypeEnum) {
        // 全部错误
        if (Objects.isNull(divisionTypeEnum)) {
            tableInfoList = tableInfoList.stream().peek(EnterpriseOrderOfferGuidePriceTable -> {
                EnterpriseOrderOfferGuidePriceTable.setError(divisionType + "地区类型不存在");
                errorInfo.add(EnterpriseOrderOfferGuidePriceTable);
            }).filter(EnterpriseOrderOfferGuidePriceTable -> StringUtils.isBlank(EnterpriseOrderOfferGuidePriceTable.getError())).collect(Collectors.toList());
        }
        return tableInfoList;
    }

    private List<EnterpriseOrderOfferGuidePriceTable> validateBizId(List<EnterpriseOrderOfferGuidePriceTable> tableInfoList, BatchTaskInfo batchTaskInfo, List<EnterpriseOrderOfferGuidePriceTable> errorInfo) {
        // 全部错误
        if (StringUtils.isNotBlank(batchTaskInfo.getBizId())) {
            return tableInfoList.stream().peek(EnterpriseOrderOfferGuidePriceTable -> {
                EnterpriseOrderOfferGuidePriceTable.setError(BizIdTypeEnum.MASTER_ID.code + "未填写");
                errorInfo.add(EnterpriseOrderOfferGuidePriceTable);
            }).filter(EnterpriseOrderOfferGuidePriceTable -> StringUtils.isBlank(EnterpriseOrderOfferGuidePriceTable.getError())).collect(Collectors.toList());
        }
        return tableInfoList;
    }

    private Map<String, List<EnterpriseOrderOfferGuidePriceTable>> toMap(DivisionTypeEnum divisionTypeEnum, List<String> haveTagList,List<EnterpriseOrderOfferGuidePriceTable> tableInfoList) {
        if (Objects.isNull(divisionTypeEnum)) {
            throw new BusException("区域类型（地区维度）错误");
        }

        return tableInfoList.stream().collect(Collectors.groupingBy(table -> {
            StringBuilder key = new StringBuilder(table.getServiceId());
            if (divisionTypeEnum != DivisionTypeEnum.COUNTRY) {
                key.append("#").append(table.getProvince().trim());
                if (divisionTypeEnum != DivisionTypeEnum.PROVINCE) {
                    key.append("#").append(StringUtils.isNotBlank(table.getCity()) ? table.getCity().trim() : "");
                    if (divisionTypeEnum != DivisionTypeEnum.CITY) {
                        key.append("#").append(StringUtils.isNotBlank(table.getDistrict()) ? table.getDistrict().trim() : "");
                        if (divisionTypeEnum == DivisionTypeEnum.STREET) {
                            key.append("#").append(table.getStreet().trim());
                        }
                    }
                }
            }

            if(StringUtils.isNotBlank(table.getBizTag())){
                key.append("#").append(table.getBizTag());
                haveTagList.add(key.toString());
            }
            return key.toString();
        }));
    }

}
