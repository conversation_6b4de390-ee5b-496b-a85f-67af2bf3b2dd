package com.wanshifu.strategy.batch;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.mongodb.WriteResult;
import com.wanshifu.adapter.api.backend.AttributePathSnapshotApi;
import com.wanshifu.adapter.dto.attribute.path.AttributePathNoWithWhiteAccountIdReq;
import com.wanshifu.api.FileUploadUtils;
import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.bean.FileUploadResp;
import com.wanshifu.domain.dto.table.BaseTable;
import com.wanshifu.domain.request.BatchTaskGenerateReq;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.*;
import com.wanshifu.fee.center.domain.enums.*;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.HttpBuilder;
import com.wanshifu.framework.utils.SnowFlakeGenerator;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.repository.BatchTaskInfoRepository;
import com.wanshifu.repository.FeeRuleDraftRepository;
import com.wanshifu.repository.FeeRuleRepository;
import com.wanshifu.repository.SceneInfoRepository;
import com.wanshifu.service.FeeRuleService;
import com.wanshifu.service.FormulaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 兜底策略
 */
@Component
@Slf4j
@Order(BaseBatchTableHandler.BASE_ORDER)
public class BaseBatchTableHandler implements BatchTableHandler {

    public static final int BASE_ORDER = 10001;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private BatchTaskInfoRepository batchTaskInfoRepository;
    @Resource
    private SceneInfoRepository sceneInfoRepository;
    @Resource
    private FeeRuleDraftRepository feeRuleDraftRepository;
    @Resource
    private FeeRuleRepository feeRuleRepository;

    @Resource
    private FormulaService formulaService;

    @Resource
    private FeeRuleService feeRuleService;

    @Resource
    private AddressApi addressApi;

    @Resource
    private AttributePathSnapshotApi attributePathSnapshotApi;

    /**
     * 管控开始时间
     */
    @Value("${controlStartTime:7}")
    private int controlStartTime;

    /**
     * 管控结束时间
     */
    @Value("${controlEndTime:21}")
    private int controlEndTime;

    /**
     * 管控期间导入文件最大行数
     */
    @Value("${controlFileMaxRowSize:20500}")
    private int controlFileMaxRowSize;

    // 创建一个 DivisionTypeEnum 到错误消息的映射
    public static final Map<DivisionTypeEnum, String> errorMessageMap = new HashMap<>();

    static {
        errorMessageMap.put(DivisionTypeEnum.PROVINCE, "省份为空");
        errorMessageMap.put(DivisionTypeEnum.CITY, "省份或者城市为空");
        errorMessageMap.put(DivisionTypeEnum.DISTRICT, "省份、城市或者区县为空");
        errorMessageMap.put(DivisionTypeEnum.STREET, "省份、城市、区县或者街道为空");
    }


    @Override
    public boolean isSupport(SceneInfo sceneInfo) {
        return true;
    }

    @Override
    public byte[] generateTemplateTable(SceneInfo sceneInfo, BatchTaskGenerateReq req) {
        String sceneCode = req.getSceneCode();
        Boolean showWhitelistAttributesAndStatus = req.getShowWhitelistAttributesAndStatus();
        showWhitelistAttributesAndStatus = showWhitelistAttributesAndStatus != null && showWhitelistAttributesAndStatus;
        Set<String> serviceIds = req.getServiceIds();
        // 模板库里面查询模板数据
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
        criteria.and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);
        criteria.and(FeeTemplate.Fields.sceneCode).is(sceneCode);
        // 费用类型不为空
        criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + "feeTypeTagName").ne(null);
        criteria.and("group").ne(null);
        criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + "serviceId").in(serviceIds);
        Query query = Query.query(criteria);
        List<FeeTemplate> feeTemplates = mongoTemplate.find(query, FeeTemplate.class);
        if (CollectionUtils.isEmpty(feeTemplates)) {
            throw new BusException("无模板信息");
        }
        if (feeTemplates.size() > 30000) {
            throw new BusException("模板数量超过30000，请缩小导出范围");
        }
        // 生成表格
        List<BaseTable> tableInfoList = new ArrayList<>(feeTemplates.size());
        // 保证同一个服务在一组
        Map<String, List<FeeTemplate>> collect = feeTemplates.stream().collect(Collectors.groupingBy(FeeTemplate::getGroup));

        for (Map.Entry<String, List<FeeTemplate>> stringListEntry : collect.entrySet()) {
            List<FeeTemplate> feeTemplateList = stringListEntry.getValue();
            String serviceId = stringListEntry.getKey();
            List<FeeTemplate> templates = feeTemplateList.stream().sorted(Comparator.comparing(FeeTemplate::getSort)).collect(Collectors.toList());

            Map<String, Set<Long>> skuNoWithAccountIdMap = null;
            if (showWhitelistAttributesAndStatus) {
                Set<String> skuNos = feeTemplateList.stream().map(f -> f.getBizRule().get(CommonBizRule.Fields.skuNo)).collect(Collectors.toSet());
                AttributePathNoWithWhiteAccountIdReq accountIdReq = new AttributePathNoWithWhiteAccountIdReq();
                accountIdReq.setServiceId(Long.valueOf(serviceId));
                accountIdReq.setAttributePathNos(skuNos);
                skuNoWithAccountIdMap = attributePathSnapshotApi.getAttributePathNoWithWhiteAccountId(accountIdReq);
            }
            // 排序
            for (FeeTemplate feeTemplate : templates) {
                BaseTable baseTable = new BaseTable();
                baseTable.setServiceId(feeTemplate.getBizRule().get("serviceId"));
                baseTable.setServiceName(feeTemplate.getBizRule().get("serviceName"));
                String skuNo = feeTemplate.getBizRule().get(CommonBizRule.Fields.skuNo);
                baseTable.setSkuNo(skuNo);
                baseTable.setTemplateId(feeTemplate.getTemplateId().toString());
                baseTable.setAttributeDisplayName(feeTemplate.getBizRule().get("attributeDisplayName"));
                if (showWhitelistAttributesAndStatus && skuNoWithAccountIdMap != null) {
                    Set<Long> skuNoSet = skuNoWithAccountIdMap.get(skuNo);
                    if (CollectionUtils.isNotEmpty(skuNoSet)) {
                        baseTable.setWhiteListAttributesAccountIds(skuNoSet.stream().sorted().map(String::valueOf).collect(Collectors.joining("、")));
                    }
                }
                baseTable.setCustomSkuUserId(feeTemplate.getBizRule().get(CommonBizRule.Fields.customSkuUserId));
                tableInfoList.add(baseTable);
            }
        }

        // 1. 构造说明文字
        String instruction = "填写说明：\n"
                + "1、业务ID，如果是商家价格，填写商家ID；如果是师傅价格，填写师傅ID\n"
                + "2、如果价格全国统一，省市区街道不填；如果是城市定价请填写省市；不同省市请自行复制添加一行，区县/街道价格以此类推\n"
                + "3、单价必须填写，即该服务sku的基础价格；单价max仅应用于期间价，特殊场合下填写\n"
                + "4、起步价选填，建议30元起，即一个订单的最低价格\n"
                + "5、白名单属性仅针对定制属性的商家，先检查所属配置的价格是不是白名单商家，如果是，则用白名单属性的sku配置价格"
                + "\n";

        // 2. 排除列列表（保持你原来的逻辑）
        List<String> excludeColumnFieldNames = new ArrayList<>();
        excludeColumnFieldNames.add(BaseTable.Fields.error);
        if (StringUtils.isBlank(sceneInfo.getBizIdType())
                || BizIdTypeEnum.EMPTY.code.equals(sceneInfo.getBizIdType())) {
            excludeColumnFieldNames.add(BaseTable.Fields.bizId);
        }

        // 3. 用反射 + 注解过滤真实要写出的列数
        Field[] allFields = BaseTable.class.getDeclaredFields();
        int columnCount = (int) Arrays.stream(allFields)
                // 必须有 @ExcelProperty
                .filter(f -> f.isAnnotationPresent(ExcelProperty.class))
                // 且不在排除列表里
                .filter(f -> !excludeColumnFieldNames.contains(f.getName()))
                .count();

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

        try (ExcelWriter writer = EasyExcel.write(byteArrayOutputStream, BaseTable.class)
                .excludeColumnFieldNames(excludeColumnFieldNames)
                .registerWriteHandler(new InstructionWriteHandler(instruction, columnCount))
                .build()) {

            // sheet 名称用场景名称
            WriteSheet sheet = EasyExcel.writerSheet(sceneInfo.getSceneName())
                    .relativeHeadRowIndex(1)
                    .build();
            writer.write(tableInfoList, sheet);
            writer.finish();
        }

        return byteArrayOutputStream.toByteArray();
    }

    @Override
    public void handleBatchTask(SceneInfo sceneInfo, BatchTaskInfo batchTaskInfo) {
        log.info("开始处理任务：sceneInfo={}，batchTaskInfo={}", JSON.toJSONString(batchTaskInfo), JSON.toJSONString(batchTaskInfo));
        try {
            log.info("开始处理任务：task={}", batchTaskInfo);
            // 下载文件
            String fileUrl = batchTaskInfo.getFileUrl();
            InputStream content = HttpBuilder.genericGet(fileUrl).build().execute().getEntity().getContent();
            log.info("文件下载完成：taskId={},fileUrl={}", batchTaskInfo.getBatchTaskId(), fileUrl);
            List<BaseTable> tableInfoList = EasyExcel.read(content)
                    .sheet(sceneInfo.getSceneName())
                    .headRowNumber(2)
                    .head(BaseTable.class)
                    .doReadSync();
            log.info("文件解析完成：taskId={}", batchTaskInfo.getBatchTaskId());

            if (CollectionUtils.isEmpty(tableInfoList)) {
                throw new BusException("文件为空");
            }

            // 对于上传数据来说，其批次唯一性由bizId + 服务ID + 地区决定
            batchTaskInfo.setCount(tableInfoList.size());
            int hour = LocalTime.now().getHour();
            if (hour >= controlStartTime && hour < controlEndTime && tableInfoList.size() > controlFileMaxRowSize) {
                log.error("导入时间在{}-{}点之间(管控时间段)，且条数大于{}条，超过阈值，导入失败！", controlStartTime, controlEndTime, controlFileMaxRowSize);
                throw new BusException(String.format("导入时间在%s-%s点之间，且条数大于%s条，超过阈值，导入失败！", controlStartTime, controlEndTime, controlFileMaxRowSize));
            } else if (tableInfoList.size() > controlFileMaxRowSize + 10000) {
                log.error("导入条数过大，大于{}. batchTaskId={}", batchTaskInfo.getBatchTaskId(), controlFileMaxRowSize + 10000);
                throw new BusException(batchTaskInfo.getBatchTaskId() + "条数大于" + controlFileMaxRowSize + 10000);
            }


            // 异常数据
            List<BaseTable> errorInfo = new ArrayList<>();

            String divisionType = batchTaskInfo.getDivisionType();
            DivisionTypeEnum divisionTypeEnum = DivisionTypeEnum.fromCode(divisionType);

            tableInfoList = validateBizId(sceneInfo, tableInfoList, batchTaskInfo, errorInfo);

            tableInfoList = validateDivisionType(tableInfoList, errorInfo, divisionType, divisionTypeEnum);

            Assert.notNull(divisionTypeEnum, "divisionTypeEnum is null");

            tableInfoList = validateDivision(tableInfoList, errorInfo, divisionTypeEnum);

            tableInfoList = validatePrice(tableInfoList, errorInfo);

            tableInfoList = validateTemplateIdAndServiceId(tableInfoList, errorInfo);

            List<String> haveTagList = new ArrayList<>();
            // key = serviceId + 地区 +bizId
            Map<String, List<BaseTable>> serviceMap = toMap(sceneInfo, divisionTypeEnum,haveTagList, tableInfoList);

            Set<String> serviceIds = tableInfoList.stream().map(BaseTable::getServiceId).collect(Collectors.toSet());
            // 模板库里面查询模板数据
            Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false);
            criteria.and(BaseDocument.Fields.status).is(TemplateStatusEnum.ACTIVE.code);
            criteria.and(FeeTemplate.Fields.sceneCode).is(sceneInfo.getSceneCode());
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + "serviceId").in(serviceIds);
            criteria.and(FeeTemplate.Fields.bizRule + PunConstant.DOT + "feeTypeTagName").ne(null);
            criteria.and(FeeTemplate.Fields.group).ne(null);
            Query query = Query.query(criteria);
            List<FeeTemplate> feeTemplates = mongoTemplate.find(query, FeeTemplate.class);
            Map<String, List<FeeTemplate>> templateMap = feeTemplates.stream().collect(Collectors.groupingBy(FeeTemplate::getGroup));


            Map<String, Address> addressesCache = new HashMap<>();

            // 数据完整性校验
            for (Map.Entry<String, List<BaseTable>> entry : serviceMap.entrySet()) {
                String key = entry.getKey();
                String[] split = key.split("#");
                String serviceId = split[0];
                String bizId = null;
                String bizTag = null;
                boolean haveTag = haveTagList.contains(key);
                if(haveTag){
                    bizTag = split[split.length - 1];
                }

                if (StringUtils.isNotBlank(sceneInfo.getBizIdType()) && !BizIdTypeEnum.EMPTY.code.equals(sceneInfo.getBizIdType())) {
                    if(haveTag){
                        bizId = split[split.length - 2];
                    }else {
                        bizId = split[split.length - 1];
                    }
                }


                List<FeeTemplate> thisFeeTemplates = templateMap.get(serviceId);
                List<BaseTable> value = entry.getValue();
                if (CollectionUtils.isEmpty(thisFeeTemplates)) {
                    for (BaseTable BaseTable : value) {
                        BaseTable.setError(serviceId + "该服务模板不存在");
                    }
                    errorInfo.addAll(value);
                    continue;
                }

                // 重复templateId
                Map<String, List<BaseTable>> repeatTemplate = value.stream().collect(Collectors.groupingBy(BaseTable::getTemplateId));
                Map.Entry<String, List<BaseTable>> reEntry = repeatTemplate.entrySet().stream().filter(repeatEntry -> repeatEntry.getValue().size() > 1).findAny().orElse(null);
                if (Objects.nonNull(reEntry)) {
                    for (BaseTable BaseTable : value) {
                        StringBuilder builder = new StringBuilder();
                        if (Objects.nonNull(batchTaskInfo.getBizId())) {
                            builder.append(batchTaskInfo.getBizId()).append("+");
                        }
                        builder.append(key).append("+").append(reEntry.getKey()).append("重复");
                        BaseTable.setError(builder.toString());
                    }
                    errorInfo.addAll(reEntry.getValue());
                    continue;
                }


                Set<Long> templateIds = value.stream().map(BaseTable::getTemplateId).map(Long::valueOf).collect(Collectors.toSet());
                Set<Long> realTemplateIds = thisFeeTemplates.stream().map(FeeTemplate::getTemplateId).collect(Collectors.toSet());

                // 取差集，找出在库中没有的数据与缺失的数据
                Collection<?> subtract = CollectionUtils.subtract(templateIds, realTemplateIds);
                if (CollectionUtils.isNotEmpty(subtract)) {
                    for (BaseTable BaseTable : value) {
                        BaseTable.setError("定价ID " + subtract.stream().filter(Objects::nonNull).map(Object::toString).collect(Collectors.joining(",")) + "在库中不存在");
                    }
                    errorInfo.addAll(value);
                    continue;
                }
//                else {
//                    subtract = CollectionUtils.subtract(realTemplateIds, templateIds);
//                    if (CollectionUtils.isNotEmpty(subtract)) {
//                        for (BaseTable BaseTable : value) {
//                            BaseTable.setError("缺失定价ID" + subtract.stream().filter(Objects::nonNull).map(Object::toString).collect(Collectors.joining(",")) + "的数据");
//                        }
//                        errorInfo.addAll(value);
//                        continue;
//                    }
//                }

//                Collection<?> intersection = CollectionUtils.intersection(templateIds, realTemplateIds);

                // 对不上
//                if (intersection.size() != realTemplateIds.size()) {
//                    for (BaseTable BaseTable : value) {
//                        BaseTable.setError(serviceId + "数据不完整");
//                    }
//                    errorInfo.addAll(value);
//                    continue;
//                }

                List<Address> addresses = new ArrayList<>();
                // 校验地区
                // 非国家级别需要校验地址
                if (DivisionTypeEnum.COUNTRY != divisionTypeEnum) {
                    // 如果是街道需要分割，如果是其他直接用,拼接
                    if (DivisionTypeEnum.STREET == divisionTypeEnum) {
                        // 默认事倒数第一个
                        String street = null;
                        boolean haveBizId = StringUtils.isNotBlank(sceneInfo.getBizIdType()) && !BizIdTypeEnum.EMPTY.code.equals(sceneInfo.getBizIdType());
                        // 同时有tag和id是倒数第三个
                        if (haveBizId && haveTag) {
                            // 不然就是倒数第二个
                            street = split[split.length - 3];
                        }else if (haveBizId || haveTag) {
                            // 只有其中一个则是倒数第二个
                            street = split[split.length - 2];
                        }else {
                            street  = split[split.length - 1];
                        }

                        String[] streets = street.split("[,，]");
                        String shouldFindAddress = Arrays.stream(split).skip(1).limit(3).collect(Collectors.joining(","));
                        for (String s : streets) {
                            String streetStr = shouldFindAddress + "," + s;
                            validateAddress(errorInfo, addressesCache, value, addresses, streetStr);
                        }
                    } else {
                        int limit = split.length - 1;
                        if (StringUtils.isNotBlank(sceneInfo.getBizIdType()) && !BizIdTypeEnum.EMPTY.code.equals(sceneInfo.getBizIdType())) {
                            limit--;
                        }
                        if(haveTag){
                            limit--;
                        }
                        String shouldFindAddress = Arrays.stream(split).skip(1).limit(limit).collect(Collectors.joining(","));
                        validateAddress(errorInfo, addressesCache, value, addresses, shouldFindAddress);
                    }
                }


                // 直接入库还是保存到草稿
                if (batchTaskInfo.isDirectFeeRule()) {
                    // 查询库中草稿数据，如果有则删除
                    log.info("批量数据直接入库 task = {}", batchTaskInfo.getBatchTaskId());
                    saveFeeRule(sceneInfo, batchTaskInfo, divisionTypeEnum, haveTag,serviceId, bizId, bizTag, thisFeeTemplates, value, addresses);
                } else {
                    log.info("批量数据入草稿 task = {}", batchTaskInfo.getBatchTaskId());
                    saveDraft(sceneInfo, batchTaskInfo, divisionTypeEnum,haveTag, serviceId, bizId, bizTag, thisFeeTemplates, value, addresses);
                }

            }

            // 回写任务状态
            batchTaskInfo.setErrorCount(0);
            batchTaskInfo.setStatus(BatchTaskStatusEnum.SUCCESS.code);
            // 有错误
            if (CollectionUtils.isNotEmpty(errorInfo)) {
                batchTaskInfo.setErrorCount(errorInfo.size());
                batchTaskInfo.setStatus(BatchTaskStatusEnum.FAIL.code);
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                EasyExcel.write(byteArrayOutputStream, BaseTable.class).sheet(sceneInfo.getSceneName()).doWrite(errorInfo);
                FileUploadResp upload = FileUploadUtils.upload(byteArrayOutputStream.toByteArray(), batchTaskInfo.getFileName().substring(batchTaskInfo.getFileName().lastIndexOf(PunConstant.DOT)));
                if (!upload.isSuccess()) {
                    log.error("上传失败 upload={}", upload);
                } else {
                    batchTaskInfo.setErrorFileUrl(upload.getData().getFileUrl());
                }
            }
        } catch (Exception e) {
            log.error("处理任务异常", e);
            if (batchTaskInfo.getStatus() == BatchTaskStatusEnum.IMPORT.code) {
                log.error("未到终态发生错误：task={}", batchTaskInfo);
                batchTaskInfo.setStatus(BatchTaskStatusEnum.FAIL.code);
                batchTaskInfo.setErrorFileUrl(batchTaskInfo.getFileUrl());
            }
        } finally {
            batchTaskInfoRepository.save(batchTaskInfo);
        }

    }

    private void saveDraft(SceneInfo sceneInfo, BatchTaskInfo batchTaskInfo, DivisionTypeEnum divisionTypeEnum,boolean haveTag, String serviceId, String bizId,String bizTag, List<FeeTemplate> thisFeeTemplates, List<BaseTable> value, List<Address> addresses) {
        // 开始删除旧草稿
        Update update = Update.update(BaseDocument.Fields.del, true);
        String divisionType = divisionTypeEnum.code;
        SceneInfo.PriceUploadAlert alert = sceneInfo.getPriceUploadAlert();
        String comparisonSceneCode = null;
        String comparisonSceneName = null;
        if (alert != null) {
            comparisonSceneCode = alert.getComparisonSceneCode();
            if (StringUtils.isNotBlank(comparisonSceneCode)) {
                SceneInfo scene = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(comparisonSceneCode);
                if (scene != null) {
                    comparisonSceneName = scene.getSceneName();
                }
            }
        }

        // FIXME 此处删除与插入需要做成事务, 但是公司spring现版本不支持
        if (DivisionTypeEnum.COUNTRY == divisionTypeEnum) {
            // 模板库里面查询模板数据
            Criteria findDraftCriteria = Criteria.where(BaseDocument.Fields.del).is(false);
            findDraftCriteria.and(FeeRuleDraft.Fields.sceneCode).is(sceneInfo.getSceneCode());
            // 国家级别 bizId + serviceId
            if (StringUtils.isNotBlank(sceneInfo.getBizIdType()) && !BizIdTypeEnum.EMPTY.code.equals(sceneInfo.getBizIdType())) {
                findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + sceneInfo.getBizIdType()).is(bizId);
            }
            if (haveTag){
                findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "bizTag").is(bizTag);
            }
            findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "divisionType").is(divisionType);
            findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "serviceId").is(serviceId);
            Query findDraftQuery = Query.query(findDraftCriteria);
            String criteriaStr = JSON.toJSONString(findDraftCriteria);
            log.info("开始删除旧草稿：query={}", criteriaStr);
            WriteResult writeResult = mongoTemplate.updateMulti(findDraftQuery, update, FeeRuleDraft.class);
            log.info("旧草稿删除完成：query={} , result={}", criteriaStr, writeResult);
            // 开始构建插入草稿数据
            List<FeeRuleDraft> feeRuleDrafts = new ArrayList<>();
            for (FeeTemplate thisFeeTemplate : thisFeeTemplates) {
                // 找到用户填写的数据
                Long templateId = thisFeeTemplate.getTemplateId();
                BaseTable thisBaseTable = value.stream().filter(BaseTable -> BaseTable.getTemplateId().equals(templateId.toString())).findFirst().orElse(null);
                if (Objects.isNull(thisBaseTable)) {
                    continue;
                }
                Assert.notNull(thisBaseTable, "thisBaseTable is null");
                // 存储参数

                Date date = new Date();
                FeeRuleDraft feeRuleDraft = new FeeRuleDraft();
                feeRuleDraft.setFeeRuleDraftId(SnowFlakeGenerator.INSTANCE.generate());
                feeRuleDraft.setBizRule(thisFeeTemplate.getBizRule());
                feeRuleDraft.setGroup(thisFeeTemplate.getGroup());
                feeRuleDraft.setTemplateId(thisFeeTemplate.getTemplateId());
                CalculateRuleData calculateRuleData = thisFeeTemplate.getCalculateRuleData();
//                feeRuleDraft.setCalculateRuleData(calculateRuleData);
                feeRuleDraft.setTemplateVersion(thisFeeTemplate.getTemplateVersion());
                feeRuleDraft.setSceneName(sceneInfo.getSceneName());
                feeRuleDraft.setSceneCode(sceneInfo.getSceneCode());
                feeRuleDraft.setCreateTime(date);
                feeRuleDraft.setModifyTime(date);
                feeRuleDraft.setDel(false);
                feeRuleDraft.setStatus(DraftStatusEnum.AUDIT.code);
                feeRuleDraft.setFeeName(thisFeeTemplate.getBizRule().get("feeName"));

                if (StringUtils.isNotBlank(sceneInfo.getBizIdType()) && !BizIdTypeEnum.EMPTY.code.equals(sceneInfo.getBizIdType())) {
                    feeRuleDraft.getBizRule().put(sceneInfo.getBizIdType(), bizId);
                    feeRuleDraft.getBizRule().put("bizId", batchTaskInfo.getBizId());
                    feeRuleDraft.getBizRule().put("bizType", sceneInfo.getBizIdType());
                }

                if (haveTag){
                    feeRuleDraft.getBizRule().put("bizTag",bizTag);
                }

                feeRuleDraft.getBizRule().put("basePrice", thisBaseTable.getBasePrice());
                feeRuleDraft.getBizRule().put("divisionType", divisionType);
                String masterInputPrice = thisBaseTable.getMasterInputPrice();
                String masterInputPriceMax = thisBaseTable.getMasterInputPriceMax();
                feeRuleDraft.getBizRule().put("masterInputPrice", masterInputPrice);
                feeRuleDraft.getBizRule().put(CommonBizRule.Fields.masterInputPriceMax, masterInputPriceMax);
                feeRuleDraft.getBizRule().put("divisionTypeName", divisionTypeEnum.name);
                feeRuleDraft.getBizRule().put("batchTaskId", batchTaskInfo.getBatchTaskId().toString());

                feeRuleDraft.setMasterInputPriceDouble(StringUtils.isNotBlank(masterInputPrice) ? Double.parseDouble(masterInputPrice) : 0D);

                CalculateRuleData calculateRule = formulaService.handleFormulas(calculateRuleData, divisionType, null, bizId);
                feeRuleDraft.setCalculateRuleData(calculateRule);

                feeRuleService.setGoodsCategoryAndServiceType(feeRuleDraft.getBizRule(), serviceId);

                feeRuleService.handleDynamicPriceRule(feeRuleDraft);

                setPriceUploadAlert(sceneInfo, serviceId, feeRuleDraft, masterInputPrice, comparisonSceneCode, comparisonSceneName);

                feeRuleDrafts.add(feeRuleDraft);
            }

            // 草稿保存
            log.info("草稿保存：info={}", feeRuleDrafts);
            feeRuleDraftRepository.save(feeRuleDrafts);
            log.info("草稿保存完成：info={}", feeRuleDrafts);
        } else {
            // 非国家则需要逐地址删除与插入
            for (Address address : addresses) {
                // 我们库中是从省开始的，而地址库是从国家开始的
                // 模板库里面查询模板数据
                Criteria findDraftCriteria = Criteria.where(BaseDocument.Fields.del).is(false);
                findDraftCriteria.and(FeeRuleDraft.Fields.sceneCode).is(sceneInfo.getSceneCode());
                // 地区级别 bizId + serviceId + 地区id
                if (StringUtils.isNotBlank(sceneInfo.getBizIdType()) && !BizIdTypeEnum.EMPTY.code.equals(sceneInfo.getBizIdType())) {
                    findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + sceneInfo.getBizIdType()).is(bizId);
                }
                if (haveTag){
                    findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "bizTag").is(bizTag);
                }

                findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "divisionType").is(divisionType);
                findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "serviceId").is(serviceId);

                if (divisionTypeEnum == DivisionTypeEnum.PROVINCE) {
                    findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "level1DivisionId").is(address.getLv2DivisionId().toString());
                }
                if (divisionTypeEnum == DivisionTypeEnum.CITY) {
                    findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "level1DivisionId").is(address.getLv2DivisionId().toString());
                    Long lv3DivisionId = address.getLv3DivisionId();
                    findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "level2DivisionId").is(address.getLv3DivisionId().toString());
                }
                if (divisionTypeEnum == DivisionTypeEnum.DISTRICT) {
                    findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "level1DivisionId").is(address.getLv2DivisionId().toString());
                    if (address.getLv3DivisionId() != 0) {
                        findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "level2DivisionId").is(address.getLv3DivisionId().toString());
                    } else {
                        findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "level2DivisionId").is(address.getLv4DivisionId().toString());
                    }
                    findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "level3DivisionId").is(address.getLv4DivisionId().toString());
                }
                if (divisionTypeEnum == DivisionTypeEnum.STREET) {
                    findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "level1DivisionId").is(address.getLv2DivisionId().toString());
                    if (address.getLv3DivisionId() != 0) {
                        findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "level2DivisionId").is(address.getLv3DivisionId().toString());
                    } else {
                        findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "level2DivisionId").is(address.getLv4DivisionId().toString());
                    }

                    if (address.getLv4DivisionId() != 0) {
                        findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "level3DivisionId").is(address.getLv4DivisionId().toString());
                    } else {
                        findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "level3DivisionId").is(address.getLv5DivisionId().toString());
                    }

                    findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "level4DivisionId").is(address.getLv5DivisionId().toString());
                }
                Query findDraftQuery = Query.query(findDraftCriteria);
                String criteriaStr = JSON.toJSONString(findDraftCriteria);
                log.info("开始删除旧草稿：query={}", criteriaStr);
                WriteResult writeResult = mongoTemplate.updateMulti(findDraftQuery, update, FeeRuleDraft.class);
                log.info("旧草稿删除完成：query={} , result={}", criteriaStr, writeResult);
                // 开始构建插入草稿数据
                List<FeeRuleDraft> feeRuleDrafts = new ArrayList<>();
                for (FeeTemplate thisFeeTemplate : thisFeeTemplates) {
                    // 找到用户填写的数据
                    Long templateId = thisFeeTemplate.getTemplateId();
                    BaseTable thisBaseTable = value.stream().filter(BaseTable -> BaseTable.getTemplateId().equals(templateId.toString())).findFirst().orElse(null);
                    if (Objects.isNull(thisBaseTable)) {
                        continue;
                    }
//                    Assert.notNull(thisBaseTable, "enterprise_order_offer_guide_price table is null");
                    // 存储参数

                    Date date = new Date();
                    FeeRuleDraft feeRuleDraft = new FeeRuleDraft();
                    feeRuleDraft.setFeeRuleDraftId(SnowFlakeGenerator.INSTANCE.generate());
                    feeRuleDraft.setBizRule(thisFeeTemplate.getBizRule());
                    feeRuleDraft.setGroup(thisFeeTemplate.getGroup());
                    feeRuleDraft.setTemplateId(thisFeeTemplate.getTemplateId());
                    CalculateRuleData calculateRuleData = thisFeeTemplate.getCalculateRuleData();
                    feeRuleDraft.setCalculateRuleData(calculateRuleData);
                    feeRuleDraft.setTemplateVersion(thisFeeTemplate.getTemplateVersion());
                    feeRuleDraft.setSceneName(sceneInfo.getSceneName());
                    feeRuleDraft.setSceneCode(sceneInfo.getSceneCode());
                    feeRuleDraft.setCreateTime(date);
                    feeRuleDraft.setModifyTime(date);
                    feeRuleDraft.setDel(false);
                    feeRuleDraft.setStatus(DraftStatusEnum.AUDIT.code);
                    feeRuleDraft.setFeeName(thisFeeTemplate.getBizRule().get("feeName"));
                    feeRuleDraft.getBizRule().put("basePrice", thisBaseTable.getBasePrice());
                    feeRuleDraft.getBizRule().put("divisionType", divisionType);
                    if (StringUtils.isNotBlank(sceneInfo.getBizIdType()) && !BizIdTypeEnum.EMPTY.code.equals(sceneInfo.getBizIdType())) {
                        feeRuleDraft.getBizRule().put(sceneInfo.getBizIdType(), bizId);
                        feeRuleDraft.getBizRule().put("bizId", bizId);
                        feeRuleDraft.getBizRule().put("bizType", sceneInfo.getBizIdType());
                    }

                    if (haveTag){
                        feeRuleDraft.getBizRule().put("bizTag",bizTag);
                    }

                    String masterInputPrice = thisBaseTable.getMasterInputPrice();
                    String masterInputPriceMax = thisBaseTable.getMasterInputPriceMax();

                    feeRuleDraft.getBizRule().put("masterInputPrice", masterInputPrice);
                    feeRuleDraft.getBizRule().put(CommonBizRule.Fields.masterInputPriceMax, masterInputPriceMax);

                    feeRuleDraft.setMasterInputPriceDouble(StringUtils.isNotBlank(masterInputPrice) ? Double.parseDouble(masterInputPrice) : 0D);

                    feeRuleDraft.getBizRule().put("divisionTypeName", divisionTypeEnum.name);
                    feeRuleDraft.getBizRule().put("batchTaskId", batchTaskInfo.getBatchTaskId().toString());

                    String divisionId = null;
                    if (divisionTypeEnum == DivisionTypeEnum.PROVINCE) {
                        feeRuleDraft.getBizRule().put("level1DivisionId", address.getLv2DivisionId().toString());
                        feeRuleDraft.getBizRule().put("province", address.getLv2DivisionName());
                        divisionId = address.getLv2DivisionId().toString();
                    }

                    if (divisionTypeEnum == DivisionTypeEnum.CITY) {
                        feeRuleDraft.getBizRule().put("level1DivisionId", address.getLv2DivisionId().toString());
                        feeRuleDraft.getBizRule().put("province", address.getLv2DivisionName());
                        Long lv3DivisionId = address.getLv3DivisionId();
                        if (lv3DivisionId.equals(0L)) {
                            feeRuleDraft.getBizRule().put("level2DivisionId", address.getLv4DivisionId().toString());
                            feeRuleDraft.getBizRule().put("city", address.getLv4DivisionName());
                            divisionId = address.getLv4DivisionId().toString();
                        } else {
                            feeRuleDraft.getBizRule().put("level2DivisionId", address.getLv3DivisionId().toString());
                            feeRuleDraft.getBizRule().put("city", address.getLv3DivisionName());
                            divisionId = address.getLv3DivisionId().toString();
                        }
                    }

                    if (divisionTypeEnum == DivisionTypeEnum.DISTRICT) {
                        feeRuleDraft.getBizRule().put("level1DivisionId", address.getLv2DivisionId().toString());
                        feeRuleDraft.getBizRule().put("province", address.getLv2DivisionName());

                        if (address.getLv3DivisionId() != 0) {
                            feeRuleDraft.getBizRule().put("level2DivisionId", address.getLv3DivisionId().toString());
                            feeRuleDraft.getBizRule().put("city", address.getLv3DivisionName());
                        } else {
                            feeRuleDraft.getBizRule().put("level2DivisionId", address.getLv4DivisionId().toString());
                            feeRuleDraft.getBizRule().put("city", address.getLv4DivisionName());
                        }

                        feeRuleDraft.getBizRule().put("level3DivisionId", address.getLv4DivisionId().toString());
                        feeRuleDraft.getBizRule().put("district", address.getLv4DivisionName());
                        divisionId = address.getLv4DivisionId().toString();
                    }

                    if (divisionTypeEnum == DivisionTypeEnum.STREET) {
                        feeRuleDraft.getBizRule().put("level1DivisionId", address.getLv2DivisionId().toString());
                        feeRuleDraft.getBizRule().put("province", address.getLv2DivisionName());
                        if (address.getLv3DivisionId() != 0) {
                            feeRuleDraft.getBizRule().put("level2DivisionId", address.getLv3DivisionId().toString());
                            feeRuleDraft.getBizRule().put("city", address.getLv3DivisionName());
                        } else {
                            feeRuleDraft.getBizRule().put("level2DivisionId", address.getLv4DivisionId().toString());
                            feeRuleDraft.getBizRule().put("city", address.getLv4DivisionName());
                        }
                        if (address.getLv4DivisionId() != 0) {
                            feeRuleDraft.getBizRule().put("level3DivisionId", address.getLv4DivisionId().toString());
                            feeRuleDraft.getBizRule().put("district", address.getLv4DivisionName());
                        } else {
                            feeRuleDraft.getBizRule().put("level3DivisionId", address.getLv5DivisionId().toString());
                            feeRuleDraft.getBizRule().put("district", address.getLv5DivisionName());
                        }

                        feeRuleDraft.getBizRule().put("level4DivisionId", address.getLv5DivisionId().toString());
                        feeRuleDraft.getBizRule().put("street", address.getLv5DivisionName());
                        divisionId = address.getLv5DivisionId().toString();
                    }
                    CalculateRuleData calculateRule = formulaService.handleFormulas(calculateRuleData, divisionType, divisionId, bizId);
                    feeRuleDraft.setCalculateRuleData(calculateRule);

                    feeRuleService.setGoodsCategoryAndServiceType(feeRuleDraft.getBizRule(), serviceId);

                    feeRuleService.handleDynamicPriceRule(feeRuleDraft);

                    setPriceUploadAlert(sceneInfo, serviceId, feeRuleDraft, masterInputPrice, comparisonSceneCode, comparisonSceneName);

                    feeRuleDrafts.add(feeRuleDraft);
                }
                // 草稿保存
                log.info("草稿保存：info={}", feeRuleDrafts);
                feeRuleDraftRepository.save(feeRuleDrafts);
                log.info("草稿保存完成：info={}", feeRuleDrafts);
            }
        }
    }


    private void saveFeeRule(SceneInfo sceneInfo, BatchTaskInfo batchTaskInfo, DivisionTypeEnum divisionTypeEnum, boolean haveTag,String serviceId, String bizId,String bizTag, List<FeeTemplate> thisFeeTemplates, List<BaseTable> value, List<Address> addresses) {
        // 开始删除旧草稿
        Update update = Update.update(BaseDocument.Fields.del, true);
        String divisionType = divisionTypeEnum.code;
        // FIXME 此处删除与插入需要做成事务, 但是公司spring现版本不支持
        if (DivisionTypeEnum.COUNTRY == divisionTypeEnum) {
            // 模板库里面查询模板数据
            Criteria findDraftCriteria = Criteria.where(BaseDocument.Fields.del).is(false);
            findDraftCriteria.and(FeeRule.Fields.sceneCode).is(sceneInfo.getSceneCode());
            // 国家级别 bizId + serviceId
            if (StringUtils.isNotBlank(sceneInfo.getBizIdType()) && !BizIdTypeEnum.EMPTY.code.equals(sceneInfo.getBizIdType())) {
                findDraftCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + sceneInfo.getBizIdType()).is(bizId);
            }
            if (haveTag) {
                findDraftCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "bizTag").is(bizTag);
            }
            findDraftCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "divisionType").is(divisionType);
            findDraftCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "serviceId").is(serviceId);
            Query findDraftQuery = Query.query(findDraftCriteria);
            String criteriaStr = JSON.toJSONString(findDraftCriteria);
            log.info("开始删除旧规则：query={}", criteriaStr);
            WriteResult writeResult = mongoTemplate.updateMulti(findDraftQuery, update, FeeRule.class);
            log.info("旧规则删除完成：query={} , result={}", criteriaStr, writeResult);
            // 开始构建插入草稿数据
            List<FeeRule> feeRules = new ArrayList<>();
            for (FeeTemplate thisFeeTemplate : thisFeeTemplates) {
                // 找到用户填写的数据
                Long templateId = thisFeeTemplate.getTemplateId();
                BaseTable thisBaseTable = value.stream().filter(BaseTable -> BaseTable.getTemplateId().equals(templateId.toString())).findFirst().orElse(null);
                if (Objects.isNull(thisBaseTable)) {
                    continue;
                }
                Assert.notNull(thisBaseTable, "thisBaseTable is null");
                // 存储参数

                Date date = new Date();
                FeeRule feeRule = new FeeRule();
                feeRule.setFeeRuleId(SnowFlakeGenerator.INSTANCE.generate());
                feeRule.setBizRule(thisFeeTemplate.getBizRule());
                feeRule.setGroup(thisFeeTemplate.getGroup());
                feeRule.setTemplateId(thisFeeTemplate.getTemplateId());
                CalculateRuleData calculateRuleData = thisFeeTemplate.getCalculateRuleData();
//                feeRule.setCalculateRuleData(calculateRuleData);
                // 跟产品（远康）确认，这里暂不需要计算
//                formulaService.handleFormulas(calculateRuleData);
                feeRule.setTemplateVersion(thisFeeTemplate.getTemplateVersion());
                feeRule.setSceneName(sceneInfo.getSceneName());
                feeRule.setSceneCode(sceneInfo.getSceneCode());
                feeRule.setCreateTime(date);
                feeRule.setModifyTime(date);
                feeRule.setDel(false);
                // 直接入正式表的初始状态为【审核通过】
                feeRule.setStatus(DraftStatusEnum.PASS.code);
                feeRule.setFeeName(thisFeeTemplate.getBizRule().get("feeName"));

                if (StringUtils.isNotBlank(sceneInfo.getBizIdType()) && !BizIdTypeEnum.EMPTY.code.equals(sceneInfo.getBizIdType())) {
                    feeRule.getBizRule().put(sceneInfo.getBizIdType(), bizId);
                    feeRule.getBizRule().put("bizId", batchTaskInfo.getBizId());
                    feeRule.getBizRule().put("bizType", sceneInfo.getBizIdType());
                }

                if (haveTag){
                    feeRule.getBizRule().put("bizTag",bizTag);
                }

                feeRule.getBizRule().put(CommonBizRule.Fields.serviceId, serviceId);
                feeRule.getBizRule().put("basePrice", thisBaseTable.getBasePrice());
                feeRule.getBizRule().put("divisionType", divisionType);
                String masterInputPrice = thisBaseTable.getMasterInputPrice();
                String masterInputPriceMax = thisBaseTable.getMasterInputPriceMax();
                feeRule.getBizRule().put("masterInputPrice", masterInputPrice);
                feeRule.getBizRule().put(CommonBizRule.Fields.masterInputPriceMax, masterInputPriceMax);
                feeRule.getBizRule().put("divisionTypeName", divisionTypeEnum.name);
                feeRule.getBizRule().put("batchTaskId", batchTaskInfo.getBatchTaskId().toString());


                feeRule.setMasterInputPriceDouble(StringUtils.isNotBlank(masterInputPrice) ? Double.parseDouble(masterInputPrice) : 0D);

                CalculateRuleData calculateRule = formulaService.handleFormulas(calculateRuleData, divisionType, null, bizId);
                feeRule.setCalculateRuleData(calculateRule);

                feeRuleService.setGoodsCategoryAndServiceType(feeRule.getBizRule(), serviceId);

                // 处理 动态价规则
                feeRuleService.handleDynamicPriceRule(feeRule);

                feeRules.add(feeRule);
            }
            // 草稿保存
            log.info("规则保存：info={}", feeRules);
            feeRuleRepository.save(feeRules);
            log.info("规则保存完成：info={}", feeRules);
        } else {
            // 非国家则需要逐地址删除与插入
            for (Address address : addresses) {
                // 我们库中是从省开始的，而地址库是从国家开始的
                // 模板库里面查询模板数据
                Criteria findDraftCriteria = Criteria.where(BaseDocument.Fields.del).is(false);
                findDraftCriteria.and(FeeRule.Fields.sceneCode).is(sceneInfo.getSceneCode());
                // 地区级别 bizId + serviceId + 地区id
                if (StringUtils.isNotBlank(sceneInfo.getBizIdType()) && !BizIdTypeEnum.EMPTY.code.equals(sceneInfo.getBizIdType())) {
                    findDraftCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + sceneInfo.getBizIdType()).is(bizId);
                }

                if (haveTag){
                    findDraftCriteria.and(FeeRuleDraft.Fields.bizRule + PunConstant.DOT + "bizTag").is(bizTag);
                }

                findDraftCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "divisionType").is(divisionType);
                findDraftCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "serviceId").is(serviceId);

                if (divisionTypeEnum == DivisionTypeEnum.PROVINCE) {
                    findDraftCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "level1DivisionId").is(address.getLv2DivisionId().toString());
                }
                if (divisionTypeEnum == DivisionTypeEnum.CITY) {
                    findDraftCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "level1DivisionId").is(address.getLv2DivisionId().toString());
                    findDraftCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "level2DivisionId").is(address.getLv3DivisionId().toString());
                }
                if (divisionTypeEnum == DivisionTypeEnum.DISTRICT) {
                    findDraftCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "level1DivisionId").is(address.getLv2DivisionId().toString());
                    if (address.getLv3DivisionId() != 0) {
                        findDraftCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "level2DivisionId").is(address.getLv3DivisionId().toString());
                    } else {
                        findDraftCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "level2DivisionId").is(address.getLv4DivisionId().toString());
                    }
                    findDraftCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "level3DivisionId").is(address.getLv4DivisionId().toString());
                }
                if (divisionTypeEnum == DivisionTypeEnum.STREET) {
                    findDraftCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "level1DivisionId").is(address.getLv2DivisionId().toString());
                    if (address.getLv3DivisionId() != 0) {
                        findDraftCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "level2DivisionId").is(address.getLv3DivisionId().toString());
                    } else {
                        findDraftCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "level2DivisionId").is(address.getLv4DivisionId().toString());
                    }

                    if (address.getLv4DivisionId() != 0) {
                        findDraftCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "level3DivisionId").is(address.getLv4DivisionId().toString());
                    } else {
                        findDraftCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "level3DivisionId").is(address.getLv5DivisionId().toString());
                    }

                    findDraftCriteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + "level4DivisionId").is(address.getLv5DivisionId().toString());
                }
                Query findDraftQuery = Query.query(findDraftCriteria);
                String criteriaStr = JSON.toJSONString(findDraftCriteria);
                log.info("开始删除旧规则：query={}", criteriaStr);
                WriteResult writeResult = mongoTemplate.updateMulti(findDraftQuery, update, FeeRuleDraft.class);
                log.info("旧规则删除完成：query={} , result={}", criteriaStr, writeResult);
                // 开始构建插入草稿数据
                List<FeeRule> feeRules = new ArrayList<>();
                for (FeeTemplate thisFeeTemplate : thisFeeTemplates) {
                    // 找到用户填写的数据
                    Long templateId = thisFeeTemplate.getTemplateId();
                    BaseTable thisBaseTable = value.stream().filter(BaseTable -> BaseTable.getTemplateId().equals(templateId.toString())).findFirst().orElse(null);
                    if (Objects.isNull(thisBaseTable)) {
                        continue;
                    }
                    Assert.notNull(thisBaseTable, "enterprise_order_offer_guide_price table is null");
                    // 存储参数

                    Date date = new Date();
                    FeeRule feeRule = new FeeRule();
                    feeRule.setFeeRuleId(SnowFlakeGenerator.INSTANCE.generate());
                    feeRule.setBizRule(thisFeeTemplate.getBizRule());
                    feeRule.setGroup(thisFeeTemplate.getGroup());
                    feeRule.setTemplateId(thisFeeTemplate.getTemplateId());
                    CalculateRuleData calculateRuleData = thisFeeTemplate.getCalculateRuleData();
//                    feeRule.setCalculateRuleData(calculateRuleData);
                    // 跟产品（远康）确认，这里暂不需要计算
//                    formulaService.handleFormulas(calculateRuleData);
                    feeRule.setTemplateVersion(thisFeeTemplate.getTemplateVersion());
                    feeRule.setSceneName(sceneInfo.getSceneName());
                    feeRule.setSceneCode(sceneInfo.getSceneCode());
                    feeRule.setCreateTime(date);
                    feeRule.setModifyTime(date);
                    feeRule.setDel(false);
                    feeRule.setStatus(DraftStatusEnum.PASS.code);
                    feeRule.setFeeName(thisFeeTemplate.getBizRule().get("feeName"));
                    feeRule.getBizRule().put("basePrice", thisBaseTable.getBasePrice());
                    feeRule.getBizRule().put("divisionType", divisionType);
                    if (StringUtils.isNotBlank(sceneInfo.getBizIdType()) && !BizIdTypeEnum.EMPTY.code.equals(sceneInfo.getBizIdType())) {
                        feeRule.getBizRule().put(sceneInfo.getBizIdType(), bizId);
                        feeRule.getBizRule().put("bizId", bizId);
                        feeRule.getBizRule().put("bizType", sceneInfo.getBizIdType());
                    }

                    if (haveTag) {
                        feeRule.getBizRule().put("bizTag", bizTag);
                    }

                    String masterInputPrice = thisBaseTable.getMasterInputPrice();
                    String masterInputPriceMax = thisBaseTable.getMasterInputPriceMax();

                    feeRule.getBizRule().put("masterInputPrice", masterInputPrice);
                    feeRule.getBizRule().put(CommonBizRule.Fields.masterInputPriceMax, masterInputPriceMax);
                    feeRule.getBizRule().put("divisionTypeName", divisionTypeEnum.name);
                    feeRule.getBizRule().put("batchTaskId", batchTaskInfo.getBatchTaskId().toString());

                    feeRule.setMasterInputPriceDouble(StringUtils.isNotBlank(masterInputPrice) ? Double.parseDouble(masterInputPrice) : 0D);

                    String divisionId = null;
                    if (divisionTypeEnum == DivisionTypeEnum.PROVINCE) {
                        feeRule.getBizRule().put("level1DivisionId", address.getLv2DivisionId().toString());
                        feeRule.getBizRule().put("province", address.getLv2DivisionName());
                        divisionId = address.getLv2DivisionId().toString();
                    }

                    if (divisionTypeEnum == DivisionTypeEnum.CITY) {
                        feeRule.getBizRule().put("level1DivisionId", address.getLv2DivisionId().toString());
                        feeRule.getBizRule().put("province", address.getLv2DivisionName());
                        feeRule.getBizRule().put("level2DivisionId", address.getLv3DivisionId().toString());
                        feeRule.getBizRule().put("city", address.getLv3DivisionName());
                        divisionId = address.getLv3DivisionId().toString();
                    }

                    if (divisionTypeEnum == DivisionTypeEnum.DISTRICT) {
                        feeRule.getBizRule().put("level1DivisionId", address.getLv2DivisionId().toString());
                        feeRule.getBizRule().put("province", address.getLv2DivisionName());

                        if (address.getLv3DivisionId() != 0) {
                            feeRule.getBizRule().put("level2DivisionId", address.getLv3DivisionId().toString());
                            feeRule.getBizRule().put("city", address.getLv3DivisionName());
                        } else {
                            feeRule.getBizRule().put("level2DivisionId", address.getLv4DivisionId().toString());
                            feeRule.getBizRule().put("city", address.getLv4DivisionName());
                        }

                        feeRule.getBizRule().put("level3DivisionId", address.getLv4DivisionId().toString());
                        feeRule.getBizRule().put("district", address.getLv4DivisionName());
                        divisionId = address.getLv4DivisionId().toString();
                    }

                    if (divisionTypeEnum == DivisionTypeEnum.STREET) {
                        feeRule.getBizRule().put("level1DivisionId", address.getLv2DivisionId().toString());
                        feeRule.getBizRule().put("province", address.getLv2DivisionName());
                        if (address.getLv3DivisionId() != 0) {
                            feeRule.getBizRule().put("level2DivisionId", address.getLv3DivisionId().toString());
                            feeRule.getBizRule().put("city", address.getLv3DivisionName());
                        } else {
                            feeRule.getBizRule().put("level2DivisionId", address.getLv4DivisionId().toString());
                            feeRule.getBizRule().put("city", address.getLv4DivisionName());
                        }
                        if (address.getLv4DivisionId() != 0) {
                            feeRule.getBizRule().put("level3DivisionId", address.getLv4DivisionId().toString());
                            feeRule.getBizRule().put("district", address.getLv4DivisionName());
                        } else {
                            feeRule.getBizRule().put("level3DivisionId", address.getLv5DivisionId().toString());
                            feeRule.getBizRule().put("district", address.getLv5DivisionName());
                        }

                        feeRule.getBizRule().put("level4DivisionId", address.getLv5DivisionId().toString());
                        feeRule.getBizRule().put("street", address.getLv5DivisionName());
                        divisionId = address.getLv5DivisionId().toString();
                    }
                    CalculateRuleData calculateRule = formulaService.handleFormulas(calculateRuleData, divisionType, divisionId, bizId);
                    feeRule.setCalculateRuleData(calculateRule);

                    feeRuleService.setGoodsCategoryAndServiceType(feeRule.getBizRule(), serviceId);

                    // 处理 动态价规则
                    feeRuleService.handleDynamicPriceRule(feeRule);

                    feeRules.add(feeRule);
                }
                // 草稿保存
                log.info("规则保存：info={}", feeRules);
                feeRuleRepository.save(feeRules);
                log.info("规则保存完成：info={}", feeRules);
            }
        }
    }


    private boolean validateAddress(List<BaseTable> errorInfo, Map<String, Address> addressesCache, List<BaseTable> value, List<Address> addresses, String streetStr) {
        Address divisionByFullName = findAddress(addressesCache, streetStr);
        if (Objects.isNull(divisionByFullName)) {
            for (BaseTable BaseTable : value) {
                BaseTable.setError(streetStr + "地址错误，库中无数据");
            }
            errorInfo.addAll(value);
            return true;
        }
        addresses.add(divisionByFullName);
        return false;
    }

    private Address findAddress(Map<String, Address> addressesCache, String address) {
        Address divisionByFullName = addressesCache.get(address);
        if (Objects.isNull(divisionByFullName)) {
            divisionByFullName = addressApi.getDivisionByFullName(address);
            if (Objects.nonNull(divisionByFullName)) {
                addressesCache.put(address, divisionByFullName);
            }
        }
        return divisionByFullName;
    }

    private List<BaseTable> validatePrice(List<BaseTable> tableInfoList, List<BaseTable> errorInfo) {
        // 校验单价/起步价
        tableInfoList = tableInfoList.stream().peek(BaseTable -> {
            if (StringUtils.isBlank(BaseTable.getMasterInputPrice())) {
                BaseTable.setError("单价为空");
                errorInfo.add(BaseTable);
                return;
            }
            try {
                BigDecimal bigDecimal = new BigDecimal(BaseTable.getMasterInputPrice());
                // 数字范围
                if (bigDecimal.compareTo(new BigDecimal("0.01")) < 0 || bigDecimal.compareTo(BigDecimal.valueOf(9999)) > 0) {
                    BaseTable.setError("单价不在0.01-9999范围内");
                    errorInfo.add(BaseTable);
                    return;
                }
                String masterInputPriceMaxStr = BaseTable.getMasterInputPriceMax();
                if (StringUtils.isNotBlank(masterInputPriceMaxStr)) {
                    BigDecimal masterInputPriceMax = new BigDecimal(masterInputPriceMaxStr);
                    if (masterInputPriceMax.compareTo(new BigDecimal("0.01")) < 0 || masterInputPriceMax.compareTo(BigDecimal.valueOf(9999)) > 0) {
                        BaseTable.setError("单价max不能小于单价");
                        errorInfo.add(BaseTable);
                        return;
                    }
                    if (masterInputPriceMax.compareTo(bigDecimal) <= 0) {
                        BaseTable.setError("单价max必须大于单价");
                        errorInfo.add(BaseTable);
                        return;
                    }
                }
            } catch (NumberFormatException e) {
                BaseTable.setError("单价不为数字");
                errorInfo.add(BaseTable);
                return;
            }
            if (StringUtils.isNotBlank(BaseTable.getBasePrice())) {
                try {
                    BigDecimal bigDecimal = new BigDecimal(BaseTable.getBasePrice());
                    // 数字范围
                    if (bigDecimal.compareTo(BigDecimal.ONE) < 0 || bigDecimal.compareTo(BigDecimal.valueOf(999)) > 0) {
                        BaseTable.setError("起步价不在1-999范围内");
                        errorInfo.add(BaseTable);
                    }
                } catch (NumberFormatException e) {
                    BaseTable.setError("起步价不为数字");
                    errorInfo.add(BaseTable);
                }
            }
        }).filter(BaseTable -> StringUtils.isBlank(BaseTable.getError())).collect(Collectors.toList());
        return tableInfoList;
    }

    private List<BaseTable> validateTemplateIdAndServiceId(List<BaseTable> tableInfoList, List<BaseTable> errorInfo) {
        // 校验单价/起步价
        tableInfoList = tableInfoList.stream().peek(BaseTable -> {
            if (StringUtils.isBlank(BaseTable.getTemplateId())) {
                BaseTable.setError("定价ID为空");
                errorInfo.add(BaseTable);
                return;
            }
            try {
                Long templateId = Long.valueOf(BaseTable.getTemplateId());
            } catch (NumberFormatException e) {
                BaseTable.setError("定价ID格式不正确");
                errorInfo.add(BaseTable);
                return;
            }
            if (StringUtils.isBlank(BaseTable.getServiceId())) {
                BaseTable.setError("服务ID为空");
                errorInfo.add(BaseTable);
                return;
            }
            try {
                Long serviceId = Long.valueOf(BaseTable.getServiceId());
            } catch (NumberFormatException e) {
                BaseTable.setError("服务ID格式不正确");
                errorInfo.add(BaseTable);
                return;
            }
        }).filter(BaseTable -> StringUtils.isBlank(BaseTable.getError())).collect(Collectors.toList());
        return tableInfoList;
    }

    private List<BaseTable> validateDivision(List<BaseTable> tableInfoList, List<BaseTable> errorInfo, DivisionTypeEnum divisionTypeEnum) {
        // 地区

        if (divisionTypeEnum != DivisionTypeEnum.COUNTRY) {
            tableInfoList = tableInfoList.stream().peek(BaseTable -> {
                String errorMessage = errorMessageMap.get(divisionTypeEnum);

                if (errorMessage != null) {
                    // 处理特殊情况，东莞没有区一级，海南（直辖市）没有市一级
                    if (StringUtils.isBlank(BaseTable.getProvince())) {
                        BaseTable.setError(errorMessage);
                        errorInfo.add(BaseTable);
                    }

                    if (divisionTypeEnum == DivisionTypeEnum.CITY && StringUtils.isBlank(BaseTable.getCity())) {
                        BaseTable.setError(errorMessage);
                        errorInfo.add(BaseTable);
                    }

                    if (divisionTypeEnum == DivisionTypeEnum.DISTRICT && StringUtils.isBlank(BaseTable.getDistrict())) {
                        // 市可为空
                        BaseTable.setError(errorMessage);
                        errorInfo.add(BaseTable);
                    }

                    if (divisionTypeEnum == DivisionTypeEnum.STREET && ((StringUtils.isBlank(BaseTable.getCity()) && StringUtils.isBlank(BaseTable.getDistrict())) || StringUtils.isBlank(BaseTable.getStreet()))) {
                        // 区或者市可为空，但是不能同时为空
                        BaseTable.setError(errorMessage);
                        errorInfo.add(BaseTable);
                    }


                }
            }).filter(BaseTable -> StringUtils.isBlank(BaseTable.getError())).collect(Collectors.toList());
        }

        return tableInfoList;
    }


    private List<BaseTable> validateDivisionType(List<BaseTable> tableInfoList, List<BaseTable> errorInfo, String divisionType, DivisionTypeEnum divisionTypeEnum) {
        // 全部错误
        if (Objects.isNull(divisionTypeEnum)) {
            tableInfoList = tableInfoList.stream().peek(BaseTable -> {
                BaseTable.setError(divisionType + "地区类型不存在");
                errorInfo.add(BaseTable);
            }).filter(BaseTable -> StringUtils.isBlank(BaseTable.getError())).collect(Collectors.toList());
        }
        return tableInfoList;
    }

    private List<BaseTable> validateBizId(SceneInfo sceneInfo, List<BaseTable> tableInfoList, BatchTaskInfo batchTaskInfo, List<BaseTable> errorInfo) {
        // bizId
        if (StringUtils.isNotBlank(sceneInfo.getBizIdType()) && !BizIdTypeEnum.EMPTY.code.equals(sceneInfo.getBizIdType())) {

            List<BaseTable> tableList = tableInfoList.stream().peek(baseTable -> {
                if (StringUtils.isBlank(baseTable.getBizId())) {
                    baseTable.setError(sceneInfo.getBizIdType() + "未填写");
                    errorInfo.add(baseTable);
                }
            }).filter(BaseTable -> StringUtils.isBlank(BaseTable.getError())).collect(Collectors.toList());
            long count = tableList.stream().map(BaseTable::getBizId).distinct().count();

            if (count > 100) {
                return tableList.stream().peek(baseTable -> {
                    baseTable.setError("业务ID数量不能大于100");
                    errorInfo.add(baseTable);
                }).filter(BaseTable -> StringUtils.isBlank(BaseTable.getError())).collect(Collectors.toList());
            }

            return tableList;
        }
        return tableInfoList;
    }

    private Map<String, List<BaseTable>> toMap(SceneInfo sceneInfo, DivisionTypeEnum divisionTypeEnum,List<String> haveTagList ,List<BaseTable> tableInfoList) {
        if (Objects.isNull(divisionTypeEnum)) {
            throw new BusException("区域类型（地区维度）错误");
        }

        return tableInfoList.stream().collect(Collectors.groupingBy(table -> {
            StringBuilder key = new StringBuilder(table.getServiceId());
            if (divisionTypeEnum != DivisionTypeEnum.COUNTRY) {
                key.append("#").append(table.getProvince().trim());
                if (divisionTypeEnum != DivisionTypeEnum.PROVINCE) {
                    key.append("#").append(StringUtils.isNotBlank(table.getCity()) ? table.getCity().trim() : "");
                    if (divisionTypeEnum != DivisionTypeEnum.CITY) {
                        key.append("#").append(StringUtils.isNotBlank(table.getDistrict()) ? table.getDistrict().trim() : "");
                        if (divisionTypeEnum == DivisionTypeEnum.STREET) {
                            key.append("#").append(table.getStreet().trim());
                        }
                    }
                }
            }
            // 添加bizId维度
            if (StringUtils.isNotBlank(sceneInfo.getBizIdType()) && !BizIdTypeEnum.EMPTY.code.equals(sceneInfo.getBizIdType())) {
                key.append("#").append(table.getBizId());
            }

            if(StringUtils.isNotBlank(table.getBizTag())){
                key.append("#").append(table.getBizTag());
                haveTagList.add(key.toString());
            }

            return key.toString();
        }));
    }

    private void setPriceUploadAlert(SceneInfo sceneInfo, String serviceId, FeeRuleDraft feeRuleDraft, String masterInputPrice, String comparisonSceneCode, String comparisonSceneName) {
        Boolean enabled = sceneInfo.getPriceUploadAlert().getEnabled();
        if (enabled == null || !enabled) {
            return;
        }
        if (StringUtils.isBlank(comparisonSceneCode)) {
            throw new BusException(StrUtil.format("价格上传告警场景编码为空，需要对比的场景为{}", sceneInfo.getSceneName()));
        }
        Map<String, String> bizRule = feeRuleDraft.getBizRule();
        String skuNo = bizRule.get(CommonBizRule.Fields.skuNo);
        String divisionType = bizRule.get(CommonBizRule.Fields.divisionType);
        Criteria criteria = Criteria.where(BaseDocument.Fields.del).is(false)
                .and(BaseDocument.Fields.status).is(RuleStatusEnum.ACTIVE.code)
                .and(FeeRule.Fields.sceneCode).is(comparisonSceneCode)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.serviceId).is(serviceId)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuNo).is(skuNo)
                .and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.skuType).is(FeeSkuTypeEnum.STANDARD_SKU.code);
        if (DivisionTypeEnum.COUNTRY.code.equals(divisionType)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(DivisionTypeEnum.COUNTRY.code);
        } else if (DivisionTypeEnum.PROVINCE.code.equals(divisionType)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(DivisionTypeEnum.PROVINCE.code);
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level1DivisionId).is(bizRule.get(CommonBizRule.Fields.level1DivisionId));
        } else if (DivisionTypeEnum.CITY.code.equals(divisionType)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(DivisionTypeEnum.CITY.code);
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level2DivisionId).is(bizRule.get(CommonBizRule.Fields.level2DivisionId));
        } else if (DivisionTypeEnum.DISTRICT.code.equals(divisionType)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(DivisionTypeEnum.DISTRICT.code);
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level3DivisionId).is(bizRule.get(CommonBizRule.Fields.level3DivisionId));
        } else if (DivisionTypeEnum.STREET.code.equals(divisionType)) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.divisionType).is(DivisionTypeEnum.STREET.code);
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + CommonBizRule.Fields.level4DivisionId).is(bizRule.get(CommonBizRule.Fields.level4DivisionId));
        } else {
            throw new BusException(StrUtil.format("价格上传告警不支持的divisionType={}", divisionType));
        }
        Query query = Query.query(criteria);
        query.limit(2); // 当存在多条时不比价，故最多取两条即可，避免做无用功
        List<FeeRule> feeRules = mongoTemplate.find(query, FeeRule.class);
        PriceUploadComparisonAlert alert = new PriceUploadComparisonAlert();
        alert.setComparisonSceneCode(comparisonSceneCode);
        alert.setComparisonSceneName(comparisonSceneName);
        if (CollectionUtils.isNotEmpty(feeRules)) {
            if (feeRules.size() > 1) {
                alert.setPriceComparisonStatus(PriceComparisonStatusEnum.UNRECOGNIZED.getCode());
                alert.setUnrecognizedReason(StrUtil.format("可对比价格存在多条，对比场景：{}，serviceId：{}，skuNo：{}", comparisonSceneCode, serviceId, skuNo));
            } else {
                alert.setPriceComparisonStatus(PriceComparisonStatusEnum.NORMAL.getCode());
                String comparisonPrice = feeRules.get(0).getBizRule().get(CommonBizRule.Fields.masterInputPrice);
                alert.setBenchmarkPrice(comparisonPrice);
                BigDecimal price = new BigDecimal(masterInputPrice);
                BigDecimal compPrice = new BigDecimal(comparisonPrice);
                Double differencePercentage = price.subtract(compPrice).divide(compPrice, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).doubleValue();
                alert.setDifferencePercentage(differencePercentage);
                Integer greaterThan = sceneInfo.getPriceUploadAlert().getGreaterThan();
                Integer lessThan = sceneInfo.getPriceUploadAlert().getLessThan();
                if (differencePercentage > 0) {
                    if (differencePercentage.compareTo(greaterThan.doubleValue()) >= 0) {
                        alert.setPriceComparisonStatus(PriceComparisonStatusEnum.ALERT.getCode());
                    }
                } else {
                    Double diffPercentage = Math.abs(differencePercentage);
                    if (diffPercentage.compareTo(lessThan.doubleValue()) >= 0) {
                        alert.setPriceComparisonStatus(PriceComparisonStatusEnum.ALERT.getCode());
                    }
                }
            }
        } else {
            alert.setPriceComparisonStatus(PriceComparisonStatusEnum.UNRECOGNIZED.getCode());
            alert.setUnrecognizedReason(StrUtil.format("无可对比的价格，对比场景：{}，serviceId：{}，skuNo：{}", comparisonSceneCode, serviceId, skuNo));
        }
        feeRuleDraft.setPriceUploadComparisonAlert(alert);
    }
}
