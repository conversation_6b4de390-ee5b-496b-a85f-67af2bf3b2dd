package com.wanshifu.strategy.export;

import com.wanshifu.fee.center.domain.document.ExportTaskInfo;
import com.wanshifu.fee.center.domain.document.SceneInfo;
import com.wanshifu.repository.SceneInfoRepository;

import javax.annotation.Resource;

public abstract class AbstractExportTableHandler implements ExportTableHandler {

    @Resource
    private SceneInfoRepository sceneInfoRepository;

    @Override
    public void export(ExportTaskInfo exportTaskInfo) {
        SceneInfo sceneInfo = sceneInfoRepository.findBySceneCodeIsAndDelIsFalse(exportTaskInfo.getSceneCode());
        if (support(sceneInfo)) {
            doExport(sceneInfo, exportTaskInfo);
        }
    }

    protected abstract boolean support(SceneInfo sceneInfo);

    protected abstract void doExport(SceneInfo sceneEnum, ExportTaskInfo exportTaskInfo);

}
