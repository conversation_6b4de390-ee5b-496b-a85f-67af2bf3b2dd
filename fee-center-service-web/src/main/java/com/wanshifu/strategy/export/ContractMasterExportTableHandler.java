package com.wanshifu.strategy.export;

import com.alibaba.excel.EasyExcel;
import com.wanshifu.api.FileUploadUtils;
import com.wanshifu.bean.FileUploadResp;
import com.wanshifu.domain.dto.table.ContractMasterExportTable;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.biz.ContractMasterBizRule;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.ExportTaskInfo;
import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.document.SceneInfo;
import com.wanshifu.fee.center.domain.enums.DivisionTypeEnum;
import com.wanshifu.fee.center.domain.enums.ExportTaskStatusEnum;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.repository.ExportTaskInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.*;

@Component
@Slf4j
@Order(ExportTableHandler.DEFAULT_ORDER)
public class ContractMasterExportTableHandler extends AbstractExportTableHandler {


    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private ExportTaskInfoRepository exportTaskInfoRepository;

    private static final Map<String, List<String>> SHOULD_IGNORE = new HashMap<>();

    static {
        SHOULD_IGNORE.put(DivisionTypeEnum.STREET.code, Collections.emptyList());
        SHOULD_IGNORE.put(DivisionTypeEnum.COUNTRY.code, Arrays.asList(ContractMasterExportTable.Fields.city, ContractMasterExportTable.Fields.district, ContractMasterExportTable.Fields.province, ContractMasterExportTable.Fields.street));
        SHOULD_IGNORE.put(DivisionTypeEnum.PROVINCE.code, Arrays.asList(ContractMasterExportTable.Fields.city, ContractMasterExportTable.Fields.district, ContractMasterExportTable.Fields.street));
        SHOULD_IGNORE.put(DivisionTypeEnum.DISTRICT.code, Collections.singletonList(ContractMasterExportTable.Fields.street));
        SHOULD_IGNORE.put(DivisionTypeEnum.CITY.code, Arrays.asList(ContractMasterExportTable.Fields.district, ContractMasterExportTable.Fields.street));
    }

    @Override
    protected boolean support(SceneInfo sceneInfo) {
        return "contract_master".equals(sceneInfo.getSceneCode());
    }

    @Override
    protected void doExport(SceneInfo sceneInfo, ExportTaskInfo exportTaskInfo) {
        // 根据条件查询数据
        Criteria criteria = Criteria.where(FeeRule.Fields.sceneCode).is(exportTaskInfo.getSceneCode());
        criteria.and(BaseDocument.Fields.del).is(false);
        // serviceId
        if (CollectionUtils.isNotEmpty(exportTaskInfo.getServiceIds())) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + ContractMasterBizRule.Fields.serviceId).in(exportTaskInfo.getServiceIds());
        }
        //  bizId
        if (CollectionUtils.isNotEmpty(exportTaskInfo.getBizIds())) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + sceneInfo.getBizIdType()).in(exportTaskInfo.getBizIds());
        }
        // 价格维度
        if (StringUtils.isNotBlank(exportTaskInfo.getDivisionType())) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + ContractMasterBizRule.Fields.divisionType).is(exportTaskInfo.getDivisionType());
        }

        Query query = Query.query(criteria);
        long count = mongoTemplate.count(query, FeeRule.class);
        // 校验
        if (count <= 0) {
            log.info("exportTaskId={},count={}", exportTaskInfo.getExportTaskId(), count);
            exportTaskInfo.setStatus(ExportTaskStatusEnum.FAIL.code);
            exportTaskInfo.setError("库中无数据");
            exportTaskInfoRepository.save(exportTaskInfo);
            return;
        }
        // 10w
        if (count > 10_0000) {
            log.info("exportTaskId={},count={}", exportTaskInfo.getExportTaskId(), count);
            exportTaskInfo.setStatus(ExportTaskStatusEnum.FAIL.code);
            exportTaskInfo.setError("数据超过10万");
            exportTaskInfoRepository.save(exportTaskInfo);
            return;
        }
        List<FeeRule> feeRuleList = mongoTemplate.find(query, FeeRule.class);

        log.info("exportTaskId={},正在转换数据,共{}条", exportTaskInfo.getExportTaskId(), feeRuleList.size());
        // 转换数据
        List<ContractMasterExportTable> contractMasterExportTables = new ArrayList<>();
        for (FeeRule feeRule : feeRuleList) {
            ContractMasterExportTable contractMasterExportTable = new ContractMasterExportTable();
            contractMasterExportTable.setBizId(feeRule.getBizRule().get(sceneInfo.getBizIdType()));
            contractMasterExportTable.setCity(feeRule.getBizRule().get(ContractMasterBizRule.Fields.city));
            contractMasterExportTable.setDistrict(feeRule.getBizRule().get(ContractMasterBizRule.Fields.district));
            contractMasterExportTable.setProvince(feeRule.getBizRule().get(ContractMasterBizRule.Fields.province));
            contractMasterExportTable.setAttributeDisplayName(feeRule.getBizRule().get(ContractMasterBizRule.Fields.attributeDisplayName));
            contractMasterExportTable.setServiceId(feeRule.getBizRule().get(ContractMasterBizRule.Fields.serviceId));
            contractMasterExportTable.setTemplateId(feeRule.getTemplateId().toString());
            contractMasterExportTable.setBizTag(feeRule.getBizRule().get(ContractMasterBizRule.Fields.bizTag));
            contractMasterExportTable.setServiceName(feeRule.getBizRule().get(ContractMasterBizRule.Fields.serviceName));
            contractMasterExportTable.setStreet(feeRule.getBizRule().get(ContractMasterBizRule.Fields.street));
            contractMasterExportTable.setMasterInputPrice(feeRule.getBizRule().get(ContractMasterBizRule.Fields.masterInputPrice));
            contractMasterExportTable.setDivisionTypeName(exportTaskInfo.getDivisionTypeName());
            contractMasterExportTable.setCustomSkuUserId(feeRule.getBizRule().get(CommonBizRule.Fields.customSkuUserId));
            contractMasterExportTables.add(contractMasterExportTable);
        }

        // 导出为表格
        log.info("exportTaskId={},正在导出表格,共{}条", exportTaskInfo.getExportTaskId(), contractMasterExportTables.size());
        ByteArrayOutputStream fileBytes = new ByteArrayOutputStream();
        EasyExcel.write(fileBytes, ContractMasterExportTable.class).excludeColumnFieldNames(SHOULD_IGNORE.getOrDefault(exportTaskInfo.getDivisionType(), Collections.emptyList())).sheet("价格导出").doWrite(contractMasterExportTables);
        // 存入结果
        String extend = "xlsx";
        String fileName = sceneInfo.getSceneName() + "价格导出" + DateUtils.formatDateTime(exportTaskInfo.getCreateTime());
        log.info("exportTaskId={},正在上传文件", exportTaskInfo.getExportTaskId());
        FileUploadResp xlsx = FileUploadUtils.upload(fileBytes.toByteArray(), extend);
        if (xlsx.isSuccess()) {
            exportTaskInfo.setFileUrl(xlsx.getData().getFileUrl());
            exportTaskInfo.setFileId(xlsx.getData().getFileId());
            exportTaskInfo.setFileName(fileName + PunConstant.DOT + extend);
            exportTaskInfo.setStatus(ExportTaskStatusEnum.SUCCESS.code);
            log.info("exportTaskId={},导出完成，共{}条数据", exportTaskInfo.getExportTaskId(), count);
        } else {
            exportTaskInfo.setError("上传文件失败:" + xlsx.getMsg());
            exportTaskInfo.setStatus(ExportTaskStatusEnum.FAIL.code);
            log.info("exportTaskId={},导出失败，共{}条数据, error={}", exportTaskInfo.getExportTaskId(), count, xlsx.getMsg());
        }
        exportTaskInfoRepository.save(exportTaskInfo);
    }
}
