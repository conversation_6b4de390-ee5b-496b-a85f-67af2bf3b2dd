package com.wanshifu.strategy.export;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.wanshifu.api.FileUploadUtils;
import com.wanshifu.bean.FileUploadResp;
import com.wanshifu.domain.dto.table.AutoReceiveOrderGuidePriceExportTable;

import com.wanshifu.fee.center.domain.biz.AutoReceiveOrderGuidePriceBizRule;
import com.wanshifu.fee.center.domain.biz.CommonBizRule;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.ExportTaskInfo;
import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.document.SceneInfo;

import com.wanshifu.fee.center.domain.enums.DivisionTypeEnum;
import com.wanshifu.fee.center.domain.enums.ExportTaskStatusEnum;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.repository.ExportTaskInfoRepository;
import com.wanshifu.strategy.batch.InstructionWriteHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.util.*;

@Component
@Slf4j
@Order(ExportTableHandler.DEFAULT_ORDER)
public class AutoReceiveOrderGuidePriceExportTableHandler extends AbstractExportTableHandler {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private ExportTaskInfoRepository exportTaskInfoRepository;

    private static final Map<String, List<String>> SHOULD_IGNORE = new HashMap<>();

    static {
        SHOULD_IGNORE.put(DivisionTypeEnum.STREET.code, Collections.emptyList());
        SHOULD_IGNORE.put(DivisionTypeEnum.COUNTRY.code, Arrays.asList(AutoReceiveOrderGuidePriceExportTable.Fields.city, AutoReceiveOrderGuidePriceExportTable.Fields.district, AutoReceiveOrderGuidePriceExportTable.Fields.province, AutoReceiveOrderGuidePriceExportTable.Fields.street));
        SHOULD_IGNORE.put(DivisionTypeEnum.PROVINCE.code, Arrays.asList(AutoReceiveOrderGuidePriceExportTable.Fields.city, AutoReceiveOrderGuidePriceExportTable.Fields.district, AutoReceiveOrderGuidePriceExportTable.Fields.street));
        SHOULD_IGNORE.put(DivisionTypeEnum.DISTRICT.code, Collections.singletonList(AutoReceiveOrderGuidePriceExportTable.Fields.street));
        SHOULD_IGNORE.put(DivisionTypeEnum.CITY.code, Arrays.asList(AutoReceiveOrderGuidePriceExportTable.Fields.district, AutoReceiveOrderGuidePriceExportTable.Fields.street));
    }

    @Override
    protected boolean support(SceneInfo sceneInfo) {
        return "auto_receive_order_guide_price".equals(sceneInfo.getSceneCode());
    }

    @Override
    protected void doExport(SceneInfo sceneInfo, ExportTaskInfo exportTaskInfo) {
        // 根据条件查询数据
        Criteria criteria = Criteria.where(FeeRule.Fields.sceneCode).is(exportTaskInfo.getSceneCode());
        criteria.and(BaseDocument.Fields.del).is(false);
        // serviceId
        if (CollectionUtils.isNotEmpty(exportTaskInfo.getServiceIds())) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.serviceId).in(exportTaskInfo.getServiceIds());
        }
        //  bizId
        if (CollectionUtils.isNotEmpty(exportTaskInfo.getBizIds())) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + sceneInfo.getBizIdType()).in(exportTaskInfo.getBizIds());
        }
        // 价格维度
        if (StringUtils.isNotBlank(exportTaskInfo.getDivisionType())) {
            criteria.and(FeeRule.Fields.bizRule + PunConstant.DOT + AutoReceiveOrderGuidePriceBizRule.Fields.divisionType).is(exportTaskInfo.getDivisionType());
        }

        Query query = Query.query(criteria);
        long count = mongoTemplate.count(query, FeeRule.class);
        // 校验
        if (count <= 0) {
            log.info("exportTaskId={},count={}", exportTaskInfo.getExportTaskId(), count);
            exportTaskInfo.setStatus(ExportTaskStatusEnum.FAIL.code);
            exportTaskInfo.setError("库中无数据");
            exportTaskInfoRepository.save(exportTaskInfo);
            return;
        }
        // 10w
        if (count > 10_0000) {
            log.info("exportTaskId={},count={}", exportTaskInfo.getExportTaskId(), count);
            exportTaskInfo.setStatus(ExportTaskStatusEnum.FAIL.code);
            exportTaskInfo.setError("数据超过10万");
            exportTaskInfoRepository.save(exportTaskInfo);
            return;
        }
        List<FeeRule> feeRuleList = mongoTemplate.find(query, FeeRule.class);

        log.info("exportTaskId={},正在转换数据,共{}条", exportTaskInfo.getExportTaskId(), feeRuleList.size());
        // 转换数据
        List<AutoReceiveOrderGuidePriceExportTable> autoReceiveOrderGuidePriceExportTables = new ArrayList<>();
        for (FeeRule feeRule : feeRuleList) {
            AutoReceiveOrderGuidePriceExportTable autoReceiveOrderGuidePriceExportTable = new AutoReceiveOrderGuidePriceExportTable();
            autoReceiveOrderGuidePriceExportTable.setCity(feeRule.getBizRule().get(AutoReceiveOrderGuidePriceBizRule.Fields.city));
            autoReceiveOrderGuidePriceExportTable.setDistrict(feeRule.getBizRule().get(AutoReceiveOrderGuidePriceBizRule.Fields.district));
            autoReceiveOrderGuidePriceExportTable.setProvince(feeRule.getBizRule().get(AutoReceiveOrderGuidePriceBizRule.Fields.province));
            autoReceiveOrderGuidePriceExportTable.setAttributeDisplayName(feeRule.getBizRule().get(AutoReceiveOrderGuidePriceBizRule.Fields.attributeDisplayName));
            autoReceiveOrderGuidePriceExportTable.setBizTag(feeRule.getBizRule().get(AutoReceiveOrderGuidePriceBizRule.Fields.bizTag));
            autoReceiveOrderGuidePriceExportTable.setServiceId(feeRule.getBizRule().get(AutoReceiveOrderGuidePriceBizRule.Fields.serviceId));
            autoReceiveOrderGuidePriceExportTable.setTemplateId(feeRule.getTemplateId().toString());
            autoReceiveOrderGuidePriceExportTable.setServiceName(feeRule.getBizRule().get(AutoReceiveOrderGuidePriceBizRule.Fields.serviceName));
            autoReceiveOrderGuidePriceExportTable.setStreet(feeRule.getBizRule().get(AutoReceiveOrderGuidePriceBizRule.Fields.street));
            autoReceiveOrderGuidePriceExportTable.setMasterInputPrice(feeRule.getBizRule().get(AutoReceiveOrderGuidePriceBizRule.Fields.masterInputPrice));
            autoReceiveOrderGuidePriceExportTable.setDivisionTypeName(exportTaskInfo.getDivisionTypeName());
            autoReceiveOrderGuidePriceExportTable.setCustomSkuUserId(feeRule.getBizRule().get(CommonBizRule.Fields.customSkuUserId));
            autoReceiveOrderGuidePriceExportTables.add(autoReceiveOrderGuidePriceExportTable);
        }

        // 导出为表格
        log.info("exportTaskId={},正在导出表格,共{}条", exportTaskInfo.getExportTaskId(), autoReceiveOrderGuidePriceExportTables.size());
        ByteArrayOutputStream fileBytes = new ByteArrayOutputStream();


        // 1. 构造说明文字
        String instruction = "填写说明:\n" +
                "1、业务ID,如果是商家价格,填写商家ID:如果是师傅价格,填写师傅ID\n" +
                "2、如果价格全国统一,省市区街道不填;如果是城市定价请填写省市;不同省市请自行复制添加一行,区县/街道价格以此类推\n" +
                "3、单价必须填写,即该服务sku的基础价格;单价max仅应用于期间价,特殊场合下填写\n" +
                "4、起步价选填,建议30元起,即一个订单的最低价格\n" +
                "5、白名单属性仅针对定制属性的商家,先检查所属配置的价格是不是白名单商家,如果是,则用白名单属性的sku配置置价格";


        List<String> excludeColumnFieldNames =SHOULD_IGNORE.getOrDefault(exportTaskInfo.getDivisionType(), Collections.emptyList());

        // 3. 用反射 + 注解过滤真实要写出的列数
        Field[] allFields = AutoReceiveOrderGuidePriceExportTable.class.getDeclaredFields();
        int columnCount = (int) Arrays.stream(allFields)
                // 必须有 @ExcelProperty
                .filter(f -> f.isAnnotationPresent(ExcelProperty.class))
                // 且不在排除列表里
                .filter(f -> !excludeColumnFieldNames.contains(f.getName()))
                .count();

        try (ExcelWriter writer = EasyExcel.write(fileBytes, AutoReceiveOrderGuidePriceExportTable.class)
                .excludeColumnFieldNames(excludeColumnFieldNames)
                .registerWriteHandler(new InstructionWriteHandler(instruction, columnCount))
                .build()) {

            // sheet 名称用场景名称
            WriteSheet sheet = EasyExcel.writerSheet("价格导出")
                    .relativeHeadRowIndex(1)
                    .build();
            writer.write(autoReceiveOrderGuidePriceExportTables, sheet);
            writer.finish();
        }

        // 存入结果
        String extend = "xlsx";
        String fileName = sceneInfo.getSceneName() + "价格导出" + DateUtils.formatDateTime(exportTaskInfo.getCreateTime());
        log.info("exportTaskId={},正在上传文件", exportTaskInfo.getExportTaskId());
        FileUploadResp xlsx = FileUploadUtils.upload(fileBytes.toByteArray(), extend);
        if (xlsx.isSuccess()) {
            exportTaskInfo.setFileUrl(xlsx.getData().getFileUrl());
            exportTaskInfo.setFileId(xlsx.getData().getFileId());
            exportTaskInfo.setFileName(fileName + PunConstant.DOT + extend);
            exportTaskInfo.setStatus(ExportTaskStatusEnum.SUCCESS.code);
            log.info("exportTaskId={},导出完成，共{}条数据", exportTaskInfo.getExportTaskId(), count);
        } else {
            exportTaskInfo.setError("上传文件失败:" + xlsx.getMsg());
            exportTaskInfo.setStatus(ExportTaskStatusEnum.FAIL.code);
            log.info("exportTaskId={},导出失败，共{}条数据, error={}", exportTaskInfo.getExportTaskId(), count, xlsx.getMsg());
        }
        exportTaskInfoRepository.save(exportTaskInfo);
    }
}
