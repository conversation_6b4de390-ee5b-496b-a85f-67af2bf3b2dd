package com.wanshifu.strategy.express;

import com.wanshifu.fee.center.domain.dto.ExpressInfo;
import com.wanshifu.framework.core.BusException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class ExpressCompilerComposite implements ExpressCompiler{
    @Autowired
    private List<ExpressCompiler> expressCompilers;

    @Override
    public boolean support(ExpressTypeEnum expressType) {
        return true;
    }

    @Override
    public String compile(ExpressInfo expressInfo) {
        ExpressCompiler expressCompiler = getExpressCompiler();
        return expressCompiler.compile(expressInfo);
    }

    @Override
    public ExpressInfo decompile(String express) {
        ExpressCompiler expressCompiler = getExpressCompiler();
        return expressCompiler.decompile(express);
    }


    private ExpressCompiler getExpressCompiler() {
        ExpressCompiler expressCompiler = null;
        // 目前只支持 QLExpress
        for (ExpressCompiler compiler : expressCompilers) {
            if (compiler.support(ExpressTypeEnum.QLExpress)) {
                expressCompiler = compiler;
                break;
            }
        }
        //
        if(Objects.isNull(expressCompiler)){
            throw new BusException("不支持的表达式类型");
        }
        return expressCompiler;
    }

}
