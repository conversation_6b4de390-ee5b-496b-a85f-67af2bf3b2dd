package com.wanshifu.strategy.express;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum ExpressTypeEnum {
    /**
     * 阿里qlexpress
     */
    QLExpress("'ql"),
    ;
    public final String code;
    public static ExpressTypeEnum fromCode(String code){
        for (ExpressTypeEnum value : ExpressTypeEnum.values()) {
            if(value.code.equals(code)){
                return value;
            }
        }
        return null;
    }
}
