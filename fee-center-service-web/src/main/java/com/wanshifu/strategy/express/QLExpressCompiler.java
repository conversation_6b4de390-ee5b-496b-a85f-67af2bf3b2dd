package com.wanshifu.strategy.express;

import com.ql.util.express.ExpressRunner;
import com.wanshifu.fee.center.domain.dto.ExpressInfo;
import com.wanshifu.fee.center.domain.enums.ExpressOperatorEnum;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Component
@Slf4j
public class QLExpressCompiler implements ExpressCompiler{
    @Resource
    private ExpressRunner expressRunner;

    @Override
    public boolean support(ExpressTypeEnum expressType) {
        return ExpressTypeEnum.QLExpress == expressType;
    }

    @Override
    public String compile(ExpressInfo expressInfo) {
        StringBuilder resultBuilder = new StringBuilder();
        resultBuilder.append("import java.math.BigDecimal; import com.wanshifu.fee.center.domain.dto.ExpressResultInfo; ");

        // 找到兜底策略
        ExpressInfo.ExpressInfoUnit endExpressInfoUnit = expressInfo.getExpressInfoUnits().stream()
                .filter(expressInfoUnit ->
                        Objects.nonNull(expressInfoUnit.getCondition())
                                && "1".equals(expressInfoUnit.getCondition().getValue())
                                && "1".equals(expressInfoUnit.getCondition().getParam())
                                && "==".equals(expressInfoUnit.getCondition().getOperator())
                ).findFirst().orElse(null);

        expressInfo.getExpressInfoUnits().removeIf(expressInfoUnit ->
                Objects.nonNull(expressInfoUnit.getCondition())
                        && "1".equals(expressInfoUnit.getCondition().getValue())
                        && "1".equals(expressInfoUnit.getCondition().getParam())
                        && "==".equals(expressInfoUnit.getCondition().getOperator()));

        for (ExpressInfo.ExpressInfoUnit expressInfoUnit : expressInfo.getExpressInfoUnits()) {
            StringBuilder unitBuilder = new StringBuilder();
            String numberExpress = expressInfoUnit.getNumberExpress();
            String priceExpress = expressInfoUnit.getPriceExpress();
            String condition = buildCondition(expressInfoUnit.getCondition());
            unitBuilder.append("if (").append(condition).append(") { ")
                    // 计算单价
                    .append("BigDecimal price = (").append(priceExpress).append("); ")
                    // 计算数量
                    .append("BigDecimal num = (").append(numberExpress).append("); ")
                    // 计算总价
                    .append("BigDecimal cost = price * num; ")
                    .append("ExpressResultInfo returnResult = new ExpressResultInfo(); ")
                    .append("returnResult.setNumber(num); ")
                    .append("returnResult.setPrice(price); ")
                    .append("returnResult.setCost(cost); ")
                    .append("return returnResult; ")
                    .append("} ");

            resultBuilder.append(unitBuilder);
        }

        if (Objects.nonNull(endExpressInfoUnit)) {
            StringBuilder unitBuilder = new StringBuilder();
            String numberExpress = endExpressInfoUnit.getNumberExpress();
            String priceExpress = endExpressInfoUnit.getPriceExpress();
            String condition = buildCondition(endExpressInfoUnit.getCondition());
            unitBuilder.append("if (").append(condition).append(") { ")
                    // 计算单价
                    .append("BigDecimal price = (").append(priceExpress).append("); ")
                    // 计算数量
                    .append("BigDecimal num = (").append(numberExpress).append("); ")
                    // 计算总价
                    .append("BigDecimal cost = price * num; ")
                    .append("ExpressResultInfo returnResult = new ExpressResultInfo(); ")
                    .append("returnResult.setNumber(num); ")
                    .append("returnResult.setPrice(price); ")
                    .append("returnResult.setCost(cost); ")
                    .append("return returnResult; ")
                    .append("} ");
            // 插入兜底策略到最后
            resultBuilder.append(unitBuilder);
            expressInfo.getExpressInfoUnits().add(endExpressInfoUnit);
        }

        String result = resultBuilder.toString();
        try{
            expressRunner.parseInstructionSet(result);
        }catch (Exception e){
            throw new BusException(e.getMessage(),e);
        }
        return result;
    }

    @Override
    public ExpressInfo decompile(String express) {
        throw new  IllegalStateException("还未实现反编译");
    }

    private String buildCondition(ExpressInfo.Condition condition){

        ExpressOperatorEnum expressOperatorEnum = ExpressOperatorEnum.fromCode(condition.getOperator());
        if(Objects.isNull(expressOperatorEnum)){
            throw new BusException("条件操作符不合法");
        }
        List<ExpressInfo.Condition> subCondition = condition.getSubCondition();
        boolean notEmpty = CollectionUtils.isNotEmpty(subCondition);
        if(expressOperatorEnum.useInExpress && notEmpty){
            throw new BusException(expressOperatorEnum.code+" 操作符使用错误，只能使用在表达式");
        }

        if(!expressOperatorEnum.useInExpress && !notEmpty){
            throw new BusException(expressOperatorEnum.code+" 操作符使用错误，只能使用在多条件");
        }

        if(expressOperatorEnum.isSingleOperator && subCondition.size() > 1){
            throw new BusException(expressOperatorEnum.code+" 操作符使用错误，只能有单个子条件");
        }

        // 正式解析
        if(!notEmpty){
            // 表达式
            return  condition.getParam() + " " + condition.getOperator() + " " + condition.getValue();
        }

        // 有逻辑运算的表达式
        if(expressOperatorEnum.isSingleOperator){
            return expressOperatorEnum.code + "(" + buildCondition(subCondition.iterator().next()) +")" ;
        }
        List<String> conditionStr = new ArrayList<>(subCondition.size());
        for (ExpressInfo.Condition subCon : subCondition) {
            String subConStr = buildCondition(subCon);
            conditionStr.add(" ("+subConStr+") ");
        }
        // 处理连字
        return String.join(expressOperatorEnum.code,conditionStr);
    }
}
