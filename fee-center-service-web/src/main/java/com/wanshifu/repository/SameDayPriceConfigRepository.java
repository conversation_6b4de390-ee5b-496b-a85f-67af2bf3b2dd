package com.wanshifu.repository;

import com.wanshifu.fee.center.domain.document.SameDayPriceConfig;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SameDayPriceConfigRepository extends MongoRepository<SameDayPriceConfig, ObjectId> {

    SameDayPriceConfig findBySameDayPriceConfigId(Long sameDayPriceConfigId);
    List<SameDayPriceConfig> findAllByLevel1GoodsCategoryId(Long leve1GoodsCategoryId);

}
