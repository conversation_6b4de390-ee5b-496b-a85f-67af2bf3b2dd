package com.wanshifu.repository;

import com.wanshifu.fee.center.domain.document.DynamicFeeRule;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface DynamicFeeRuleRepository extends MongoRepository<DynamicFeeRule, ObjectId> {
    DynamicFeeRule findDynamicFeeRuleByDynamicFeeRuleIdIsAndDelIsFalse(Long dynamicFeeRuleId);
    DynamicFeeRule findDynamicFeeRuleByDynamicFeeRuleIdIs(Long dynamicFeeRuleId);
}
