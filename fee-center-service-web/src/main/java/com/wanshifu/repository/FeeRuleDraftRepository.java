package com.wanshifu.repository;

import com.wanshifu.fee.center.domain.document.FeeRuleDraft;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Set;

/**
 * @author: <PERSON>
 * @create: 2023-09-11 15:04
 * @description: 计价规则草稿repository
 */
@Repository
public interface FeeRuleDraftRepository extends MongoRepository<FeeRuleDraft, ObjectId> {

    void deleteAllByIdIn(Set<String> feeRuleDraftIdSet);

    FeeRuleDraft findByFeeRuleDraftId(Long feeRuleDraftId);

    FeeRuleDraft findOneByTemplateIdAndDelIsFalse(Long templateId);
}
