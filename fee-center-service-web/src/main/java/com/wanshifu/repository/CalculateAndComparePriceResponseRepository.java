package com.wanshifu.repository;

import com.wanshifu.fee.center.domain.request.calculate.CalculateAndComparePriceResponse;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CalculateAndComparePriceResponseRepository extends MongoRepository<CalculateAndComparePriceResponse, ObjectId> {

}
