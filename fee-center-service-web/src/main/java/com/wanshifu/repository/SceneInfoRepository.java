package com.wanshifu.repository;

import cn.hutool.core.util.StrUtil;
import com.wanshifu.fee.center.domain.document.SceneInfo;
import com.wanshifu.framework.core.BusException;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface SceneInfoRepository extends MongoRepository<SceneInfo, ObjectId> {
    SceneInfo findBySceneIdIsAndDelIsFalse(Long sceneId);

    SceneInfo findBySceneCodeIsAndDelIsFalse(String sceneCode);

    List<SceneInfo> findAllByDelIsFalse();

    List<SceneInfo> findAllBySceneCodeInAndDelIsFalse(Collection<String> sceneCodes);

    default String findSceneNameBySceneCode(String sceneCode) {
        SceneInfo sceneInfo = findBySceneCodeIsAndDelIsFalse(sceneCode);
        if (sceneInfo == null) {
            throw new BusException(StrUtil.format("场景编码（sceneCode={}）有误", sceneCode));
        }
        return sceneInfo.getSceneName();
    }

}
