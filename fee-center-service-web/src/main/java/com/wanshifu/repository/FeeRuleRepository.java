package com.wanshifu.repository;

import com.wanshifu.fee.center.domain.document.FeeRule;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface FeeRuleRepository extends MongoRepository<FeeRule, ObjectId> {
    FeeRule findByFeeRuleId(Long feeRuleId);

    List<FeeRule> findAllByFeeRuleIdIn(Collection<Long> feeRuleIds);

    void deleteAllByFeeRuleIdIn(Collection<Long> ids);

    void deleteAllByIdIn(Collection<String> ids);

    FeeRule findOneByTemplateIdAndDelIsFalse(Long templateId);
}
