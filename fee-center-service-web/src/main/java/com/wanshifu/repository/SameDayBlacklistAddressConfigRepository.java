package com.wanshifu.repository;

import com.wanshifu.fee.center.domain.document.SameDayBlacklistAddressConfig;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface SameDayBlacklistAddressConfigRepository extends MongoRepository<SameDayBlacklistAddressConfig, ObjectId> {

    SameDayBlacklistAddressConfig findByConfigId(Long configId);

    SameDayBlacklistAddressConfig findByLevel1GoodsCategoryIdAndDistrictId(Long level1GoodsCategoryId, String districtId);

    void deleteByConfigId(Long configId);
}
