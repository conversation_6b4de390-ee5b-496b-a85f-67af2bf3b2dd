package com.wanshifu.repository;

import com.wanshifu.fee.center.domain.document.BizRuleMapping;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BizRuleMappingRepository extends MongoRepository<BizRuleMapping, ObjectId> {

    BizRuleMapping findByBizRuleMappingId(Long bizRuleMappingId);

    BizRuleMapping findByTemplateIdAndAndDelIsTrue(Long templateId);

    List<BizRuleMapping> findBizRuleMappingByBizRuleMappingIdIn(List<Long> bizRuleMappingIds);
}
