package com.wanshifu.repository;

import com.wanshifu.fee.center.domain.constant.PunConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.IndexOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.index.IndexInfo;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.util.ReflectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;

@Slf4j
@Component
public class MongodbIndexCreator {
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private Executor mongodbTaskExecutor;

    private final Map<Class<?>,Set<Field>> SHOULD_INDEXED = new ConcurrentHashMap<>();

    private final static ReflectionUtils.AnnotationFieldFilter INDEXED_FIELD_FILTER = new ReflectionUtils.AnnotationFieldFilter(Indexed.class);

    public void submitCreateTask(List<?> list){
        mongodbTaskExecutor.execute(()->{
            for (Object object : list) {
                doSubmitCreateTask(object);
            }
        });
    }

    public void submitCreateTask(Object object){
        mongodbTaskExecutor.execute(()->{
            doSubmitCreateTask(object);
        });
    }

    private void  doSubmitCreateTask(Object object){
        Class<?> aClass = object.getClass();
        if(Objects.isNull(aClass.getAnnotation(Document.class))){
            return;
        }

        Set<Field> fields = SHOULD_INDEXED.get(aClass);
        if(CollectionUtils.isEmpty(fields)){
            fields = findField(aClass);
        }
        if(CollectionUtils.isEmpty(fields)){
            return;
        }
        SHOULD_INDEXED.put(aClass,fields);

        IndexOperations indexOperations = mongoTemplate.indexOps(aClass);
        List<IndexInfo> indexInfo = indexOperations.getIndexInfo();

        for (Field field : fields) {
            String name = field.getName();

            if(field.getType().isAssignableFrom(Map.class)){
                PropertyDescriptor propertyDescriptor = BeanUtils.getPropertyDescriptor(aClass, name);
                Map<String,String> map = (Map<String, String>) org.springframework.util.ReflectionUtils.invokeMethod(propertyDescriptor.getReadMethod(), object);
                Set<String> keySet = map.keySet();
                for (String key : keySet) {
                    if(indexInfo.stream().flatMap(index -> index.getIndexFields().stream()).anyMatch(indexField -> indexField.getKey().equals(name+PunConstant.DOT+key))){
                        // 已经有该字段索引，
                        continue;
                    }else {
                        Index index = new Index(name+ PunConstant.DOT+key, Sort.Direction.ASC);
                        indexOperations.ensureIndex(index);
                    }
                }

            }else {
                if(indexInfo.stream().flatMap(index -> index.getIndexFields().stream()).anyMatch(indexField -> indexField.getKey().equals(name))){
                    // 已经有该字段索引，
                    continue;
                }

                Indexed annotation = field.getAnnotation(Indexed.class);
                Index index = new Index(name, Sort.Direction.ASC);
                if(annotation.unique()){
                    index.unique();
                }
                indexOperations.ensureIndex(index);
            }
        }
    }

    private Set<Field> findField(Class<?> type) {

        Assert.notNull(type, "Type must not be null!");

        Class<?> targetClass = type;
        Set<Field> foundField = new HashSet<>();

        while (targetClass != Object.class) {

            for (Field field : targetClass.getDeclaredFields()) {
                if (INDEXED_FIELD_FILTER.matches(field)) {
                    foundField.add(field);
                }
            }

            targetClass = targetClass.getSuperclass();
        }

        return foundField;
    }
}
