package com.wanshifu.repository;

import com.wanshifu.fee.center.domain.document.FeeTemplateMapping;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface FeeTemplateMappingRepository extends MongoRepository<FeeTemplateMapping, ObjectId> {

    FeeTemplateMapping findOneByMappingId(Long mappingId);

    List<FeeTemplateMapping> findAllByMappingIdIn(List<Long> mappingIds);

}
