package com.wanshifu.repository.dao;

import com.wanshifu.fee.center.domain.document.master2c.DraftServePrice;
import com.wanshifu.framework.core.page.SimplePageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/3 15:35
 */
public interface DraftServePriceDao {

    /**
     * 根据 服务名称、城市id、状态分页查询
     *
     * @param serveName 服务名称，支持前后模糊查询
     * @param configName 配置名称，支持前后模糊查询
     * @param cityId    城市id， 精确查询
     * @param status    状态，精确查询
     * @param pageNum   页码
     * @param pageSize  每页条数
     * @return SimplePageInfo<ServePrice>
     */
    SimplePageInfo<DraftServePrice> findPageByServeNameAndConfigNameAndCityIdAndStatus(
            String serveName,
            String configName,
            Long cityId,
            String status,
            Integer pageNum, Integer pageSize);

    void updateStatusByDraftId(Long draftId, String status, String remark);

    List<DraftServePrice> findListByStatusAndNowBeforeStartTime(String status);

    List<DraftServePrice> findListByStatusAndNowAfterOrEqualStartTime(String status);

    /**
     * 根据配置名称 并根据priceId排除 获取
     * @param configName 配置名称
     * @param draft 排除的priceId
     * @return ServePrice
     */
    DraftServePrice findOneByConfigNameExcludeByDraftId(String configName, Long draft);

    /**
     * 根据配置名称 获取
     * @param configName 配置名称
     * @return ServePrice
     */
    DraftServePrice findOneByConfigName(String configName);
}
