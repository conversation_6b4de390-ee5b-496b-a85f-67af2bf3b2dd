package com.wanshifu.repository.dao.impl;

import cn.hutool.core.util.StrUtil;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.master2c.DraftServePrice;

import com.wanshifu.fee.center.domain.enums.master2c.DraftServePriceStatusEnum;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.repository.dao.DraftServePriceDao;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;

import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/3 15:39
 */
@Repository
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DraftServePriceDaoImpl implements DraftServePriceDao {

    private final MongoTemplate mongoTemplate;

    @Override
    public SimplePageInfo<DraftServePrice> findPageByServeNameAndConfigNameAndCityIdAndStatus(String serveName,
                                                                                              String configName,
                                                                                              Long cityId,
                                                                                              String status,
                                                                                              Integer pageNum,
                                                                                              Integer pageSize) {
        Criteria criteria = Criteria.where(DraftServePrice.Fields.del).is(false);
        if (StringUtils.isNotBlank(serveName)) {
            criteria.and(DraftServePrice.Fields.serveName).regex(serveName);
        }
        if (StringUtils.isNotBlank(configName)) {
            criteria.and(DraftServePrice.Fields.configName).regex(configName);
        }
        if (cityId != null) {
            criteria.orOperator(
                    Criteria.where(DraftServePrice.Fields.cityIds).is(cityId),
                    Criteria.where(DraftServePrice.Fields.cityGroup + PunConstant.DOT + DraftServePrice.CityGroup.Fields.cityIds).is(cityId)
            );
        }
        if (StringUtils.isNotBlank(status)) {
            criteria.and(DraftServePrice.Fields.status).is(status);
        }
        Query query = new Query(criteria);
        long count = mongoTemplate.count(query, DraftServePrice.class);
        if (count <= 0) {
            return new SimplePageInfo<>();
        }


        query.with(new Sort(Sort.Direction.DESC, BaseDocument.Fields.id));
        PageRequest requestPage = new PageRequest(pageNum - 1, pageSize);
        query.with(requestPage);
        List<DraftServePrice> servePrices = mongoTemplate.find(query, DraftServePrice.class);

        SimplePageInfo<DraftServePrice> resultPage = new SimplePageInfo<>(servePrices);
        resultPage.setTotal(count);
        return resultPage;
    }

    @Override
    public void updateStatusByDraftId(Long draftId, String status, String remark) {
        if (draftId == null || StringUtils.isBlank(status)) {
            throw new BusException("参数不能为空");
        }
        DraftServePriceStatusEnum statusEnum = DraftServePriceStatusEnum.fromStatus(status);
        if  (statusEnum == null) {
            throw new BusException(StrUtil.format("状态有误:{}", status));
        }
        Update update = new Update();
        update.set(DraftServePrice.Fields.status, status);
        update.set(BaseDocument.Fields.modifyTime, new Date());
        if (StringUtils.isNotBlank(remark)) {
            update.set(DraftServePrice.Fields.remark, remark);
        }
        mongoTemplate.updateFirst(new Query(Criteria.where(DraftServePrice.Fields.draftId).is(draftId)), update, DraftServePrice.class);
    }

    @Override
    public List<DraftServePrice> findListByStatusAndNowBeforeStartTime(String status) {
        Date now = new Date();
        Criteria criteria = Criteria.where(DraftServePrice.Fields.status).is(status)
                .and(DraftServePrice.Fields.del).is(false)
                .and(DraftServePrice.Fields.startTime).gt(now);
        return mongoTemplate.find(new Query(criteria), DraftServePrice.class);
    }

    @Override
    public List<DraftServePrice> findListByStatusAndNowAfterOrEqualStartTime(String status) {
        Date now = new Date();
        Criteria criteria = Criteria.where(DraftServePrice.Fields.status).is(status)
                .and(DraftServePrice.Fields.del).is(false)
                .and(DraftServePrice.Fields.startTime).lte(now);
        return mongoTemplate.find(new Query(criteria), DraftServePrice.class);
    }

    @Override
    public DraftServePrice findOneByConfigNameExcludeByDraftId(String configName, Long draftId) {
        Criteria criteria = Criteria.where(DraftServePrice.Fields.configName).is(configName)
                .and(DraftServePrice.Fields.draftId).ne(draftId)
                .and(DraftServePrice.Fields.del).is(false);
        return mongoTemplate.findOne(new Query(criteria), DraftServePrice.class);
    }

    @Override
    public DraftServePrice findOneByConfigName(String configName) {
        Criteria criteria = Criteria.where(DraftServePrice.Fields.configName).is(configName)
                .and(DraftServePrice.Fields.del).is(false);
        return mongoTemplate.findOne(new Query(criteria), DraftServePrice.class);
    }
}
