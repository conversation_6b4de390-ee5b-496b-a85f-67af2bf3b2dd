package com.wanshifu.repository.dao.impl;

import cn.hutool.core.util.StrUtil;
import com.wanshifu.fee.center.domain.constant.PunConstant;
import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.master2c.ServePrice;
import com.wanshifu.fee.center.domain.enums.master2c.ServePriceStatusEnum;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.infrastructure.utils.CommonUtils;
import com.wanshifu.repository.dao.ServePriceDao;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ServePriceDaoImpl implements ServePriceDao {

    private final MongoTemplate mongoTemplate;

    @Override
    public List<ServePrice> findListByStatusAndNowAfterOrEqualStartTime(String status) {
        Date now = new Date();
        Criteria criteria = Criteria.where(ServePrice.Fields.status).is(status)
                .and(ServePrice.Fields.del).is(false)
                .and(ServePrice.Fields.startTime).lte(now);
        return mongoTemplate.find(new Query(criteria), ServePrice.class);
    }

    @Override
    public List<ServePrice> findListByStatusAndNowBeforeStartTime(String status) {
        Date now = new Date();
        Criteria criteria = Criteria.where(ServePrice.Fields.status).is(status)
                .and(ServePrice.Fields.del).is(false)
                .and(ServePrice.Fields.startTime).gt(now);
        return mongoTemplate.find(new Query(criteria), ServePrice.class);
    }

    @Override
    public void updateStatusByPriceId(Long priceId, String status, String updateBy) {
        if (priceId == null || StringUtils.isBlank(status)) {
            throw new BusException("参数不能为空");
        }
        ServePriceStatusEnum statusEnum = ServePriceStatusEnum.fromStatus(status);
        if  (statusEnum == null) {
            throw new BusException(StrUtil.format("状态有误:{}", status));
        }
        Update  update = new Update();
        update.set(ServePrice.Fields.status, status);
        update.set(BaseDocument.Fields.modifyTime, new Date());
        if (StringUtils.isNotBlank(updateBy)) {
            update.set(BaseDocument.Fields.updateBy, updateBy);
        } else {
            update.set(BaseDocument.Fields.updateBy, CommonUtils.getCurrentLoginName());
        }
        mongoTemplate.updateFirst(new Query(Criteria.where(ServePrice.Fields.priceId).is(priceId)), update, ServePrice.class);
    }

    /**
     * 下面自定义排序使用聚合查询，查询语句转化为原生mongoDB shell如下：
     * db.serve_price.aggregate([
     *   // 1. 匹配阶段：过滤文档
     *   {
     *     $match: {
     *       del: false,
     *       // 其他查询条件（根据实际参数）
     *       serveName: { $regex: "搜索词" },
     *       configName: { $regex: "搜索词" },
     *       $or: [
     *         { cityIds: cityId },
     *         { "cityGroup.cityIds": cityId }
     *       ],
     *       status: "特定状态"
     *     }
     *   },
     *
     *   // 2. 投影阶段：添加排序字段 + 选择返回字段
     *   {
     *     $project: {
     *       status: 1,
     *       _id: 1,
     *       //...其他所有字段
     *       // 添加自定义排序字段
     *       statusOrder: {
     *         $cond: {
     *           if: { $eq: ["$status", "pending_review"] },
     *           then: 1,         // 待审核状态赋值为1（最高优先级）
     *           else: 2          // 其他状态赋值为2
     *         }
     *       }
     *     }
     *   },
     *
     *   // 3. 排序阶段：按自定义规则排序
     *   {
     *     $sort: {
     *       statusOrder: 1,     // 先按自定义状态排序（1在前）
     *       _id: -1             // 相同状态下按ID倒序（新数据在前）
     *     }
     *   },
     *
     *   // 4. 分页阶段
     *   {
     *     $skip: (pageNum - 1) * pageSize // 跳过前面的记录
     *   },
     *   {
     *     $limit: pageSize // 限制返回记录数
     *   }
     * ])
     *
     *
     * 查询服务价格列表
     * @param serveName 服务名称，支持前后模糊查询
     * @param configName 配置名称，支持前后模糊查询
     * @param cityId    城市id， 精确查询
     * @param status    状态，精确查询
     * @param pageNum   页码
     * @param pageSize  每页条数
     * @return
     */
    @Override
    public SimplePageInfo<ServePrice> findPageByServeNameAndConfigNameAndCityIdAndStatus(String serveName, String configName,
                                                                            Long cityId, String status,
                                                                            Integer pageNum, Integer pageSize) {
        Criteria criteria = Criteria.where(ServePrice.Fields.del).is(false);
        if (StringUtils.isNotBlank(serveName)) {
            criteria.and(ServePrice.Fields.serveName).regex(serveName);
        }
        if (StringUtils.isNotBlank(configName)) {
            criteria.and(ServePrice.Fields.configName).regex(configName);
        }
        if (cityId != null) {
            criteria.orOperator(
                    Criteria.where(ServePrice.Fields.cityIds).is(cityId),
                    Criteria.where(ServePrice.Fields.cityGroup + PunConstant.DOT + ServePrice.CityGroup.Fields.cityIds).is(cityId)
            );
        }
        if (StringUtils.isNotBlank(status)) {
            criteria.and(ServePrice.Fields.status).is(status);
        }
        Query query = new Query(criteria);
        long count = mongoTemplate.count(query, ServePrice.class);
        if (count <= 0) {
            return new SimplePageInfo<>();
        }

        //使用 MongoDB 的聚合表达式实现自定义排序,先按状态排序，待审核>其他状态，再按创建时间倒序
        AggregationExpression statusOrderExpr = ConditionalOperators
                .when(Criteria.where(ServePrice.Fields.status).is(ServePriceStatusEnum.PENDING_REVIEW.getStatus()))
                .then(1)
                .otherwise(2);

        //构建聚合查询
        MatchOperation matchStage = Aggregation.match(criteria);


        String[] allFields = new String[]{
                ServePrice.Fields.id, ServePrice.Fields.priceId, ServePrice.Fields.serveId, ServePrice.Fields.serveName, ServePrice.Fields.configName,
                ServePrice.Fields.level1ServeId, ServePrice.Fields.level1ServeName, ServePrice.Fields.level2ServeId, ServePrice.Fields.level2ServeName,
                ServePrice.Fields.level3ServeId, ServePrice.Fields.level3ServeName, ServePrice.Fields.priority, ServePrice.Fields.masterBasePrice, ServePrice.Fields.serveConfigs,
                ServePrice.Fields.cityIds, ServePrice.Fields.cityGroup, ServePrice.Fields.status,
                ServePrice.Fields.startTime, ServePrice.Fields.createBy,
                BaseDocument.Fields.createTime, BaseDocument.Fields.modifyTime, BaseDocument.Fields.updateBy, BaseDocument.Fields.del
        };

        ProjectionOperation projectStage = Aggregation.project()
                .andInclude(allFields)
                .and(statusOrderExpr).as("statusOrder");

        SortOperation sortStage = Aggregation.sort(Sort.Direction.ASC, "statusOrder").and(Sort.Direction.DESC, BaseDocument.Fields.id);


        SkipOperation skipStage = Aggregation.skip((long) (pageNum - 1) * pageSize);

        LimitOperation limitStage = Aggregation.limit(pageSize);

        Aggregation aggregation = Aggregation.newAggregation(
                matchStage,
                projectStage,
                sortStage,
                skipStage,
                limitStage
        );
        //执行聚合查询
        List<ServePrice> servePrices = mongoTemplate.aggregate(
                aggregation,
                ServePrice.class,
                ServePrice.class
        ).getMappedResults();

        SimplePageInfo<ServePrice> resultPage = new SimplePageInfo<>(servePrices);
        resultPage.setTotal(count);
        return resultPage;
    }

    @Override
    public long countByStatus(String status) {
        if (StringUtils.isBlank(status)) {
            return 0;
        }
        Criteria criteria = Criteria.where(ServePrice.Fields.del).is(false);
        criteria.and(ServePrice.Fields.status).is(status);
        Query query = new Query(criteria);
        return mongoTemplate.count(query, ServePrice.class);
    }


    @Override
    public ServePrice findOneByServeIdAndPriorityExcludeByPriceId(Long serveId, Integer priority, Long priceId) {
        Criteria criteria = Criteria.where("serveId").is(serveId)
                .and("priority").is(priority)
                .and("priceId").ne(priceId)
                .and("del").is(false);
        return mongoTemplate.findOne(new Query(criteria), ServePrice.class);
    }

    @Override
    public ServePrice findOneByServeIdAndPriority(Long serveId, Integer priority) {
        Criteria criteria = Criteria.where("serveId").is(serveId)
                .and("priority").is(priority)
                .and("del").is(false);
        return mongoTemplate.findOne(new Query(criteria), ServePrice.class);
    }

    @Override
    public ServePrice findOneByConfigNameExcludeByPriceId(String configName, Long priceId) {
        Criteria criteria = Criteria.where(ServePrice.Fields.configName).is(configName)
                .and(ServePrice.Fields.priceId).ne(priceId)
                .and(ServePrice.Fields.del).is(false);
        return mongoTemplate.findOne(new Query(criteria), ServePrice.class);
    }

    @Override
    public ServePrice findOneByConfigName(String configName) {
        Criteria criteria = Criteria.where(ServePrice.Fields.configName).is(configName)
                .and(ServePrice.Fields.del).is(false);
        return mongoTemplate.findOne(new Query(criteria), ServePrice.class);
    }
}
