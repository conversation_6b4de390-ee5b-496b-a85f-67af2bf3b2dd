package com.wanshifu.repository.dao;

import com.wanshifu.fee.center.domain.document.master2c.ServePrice;
import com.wanshifu.framework.core.page.SimplePageInfo;

import java.util.List;

public interface ServePriceDao {

    List<ServePrice> findListByStatusAndNowAfterOrEqualStartTime(String status);

    List<ServePrice> findListByStatusAndNowBeforeStartTime(String status);

    void updateStatusByPriceId(Long priceId, String status, String updateBy);

    /**
     * 根据 服务名称、城市id、状态分页查询
     *
     * @param serveName 服务名称，支持前后模糊查询
     * @param configName 配置名称，支持前后模糊查询
     * @param cityId    城市id， 精确查询
     * @param status    状态，精确查询
     * @param pageNum   页码
     * @param pageSize  每页条数
     * @return SimplePageInfo<ServePrice>
     */
    SimplePageInfo<ServePrice> findPageByServeNameAndConfigNameAndCityIdAndStatus(
            String serveName,
            String configName,
            Long cityId,
            String status,
            Integer pageNum, Integer pageSize);

    /**
     * 根据状态查询数量
     * @param status
     * @return
     */
    long countByStatus(String status);

    /**
     * 根据服务id和优先级 并根据priceId排除 查询
     * @param serveId 服务id
     * @param priority 优先级
     * @param priceId 排除的priceId
     * @return ServePrice
     */
    ServePrice findOneByServeIdAndPriorityExcludeByPriceId(Long serveId, Integer priority, Long priceId);

    /**
     * 根据服务id和优先级 查询
     * @param serveId 服务id
     * @param priority 优先级
     * @return ServePrice
     */
    ServePrice findOneByServeIdAndPriority(Long serveId, Integer priority);

    /**
     * 根据配置名称 并根据priceId排除 获取
     * @param configName 配置名称
     * @param priceId 排除的priceId
     * @return ServePrice
     */
    ServePrice findOneByConfigNameExcludeByPriceId(String configName, Long priceId);

    /**
     * 根据配置名称 获取
     * @param configName 配置名称
     * @return ServePrice
     */
    ServePrice findOneByConfigName(String configName);
}
