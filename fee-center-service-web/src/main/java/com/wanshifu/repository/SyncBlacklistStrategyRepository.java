package com.wanshifu.repository;

import com.wanshifu.fee.center.domain.document.SyncBlacklistStrategy;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface SyncBlacklistStrategyRepository  extends MongoRepository<SyncBlacklistStrategy, ObjectId> {

    SyncBlacklistStrategy findByStrategyId(Long strategyId);

    void deleteByStrategyId(Long strategyId);

    List<SyncBlacklistStrategy> findAllBySceneCode(String sceneCode);
}
