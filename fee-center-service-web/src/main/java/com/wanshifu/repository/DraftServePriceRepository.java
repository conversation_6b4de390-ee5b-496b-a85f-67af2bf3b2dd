package com.wanshifu.repository;

import com.wanshifu.fee.center.domain.document.master2c.DraftServePrice;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2025/7/3 16:02
 */
@Repository
public interface DraftServePriceRepository extends MongoRepository<DraftServePrice, ObjectId> {

    DraftServePrice findDraftServePriceByPriceIdAndDelAndStatusIn(Long priceId, boolean del, Collection<String> status);

    DraftServePrice findOneByDraftId(Long draftId);

    long countByStatusAndDelIsFalse(String status);
}
