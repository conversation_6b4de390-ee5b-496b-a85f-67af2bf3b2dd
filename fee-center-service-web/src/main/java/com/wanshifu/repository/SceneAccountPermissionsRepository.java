package com.wanshifu.repository;

import com.wanshifu.fee.center.domain.document.SceneAccountPermissions;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SceneAccountPermissionsRepository extends MongoRepository<SceneAccountPermissions, ObjectId> {

    List<SceneAccountPermissions> findAllByDelIsFalse();

    SceneAccountPermissions findBySceneCodeIsAndDelIsFalse(String sceneCode);
}
