package com.wanshifu.repository;

import com.wanshifu.fee.center.domain.document.FeeTemplate;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface FeeTemplateRepository extends MongoRepository<FeeTemplate, ObjectId> {

    List<FeeTemplate> findAllByTemplateIdIn(Collection<Long> templateIds);

    List<FeeTemplate> findAllBySceneCodeIsAndGroupInAndDelIsFalse(String sceneCode, Collection<String> groups);

    FeeTemplate findByTemplateId(Long templateId);

    FeeTemplate findOneByTemplateIdAndDelIsFalse(Long templateId);

    List<FeeTemplate> findAllByGroupAndSceneCodeAndDelIsFalse(String group, String sceneCode);

    List<FeeTemplate> findAllBySceneCodeAndDelIsFalse(String sceneCode);

}
