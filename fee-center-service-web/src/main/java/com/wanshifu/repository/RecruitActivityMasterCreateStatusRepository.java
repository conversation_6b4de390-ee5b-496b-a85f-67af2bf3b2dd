package com.wanshifu.repository;

import com.wanshifu.fee.center.domain.document.RecruitActivityMasterCreateStatus;
import org.apache.commons.lang.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.Date;

@Repository
public interface RecruitActivityMasterCreateStatusRepository extends MongoRepository<RecruitActivityMasterCreateStatus, ObjectId> {

    default void saveStatus(String masterId, String recruitId, String buildType, String auditStatus, String failReason) {
        if (StringUtils.isBlank(masterId) || StringUtils.isBlank(recruitId) || StringUtils.isBlank(auditStatus)) {
            return;
        }
        Instant instant = Instant.now();
        Date now = Date.from(instant);
        RecruitActivityMasterCreateStatus result = findByMasterIdAndRecruitId(masterId, recruitId);
        if (result == null) {
            result = new RecruitActivityMasterCreateStatus();
            result.setMasterId(masterId);
            result.setRecruitId(recruitId);
            result.setCreateTime(now);
        }
        result.setBuildType(buildType);
        result.setAuditStatus(auditStatus);
        result.setDel(false);
        result.setStatus(0);
        result.setModifyTime(now);
        result.setAuditTime(now);
        if (StringUtils.isNotBlank(failReason)) {
            result.setFailReason(failReason);
        }
        save(result);
    }

    RecruitActivityMasterCreateStatus findByMasterIdAndRecruitId(String masterId, String recruitId);
}
