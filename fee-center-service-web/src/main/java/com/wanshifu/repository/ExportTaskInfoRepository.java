package com.wanshifu.repository;

import com.wanshifu.fee.center.domain.document.ExportTaskInfo;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ExportTaskInfoRepository extends MongoRepository<ExportTaskInfo, ObjectId> {

    ExportTaskInfo findExportTaskInfoByExportTaskIdAndDelIsFalse(Long exportTaskId);

}
