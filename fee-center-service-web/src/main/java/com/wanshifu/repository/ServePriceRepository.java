package com.wanshifu.repository;

import com.wanshifu.fee.center.domain.document.master2c.ServePrice;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ServePriceRepository extends MongoRepository<ServePrice, ObjectId> {

    ServePrice findOneByPriceId(Long priceId);

    List<ServePrice> findAllByServeIdAndDelIsFalse(Long serveId);
}
