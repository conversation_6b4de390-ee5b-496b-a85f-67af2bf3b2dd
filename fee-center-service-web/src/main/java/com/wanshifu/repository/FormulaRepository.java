package com.wanshifu.repository;

import com.wanshifu.fee.center.domain.document.Formula;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: <PERSON>
 * @create: 2024-02-27 14:36
 * @description: 公式Repository
 */
@Repository
public interface FormulaRepository extends MongoRepository<Formula, ObjectId> {
    Formula findByFormulaId(Long formulaId);

    Formula findByFormulaNameAndDelIsFalse(String formulaName);
    Formula findByFormulaNameAndDelIsFalseAndFormulaIdNot(String formulaName, Long formulaId);

    List<Formula> findAllByDelIsFalseAndFormulaIdIn(List<Long> formulaIds);
}
