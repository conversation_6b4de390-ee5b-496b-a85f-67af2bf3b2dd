<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.wanshifu</groupId>
        <artifactId>fee-center-service</artifactId>
        <version>1.0.51-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fee-center-service-api</artifactId>
    <version>1.0.51-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-api-sdk</artifactId>
            <version>1.0.19</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>5.8.0.M3</version>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-lang</artifactId>
            <version>${wanshfiu-framework-version}</version>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-microservice-cloud-fegin-component</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.wanshifu</groupId>
                    <artifactId>wshifu-framework-lang</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-core</artifactId>
            <version>${wanshfiu-framework-version}</version>
            <scope>provided</scope>
        </dependency>

        <!--   基础平台服务配置    -->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>base-service-config-api</artifactId>
            <version>1.0.65</version>
        </dependency>


<!--        <dependency>-->
<!--            <groupId>org.springframework.data</groupId>-->
<!--            <artifactId>spring-data-mongodb</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-mongodb</artifactId>
<!--           需要exclusive MongoDataAutoConfiguration,MongoAutoConfiguration。不然会自动配置连mongodb-->
        </dependency>

        <!-- https://mvnrepository.com/artifact/io.springfox/springfox-swagger2 -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>3.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>net.bytebuddy</groupId>
                    <artifactId>byte-buddy</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
            <version>1.18.28</version>
        </dependency>
    </dependencies>

    <build>
    </build>

</project>