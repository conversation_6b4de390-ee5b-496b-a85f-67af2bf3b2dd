package com.wanshifu.fee.center.domain.request.template;

import com.wanshifu.fee.center.domain.request.BasePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("获取计价模板sku列表请求体")
public class GetFeeTemplateSkuListReq extends BasePageReq {

    @NotBlank(message = "场景编号不能为空")
    @ApiModelProperty(value = "场景编号", required = true)
    private String sceneCode;

    @ApiModelProperty(value = "服务ID", required = true)
    @NotEmpty(message = "服务id不能为空")
    private List<String> serviceIdList;

    @ApiModelProperty("sku_no")
    private String skuNo;

    @ApiModelProperty("费用类型标签")
    private String feeTypeTag;
}
