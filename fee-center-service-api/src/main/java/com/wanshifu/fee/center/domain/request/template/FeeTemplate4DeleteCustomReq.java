package com.wanshifu.fee.center.domain.request.template;

import com.wanshifu.fee.center.domain.request.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/18 10:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FeeTemplate4DeleteCustomReq extends BasePageReq {

    @ApiModelProperty(value = "场景编码", required = true)
    @NotBlank(message = "场景编码不能为空")
    private String sceneCode;

    @ApiModelProperty(value = "服务类目id")
    private String serviceId;

    @ApiModelProperty(value = "sku_no")
    private String skuNo;

    /**
     * @see com.wanshifu.fee.center.domain.enums.FeeTypeTagEnum
     */
    @ApiModelProperty(value = "费用类型")
    private String feeTypeTag;

    /**
     * @see com.wanshifu.fee.center.domain.enums.FeeSkuTypeEnum
     */
    @ApiModelProperty(value = "sku类型")
    private String skuType;

    @ApiModelProperty(value = "商家id")
    private String customSkuUserId;
}
