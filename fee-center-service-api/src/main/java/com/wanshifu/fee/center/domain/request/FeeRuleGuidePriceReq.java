package com.wanshifu.fee.center.domain.request;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;

@Data
public class FeeRuleGuidePriceReq {
    @NotBlank(message = "场景不能为空")
    private String sceneCode; // com.wanshifu.fee.center.domain.enums.SceneEnum.AUTO_RECEIVE_ORDER_GUIDE_PRICE

    /**
     * 服务品类的服务id
     */
    private List<String> serviceId;

    /**
     * 服务品类的服务类目id，对应老的serveId
     */
    private List<String> serviceCategoryId;

    /**
     * 服务品类的服务模式id，一口价 或者 公开报价 serviceId = serviceCategoryId + serviceModelId
     */
    private String serviceModelId;

    /**
     * 父级地区id列表 ，比如你当前是区级别 需要查省市区三级 则此处需要将【市id、省id】传到这边
     */
    private List<ParentDivisionUnit> parentDivisionUnits;

    /**
     * 当前地区列表的类型 比如 区
     */
    private String divisionType;// com.wanshifu.fee.center.domain.enums.DivisionTypeEnum

    /**
     * 当前地区列表id
     */
    private List<String> divisionIds;

    @Data
    public static class ParentDivisionUnit {
        private String divisionType;
        private String divisionId;
    }

}
