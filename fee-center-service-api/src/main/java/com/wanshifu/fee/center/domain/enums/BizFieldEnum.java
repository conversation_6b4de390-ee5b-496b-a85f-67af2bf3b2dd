package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

import java.util.Objects;


@AllArgsConstructor
public enum BizFieldEnum {
    SERVICE_ID("serviceId", "服务id"),
    SERVICE_CATEGORY_ID("serviceCategoryId", "服务类目id"),
    SERVICE_MODEL_ID("serviceModelId", "服务模式id"),
    DIVISION_TYPE("divisionType", "区域维度类型"),
    ;

    public final String name;
    public final String description;

    public static BizFieldEnum fromName(String name) {
        if (Objects.isNull(name)) {
            return null;
        }
        for (BizFieldEnum value : BizFieldEnum.values()) {
            if (value.name.equals(name)) {
                return value;
            }
        }
        return null;
    }
}
