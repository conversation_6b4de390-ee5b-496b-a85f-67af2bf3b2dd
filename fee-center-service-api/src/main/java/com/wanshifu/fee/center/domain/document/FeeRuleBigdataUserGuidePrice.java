package com.wanshifu.fee.center.domain.document;

import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Id;
import java.math.BigDecimal;

@Document
@Data
@FieldNameConstants
public class FeeRuleBigdataUserGuidePrice {

    @Id
    private String id;

    private Long serviceId;
    private String serviceName;

    private Long serviceCategoryId;

    private Long templateId;
    private String skuNo;
    private String skuType;

    private String divisionType;
    private Long provinceId;
    private String provinceName;
    private Long cityId;
    private String cityName;
    private Long districtId;
    private String districtName;
    private Long streetId;
    private String streetName;

    private String bizTag;

    private Long userId;

    private Double masterInputPrice;
}
