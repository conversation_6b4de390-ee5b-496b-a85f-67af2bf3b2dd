package com.wanshifu.fee.center.api;


import com.wanshifu.fee.center.config.FeeCenterEncoder;
import com.wanshifu.fee.center.domain.document.BizRuleMapping;
import com.wanshifu.fee.center.domain.request.BizRuleMappingBatchQueryReq;
import com.wanshifu.fee.center.domain.request.BizRuleMappingConfigureReq;
import com.wanshifu.fee.center.domain.request.BizRuleMappingDelReq;
import com.wanshifu.fee.center.domain.request.BizRuleMappingQueryReq;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * <AUTHOR>
 */
@FeignClient(value = "fee-center-service", path = "bizRuleMapping", configuration = {DefaultDecoder.class, FeeCenterEncoder.class, DefaultErrorDecode.class}, url = "${wanshifu.fee-center-service.url}")
@Deprecated
public interface BizRuleMappingApi {

    @PostMapping("query")
    SimplePageInfo<BizRuleMapping> query(@RequestBody @Validated BizRuleMappingQueryReq bizRuleMappingQueryReq);

    @PostMapping("batchQuery")
    SimplePageInfo<BizRuleMapping> batchQuery(@RequestBody @Validated BizRuleMappingBatchQueryReq bizRuleMappingBatchQueryReq);

    @PostMapping("del")
    BizRuleMapping del(@RequestBody @Validated BizRuleMappingDelReq bizRuleMappingDelReq);

    @PostMapping("configure")
    BizRuleMapping configure(@RequestBody @Validated BizRuleMappingConfigureReq bizRuleMappingConfigureReq);

}
