package com.wanshifu.fee.center.domain.dto;

import com.wanshifu.fee.center.domain.enums.DivisionTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class AddressInfo {

    @NotNull(message = "divisionId不能为空")
    private Long divisionId;

    private Long divisionCode;

    private Integer divisionLevel;

    private String pinyin;

    private String divisionName;

    private String divisionTname;

    private String divisionAbbName;

    private Integer isDelete;

    private Integer isLast;

    private Long lv1DivisionId;

    private String lv1DivisionName;

    private Long lv2DivisionId;

    private String lv2DivisionName;

    private Long lv3DivisionId;

    private String lv3DivisionName;

    private Long lv4DivisionId;

    private String lv4DivisionName;

    private Long lv5DivisionId;

    private String lv5DivisionName;

    private Long parentId;
    private Integer dataSource;

    private Long cainiaoDivisionId;

    // 查询最低一级地址，比如当前场景只需要从区开始查，而这个地址对象是一个街道的地址对象。
    private String divisionType = DivisionTypeEnum.DISTRICT.code;

    // 地址是否需要向上查找
    private boolean needSearchParent = false;
}
