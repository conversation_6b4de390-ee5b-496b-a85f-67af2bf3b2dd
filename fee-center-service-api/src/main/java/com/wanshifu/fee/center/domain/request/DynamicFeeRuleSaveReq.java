package com.wanshifu.fee.center.domain.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.wanshifu.fee.center.domain.document.DynamicCalculateRuleData;
import com.wanshifu.fee.center.domain.document.DynamicPricingIndicator;
import com.wanshifu.fee.center.domain.document.ServiceData;
import com.wanshifu.fee.center.domain.request.common.Create;
import com.wanshifu.fee.center.domain.request.common.Modify;
import com.wanshifu.framework.core.validation.annotation.ValueIn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.Date;
import java.util.List;

/**
 * @author: <PERSON>
 * @create: 2023-09-12 14:24
 * @description: 计价规则（草稿）列表request body
 */
@Data
@ApiModel("动态计价规则保存request body")
public class DynamicFeeRuleSaveReq {

    @JSONField(serializeUsing = ToStringSerializer.class)
    @NotNull(message = "dynamicFeeRuleId不能为空", groups = {Modify.class})
    private Long dynamicFeeRuleId;

    /**
     * @see com.wanshifu.fee.center.domain.enums.AdjustPriceUnitEnum
     */
    @ApiModelProperty(value = "调价单元", required = true)
    @NotBlank(message = "调价单元不能为空(按服务:service，按sku:sku)", groups = {Create.class})
    @ValueIn("service,sku")
    private String adjustPriceUnit;

    @ApiModelProperty(value = "价格场景", required = true)
    @NotBlank(message = "价格场景不能为空", groups = {Create.class})
    private String sceneCode;

    @ApiModelProperty("子场景编码")
    @Pattern(regexp = "^[A-Za-z0-9_]{6,}$", message = "sceneCode只能由英文、数字或下划线组成，且至少6位")
    private String subSceneCode;

    @NotBlank(message = "请输入正确的策略名称", groups = {Create.class})
    @Length(min = 1, max = 30, message = "请输入正确的策略名称", groups = {Create.class})
    private String strategyName;

    @NotNull(message = "startTime不能为空", groups = {Create.class})
    private Date startTime;

    @NotNull(message = "endTime不能为空", groups = {Create.class})
    private Date endTime;


    private String divisionType;

    private List<Long> divisionIds;

    private List<Long> userIds;

    @NotNull(message = "服务不能为空", groups = {Create.class})
    private List<ServiceData> serviceDataList;


    private String feeTypeTag;

    private String operator;

    /**
     * 备注
     */
    private String note;

    private DynamicCalculateRuleData dynamicCalculateRuleData;

    @ApiModelProperty("模板id（计价sku）")
    private List<String> templateIds;

    @ApiModelProperty("业务标识")
    @Length(max = 30, message = "业务标识长度不能超过30")
    private String bizTag;

    @ApiModelProperty("动态调价指标")
    private List<DynamicPricingIndicator> dynamicPricingIndicators;

}
