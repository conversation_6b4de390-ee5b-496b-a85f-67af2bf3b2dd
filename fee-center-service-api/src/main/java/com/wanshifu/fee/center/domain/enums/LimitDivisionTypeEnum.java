package com.wanshifu.fee.center.domain.enums;

import java.util.Objects;

public enum LimitDivisionTypeEnum {

    COUNTRY("country", "无限制"),
    CITY("city", "城市"),
    DISTRICT("district", "区县");

    public final String code;
    public final String name;

    LimitDivisionTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static LimitDivisionTypeEnum fromCode(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (LimitDivisionTypeEnum value : LimitDivisionTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
