package com.wanshifu.fee.center.domain.document;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.Map;

@Document
@Data
@FieldNameConstants
@Deprecated
public class BizRuleMapping extends BaseDocument {
    @Indexed(unique = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long bizRuleMappingId;
    @Indexed(background = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long templateId;
    private Integer templateVersion;
    @Indexed(background = true)
    private String sceneCode;
    // 在算费时需要进行组完整性校验
    @Indexed(background = true)
    private String group;
    @Indexed(background = true)
    private String sceneName;

    /**
     * 应用类型
     * @see com.wanshifu.fee.center.domain.enums.ApplyTypeEnum
     */
    private List<String> applyTypes;

//    // 映射场景code
//    @Indexed(background = true)
//    private String toSceneCode;
//
//    // 映射场景name
//    @Indexed(background = true)
//    private String toSceneName;
//
//    // 映射定价ID（基础服务计价属性 没有定价ID）
//    @Indexed(background = true)
//    @JSONField(serializeUsing = ToStringSerializer.class)
//    private Long toTemplateId;

    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private Map<String, String> fromBizRule;

    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private Map<String, String> toBizRule;

}
