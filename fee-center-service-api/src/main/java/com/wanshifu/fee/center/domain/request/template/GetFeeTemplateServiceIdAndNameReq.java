package com.wanshifu.fee.center.domain.request.template;

import com.wanshifu.fee.center.domain.request.BasePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("获取计价模板服务id和名称请求体")
public class GetFeeTemplateServiceIdAndNameReq extends BasePageReq {

    @ApiModelProperty(value = "场景编号", required = true)
    @NotBlank(message = "场景编号不能为空")
    private String sceneCode;

    @ApiModelProperty(value = "服务名称", required = true)
    @NotEmpty(message = "服务id不能为空")
    private List<String> serviceIdList;
}
