//package com.wanshifu.fee.center.domain.document;
//
//import lombok.Data;
//import lombok.NoArgsConstructor;
//import lombok.experimental.FieldNameConstants;
//import org.springframework.data.mongodb.core.index.Indexed;
//import org.springframework.data.mongodb.core.mapping.Document;
//
//import javax.persistence.Id;
//import java.util.Date;
//
//@Document
//@Data
//@FieldNameConstants
//@NoArgsConstructor
//public class FeeRuleFromBigdataToBeAdd {
//
//    @Id
//    private String id;
//
//    @Indexed(unique = true)
//    private Long feeRuleId;
//
//    private String sceneCode;
//
//    /**
//     * 同步大数据时的版本号
//     */
//    private String versionNo;
//
//    private Date createTime;
//
//    public FeeRuleFromBigdataToBeAdd(Long feeRuleId, String sceneCode, String versionNo, Date createTime) {
//        this.feeRuleId = feeRuleId;
//        this.sceneCode = sceneCode;
//        this.versionNo = versionNo;
//        this.createTime = createTime;
//    }
//
//}
