package com.wanshifu.fee.center.domain.request.template;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: <PERSON>
 * @create: 2024-02-21 10:12
 * @description: 复制计价属性response body
 */
@Data
@ApiModel("复制计价属性response body")
public class CopyPricingAttributeResp {

    @ApiModelProperty("成功数量")
    private int successCount;

    @ApiModelProperty("失败数量")
    private int failureCount;
}
