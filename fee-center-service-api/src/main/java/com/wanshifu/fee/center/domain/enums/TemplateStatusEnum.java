package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

import java.util.Objects;

@AllArgsConstructor
public enum TemplateStatusEnum {
    /**
     * 业务端可用
     */
    ACTIVE(0, "激活"),
    /**
     * 锁定态业务端不可用，管理后台可用
     */
    LOCK(1, "锁定");

    public final int code;
    public final String name;

    public static TemplateStatusEnum fromCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (TemplateStatusEnum value : TemplateStatusEnum.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
