package com.wanshifu.fee.center.domain.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

@EqualsAndHashCode(callSuper = true)
@Data
@FieldNameConstants
public class FeeBizRuleDTO extends TemplateBizRuleDTO {

    protected String bizTag;

    protected String masterId;
    protected String userId;
    protected String bizId;

    protected String divisionType;
    protected String divisionTypeName;

    protected String level1DivisionId;
    protected String level2DivisionId;
    protected String level3DivisionId;
    protected String level4DivisionId;
    protected String province;
    protected String city;
    protected String district;
    protected String street;

    protected String goodsCategoryId;
    protected String goodsCategoryName;

    // sku单价（取名为历史原因）
    protected String masterInputPrice;
}
