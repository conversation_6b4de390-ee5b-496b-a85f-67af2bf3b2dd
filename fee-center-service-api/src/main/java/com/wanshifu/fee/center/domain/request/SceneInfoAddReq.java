package com.wanshifu.fee.center.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class SceneInfoAddReq extends SceneInfoSaveReq {

    @NotBlank
    private String sceneName;

    @NotBlank
    private String sceneCode;
    // 对接系统
    private List<String> integrationSys;

    // 适用业务
    @NotBlank
    private String applicableBiz;

    // 企业标准一口价、企业报价招标
    @NotBlank
    private String skuType;

    @NotBlank
    private String priceType;

    @NotBlank
    private String bizIdType;

    private List<String> orderRange;

    @NotBlank
    private String amountType;

    // 价格维护类型
    @NotNull
    private Integer priceMaintain;

    private Boolean haveBasePrice;

    private String note;

    @ApiModelProperty(value = "是否映射计价")
    private Boolean isMappingPricing = false;

    @ApiModelProperty(value = "是否需要做 计价模板与计价规则的完整性校验")
    private Boolean needIntegrityValidation = true;

    @ApiModelProperty("地区维度")
    private String divisionType;

    @ApiModelProperty("地区是否需要向上查找")
    private Boolean needSearchParent;


}
