package com.wanshifu.fee.center.domain.document;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.wanshifu.fee.center.domain.dto.ServiceDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;
import java.util.List;

@ApiModel(description = "标准计价日志")
@EqualsAndHashCode(callSuper = true)
@Data
@Document
@FieldNameConstants
public class StandardPricingLog extends BaseDocument {

    @ApiModelProperty(value = "标准计价任务id")
    @Indexed(unique = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long tid;

    @ApiModelProperty(value = "订单id")
    @Indexed(background = true, sparse = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long orderId;

    @ApiModelProperty(value = "订单号")
    @Indexed(background = true, sparse = true)
    private String orderNo;

    @ApiModelProperty(value = "全局订单id")
    @Indexed(background = true, sparse = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long globalOrderTraceId;

    @ApiModelProperty(value = "场景编码列表")
    private List<String> sceneCodeList;

    @ApiModelProperty(value = "标准总价")
    private BigDecimal cost;

    @ApiModelProperty(value = "标准总价最大价")
    private BigDecimal costMax;

    @ApiModelProperty(value = "动态总价")
    private BigDecimal dynamicFeeCost;

    @ApiModelProperty(value = "动态总价最大价")
    private BigDecimal dynamicFeeCostMax;

    @ApiModelProperty(value = "起步价")
    private BigDecimal basePrice;

    @ApiModelProperty(value = "最小总价")
    private BigDecimal minCost;

    @ApiModelProperty(value = "最大总价")
    private BigDecimal maxCost;

    private boolean success = false;

    @ApiModelProperty(value = "服务信息（入参）")
    private List<ServiceDTO> serviceDtoList;

    @ApiModelProperty(value = "场景计价结果")
    private List<ServiceResultLog> serviceResultLogList;

//    @ApiModel(value = "场景计价结果")
//    @Data
//    public static class SceneResultLog {
//
//        @ApiModelProperty(value = "是否成功")
//        private Boolean success = true;
//
//        @ApiModelProperty(value = "场景编码")
//        private String sceneCode;
//
//        @ApiModelProperty(value = "场景名称")
//        private String sceneName;
//
//        @ApiModelProperty(value = "场景标准总价")
//        private BigDecimal cost;
//
//        @ApiModelProperty(value = "场景动态总价")
//        private BigDecimal dynamicFeeCost;
//
//        @ApiModelProperty(value = "场景最小总价")
//        private BigDecimal minCost;
//
//        @ApiModelProperty(value = "场景最大总价")
//        private BigDecimal maxCost;
//
//        @ApiModelProperty(value = "服务计价结果")
//        private List<ServiceResultLog> serviceResultLogList;
//
//        private String errorInfo;
//    }

    @ApiModel(value = "服务计价结果")
    @Data
    public static class ServiceResultLog {

        @ApiModelProperty("是否成功")
        private Boolean isSuccess = false;

        @ApiModelProperty("服务id")
        private String serviceId;

        @ApiModelProperty("服务名称")
        private String serviceName;

        @ApiModelProperty("场景编码")
        private String sceneCode;

        @ApiModelProperty("场景名称")
        private String sceneName;

        @ApiModelProperty("服务标准总价")
        private BigDecimal cost;

        @ApiModelProperty("服务动态总价")
        private BigDecimal dynamicFeeCost;

        @ApiModelProperty("服务最小总价")
        private BigDecimal minCost;

        @ApiModelProperty("服务最大总价")
        private BigDecimal maxCost;

        @ApiModelProperty("服务计价规则结果")
        private List<FeeRuleLog> feeRuleLogList;

        @ApiModelProperty("错误信息")
        private String errorInfo;
    }

    @ApiModel(value = "命中的计价规则结果")
    @Data
    public static class FeeRuleLog {

        @ApiModelProperty("模板id")
        private Long templateId;

        @ApiModelProperty("计价规则id")
        private Long feeRuleId;

        @ApiModelProperty("skuNo")
        private String skuNo;

        @ApiModelProperty("sku属性路径名称")
        private String skuAttributePathName;

        @ApiModelProperty("sku计价数量路径编号")
        private String skuNumberPathNo;

        @ApiModelProperty("sku计价数量路径名称")
        private String skuNumberName;

        @ApiModelProperty("费用展示名称")
        private String attributeDisplayName;

        @ApiModelProperty("单位")
        private String feeUnit;

        @ApiModelProperty("费用名称")
        private String feeName;

        /**
         * @see com.wanshifu.fee.center.domain.enums.FeeSkuTypeEnum
         */
        @ApiModelProperty("sku类型（standardSku-标准sku；customSku-自定义sku）")
        private String skuType;

        /**
         * @see com.wanshifu.fee.center.domain.enums.FeeTypeTagEnum
         */
        @ApiModelProperty("费用类型标签（serviceFee-服务费；goodSurcharge-商品附加费；standardSurcharge-标准附加费）")
        private String feeTypeTag;

        @ApiModelProperty("费用类型标签名称")
        private String feeTypeTagName;

        @ApiModelProperty("单价")
        private String price;

        @ApiModelProperty("属性值-最小值")
        private String attributeValueMin;

        @ApiModelProperty("属性值-最大值")
        private String attributeValueMax;

        @ApiModelProperty("计算表达式")
        private String express;
    }
}
