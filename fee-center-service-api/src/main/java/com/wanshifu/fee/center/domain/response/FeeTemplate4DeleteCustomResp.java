package com.wanshifu.fee.center.domain.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/18 10:18
 */
@Data
public class FeeTemplate4DeleteCustomResp {

    @JSONField(serializeUsing = ToStringSerializer.class)
    @ApiModelProperty(value = "模板id")
    private Long templateId;

    @ApiModelProperty(value = "费用名称")
    private String attributeDisplayName;

    @ApiModelProperty(value = "sku_no")
    private String skuNo;

    @ApiModelProperty(value = "费用类型")
    private String feeTypeTag;

    @ApiModelProperty(value = "sku类型")
    private String skuType;

    @ApiModelProperty(value = "服务名称")
    private String serviceName;


    @ApiModelProperty(value = "商家id")
    private String customSkuUserId;
}
