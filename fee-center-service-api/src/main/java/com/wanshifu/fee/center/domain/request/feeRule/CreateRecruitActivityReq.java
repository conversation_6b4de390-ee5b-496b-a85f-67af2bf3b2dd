package com.wanshifu.fee.center.domain.request.feeRule;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

@Data
@ApiModel("创建招募")
public class CreateRecruitActivityReq {

    @ApiModelProperty(value = "招募ID，FeeRule表中的bizRule字段下的bizTag", required = true)
    @NotBlank(message = "招募ID不能为空")
    private String recruitId;

    @ApiModelProperty(value = "区域维度", required = true)
    @NotBlank(message = "区域维度不能为空")
    @ValueIn(value = "country,city,district", message = "地区维度不合法")
    private String divisionType;

    @ApiModelProperty(value = "区域ID列表, 如果不是全国，则必填")
    private List<String> divisionIds;

    @ApiModelProperty(value = "服务类目ID列表", required = true)
    @NotEmpty(message = "服务类目ID列表不能为空")
    private List<String> serviceCategoryIdList;
}
