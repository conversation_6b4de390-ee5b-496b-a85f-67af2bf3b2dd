package com.wanshifu.fee.center.domain.document;


import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;


/**
 * 导入任务
 */
@Document
@Data
@FieldNameConstants
public class BatchTaskInfo extends BaseDocument {

    @Indexed(unique = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long batchTaskId;

    private String sceneCode;

    private String sceneName;

    private String bizId;

    private String divisionType;

    private String divisionTypeName;

    private String fileName;

    private Long fileId;

    private String fileUrl;

    // 总数
    private Integer count;


    private Integer successCount;

    private Integer errorCount;

    private String operator;

    private String errorFileUrl;

    // 直接整正式库 不入草稿，不需要审核
    private boolean directFeeRule = false;

}
