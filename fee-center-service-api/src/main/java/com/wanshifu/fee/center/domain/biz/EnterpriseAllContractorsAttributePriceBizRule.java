package com.wanshifu.fee.center.domain.biz;

import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class EnterpriseAllContractorsAttributePriceBizRule {
    private String level4DivisionId;
    private String templateId;
    private String serviceModelId;
    private String attributeValueMax;
    private String pricingMode;
    private String feeInputType;
    /**
     * 一级商品类目，比如 家具
     */
    private String lv1goodsCategoryId;
    private String lv1goodsCategoryName;
    /**
     * 二级商品类目 比如 桌子
     */
    private String lv2goodsCategoryId;
    private String lv2goodsCategoryName;

    /**
     * 三级商品类目 比如实木桌
     */
    private String goodsCategoryId;

    private String goodsCategoryName;
    private String sceneCode;
    private String feeTypeTagName;
    private String price;
    private String skuAttributePathName;
    private String sceneId;
    private String serviceTypeId;
    private String serviceId;
    private String skuNumberPathNo;
    /**
     * 一级服务类目 比如家具安装
     */
    private String lv1serviceCategoryId;
    /**
     * 二级服务类目 比如 木桌安装
     */
    private String lv2serviceCategoryId;

    /**
     * 三级服务类目 比如 实木桌安装
     */
    private String serviceCategoryId;
    private String group;
    private String level2DivisionId;
    private String skuNumberName;
    /**
     * @see FeeTypeTageEnum
     */
    private String feeTypeTag;
    private String attributeValueMin;
    private String feeUnit;
    private String skuNumberExpress;
    private String sceneName;
    private String serviceName;
    private String skuType;
    private String masterInputPrice;
    private String attributeDisplayName;
    private String masterId;
    private String level1DivisionId;
    private String feeName;
    private String skuNo;
    private String level3DivisionId;
    private String serviceTypeName;
    private String skuNoIndex;


    private String divisionType;
    private String divisionTypeName;

    private String province;

    private String city;

    private String district;

    private String street;

    private String bizTag;

    private String applyFlag;

}
