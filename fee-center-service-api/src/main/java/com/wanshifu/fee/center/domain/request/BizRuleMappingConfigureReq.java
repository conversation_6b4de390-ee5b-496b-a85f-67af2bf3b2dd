package com.wanshifu.fee.center.domain.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.Map;

@ApiModel("算费模板映射配置request body")
@Data
public class BizRuleMappingConfigureReq {
    private Long bizRuleMappingId;

    @NotNull
    private Long templateId;
    private Long templateVersion;
    @NotBlank
    private String sceneCode;
    // 在算费时需要进行组完整性校验
    private String group;
    private String sceneName;

    @NotEmpty
    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private Map<String, String> fromBizRule;
    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private Map<String, String> toBizRule;

}
