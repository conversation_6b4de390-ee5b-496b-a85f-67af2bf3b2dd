package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum OrderRangeEnum {
    // 企业平台一口价、企业总包一口价、企业报价招标、企业总包报价
    USER_DEFINITE_PRICE("user_definite_price", "企业平台一口价"),
    USER_ENTERPRISE_DEFINITE_PRICE("user_enterprise_definite_price", "企业总包一口价"),
    USER_OFFER_PRICE("user_offer_price", "企业报价招标"),
    USER_ENTERPRISE_OFFER_PRICE("user_enterprise_offer_price", "企业总包报价"),

    ;
    public final String code;
    public final String name;

    public static OrderRangeEnum fromCode(String code) {
        for (OrderRangeEnum e : OrderRangeEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
}
