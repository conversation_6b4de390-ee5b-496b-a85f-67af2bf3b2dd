package com.wanshifu.fee.center.domain.request;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * @author: <PERSON>
 * @create: 2023-09-08 17:13
 * @description: 计费规则（草稿）批量操作 request body
 */
@Data
public class FeeRuleBatchOperationReq {

    /**
     * 场景编码（即价格模式编号）
     */
    @NotBlank
    private String sceneCode;

    @NotBlank
    private String serviceId;

    /**
     * 价格维度
     */
    @NotBlank
    private String divisionType;

    /**
     * 业务id
     */
    private String bizId;


}
