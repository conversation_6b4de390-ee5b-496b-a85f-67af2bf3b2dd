package com.wanshifu.fee.center.domain.dto;

import com.wanshifu.framework.utils.CollectionUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ServicePriceDto {

    private String serviceId;
    private BigDecimal price;

    private List<SkuPriceDto> skuPriceList;

    public int getSkuPriceListSize() {
        if (CollectionUtils.isEmpty(skuPriceList)) {
            return 0;
        }
        return skuPriceList.size();
    }

    @Data
    public static class SkuPriceDto {

        private String skuNo;
    }

}
