package com.wanshifu.fee.center.domain.request.template;

import com.wanshifu.fee.center.domain.document.CalculateRuleData;
import com.wanshifu.fee.center.domain.dto.TemplateBizRuleDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@ApiModel("获取模板分页列表response body")
@Data
public class TemplatePageListResponse extends TemplateBizRuleDTO {

    @ApiModelProperty("场景编码")
    private String sceneCode;

    @ApiModelProperty("计价模板id")
    private Long templateId;

    @ApiModelProperty("计算规则")
    private CalculateRuleData calculateRuleData;
}
