package com.wanshifu.fee.center.domain.enums;

import java.util.Objects;


public enum DynamicSymbolEnum {

    INCREASE("increase", "涨价", "+", 1),
    DECREASE("decrease", "降价", "-", -1),
    ;

    public final String code;
    public final String name;
    public final String symbol;
    private final int multiplier;

    DynamicSymbolEnum(String code, String name, String symbol, int multiplier) {
        this.code = code;
        this.name = name;
        this.symbol = symbol;
        this.multiplier = multiplier;
    }

    public static DynamicSymbolEnum fromCode(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (DynamicSymbolEnum value : DynamicSymbolEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public Double getAdjustPrice(Double delta) {
        return multiplier * delta;
    }
}
