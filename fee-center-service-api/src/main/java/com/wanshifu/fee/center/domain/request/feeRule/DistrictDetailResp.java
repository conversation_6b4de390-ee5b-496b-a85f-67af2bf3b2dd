package com.wanshifu.fee.center.domain.request.feeRule;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("区域详情response body")
public class DistrictDetailResp {

    @ApiModelProperty("城市名称")
    private String cityName;

    @ApiModelProperty("区县/街道详情列表")
    private List<DistrictDetail> districtDetailList;

    @Data
    public static class DistrictDetail {

        @ApiModelProperty("区县名称")
        private String districtName;

        @ApiModelProperty("街道/乡镇名称列表")
        private List<String> streetNameList;
    }
}
