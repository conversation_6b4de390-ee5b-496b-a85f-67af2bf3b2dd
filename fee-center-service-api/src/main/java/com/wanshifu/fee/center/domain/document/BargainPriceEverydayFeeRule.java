package com.wanshifu.fee.center.domain.document;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wanshifu.fee.center.serializer.BigDecimalToDecimal128Serializer;
import com.wanshifu.fee.center.serializer.Decimal128ToBigDecimalDeserializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Document
@FieldNameConstants
public class BargainPriceEverydayFeeRule extends BaseDocument{

    private String sceneCode;
    
    @Indexed(background = true)
    private String serviceId;

    @Indexed(background = true)
    private String skuNo;

    private String attributeDisplayName;

    private String attributeDisplayNamePinYin;

//    @JsonSerialize(using = BigDecimalToDecimal128Serializer.class)
//    @JsonDeserialize(using = Decimal128ToBigDecimalDeserializer.class)
    private Double originPrice;

    // 折扣后的价格，可用于排序
    @Indexed(background = true)
//    @JsonSerialize(using = BigDecimalToDecimal128Serializer.class)
//    @JsonDeserialize(using = Decimal128ToBigDecimalDeserializer.class)
    private Double discountPrice;

//    @JsonSerialize(using = BigDecimalToDecimal128Serializer.class)
//    @JsonDeserialize(using = Decimal128ToBigDecimalDeserializer.class)
    private Double savingPrice;

    private String level1DivisionId;

    private String provinceName;

    private String provinceNamePinYin;

    private String level2DivisionId;

    private String cityName;

    private String cityNamePinYin;

    private Integer districtCount;

    private Integer streetCount;

    private String lastVersionNo;

    @ApiModelProperty("区县/街道详情列表")
    private List<BargainPriceEverydayFeeRule.DistrictDetail> districtDetailList;

    @Data
    public static class DistrictDetail {

        @ApiModelProperty("区县名称")
        private String districtName;

        @ApiModelProperty("街道/乡镇名称列表")
        private List<String> streetNameList;
    }
}
