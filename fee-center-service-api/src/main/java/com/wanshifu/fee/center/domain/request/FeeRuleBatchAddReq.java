package com.wanshifu.fee.center.domain.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.wanshifu.fee.center.domain.document.CalculateRuleData;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;
import java.util.Map;

@Data
public class FeeRuleBatchAddReq {

    @NotEmpty
    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private List<FeeRuleData> feeRuleDataList;

    @Data
    public static class FeeRuleData {
        private Long templateId;
        private int templateVersion;
        private String sceneCode;
        // 在算费时需要进行组完整性校验
        private String group;
        private String sceneName;
        private String feeName;
        @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
        private Map<String, String> bizRule;

        private CalculateRuleData calculateRuleData;
    }

}
