package com.wanshifu.fee.center.domain.document.master2c;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanshifu.fee.center.domain.enums.master2c.ServePriceStatusEnum;
import com.wanshifu.fee.center.domain.enums.MasterPrice2CActivationMethodEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Id;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@EqualsAndHashCode
@Document
@Data
@FieldNameConstants
public class ServePrice {

    @Id
    private String id;

    @Indexed(unique = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long priceId;

    private String configName;

    @Indexed(background = true)
    private Long serveId;

    private String serveName;

    private Long level1ServeId;

    private String level1ServeName;

    private Long level2ServeId;

    private String level2ServeName;

    private Long level3ServeId;

    private String level3ServeName;

    private Integer priority;

    /**
     * 服务基础价格
     */
    private BigDecimal masterBasePrice;

    private List<ServeConfig> serveConfigs;

    private List<Long> cityIds;

    private CityGroup cityGroup;

    /**
     * 生效方式
     *
     * @see MasterPrice2CActivationMethodEnum
     */
    private String activationMethod;

    /**
     * @see ServePriceStatusEnum
     */
    private String status;

    /**
     * 开始生效时间
     */
    private Date startTime;

    /**
     * 结束生效时间
     */
//    private Date endTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改人
     */
    private String updateBy;

    private boolean del = false;


    @Data
    @FieldNameConstants
    public static class CityGroup {

        // 城市群组分类id
        private String classifyId;

        // 城市群组id
        private Long cityGroupId;

        // 城市id列表
        private List<Long> cityIds;
    }

    @Data
    public static class ServeConfig {

        private Long serveConfigId;

        private BigDecimal masterPrice;

        @ApiModelProperty(value = "关联参数id")
        private Long relationId;

        // 选项
        private List<Option> options;

        // 附加选项
        private List<Option> attaches;

        @Data
        public static class Option {

            private String externalId;

            private BigDecimal masterPrice;

            private List<Option> relationOptionList;
        }
    }


}
