package com.wanshifu.fee.center.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(value = "订单基础信息")
@Data
public class OrderBase {

    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "全局订单id")
    private Long globalOrderTraceId;

    @ApiModelProperty(value = "订单创建时间")
    private Date createTime;
}
