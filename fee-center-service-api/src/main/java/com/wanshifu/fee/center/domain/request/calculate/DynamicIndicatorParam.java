package com.wanshifu.fee.center.domain.request.calculate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

@Data
@ApiModel("动态指标参数")
public class DynamicIndicatorParam {

    /**
     * @see com.wanshifu.fee.center.domain.enums.DynamicParamKeyEnum
     */
    @NotBlank(message = "动态入参键不能为空")
    @ApiModelProperty("动态入参键，由计价服务定义")
    private String key;

    @NotBlank(message = "动态入参值不能为空")
    @ApiModelProperty("动态入参值，由调用方传入")
    private String value;

}