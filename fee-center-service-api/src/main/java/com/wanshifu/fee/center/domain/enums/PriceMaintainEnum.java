package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum PriceMaintainEnum {
    // 中台维护价格支持/不支持，默认不选中
    CENTER(0, "中台维护价格"),
    BIZ(1, "业务维护价格"),
    ;
    public final Integer code;
    public final String name;

    public static PriceMaintainEnum fromCode(Integer code) {
        for (PriceMaintainEnum e : PriceMaintainEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
}
