package com.wanshifu.fee.center.domain.biz;

import lombok.Data;
import lombok.experimental.FieldNameConstants;

/**
 * @author: <PERSON>
 * @create: 2024-03-06 16:16
 * @description: 常用bizRule
 */
@Data
@FieldNameConstants
public class CommonBizRule {
    private String feeTypeTag;
    private String serviceId;
    private String serviceName;
    private String group;
    private String serviceCategoryId;
    private String bizId;
    private String bizType;
    private String bizTag;
    private String skuNo;
    private String skuNumberPathNo;
    private String masterId;
    private String userId;
    private String skuType;
    private String attributeDisplayName;
    private String feeTypeTagName;
    private String templateId;

    // 起步价
    private String basePrice;
    private String divisionType;
    private String divisionTypeName;
    private String masterInputPrice;
    private String masterInputPriceMax;
    private String feeUnit;
    private String feeName;
    private String applyFlag;
    private String skuNumberName;
    private String skuAttributePathName;


    private String level1DivisionId;
    private String level2DivisionId;
    private String level3DivisionId;
    private String level4DivisionId;
    private String province;
    private String city;
    private String district;
    private String street;

    private String goodsCategoryId;
    private String goodsCategoryName;
    private String level1GoodsCategoryId;
    private String level2GoodsCategoryId;
    private String level3GoodsCategoryId;
    private String level1GoodsCategoryName;
    private String level2GoodsCategoryName;
    private String level3GoodsCategoryName;
    private String serviceTypeId;
    private String serviceTypeName;
    private String serviceModelId;

    private String sceneCode;
    private String sceneName;

    // 自定义SKU
    // 自定义SKU最小值
    private String attributeValueMin;
    // 自定义SKU最大值
    private String attributeValueMax;
    // 自定义SKU固定值
    private String attributeValueFixed;
    // 自定义SKU企业用户id
    private String customSkuUserId;
    private String matchSkuType;
    private String matchSkuNo;
    /**
     * 自定义SKU value值类型
     *
     * @see com.wanshifu.fee.center.domain.enums.CustomSkuValueTypeEnum
     */
    private String customSkuValueType;

    /**
     * 大数据同步自定义sku标记
     */
    private String pullCustomSkuCurrentVersion;

}

