package com.wanshifu.fee.center.domain.enums;

import com.wanshifu.framework.utils.StringUtils;
import lombok.Getter;

@Getter
public enum SameDayPriceConfigStateEnum {

    ACTIVE("active", "开通"),

    INACTIVE("inactive", "关闭");

    private final String code;

    private final String desc;

    SameDayPriceConfigStateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SameDayPriceConfigStateEnum fromCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (SameDayPriceConfigStateEnum state : SameDayPriceConfigStateEnum.values()) {
            if (state.code.equals(code)) {
                return state;
            }
        }
        return null;
    }
}
