package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

import java.util.Objects;

@AllArgsConstructor
public enum BatchTaskStatusEnum {
    /**
     * 导入草稿
     */
    SUCCESS(0, "导入完成"),
    /**
     * task已入库
     */
    IMPORT(1, "导入中"),
    /**
     * 已拒绝
     */
    FAIL(2, "导入失败");

    public final int code;
    public final String name;

    public static BatchTaskStatusEnum fromCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (BatchTaskStatusEnum value : BatchTaskStatusEnum.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
