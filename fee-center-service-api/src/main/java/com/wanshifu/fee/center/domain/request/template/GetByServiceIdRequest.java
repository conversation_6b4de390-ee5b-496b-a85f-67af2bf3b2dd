package com.wanshifu.fee.center.domain.request.template;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

@Data
@ApiModel("通过serviceId获取计价模板分页列表request body")
public class GetByServiceIdRequest {

    @ApiModelProperty(value = "场景编码", required = true)
    @NotBlank(message = "场景编码不能为空")
    private String sceneCode;

    @ApiModelProperty(value = "服务id", required = true)
    @NotBlank(message = "服务id不能为空")
    private String serviceId;


}
