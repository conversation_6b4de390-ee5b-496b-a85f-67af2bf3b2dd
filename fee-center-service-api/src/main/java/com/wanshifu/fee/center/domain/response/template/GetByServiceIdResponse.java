package com.wanshifu.fee.center.domain.response.template;

import com.wanshifu.fee.center.domain.document.FeeTemplate;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("通过serviceId获取计价模板分页列表response body")
public class GetByServiceIdResponse {

    @ApiModelProperty("计价模板")
    private FeeTemplate feeTemplate;

    /**
     * @see com.wanshifu.fee.center.domain.enums.TemplateCompareStatusEnum
     */
    @ApiModelProperty("对比状态")
    private String status;
}
