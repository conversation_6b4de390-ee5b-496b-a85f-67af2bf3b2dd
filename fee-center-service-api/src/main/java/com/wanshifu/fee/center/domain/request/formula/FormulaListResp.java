package com.wanshifu.fee.center.domain.request.formula;

import com.alibaba.fastjson.annotation.JSONField;
import com.wanshifu.fee.center.domain.document.CalculateRuleData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: <PERSON>
 * @create: 2024-02-27 16:26
 * @description: 公式列表response body
 */
@Data
public class FormulaListResp {

    @ApiModelProperty("公式ID")
    private String formulaId;

    @ApiModelProperty("公式名称")
    private String formulaName;

    @ApiModelProperty("公式表达式")
    private CalculateRuleData formulaContent;

    @ApiModelProperty("地区维度")
    private String divisionType;

    @ApiModelProperty("已选地区数")
    private Integer divisionIdCount;

    @ApiModelProperty("已填业务Id数")
    private Integer bizIdCount;

    @ApiModelProperty("最后更新时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;
}
