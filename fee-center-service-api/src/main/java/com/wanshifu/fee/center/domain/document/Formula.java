package com.wanshifu.fee.center.domain.document;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * @author: <PERSON>
 * @create: 2024-02-27 14:06
 * @description: 公式
 */
@Data
@Document
@FieldNameConstants
public class Formula extends BaseDocument{

    @Indexed(unique = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long formulaId;

    private String formulaName;

    /**
     * 公式类别，单价公式，总价公式
     */
    private String formulaType;

    /**
     * 区域维度
     */
    private String divisionType;

    private CalculateRuleData formulaContent;

    /**
     * 地区Id，多个用英文逗号分隔
     */
    private String divisionIds;

    /**
     * 业务Id，多个用英文逗号分隔
     */
    private String bizIds;

}
