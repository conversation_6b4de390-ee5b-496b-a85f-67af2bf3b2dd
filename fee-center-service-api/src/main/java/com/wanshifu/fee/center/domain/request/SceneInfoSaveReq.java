package com.wanshifu.fee.center.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("保存（添加、修改）场景信息共有参数")
public class SceneInfoSaveReq {

    @ApiModelProperty("价格上传告警")
    private PriceUploadAlert priceUploadAlert;

    @Data
    public static class PriceUploadAlert {

        @ApiModelProperty(value = "价格上传是否告警")
        private Boolean enabled;

        @ApiModelProperty(value = "价格上传告警 对比场景编码")
        private String comparisonSceneCode;

        @ApiModelProperty(value = "大于对比价格的百分比")
        private Integer greaterThan;

        @ApiModelProperty(value = "小于对比价格的百分比")
        private Integer lessThan;
    }

    /**
     * 是否需要拉取大数据自定义SKU
     * on:开启
     * off:禁用
     * 默认禁用
     */
    @ApiModelProperty("是否需要拉取大数据自定义SKU,on:开启,off：禁用，默认禁用")
    private String pullBigDataCustomSku;
}
