package com.wanshifu.fee.center.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("获取计价模板sku列表返回体")
public class GetFeeTemplateSkuListResp {

    @ApiModelProperty("模板id")
    private String templateId;

    @ApiModelProperty("属性展示名称")
    private String attributeDisplayName;

    @ApiModelProperty("sku_no")
    private String skuNo;

    @ApiModelProperty("费用类型标签")
    private String feeTypeTag;

    @ApiModelProperty("服务名称")
    private String serviceName;

}
