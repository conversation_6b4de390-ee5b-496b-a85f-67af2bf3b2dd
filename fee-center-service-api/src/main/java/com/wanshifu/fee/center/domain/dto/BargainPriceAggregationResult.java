package com.wanshifu.fee.center.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BargainPriceAggregationResult {

    private String skuNo;

    @ApiModelProperty("费用展示名（规格）")
    private String attributeDisplayName;

    @ApiModelProperty("原价（即划线价）")
    private Double originPrice;

    @ApiModelProperty("折扣价（补贴价）")
    private Double discountPrice;

    @ApiModelProperty("节省金额")
    private Double savingPrice;

    @ApiModelProperty("省id")
    private String level1DivisionId;

    @ApiModelProperty("省名称")
    private String provinceName;

    @ApiModelProperty("城市id")
    private String level2DivisionId;

    @ApiModelProperty("城市名称")
    private String cityName;

    @ApiModelProperty("区县数量")
    private Integer districtCount;

    @ApiModelProperty("街道/乡镇数量")
    private Integer streetCount;

    private Double masterInputPriceDouble;

    private String serviceId;
}
