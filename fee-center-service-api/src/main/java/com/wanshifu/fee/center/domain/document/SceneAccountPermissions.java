package com.wanshifu.fee.center.domain.document;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@ApiModel(description = "场景账号权限")
@EqualsAndHashCode(callSuper = true)
@Data
@Document
@FieldNameConstants
public class SceneAccountPermissions extends BaseDocument{

    @Indexed(unique = true)
    private String sceneCode;

    private String sceneName;

    @Indexed(background = true, sparse = true)
    private List<String> accounts;

}
