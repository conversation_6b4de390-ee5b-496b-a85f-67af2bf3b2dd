package com.wanshifu.fee.center.domain.request;

import com.wanshifu.fee.center.domain.dto.*;
import com.wanshifu.fee.center.domain.request.calculate.DynamicIndicatorParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 订单算费上下请求
 * 主要影响元素：账户、类目、地区、时间
 */
@Data
public class ApplyOrderCalculateReq {
    /**
     * 场景码
     */
    @NotEmpty
    private List<String> sceneCode;

    /**
     * 子场景
     */
    private String subSceneCode;

    /**
     * 服务信息
     */
    private List<CalculateServiceInfo> serviceInfos;

    private List<ExtraService> extraServices;

    /**
     * orderBase，订单基本信息
     */
    private OrderBase orderBase;

    /**
     * 下单账户
     */
    private AccountInfo from;

    /**
     * 接受订单账户（指派，报价的对象）
     */
    private AccountInfo to;

    /**
     * 来源平台信息
     */
    private PlatformInfo platformInfo;

    /**
     * 地址信息
     */
    private AddressInfo addressInfo;

    private Map<String, String> bizRule;

    /**
     * 是否需要做 计价模板与计价规则的完整性校验
     */
    private Boolean needIntegrityValidation = true;

    /**
     * 是否按场景优先级合并返回结果
     */
    private Boolean mergeByPriority = false;

    /**
     * 动态计价时间(该时间需要落在动态计价范围，才会计价动态价)
     */
    private Date dynamicCalculateTime;

    /**
     * 是否需要映射计价
     */
    private Boolean isMappingPricing;

    /**
     * 映射的target部分的sceneCode
     */
    private String fromSceneCode;

    @ApiModelProperty(value = "动态指标参数")
    List<DynamicIndicatorParam> dynamicIndicatorParamList;

    /**
     * 只有多场景才会合并
     */
    public Boolean getMergeByPriority() {
        return mergeByPriority != null && sceneCode.size() >= 2;
    }

}
