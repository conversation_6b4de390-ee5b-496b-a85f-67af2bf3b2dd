package com.wanshifu.fee.center.domain.document;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@EqualsAndHashCode(callSuper = true)
@Document
@Data
public class TaskDuration extends BaseDocument {

    @Indexed(unique = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务量
     */
    private Long taskCount;

    /**
     * 任务耗时，单位：秒
     */
    private Long taskDurationSeconds;

    /**
     * 任务状态
     * @see com.wanshifu.fee.center.domain.enums.AsyncTaskStatusEnum
     */
    private String taskStatus;


}
