package com.wanshifu.fee.center.api;


import com.wanshifu.fee.center.config.FeeCenterEncoder;
import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.request.*;
import com.wanshifu.fee.center.domain.request.feeRule.*;
import com.wanshifu.fee.center.domain.request.feeRule.master.*;
import com.wanshifu.fee.center.domain.response.*;
import com.wanshifu.fee.center.domain.response.feerule.BatchAddRulesResponse;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * <AUTHOR>
 */
@FeignClient(value = "fee-center-service", path = "feeRule", configuration = {DefaultDecoder.class, FeeCenterEncoder.class, DefaultErrorDecode.class}, url = "${wanshifu.fee-center-service.url}")
public interface FeeRuleApi {

    @PostMapping("queryMaster")
    @Deprecated
    List<FeeRule> queryMaster(@RequestBody @Validated FeeRuleQueryReq feeRuleQueryReq);

    @PostMapping("query")
    @Deprecated
    List<FeeRule> query(@RequestBody @Validated FeeRuleQueryReq feeRuleQueryReq);

    @PostMapping("batchQuery")
    @Deprecated
    List<FeeRule> batchQuery(@RequestBody @Validated FeeRuleBatchQueryReq feeRuleBatchQueryReq);

    @PostMapping("configure")
    FeeRule configure(@RequestBody @Validated FeeRuleConfigureReq feeRuleConfigureReq);

    @PostMapping("batchAdd")
    List<FeeRule> batchAdd(@RequestBody @Validated FeeRuleBatchAddReq feeRuleBatchAddReq);

    @PostMapping("batchAddRules")
    List<BatchAddRulesResponse> batchAddRules(@RequestBody @Validated BatchAddRulesRequest request);

    @PostMapping("batchDel")
    List<FeeRule> batchDel(@RequestBody @Validated FeeRuleBatchDelReq feeRuleBatchDelReq);


    @PostMapping("batchDelRules")
    boolean batchDelRules(@RequestBody @Validated FeeRuleBatchDelReq feeRuleBatchDelReq);


    @PostMapping("apply")
    @Deprecated
    ApplyCalculateResp applyCalculate(@RequestBody @Validated ApplyCalculateReq applyCalculateReq);

    @PostMapping("applyOrder")
    ApplyOrderCalculateResp applyOrderCalculate(@RequestBody @Validated ApplyOrderCalculateReq applyOrderCalculateReq);

//    @PostMapping("standardPricing")
//    StandardPricingResponse standardPricing(@RequestBody @Validated StandardPricingRequest request);

    @PostMapping("applyOrderBatch")
    List<ApplyOrderCalculateBatchResp> applyOrderCalculateBatch(@RequestBody @Validated ApplyOrderCalculateBatchReq applyOrderCalculateBatchReq);


    /**
     * 调算费接口前的 探测接口（企业用户前端专用），用于探测是否可以算费
     */
    @PostMapping("applyOrderDetector")
    boolean applyOrderDetector(@RequestBody @Validated ApplyOrderCalculateReq applyOrderCalculateReq);

    @PostMapping("queryGuidePrice")
    @Deprecated
    List<FeeRule> queryGuidePrice(@RequestBody @Validated FeeRuleGuidePriceReq feeRuleGuidePriceReq);

    @PostMapping("getFeeRuleServicePage")
    SimplePageInfo<FeeRuleServicePageResp> getFeeRuleServicePage(@Validated @RequestBody FeeRuleServicePageReq req);

    @PostMapping("getServiceIdsByDivision")
    Set<Long> getServiceIdsByDivision(@Validated @RequestBody ServiceIdByDivisionIdsReq req);

    @PostMapping("getAttributeValuePriceByServiceId")
    Map<String, AttributeValuePriceResp> getAttributeValuePriceByServiceId(@RequestParam("serviceId") Long serviceId,
                                                                           @RequestParam("userId") Long userId);

    @PostMapping("getAttributeValuePriceByServiceIdBatch")
    Map<Long, Map<String, AttributeValuePriceResp>> getAttributeValuePriceByServiceIdBatch(@RequestParam("serviceIds") Set<Long> serviceIds,
                                                                                           @RequestParam("userId") Long userId);

    /*************天天特价 START **************/

    @PostMapping("getBargainPriceEverydayFeeRuleByServiceId")
    SimplePageInfo<BargainPriceEverydayFeeRuleResp> getBargainPriceEverydayFeeRuleByServiceId(@Validated @RequestBody BargainPriceEverydayFeeRuleReq req);

    @PostMapping("getDistrictDetail")
    DistrictDetailResp getDistrictDetail(@Validated @RequestBody DistrictDetailReq req);

    @PostMapping("getLowestPriceByLevel1GoodsCategoryIdOrServiceIds")
    SimplePageInfo<LowestPriceResp> getLowestPriceByLevel1GoodsCategoryIdOrServiceIds(@RequestBody LowestPriceReq req);

    /*************天天特价 END **************/

    @PostMapping("getAttributeValueByServiceId")
    List<AttributeValueResp> getAttributeValueByServiceId(@RequestParam("sceneCode") String sceneCode,
                                                          @RequestParam("serviceId") Long serviceId);

    @PostMapping("getPriceAndFeeName")
    List<PriceAndFeeNameResp> getPriceAndFeeName(@Validated @RequestBody PriceAndFeeNameReq req);


    /******  以下为师傅端定制接口 START ********/
    @PostMapping("createRecruitActivity")
    void createRecruitActivity(@Validated @RequestBody CreateRecruitActivityReq req);

    @PostMapping("deleteForMaster")
    void deleteForMaster(@RequestBody @Validated RemoveForMasterReq req);

    @PostMapping("deleteRuleByMasterIdAsync")
    void deleteRuleByMasterIdAsync(@Validated @RequestBody DeleteRuleByMasterIdRequest request);

    @Deprecated
    @PostMapping("createRecruitMaster")
    void createRecruitMaster(@Validated @RequestBody CreateRecruitMasterReq req);

    @PostMapping("createRecruitMasterAsync")
    void createRecruitMasterAsync(@Validated @RequestBody CreateRecruitMasterReq req);

    @PostMapping("createRecruitActivityMaster")
    @Deprecated // 改为走异步
    void createRecruitActivityMaster(@Validated @RequestBody CreateRecruitActivityMasterReq req);

    @PostMapping("createRecruitActivityMasterAsync")
    void createRecruitActivityMasterAsync(@Validated @RequestBody CreateRecruitActivityMasterReq req);

    @ApiOperation("修改招募活动--管理后台，同步")
    @PostMapping("modifyRecruitActivityMasterAsync")
    void modifyRecruitActivityMasterAsync(@Validated @RequestBody ModifyRecruitActivityMasterReq req);

    @PostMapping("getRecruitActivityMasterCreateStatus")
    List<GetRecruitActivityMasterCreateStatusResp> getRecruitActivityMasterCreateStatus(@Validated @RequestBody GetRecruitActivityMasterCreateStatusReq req);

    @PostMapping("deleteRecruitMasterBatchAsync")
    void deleteRecruitMasterBatchAsync(@Validated @RequestBody List<DeleteRecruitMasterReq> reqList);

    @PostMapping("queryBaseInfoForMaster")
    @Deprecated
    FeeRuleBaseInfoForMasterResp queryBaseInfoForMaster(@RequestBody @Validated FeeRuleBaseInfoForMasterReq feeRuleBaseInfoForMasterReq);

    @PostMapping("modifyRecruitMaster")
    @Deprecated
    void modifyRecruitMaster(@Validated @RequestBody ModifyRecruitMasterReq req);

    /******  以上师傅端定制接口 END ********/
}
