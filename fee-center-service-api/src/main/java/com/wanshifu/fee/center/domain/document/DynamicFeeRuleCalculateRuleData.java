package com.wanshifu.fee.center.domain.document;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.Indexed;

import java.util.Date;
import java.util.List;

@Data
@FieldNameConstants
public class DynamicFeeRuleCalculateRuleData {

    // 动态价格规则id
    @Indexed(background = true, sparse = true)
    private Long dynamicFeeRuleId;

    // 动态规则策略名
    private String dynamicStrategyName;

    private MatchingRule matchingRule;

    // 动态单价计算
    private CalculateData unitPrice;

    // sku维度价格计算
    private CalculateData skuPrice;

    // 服务维度价格计算
    private CalculateData servicePrice;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldNameConstants
    public static class MatchingRule {
        private Date startTime;
        private Date endTime;
    }


    @Data
    public static class CalculateData {
        private String express;
        private List<String> expressionParamList;
    }
}
