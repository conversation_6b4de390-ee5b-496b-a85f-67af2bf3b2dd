package com.wanshifu.fee.center.domain.response.strategy;

import lombok.Data;

import java.util.Map;
import java.util.Set;

@Data
public class SyncBlacklistStrategyFilter {

    private Long strategyId;

    private String strategyName;

    private String sceneCode;

    /**
     * @see com.wanshifu.fee.center.domain.enums.DivisionTypeEnum
     */
    private String divisionType;

    /**
     * @see com.wanshifu.fee.center.domain.enums.LimitDivisionTypeEnum
     */
    private String limitDivisionType;

    private Set<Long> divisionIds;

    private Map<Long, Boolean> divisionIdMap;

    private Set<Long> serviceIds;

    private Map<Long, Boolean> serviceIdMap;

    private String bizIds;

    private Map<String, Boolean> bizIdMap;

    private String operator;
}
