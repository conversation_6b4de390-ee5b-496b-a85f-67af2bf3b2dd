package com.wanshifu.fee.center.domain.request.feeRule;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("属性值价格response body")
public class AttributeValuePriceResp {

    @ApiModelProperty("费用名称")
    private String feeName;

    @ApiModelProperty("单价")
    private String unitPrice;

    @ApiModelProperty("单位")
    private String unit;

}
