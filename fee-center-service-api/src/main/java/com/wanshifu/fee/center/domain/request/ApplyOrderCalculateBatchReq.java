package com.wanshifu.fee.center.domain.request;

import com.wanshifu.fee.center.domain.request.feeRule.BizRuleBatchReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: <PERSON>
 * @create: 2024-04-02 16:36
 * @description: 批量计价接口Request body
 */
@ApiModel("批量计价接口Request body")
@Data
public class ApplyOrderCalculateBatchReq {

    @ApiModelProperty(value = "业务id。例如师傅id，用户id等。为了兼容，故采用String类型", required = true)
    @NotEmpty(message = "业务id不能为空")
    private List<BizRuleBatchReq> bizRuleBatchReqList;

    @ApiModelProperty(value = "订单计价标准上下文", required = true)
    @NotNull(message = "订单计价标准上下文不能为空")
    private ApplyOrderCalculateReq applyOrderCalculateReq;
}
