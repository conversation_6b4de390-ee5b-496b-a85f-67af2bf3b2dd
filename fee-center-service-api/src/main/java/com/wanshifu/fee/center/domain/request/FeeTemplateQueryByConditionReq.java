package com.wanshifu.fee.center.domain.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.wanshifu.framework.core.page.Pager;
import lombok.Data;

import java.util.List;

@Data
public class FeeTemplateQueryByConditionReq extends Pager {
    private Long templateId;
    private String sceneCode;
    // 在算费时需要进行组完整性校验
    private String group;
    private String sceneName;

    // 默认不限制
    private List<Integer> statusList;

    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private List<BizRuleCondition> bizRule;

    /**
     * batchUpdateSkuByService:批量按服务自定义sku的查询场景
     */
    private String queryType;

    @Data
    public static class BizRuleCondition {
        private String key;
        private String value;
        /**
         * @see com.wanshifu.fee.center.domain.enums.BizRuleConditionModeEnum
         */
        private int mode = 0;
    }
}
