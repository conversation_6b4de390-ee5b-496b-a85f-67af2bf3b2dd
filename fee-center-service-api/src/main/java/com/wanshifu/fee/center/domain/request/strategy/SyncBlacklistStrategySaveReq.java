package com.wanshifu.fee.center.domain.request.strategy;

import com.wanshifu.fee.center.domain.document.ServiceData;
import com.wanshifu.fee.center.domain.request.common.Create;
import com.wanshifu.fee.center.domain.request.common.Modify;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Set;

@ApiModel(description = "同步数据黑名单策略保存request body")
@Data
public class SyncBlacklistStrategySaveReq {

    @ApiModelProperty(value = "策略id")
    @NotBlank(message = "策略id不能为空", groups = {Modify.class})
    private String strategyId;

    @ApiModelProperty(value = "场景编码", required = true)
    @NotBlank(message = "场景编码不能为空", groups = {Create.class})
    private String sceneCode;

    @ApiModelProperty(value = "场景名称")
    private String sceneName;

    @ApiModelProperty(value = "策略名称", required = true)
    @NotBlank(message = "策略名称不能为空", groups = {Create.class})
    @Length(max = 40, min = 1, message = "策略名称不能超过40个字符")
    private String strategyName;

    @ApiModelProperty(value = "服务信息", required = true)
    @NotEmpty(message = "服务信息不能为空", groups = {Create.class})
//    private Set<String> serviceIds;
    private Set<ServiceData> serviceDataSet;

    @ApiModelProperty(value = "地区维度", required = true)
    @NotBlank(message = "地区维度不能为空", groups = {Create.class})
    private String divisionType;

    @ApiModelProperty(value = "限定区域")
    private String limitDivisionType;

    @ApiModelProperty(value = "地区id")
    private Set<Long> divisionIds;

    @ApiModelProperty(value = "业务id")
    private String bizIds;

    @ApiModelProperty(value = "操作人", required = true)
    @NotBlank(message = "操作人不能为空", groups = {Modify.class})
    private String operator;
}

