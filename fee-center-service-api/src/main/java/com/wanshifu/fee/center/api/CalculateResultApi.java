package com.wanshifu.fee.center.api;


import com.wanshifu.fee.center.config.FeeCenterEncoder;
import com.wanshifu.fee.center.domain.document.CalculateResult;
import com.wanshifu.fee.center.domain.request.CalculateResultReq;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * <AUTHOR>
 */
@FeignClient(value = "fee-center-service", path = "calculateResult", configuration = {DefaultDecoder.class, FeeCenterEncoder.class, DefaultErrorDecode.class}, url = "${wanshifu.fee-center-service.url}")
public interface CalculateResultApi {

    @PostMapping("query")
    SimplePageInfo<CalculateResult> query(@RequestBody @Validated CalculateResultReq calculateResultReq);

}
