package com.wanshifu.fee.center.domain.document;

import com.wanshifu.fee.center.domain.enums.IndicatorOptionalConditionEnum;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.hibernate.validator.constraints.NotBlank;

import java.util.Comparator;
import java.util.List;

@Data
@FieldNameConstants
@ApiModel("动态调价指标")
public class DynamicPricingIndicator {

    @NotBlank(message = "指标编码不能为空")
    @ApiModelProperty(value = "指标编码", required = true)
    private String indicatorCode;

    /**
     * @see com.wanshifu.fee.center.domain.enums.IndicatorOptionalConditionEnum
     */
    @ApiModelProperty("指标可选逻辑条件")
    private String indicatorOptionalCondition;

    @ApiModelProperty("动态调价指标项")
    private List<DynamicPricingIndicator.IndicatorValue> indicatorValueList;

    @Data
    public static class IndicatorValue {

        @ApiModelProperty("固定值")
        private Double fixedValue;

        @ApiModelProperty("最小值")
        private Double minValue;

        @ApiModelProperty("最大值")
        private Double maxValue;

        /**
         * @see com.wanshifu.fee.center.domain.enums.AdjustPriceTypeEnum
         */
        @ApiModelProperty("动态调价类型")
        private String adjustPriceType;

        /**
         * @see com.wanshifu.fee.center.domain.enums.DynamicSymbolEnum
         */
        @ApiModelProperty("调价动作（涨价or降价）")
        private String adjustPriceAction;

        @ApiModelProperty("调整值")
        private Double adjustValue;

    }

    public void validate() {
        boolean isGreaterThan = IndicatorOptionalConditionEnum.GREATER_THAN.getCode().equals(indicatorOptionalCondition);
        boolean isLessThan = IndicatorOptionalConditionEnum.LESS_THAN.getCode().equals(indicatorOptionalCondition);
        boolean isGreaterThanOrEqual = IndicatorOptionalConditionEnum.GREATER_THAN_OR_EQUAL.getCode().equals(indicatorOptionalCondition);
        boolean isLessThanOrEqual = IndicatorOptionalConditionEnum.LESS_THAN_OR_EQUAL.getCode().equals(indicatorOptionalCondition);
        boolean isEqual = IndicatorOptionalConditionEnum.EQUAL.getCode().equals(indicatorOptionalCondition);

        if (isGreaterThan || isLessThan || isGreaterThanOrEqual || isLessThanOrEqual || isEqual) {
            if (CollectionUtils.isEmpty(indicatorValueList)) {
                throw new BusException("动态调价指标项不能为空");
            }
            if (this.indicatorValueList.size() != 1) {
                throw new BusException("只能有一个动态调价指标项");
            }
        }

        boolean isRange = IndicatorOptionalConditionEnum.RANGE.getCode().equals(indicatorOptionalCondition);
        if (isRange) {
            if (CollectionUtils.isEmpty(indicatorValueList)) {
                throw new BusException("动态调价指标项不能为空");
            }
            for (IndicatorValue indicatorValue : indicatorValueList) {
                if (indicatorValue.getMinValue() == null || indicatorValue.getMaxValue() == null) {
                    throw new BusException("动态调价指标项的最小值和最大值不能为空");
                }
                if (indicatorValue.getMinValue() >= indicatorValue.getMaxValue()) {
                    throw new BusException("动态调价指标项的最大值应大于最小值");
                }
            }

            // 先排序，如果当前的最大值大于等于下一个的最小值，则说明存在重叠
            indicatorValueList.sort(Comparator.comparingDouble(IndicatorValue::getMinValue));
            for (int i = 0; i < indicatorValueList.size() - 1; i++) {
                DynamicPricingIndicator.IndicatorValue current = indicatorValueList.get(i);
                DynamicPricingIndicator.IndicatorValue next = indicatorValueList.get(i + 1);
                if (current.getMaxValue() >= next.getMinValue()) {
                    throw new BusException("动态调价指标项的区间值有重叠");
                }
            }
        }
    }
}
