package com.wanshifu.fee.center.domain.enums.master2c;

import lombok.Getter;

@Getter
public enum ServePriceStatusEnum {

    PENDING_REVIEW("pending_review", "待审核"),
    PENDING_ACTIVATION("pending_activation", "待生效"),
    ACTIVE("active", "生效中"),
    REJECTED("rejected", "已驳回"),
    INACTIVE("inactive", "已下线");

    private final String status;
    private final String desc;

    ServePriceStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static ServePriceStatusEnum fromStatus(String status) {
        for (ServePriceStatusEnum value : ServePriceStatusEnum.values()) {
            if (value.status.equals(status)) {
                return value;
            }
        }
        return null;
    }

}
