package com.wanshifu.fee.center.domain.request.feeRule;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Set;

@Data
@ApiModel("删除师傅招募算费规则请求体")
public class RemoveForMasterReq {

    @ApiModelProperty(value = "场景编码", required = true)
    @NotBlank(message = "场景编码不能为空")
    private String sceneCode;

    @ApiModelProperty(value = "师傅id", required = true)
    @NotBlank(message = "师傅id不能为空")
    private String masterId;

    @ApiModelProperty(value = "招募id", required = true)
    @NotBlank(message = "招募id不能为空")
    private String recruitId;

//    @NotEmpty(message = "街道id列表不能为空")
    @ApiModelProperty(value = "街道id列表")
    private Set<String> streetIds;

}
