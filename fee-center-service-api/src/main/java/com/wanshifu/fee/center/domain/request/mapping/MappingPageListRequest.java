package com.wanshifu.fee.center.domain.request.mapping;

import com.wanshifu.framework.core.page.Pager;
import com.wanshifu.framework.core.validation.annotation.ValueIn;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class MappingPageListRequest extends Pager {

    @ApiModelProperty("应用类型编码。pricing:计价，price_sync:价格同步，all:全部，none:无")
    @ValueIn("pricing,price_sync,all,none")
    private String applyTypeCode;

    private TemplateInfo source;

    private TemplateInfo target;

    @Data
    public static class TemplateInfo {

        @ApiModelProperty(value = "模板编码")
        private String sceneCode;

        @ApiModelProperty(value = "服务名称")
        private String serviceName;

        @ApiModelProperty(value = "skuNo")
        private String skuNo;
    }
}
