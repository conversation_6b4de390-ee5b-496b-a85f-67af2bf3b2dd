package com.wanshifu.fee.center.domain.document;


import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;


/**
 * 导出任务
 */
@Document
@Data
@FieldNameConstants
public class ExportTaskInfo extends BaseDocument {

    @Indexed(unique = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long exportTaskId;

    @Indexed
    private String sceneCode;

    private String sceneName;

    private String fromSceneCode;

    private String fromSceneName;

    @Indexed
    private String divisionType;

    private String divisionTypeName;

    private List<String> bizIds;

    private List<String> serviceIds;

    private Long fileId;

    // 场景名称 + 价格导出 + 时间 + .xlsx
    private String fileName;

    private String fileUrl;

    private String error;

    private String operator;

}
