package com.wanshifu.fee.center.domain.request.calculate;

import com.wanshifu.fee.center.domain.dto.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Date;
import java.util.List;

@Data
@ApiModel("计价并比价请求参数")
public class CalculateAndComparePriceRequest {

    @ApiModelProperty(value = "算价场景编码列表，算价场景优先级按列表中元素顺序从高到低", required = true)
    @NotEmpty(message = "算价场景编码列表不能为空")
    private List<String> priceSceneCodeList;

    @ApiModelProperty(value = "销售价场景编码", required = true)
    @NotBlank(message = "销售价场景编码不能为空")
    private String salePriceSceneCode;

    @ApiModelProperty(value = "底价场景编码", required = true)
    @NotBlank(message = "底价场景编码不能为空")
    private String floorPriceSceneCode;

    @ApiModelProperty(value = "订单基础信息")
    protected OrderBase orderBase;

//    @ApiModelProperty(value = "账号信息")
//    protected AccountInfo accountInfo;

    @ApiModelProperty(value = "地址信息")
    protected CoreAddressInfo coreAddressInfo;

    @ApiModelProperty(value = "业务信息(用户id、师傅id、总包id、招募id等)")
    protected BizInfo bizInfo;

    @ApiModelProperty(value = "服务信息", required = true)
    @NotEmpty(message = "服务信息不能为空")
    private List<ServiceDTO> serviceDtoList;

    @ApiModelProperty(value = "是否需要做计价模板与计价规则的完整性校验")
    protected Boolean needIntegrityValidation = true;

    @ApiModelProperty(value = "动态计价时间(该时间需要落在动态计价范围，才会计价动态价)")
    protected Date dynamicCalculateTime;

    @ApiModelProperty(value = "是否需要映射计价")
    protected Boolean isMappingPricing;

    @ApiModelProperty(value = "映射目标场景编码")
    protected String targetSceneCode;
}
