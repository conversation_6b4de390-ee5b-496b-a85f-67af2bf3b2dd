package com.wanshifu.fee.center.domain.request.mapping;

import com.wanshifu.fee.center.domain.request.BasePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("分页查询请求")
public class QueryPageReq extends BasePageReq {

    @ApiModelProperty("from场景")
    private String fromSceneCode;

    @ApiModelProperty("from服务名称")
    private String fromServiceName;

    @ApiModelProperty("from sku no")
    private String fromSkuNo;

    @ApiModelProperty("to场景")
    private String toSceneCode;

    @ApiModelProperty("to服务名称")
    private String toServiceName;

    @ApiModelProperty("to sku no")
    private String toSkuNo;

    @ApiModelProperty("应用类型。pricing:计价，price_sync:价格同步，all:全部，none:无")
    private String applyType;
}
