package com.wanshifu.fee.center.domain.enums;

public enum ExceptionEnum {

    WITHOUT_TEMPLATE("10001", "没有维护计价模板，或模板维护信息错误！请检查场景(编码:{})对应服务(ID:{})下单的规格sku，是否都维护了计价模板、计价数量是否准确等"),
    MAPPING_WITHOUT_TEMPLATE("10002", "映射无计价模板"),
    WITHOUT_FEE_TYPE_TAG("10003", "无费用类型"),
    WITHOUT_SKU_NUMBER("10004", "无计价数量"),
    MAPPING_WITHOUT_TEMPLATE_NO_APPLY("10005", "排除【无需算费】的模板后，映射无计价模板"),

    WITHOUT_RULE("20001", "没有配置相应的价格！请检查场景(编码:{})对应服务(ID:{})下单的规格是否维护了价格"),
    INCOMPLETE_RULE("20002", "维护价格不全！请检查场景(编码:{})对应服务(ID:{})下所有的计价sku是否都设置了价格"),
    INCOMPLETE_RULE_MAPPING("20003", "映射的算费规则为空"),

    ORDER_CONTEXT_WITHOUT_RULE("30001", "订单上下文无算费规则"),
    ORDER_CONTEXT_EXIST_BUT_RULE_WITHOUT_SERVICE_FEE("30002", "订单信息中服务费的sku（{}）没有与之匹配的价格"),
    ORDER_CONTEXT_WITHOUT_SERVICE_FEE("30003", "订单上下文无服务费"),
    NO_APPLY_IF_HAVE("30004", "订单信息中有明确不计较的属性（如其他）"),
    ORDER_CONTEXT_INCOMPLETE_SKU_NUMBER("30005", "订单上下文中 计价数量参数值不完整"),
    FEE_TEMPLATE_WITHOUT_SERVICE_FEE("30006", "计价模板没有维护【服务费】"),

    EXPRESSION_ERROR("40001", "表达式错误"),

    COST_LESS_OR_EQU_ZERO("50001", "算费结果<=0"),

    SCENE_CIRCUIT_BREAKER("60001", "计价场景已熔断"),

    ;

    public final String code;

    public final String msg;

    ExceptionEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
