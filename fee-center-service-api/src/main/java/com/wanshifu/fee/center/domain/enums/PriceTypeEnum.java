package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum PriceTypeEnum {
    // 基础定价、商家、师傅、运营
    BASE("base", "基础定价"),
    USER("user", "商家"),
    MASTER("master", "师傅"),
    WSF("wsf", "运营"),
    ;
    public final String code;
    public final String name;

    public static PriceTypeEnum fromCode(String code) {
        for (PriceTypeEnum e : PriceTypeEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
}
