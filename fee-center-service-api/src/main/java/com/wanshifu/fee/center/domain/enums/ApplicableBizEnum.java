package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum ApplicableBizEnum {
    USER("user", "企业"),
    CUSTOMER("customer", "家庭");
    public final String code;
    public final String name;

    public static ApplicableBizEnum fromCode(String code) {
        for (ApplicableBizEnum e : ApplicableBizEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
}
