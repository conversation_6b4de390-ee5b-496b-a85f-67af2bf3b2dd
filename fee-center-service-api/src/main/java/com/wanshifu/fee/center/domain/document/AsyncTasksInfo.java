package com.wanshifu.fee.center.domain.document;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.mapping.Document;

@EqualsAndHashCode(callSuper = true)
@Document
@Data
@CompoundIndex(def = "{'taskId': 1, 'taskType': 1}", unique = true)
public class AsyncTasksInfo extends BaseDocument{

    @JSONField(serializeUsing = ToStringSerializer.class)
    private String taskId;

    /**
     * @see com.wanshifu.fee.center.domain.enums.AsyncTaskTypeEnum
     */
    private String taskType;

    /**
     * @see com.wanshifu.fee.center.domain.enums.AsyncTaskStatusEnum
     */
    private String taskStatus;

    private String failReason;

}
