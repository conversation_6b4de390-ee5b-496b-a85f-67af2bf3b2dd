package com.wanshifu.fee.center.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TemplateBizRuleDTO {

    @ApiModelProperty("服务类目id")
    protected String serviceCategoryId;

    @ApiModelProperty("服务id")
    protected String serviceId;

    @ApiModelProperty("服务名称")
    protected String serviceName;

    @ApiModelProperty("服务模式/下单模式（2-报价模式/报价招标；4-定价模式/一口价）")
    protected String serviceModelId;


    @ApiModelProperty("skuNo")
    protected String skuNo;

    @ApiModelProperty("sku属性路径名称")
    protected String skuAttributePathName;

    @ApiModelProperty("sku计价数量路径编号")
    protected String skuNumberPathNo;

    @ApiModelProperty("sku计价数量路径名称")
    protected String skuNumberName;

    @ApiModelProperty("费用展示名称")
    protected String attributeDisplayName;

    @ApiModelProperty("单位")
    protected String feeUnit;

    @ApiModelProperty("费用名称")
    protected String feeName;

    /**
     * @see com.wanshifu.fee.center.domain.enums.FeeSkuTypeEnum
     */
    @ApiModelProperty("sku类型（standardSku-标准sku；customSku-自定义sku）")
    protected String skuType;

    /**
     * @see com.wanshifu.fee.center.domain.enums.ApplyFlagEnum
     */
    @ApiModelProperty("是否需要算费标签）")
    protected String applyFlag;

    /**
     * @see com.wanshifu.fee.center.domain.enums.FeeTypeTagEnum
     */
    @ApiModelProperty("费用类型标签（serviceFee-服务费；goodSurcharge-商品附加费；standardSurcharge-标准附加费）")
    protected String feeTypeTag;

    @ApiModelProperty("费用类型标签名称")
    protected String feeTypeTagName;

    @ApiModelProperty("属性值-最小值")
    protected String attributeValueMin;

    @ApiModelProperty("属性值-最大值")
    protected String attributeValueMax;
}
