package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

/**
 * 账号类型
 */
@AllArgsConstructor
public enum AccountTypeEnum {
    /**
     * 账号类型
     */

    USER("user"),
    MASTER("master"),
    ENTERPRISE("enterprise"),
    CUSTOMER("customer"),
    SYSTEM("system"),
    CITY("city");

    public final String code;

    public static AccountTypeEnum fromCode(String code) {
        for (AccountTypeEnum e : AccountTypeEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
}