package com.wanshifu.fee.center.domain.request.feeRule.master;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Data
public class ModifyRecruitActivityMasterReq {

    @ApiModelProperty(value = "师傅ID", required = true)
    @NotBlank(message = "师傅ID不能为空")
    private String masterId;

    @ApiModelProperty(value = "招募ID", required = true)
    @NotBlank(message = "招募ID不能为空")
    private String recruitId;

    @ApiModelProperty(value = "费用规则信息", required = true)
    @NotEmpty(message = "费用规则信息不能为空")
    private List<FeeRuleBaseInfo> feeRuleBaseInfoList;

    @Data
    public static class FeeRuleBaseInfo {

        @ApiModelProperty(value = "费用规则ID", required = true)
        @NotNull(message = "费用规则ID不能为空")
        private Long feeRuleId;

        @ApiModelProperty(value = "街道ID", required = true)
        @NotEmpty(message = "街道ID不能为空")
        private Set<String> streetIds;

        @ApiModelProperty(value = "场景编码", required = true)
        @NotBlank(message = "场景编码不能为空")
        private String sceneCode;

        @ApiModelProperty(value = "价格", required = true)
        @NotNull(message = "价格不能为空")
        private BigDecimal price;
    }
}
