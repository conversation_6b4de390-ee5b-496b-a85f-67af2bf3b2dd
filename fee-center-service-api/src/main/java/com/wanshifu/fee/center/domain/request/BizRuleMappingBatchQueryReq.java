package com.wanshifu.fee.center.domain.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.wanshifu.framework.core.page.Pager;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("批量查询sku模板映射request body")
public class BizRuleMappingBatchQueryReq extends Pager {

    private Long bizRuleMappingId;

    @ApiModelProperty("模板id（定价id）")
    private Long templateId;

    @ApiModelProperty("场景编码")
    private String sceneCode;

    @ApiModelProperty("场景名称")
    private String sceneName;

    // 在算费时需要进行组完整性校验
    private String group;

    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private Map<String, List<String>> fromBizRule;
    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private Map<String, List<String>> toBizRule;
}
