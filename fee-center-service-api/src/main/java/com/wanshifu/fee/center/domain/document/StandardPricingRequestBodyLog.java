package com.wanshifu.fee.center.domain.document;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.mapping.Document;

@EqualsAndHashCode(callSuper = true)
@Data
@Document
@FieldNameConstants
public class StandardPricingRequestBodyLog extends BaseDocument {

    private String requestBody;

    /**
     * 日志类型，failure or success
     */
    private String type;
}
