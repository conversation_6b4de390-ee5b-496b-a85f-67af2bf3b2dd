package com.wanshifu.fee.center.domain.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.wanshifu.fee.center.domain.document.CalculateResult;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ApplyCalculateResp {
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long tid;
    private BigDecimal cost;
    private BigDecimal costMax;
    private BigDecimal dynamicFeeCost;
    private BigDecimal dynamicFeeCostMax;
    private List<CalculateResult> calculateResultList;
}
