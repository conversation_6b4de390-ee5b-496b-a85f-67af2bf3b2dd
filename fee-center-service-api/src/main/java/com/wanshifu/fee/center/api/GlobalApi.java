package com.wanshifu.fee.center.api;


import com.wanshifu.fee.center.config.FeeCenterEncoder;
import com.wanshifu.fee.center.domain.response.SceneListResp;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(value = "fee-center-service", path = "global", configuration = {DefaultDecoder.class, FeeCenterEncoder.class, DefaultErrorDecode.class}, url = "${wanshifu.fee-center-service.url}")
public interface GlobalApi {
    /**
     * 获取场景列表
     *
     * @return
     */
    @GetMapping("sceneList")
    List<SceneListResp> sceneList();

}
