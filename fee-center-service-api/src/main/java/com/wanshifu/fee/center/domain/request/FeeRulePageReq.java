package com.wanshifu.fee.center.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @author: <PERSON>
 * @create: 2023-09-12 14:24
 * @description: 计价规则（草稿）列表request body
 */
@ApiModel("计价规则（草稿）列表request body")
@Data
public class FeeRulePageReq extends BasePageReq {

    private String batchTaskId;

//    @ApiModelProperty("服务名称")
//    private String serviceName;

    @ApiModelProperty("服务Id")
    private String serviceId;

    @ApiModelProperty("商品类目Id")
    private String goodsCategoryId;

    @ApiModelProperty("费用类型标签")
    private String feeTypeTag;

    /**
     * 场景编码（即价格模式编号）
     */
    @ApiModelProperty("场景编码")
    @NotBlank(message = "场景编码不能为空")
    private String sceneCode;

    @ApiModelProperty("起始时间")
    @NotNull(message = "起始时间不能为空")
    private Date startTime;

    @ApiModelProperty("终止时间")
    @NotNull(message = "终止时间不能为空")
    private Date endTime;

    @ApiModelProperty("城市Id")
    private Long level2DivisionId;

    @ApiModelProperty("区县Id")
    private Long level3DivisionId;

    @ApiModelProperty("街道Id")
    private Long level4DivisionId;

    @ApiModelProperty("业务Id")
    private String bizId;

    @ApiModelProperty("业务标签")
    private String bizTag;

    @ApiModelProperty("价格类型（地区维度）")
    private String divisionType;

    @ApiModelProperty("skuNo")
    private String skuNo;

    /**
     * 比较状态
     *
     * @see com.wanshifu.fee.center.domain.enums.PriceComparisonStatusEnum
     */
    private String priceComparisonStatus;
}
