package com.wanshifu.fee.center.domain.request.feeRule;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

@Data
@ApiModel("区域详情request body")
public class DistrictDetailReq {

    @ApiModelProperty(value = "场景编码", required = true)
    @NotBlank(message = "场景编码不能为空")
    private String sceneCode;

    @ApiModelProperty(value = "服务id", required = true)
    @NotBlank(message = "服务Id不能为空")
    private String serviceId;

    @ApiModelProperty(value = "规格")
    @NotBlank(message = "规格不能为空")
    private String skuNo;

    @ApiModelProperty(value = "城市id")
    @NotBlank(message = "城市id不能为空")
    private String level2DivisionId;

    @ApiModelProperty(value = "城市名称")
    @NotBlank(message = "城市名称不能为空")
    private String cityName;
}
