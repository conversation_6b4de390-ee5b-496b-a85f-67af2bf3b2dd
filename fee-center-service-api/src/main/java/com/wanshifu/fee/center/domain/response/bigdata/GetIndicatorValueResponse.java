package com.wanshifu.fee.center.domain.response.bigdata;

import com.wanshifu.fee.center.domain.document.DynamicPricingIndicator;
import lombok.Data;

import java.util.List;

@Data
public class GetIndicatorValueResponse {

    private List<ServiceIndicatorResult> serviceIndicators;

    @Data
    public static class ServiceIndicatorResult {
        private Long serviceId;
        private List<DynamicPricingIndicator> indicators;
    }

}
