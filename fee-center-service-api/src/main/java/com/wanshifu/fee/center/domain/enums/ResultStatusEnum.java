package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

import java.util.Objects;

@AllArgsConstructor
public enum ResultStatusEnum {
    /**
     * 可用
     */
    ACTIVE(0, "可用"),
    ;
    public final int code;
    public final String name;

    public static ResultStatusEnum fromCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (ResultStatusEnum value : ResultStatusEnum.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }

}
