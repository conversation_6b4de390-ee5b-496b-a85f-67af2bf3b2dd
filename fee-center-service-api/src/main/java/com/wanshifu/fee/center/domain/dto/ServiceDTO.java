package com.wanshifu.fee.center.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel(description = "服务信息")
public class ServiceDTO {

    @ApiModelProperty(value = "服务id")
    @NotNull(message = "服务id不能为空")
    private Long serviceId;

    @ApiModelProperty(value = "外部服务id，为了解决同一次请求有多个相同的服务")
    private String externalServiceId;

    @ApiModelProperty(value = "服务属性信息")
    @NotEmpty(message = "服务属性信息不能为空")
    private List<AttributeDTO> attributeDtoList;

    @ApiModelProperty(value = "是否已经计算过了，用于多场景且有优先级的计算场景")
    private Boolean isCalculated = false;

}
