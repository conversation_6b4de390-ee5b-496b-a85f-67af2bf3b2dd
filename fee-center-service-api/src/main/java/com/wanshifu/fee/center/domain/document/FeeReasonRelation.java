package com.wanshifu.fee.center.domain.document;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * @author: <PERSON>
 * @create: 2023-11-01 15:16
 * @description: 增减费用项关联平台通用SKU
 */
@Document
@Data
@FieldNameConstants
public class FeeReasonRelation extends BaseDocument {

    @Indexed(unique = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long relationId;

    @Indexed(background = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long reasonId;

    @JSONField(serializeUsing = ToStringSerializer.class)
    @Indexed(background = true)
    private Long level1GoodsCategoryId;

    private String level1GoodsCategoryName;

    @JSONField(serializeUsing = ToStringSerializer.class)
    @Indexed(background = true)
    private Long level2GoodsCategoryId;

    private String level2GoodsCategoryName;

    @JSONField(serializeUsing = ToStringSerializer.class)
    @Indexed(background = true)
    private Long level3GoodsCategoryId;

    private String level3GoodsCategoryName;

    @JSONField(serializeUsing = ToStringSerializer.class)
    @Indexed(background = true)
    private Long serviceId;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long skuId;

    private String serviceName;

    private String serviceMode;

    private String skuType;

    private String skuNo;


    private String skuPathName;

    private String skuRuleScene;

}
