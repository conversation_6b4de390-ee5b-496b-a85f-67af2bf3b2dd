package com.wanshifu.fee.center.domain.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.wanshifu.fee.center.domain.document.CalculateResult;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ApplyOrderCalculateResp {
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long tid;
    private BigDecimal cost;
    private BigDecimal costMax;
    private BigDecimal dynamicFeeCost;
    private BigDecimal dynamicFeeCostMax;
    private List<SceneResult> sceneResultList;

    @Data
    public static class SceneResult {
        private BigDecimal cost;
        private BigDecimal costMax;
        private BigDecimal dynamicFeeCost;
        private BigDecimal dynamicFeeCostMax;
        private BigDecimal basePrice;
        private String sceneCode;
        // 算费是否成功
        private boolean success = true;
        // 如果失败为什么失败
        private String errorInfo;
        private List<ServiceResult> serviceResultList;
        private List<CalculateResult> calculateResultList;
    }

    @Data
    public static class ServiceResult {
        private String sceneCode;
        private String index;
        private BigDecimal cost;
        private BigDecimal costMax;
        private BigDecimal dynamicFeeCost;
        private BigDecimal dynamicFeeCostMax;
        private List<CalculateResult> calculateResultList;
    }
}
