package com.wanshifu.fee.center.domain.request.mapping;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Set;

@Data
@ApiModel("复制映射request body")
public class CopyMappingRequest {

    @ApiModelProperty(value = "映射id", required = true)
    @NotEmpty(message = "映射id不能为空")
    private Set<Long> mappingIds;

    @ApiModelProperty(value = "源场景", required = true)
    @NotBlank(message = "源场景不能为空")
    private String  sourceSceneCode;

    @ApiModelProperty(value = "目标场景", required = true)
    @NotBlank(message = "目标场景不能为空")
    private String  targetSceneCode;
}
