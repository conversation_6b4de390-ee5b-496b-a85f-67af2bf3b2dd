package com.wanshifu.fee.center.domain.request.strategy;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("同步数据黑名单策略分页查询response body")
public class SyncBlacklistStrategyPageResp {

    @ApiModelProperty(value = "策略id")
    private String strategyId;

    @ApiModelProperty(value = "策略名称")
    private String strategyName;

    @ApiModelProperty(value = "场景编码")
    private String sceneCode;

    @ApiModelProperty(value = "场景名称")
    private String sceneName;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "操作人")
    private String operator;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="Asia/Shanghai")
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
}
