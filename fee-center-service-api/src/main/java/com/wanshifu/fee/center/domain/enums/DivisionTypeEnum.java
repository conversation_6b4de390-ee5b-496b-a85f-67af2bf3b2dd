package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

import java.util.Objects;


@AllArgsConstructor
public enum DivisionTypeEnum {
    COUNTRY("country", "全国统一定价", null, null),
    PROVINCE("province", "按省份定价", "level1DivisionId", COUNTRY),
    CITY("city", "按城市定价", "level2DivisionId", PROVINCE),
    DISTRICT("district", "按区县定价", "level3DivisionId", CITY),
    STREET("street", "按街道定价", "level4DivisionId", DISTRICT),
    ;

    public final String code;
    public final String name;
    public final String divisionKey;
    public final DivisionTypeEnum parent;

    public static DivisionTypeEnum fromCode(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (DivisionTypeEnum value : DivisionTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
