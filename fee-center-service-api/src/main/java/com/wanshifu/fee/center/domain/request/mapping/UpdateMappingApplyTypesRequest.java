package com.wanshifu.fee.center.domain.request.mapping;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

@ApiModel("业务规则映射修改应用类型请求体")
@Data
public class UpdateMappingApplyTypesRequest {

    @ApiModelProperty(value = "业务规则映射id列表", required = true)
    @NotEmpty(message = "业务规则映射id列表不能为空")
    private List<String> mappingIdList;

    @ApiModelProperty(value = "应用类型列表", required = true)
    @NotEmpty(message = "应用类型列表不能为空")
    private List<String> applyTypeList;
}
