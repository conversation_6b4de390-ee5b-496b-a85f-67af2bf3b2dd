package com.wanshifu.fee.center.domain.enums;

import com.wanshifu.framework.utils.StringUtils;
import lombok.Getter;

@Getter
public enum AsyncTaskTypeEnum {

    RECRUIT_ACTIVITY("recruit_activity", "招募活动"),

    DELETE_RULE_BY_MASTER_ID("delete_rule_by_master_id", "根据师傅id删除规则");

    final private String code;
    final private String desc;

    AsyncTaskTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AsyncTaskTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (AsyncTaskTypeEnum asyncTaskTypeEnum : AsyncTaskTypeEnum.values()) {
            if (asyncTaskTypeEnum.getCode().equals(code)) {
                return asyncTaskTypeEnum;
            }
        }
        return null;
    }
}
