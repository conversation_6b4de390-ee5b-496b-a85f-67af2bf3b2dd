package com.wanshifu.fee.center.domain.document;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Document
@Data
@FieldNameConstants
@CompoundIndex(def = "{'level1GoodsCategoryId': 1, 'serviceTypeId': 1}", unique = true)
public class SameDayPriceConfig extends BaseDocument{

    @Indexed(unique = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long sameDayPriceConfigId;

    private Long level1GoodsCategoryId;

    private String level1GoodsCategoryName;

    private Long serviceTypeId;

    private Integer price;

    /**
     * @see com.wanshifu.fee.center.domain.enums.SameDayPriceConfigStateEnum
     */
    private String activationState;

    private int provinceCount;

    private List<AddressConfig> addressConfigs;

    @Data
    @FieldNameConstants
    public static class AddressConfig {

        private String districtId;

        private String districtName;

    }
}
