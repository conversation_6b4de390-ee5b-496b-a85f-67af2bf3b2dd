package com.wanshifu.fee.center.domain.dto;

import com.wanshifu.fee.center.domain.document.CalculateResult;
import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.document.FeeTemplate;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("服务计算结果")
public class ServiceCalculateResult {

    private String sceneCode;

    private String serviceId;

    private BigDecimal cost;

    private BigDecimal costMax;

    private BigDecimal dynamicFeeCost;

    private BigDecimal dynamicFeeCostMax;

    private BigDecimal basePrice;

    private boolean success = false;

    private List<CalculateResult> calculateResultList;
}
