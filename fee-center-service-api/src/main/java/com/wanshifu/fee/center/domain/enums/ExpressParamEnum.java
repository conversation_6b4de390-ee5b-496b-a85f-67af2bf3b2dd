package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;


/**
 * 表达式除去sku还有以下参数
 */
@AllArgsConstructor
public enum ExpressParamEnum {
    MASTER_INPUT_PRICE("masterInputPrice", "师傅自定义价格", "masterInputPrice"),
    AP_SKU_PRICE("AP_SKU_PRICE", "基础单价", "apSkuPrice"),
    FEE_TYPE_TAG("FEE_TYPE_TAG", "费用类型", "feeTypeTag"),
    ALL_SERVICE_AP_SKU_NUMBER("ALL_SERVICE_AP_SKU_NUMBER", "基础服务商品数量", "allServiceApSkuNumber"),
    AP_SKU_NUMBER("AP_SKU_NUMBER", "计价数量属性", "skuNumberPathNo"),
    AP_SKU_NO("AP_SKU_NO", "当前单价属性", "skuNo"),

    ;
    public final String code;
    public final String name;
    public final String bizKey;

    public static ExpressParamEnum fromCode(String code) {
        for (ExpressParamEnum value : ExpressParamEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
