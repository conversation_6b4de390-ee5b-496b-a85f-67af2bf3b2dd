package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

import java.util.Objects;

@AllArgsConstructor
public enum BizRuleConditionModeEnum {
    ALL_MATCH(0, "全匹配"),
    FUZZY_MATCH(1, "模糊匹配"),
    IN_MATCH(2, "in匹配");

    public final int code;
    public final String name;

    public static BizRuleConditionModeEnum fromCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (BizRuleConditionModeEnum value : BizRuleConditionModeEnum.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
