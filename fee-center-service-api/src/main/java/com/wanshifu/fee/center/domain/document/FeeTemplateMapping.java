package com.wanshifu.fee.center.domain.document;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Document
@Data
@FieldNameConstants
public class FeeTemplateMapping extends BaseDocument {

    @Indexed(unique = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long mappingId;

    // 应用类型（计价、价格同步）
    private List<String> applyTypes;

    // “源”计价模板信息
    private TemplateInfo source;

    // “目标”计价模板信息
    private TemplateInfo target;

    @Data
    @FieldNameConstants
    public static class TemplateInfo {

        @Indexed(background = true)
        @JSONField(serializeUsing = ToStringSerializer.class)
        private Long templateId;

        private String serviceId;

        private String serviceName;

        // 计价规则模板的skuType（standardSku：标准sku，customSku：自定义sku）
        private String templateSkuType;

        // 场景的sku类型（definite_price：一口价，offer_price：报价招标），由于场景中的sku类型不可更改，所以这里冗余
        private String sceneSkuType;

        private String skuNo;

        // sku路径名（来源于服务品类）
        private String skuAttributePathName;

        // 费用名称（来源于 计价管理后台配置计价规则模板时 填的“费用名称”）
        private String attributeDisplayName;

        // 计价数量路径编码
        private String skuNumberPathNo;

        // 计价数量名（来源于服务品类）
        private String skuNumberName;

        private String feeUnit;

        // 自定义范围的 最小值
//        private String attributeValueMin;

        // 自定义范围的 最大值
//        private String attributeValueMax;

        private String sceneCode;

        private String sceneName;

        private String mappingType;

    }
}
