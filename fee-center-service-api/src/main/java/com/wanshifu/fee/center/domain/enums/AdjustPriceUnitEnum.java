package com.wanshifu.fee.center.domain.enums;

import lombok.Getter;

@Getter
public enum AdjustPriceUnitEnum {

    SERVICE("service", "按服务"),

    SKU("sku", "按sku");

    private final String code;
    private final String desc;

    AdjustPriceUnitEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AdjustPriceUnitEnum getEnumByCode(String code) {
        for (AdjustPriceUnitEnum value : AdjustPriceUnitEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
