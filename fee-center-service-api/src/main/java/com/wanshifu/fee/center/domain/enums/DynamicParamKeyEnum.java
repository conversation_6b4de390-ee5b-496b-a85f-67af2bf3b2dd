package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DynamicParamKeyEnum {

    ORDER_NO("order_no", "订单号"),
    ORDER_SUBMIT_TIME("order_submit_time", "订单提交时间"),
    STREET_ID("street_id", "街道id"),
    ;
    private final String code;
    private final String desc;

    public static DynamicParamKeyEnum fromCode(String code) {
        if (code == null) {
            return null;
        }
        for (DynamicParamKeyEnum value : DynamicParamKeyEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}