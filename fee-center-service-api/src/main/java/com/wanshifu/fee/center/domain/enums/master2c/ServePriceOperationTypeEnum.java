package com.wanshifu.fee.center.domain.enums.master2c;

import lombok.Getter;

@Getter
public enum ServePriceOperationTypeEnum {

    ADD("add", "新增"),
    MODIFY("modify", "修改"),
    APPROVE("approve", "审核通过"),
    REJECT("reject", "驳回"),
    ACTIVE_SYSTEM("active_system", "系统上线"),
    DRAFT_ONLINE("draft_online", "改价生效"),
    INACTIVE("inactive", "下线"),
    ;

    private final String operationType;
    private final String desc;

    ServePriceOperationTypeEnum(String operationType, String desc) {
        this.operationType = operationType;
        this.desc = desc;
    }

    public static ServePriceOperationTypeEnum fromType(String type) {
        for (ServePriceOperationTypeEnum value : ServePriceOperationTypeEnum.values()) {
            if (value.operationType.equals(type)) {
                return value;
            }
        }
        return null;
    }
}
