package com.wanshifu.fee.center.domain.request.formula;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: <PERSON>
 * @create: 2024-02-27 14:43
 * @description: 修改公式Request body
 */
@Data
@ApiModel("修改公式Request body")
public class FormulaModifyReq extends FormulaBaseReq{

    @NotNull(message = "公式ID不能为空")
    @ApiModelProperty(value = "公式ID", required = true)
    private String formulaId;
}
