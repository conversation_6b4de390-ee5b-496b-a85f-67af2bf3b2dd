package com.wanshifu.fee.center.domain.request.strategy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.Set;

@ApiModel("批量更新状态request body")
@Data
public class SyncBlacklistStrategyBatchUpdateStatusReq {

    @ApiModelProperty(value = "状态", required = true)
    @NotNull(message = "状态不能为空")
    private Integer status;

    @ApiModelProperty(value = "策略id", required = true)
    @NotEmpty(message = "策略id不能为空")
    private Set<String> strategyIds;

    @ApiModelProperty(value = "操作人", required = true)
    @NotEmpty(message = "操作人不能为空")
    private String operator;
}
