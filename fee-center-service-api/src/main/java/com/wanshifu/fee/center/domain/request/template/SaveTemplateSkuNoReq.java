package com.wanshifu.fee.center.domain.request.template;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("保存模板skuNo request body")
public class SaveTemplateSkuNoReq {

    @NotEmpty(message = "场景编码不能为空")
    @ApiModelProperty(value = "场景编码", required = true)
    private List<String> sceneCodeList;

    @NotNull(message = "服务ID不能为空")
    @ApiModelProperty(value = "服务ID", required = true)
    private Long serviceId;

    @NotNull(message = "服务名称不能为空")
    @ApiModelProperty(value = "服务名称", required = true)
    private String serviceName;

    @NotNull(message = "bizRule不能为空")
    @ApiModelProperty(value = "业务规则", required = true)
    private List<BizRule> bizRuleList;

    @Data
    public static class BizRule {

        @NotBlank(message = "skuNo不能为空")
        @ApiModelProperty(value = "sku编号", required = true)
        private String skuNo;

        @NotBlank(message = "sku属性路径名不能为空")
        @ApiModelProperty(value = "sku路径名称", required = true)
        private String skuAttributePathName;

        @NotBlank(message = "计价数量属性编号不能为空")
        @ApiModelProperty(value = "计价数量属性编号", required = true)
        private String skuNumberNo;

        @NotBlank(message = "计价数量属性路径名不能为空")
        @ApiModelProperty(value = "计价数量属性路径名", required = true)
        private String skuNumberAttributePathName;

        @NotBlank(message = "费用名称不能为空")
        @ApiModelProperty(value = "费用名称", required = true)
        private String feeName;

        @NotBlank(message = "费用单位不能为空")
        @ApiModelProperty(value = "费用单位", required = true)
        private String feeUnit;

        @NotNull(message = "单价不能为空")
        @ApiModelProperty(value = "单价", required = true)
        private String price;

        @ApiModelProperty(value = "费用类型")
        private String feeTypeTag;

        @ApiModelProperty(value = "不返价标识")
        private String applyFlag;

        @ApiModelProperty(value = "费用展示名")
        private String attributeDisplayName;

    }

}
