package com.wanshifu.fee.center.domain.request;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * @author: <PERSON>
 * @create: 2023-09-12 16:02
 * @description: 获取计价规则服务信息request body
 */
@Data
public class FeeRuleServicePageReq extends BasePageReq {

    /**
     * 场景编码（即价格模式编号）
     */
    @NotBlank(message = "场景编码不能为空", groups = {FeeRuleGroup.class, FeeRuleDraftGroup.class})
    private String sceneCode;

    private String serviceName;

    @NotBlank(message = "服务ID不能为空", groups = FeeRuleGroup.class)
    private String serviceId;

    private String goodsCategoryId;

    private String bizId;

    public interface FeeRuleGroup {

    }

    public interface FeeRuleDraftGroup {

    }

}
