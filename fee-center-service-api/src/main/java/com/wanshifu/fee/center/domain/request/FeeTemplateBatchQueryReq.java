package com.wanshifu.fee.center.domain.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.wanshifu.framework.core.page.Pager;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class FeeTemplateBatchQueryReq extends Pager {
    private Long templateId;
    private String sceneCode;
    // 在算费时需要进行组完整性校验
    private String group;
    private String sceneName;
    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private Map<String, List<String>> bizRule;

    // 默认不限制
    private List<Integer> statusList;

}
