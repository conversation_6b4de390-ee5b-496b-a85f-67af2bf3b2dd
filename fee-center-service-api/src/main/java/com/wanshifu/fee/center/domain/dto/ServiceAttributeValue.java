package com.wanshifu.fee.center.domain.dto;

import com.wanshifu.adapter.dto.attribute.sku.ServiceSkuDto;
import com.wanshifu.adapter.dto.service.BaseAttributeCommonDetailDto;
import lombok.Data;

import java.util.Objects;

@Data
public class ServiceAttributeValue extends BaseAttributeCommonDetailDto<ServiceAttribute> {

    //    @ApiModelProperty("属性路径ID")
    private Long attributePathId;

    //    @ApiModelProperty("属性值ID")
    private Long attributeValueId;

    //    @ApiModelProperty("属性值key")
    private String attributeValueKey;

    //    @ApiModelProperty("属性值组ID")
    private String attributeValueGroupId;

    //    @ApiModelProperty("属性值名称")
    private String attributeValueName;

    //    @ApiModelProperty("是否为默认值")
    private Boolean isDefault;

    //    @ApiModelProperty("服务sku")
    private ServiceSkuDto serviceSku;

    /**
     * 用户所填参数
     */
    private String value;

    /**
     * 已提取
     */
    private Boolean isExtracted = false;

    @Override
    public Long getId() {
        return super.id;
    }

    public Long getAttributePathId() {
        return Objects.isNull(id) ? getId() : attributePathId;
    }
}
