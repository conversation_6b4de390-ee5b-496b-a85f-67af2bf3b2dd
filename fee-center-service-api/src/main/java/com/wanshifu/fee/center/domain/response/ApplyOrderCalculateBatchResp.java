package com.wanshifu.fee.center.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: <PERSON>
 * @create: 2024-04-02 16:56
 * @description: 批量计价response body
 */
@Data
@NoArgsConstructor
public class ApplyOrderCalculateBatchResp {

    @ApiModelProperty(required = true, value = "业务id。例如师傅id，用户id等。为了兼容，故采用String类型")
    private String bizId;

    @ApiModelProperty(required = true, value = "招募id")
    private String bizTag;

    @ApiModelProperty(required = true, value = "订单计价结果")
    private ApplyOrderCalculateResp applyOrderCalculateResp;

    public ApplyOrderCalculateBatchResp(String bizId, String bizTag, ApplyOrderCalculateResp applyOrderCalculateResp) {
        this.bizId = bizId;
        this.bizTag = bizTag;
        this.applyOrderCalculateResp = applyOrderCalculateResp;
    }
}
