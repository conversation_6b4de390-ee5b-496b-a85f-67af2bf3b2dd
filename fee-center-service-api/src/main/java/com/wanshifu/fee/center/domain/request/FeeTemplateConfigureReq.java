package com.wanshifu.fee.center.domain.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.wanshifu.fee.center.domain.document.BaseDocument;
import com.wanshifu.fee.center.domain.document.CalculateRuleData;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;
import java.util.Map;

@Data
public class FeeTemplateConfigureReq {
    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private List<FeeTemplateConfigure> configureList;

    private boolean checkHasFeeTypeTag = true;

    @Data
    public static class FeeTemplateConfigure {
        private Long templateId;

        private String sceneCode;
        // 在算费时需要进行组完整性校验
        private String group;
        private String sceneName;
        @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
        private Map<String, String> bizRule;

        private CalculateRuleData calculateRuleData;

        @Min(value = 1L, message = "最小不能小于1")
        @Max(value = 999L, message = "最大不能小大于999")
        private int sort = BaseDocument.DEFAULT_SORT;
    }
}
