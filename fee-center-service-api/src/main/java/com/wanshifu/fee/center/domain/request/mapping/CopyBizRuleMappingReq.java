package com.wanshifu.fee.center.domain.request.mapping;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Set;

@Data
@Deprecated
public class CopyBizRuleMappingReq {

    @ApiModelProperty(value = "映射id", required = true)
    @NotEmpty(message = "映射id不能为空")
    private Set<Long> bizRuleMappingIds;

    @ApiModelProperty(value = "目标映射from场景", required = true)
    @NotBlank(message = "目标映射from场景不能为空")
    private String  targetFromSceneCode;

    @ApiModelProperty(value = "目标映射to场景", required = true)
    @NotBlank(message = "目标映射to场景不能为空")
    private String  targetToSceneCode;
}
