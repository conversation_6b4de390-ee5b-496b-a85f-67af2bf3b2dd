package com.wanshifu.fee.center.domain.enums;

import lombok.Getter;

/**
 * @author: <PERSON>
 * @create: 2024-03-18 18:08
 * @description: 属性标签枚举
 */
@Getter
public enum AttributeTagEnum {
    
    SKU_NUMBER_ARRAY("sku_number_array", "计价数量为数组");

    private final String code;

    private final String desc;

    AttributeTagEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    
}
