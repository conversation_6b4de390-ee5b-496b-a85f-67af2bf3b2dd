package com.wanshifu.fee.center.domain.document;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;


@Document
@Data
@FieldNameConstants
public class FeeTemplate extends BaseDocument {

    @Indexed(unique = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long templateId; // 定价ID
    // 在算费时需要进行组完整性校验
    @Indexed(background = true)
    private String group;

    private int templateVersion = 0;

    @Indexed(background = true)
    private String sceneCode;
    @Indexed(background = true)
    private String sceneName;

    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private Map<String, String> bizRule;

    private CalculateRuleData calculateRuleData;

}
