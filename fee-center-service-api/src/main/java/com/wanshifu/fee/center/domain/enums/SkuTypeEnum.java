package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum SkuTypeEnum {
    // 企业标准一口价、企业报价招标
    DEFINITE_PRICE("definite_price", "企业标准一口价", 4),
    OFFER_PRICE("offer_price", "企业报价招标", 2),
    ;
    public final String code;
    public final String name;
    public final Integer serviceModelId;

    public static SkuTypeEnum fromCode(String code) {
        for (SkuTypeEnum e : SkuTypeEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
}
