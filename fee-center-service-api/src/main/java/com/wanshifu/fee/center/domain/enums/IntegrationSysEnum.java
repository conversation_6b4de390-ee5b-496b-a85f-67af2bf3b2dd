package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum IntegrationSysEnum {
    // 智能运营、师傅APP、风控系统、用户端、OCS、推单系统、CRM、总包、订单系统、基础平台
//    CONFIG_SYS("config_sys","配置平台"),
    SMART_OPERATION("smart_operation", "智能运营"),
    MASTER_APP("master_app", "师傅APP"),
    RISK_SYS("risk_sys", "风控系统"),
    USER("user", "用户端"),
    OCS("ocs", "OCS"),
    PUSH_ORDER("push_order", "推单系统"),
    CRM("crm", "CRM"),
    ENTERPRISE("enterprise", "总包"),
    ORDER_SYS("order_sys", "订单系统"),
    BASE_SYS("base_sys", "基础平台"),
    ;
    public final String code;
    public final String name;

    public static IntegrationSysEnum fromCode(String code) {
        for (IntegrationSysEnum e : IntegrationSysEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
}
