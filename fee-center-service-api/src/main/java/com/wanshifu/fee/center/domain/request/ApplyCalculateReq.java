package com.wanshifu.fee.center.domain.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.wanshifu.fee.center.domain.dto.CalculateServiceInfo;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class ApplyCalculateReq {
    @NotBlank
    private String sceneCode;
    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private Map<String, String> bizRule;
    @Valid
    @NotEmpty
    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private List<CalculateRuleDataUnit> calculateRuleDataList;

    /**
     * 不用检查的 模板列表
     */
    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private List<Long> shouldNotCheckTemplateIds;

    /**
     * 动态计价时间(该时间需要落在动态计价范围，才会计价动态价)
     */
    private Date dynamicCalculateTime;

    private CalculateServiceInfo serviceInfo;

    @Data
    public static class CalculateRuleDataUnit {
        @NotNull
        private Long feeRuleId;
        @NotNull
        private Long templateId;
        private Map<String, String> bizRule;
        // 在算费时需要进行组完整性校验
        private String group;
        private String express;
        private List<String> expressionParamList;
        private Map<String, String> expressionParamMap;

        private List<Map<String, String>> expressionParamMapList;
    }
}
