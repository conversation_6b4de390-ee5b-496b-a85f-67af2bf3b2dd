package com.wanshifu.fee.center.domain.request.calculate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@ApiModel("计算动态价返回参数")
@NoArgsConstructor
@AllArgsConstructor
public class CalculateServiceDynamicPriceResponse {

    @ApiModelProperty(value = "动态价")
    private BigDecimal dynamicFeeCost;
}
