package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

import java.util.Objects;


@AllArgsConstructor
public enum DynamicTypeEnum {
    // 固定 百分比 规则
    FIXED("fixed", "固定费"),
    PERCENT("percent", "百分比"),
    RULE("rule", "规则"),
    ;

    public final String code;
    public final String name;

    public static DynamicTypeEnum fromCode(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (DynamicTypeEnum value : DynamicTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
