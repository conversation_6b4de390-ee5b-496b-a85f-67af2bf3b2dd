package com.wanshifu.fee.center.domain.biz;

import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class UserOrderOfferGuidePriceBizRule {
    private String level4DivisionId;
    private String templateId;
    private String serviceModelId;
    private String attributeValueMax;
    private String pricingMode;
    private String feeInputType;
    private String goodsCategoryId;
    private String sceneCode;
    private String feeTypeTagName;
    private String skuAttributePathName;
    private String sceneId;
    private String serviceTypeId;
    private String serviceId;
    private String skuNumberPathNo;
    private String serviceCategoryId;
    private String group;
    private String level2DivisionId;
    private String skuNumberName;
    private String feeTypeTag;
    private String attributeValueMin;
    private String feeUnit;
    private String skuNumberExpress;
    private String sceneName;
    private String serviceName;
    private String skuType;
    private String userId;
    private String masterInputPrice;
    private String attributeDisplayName;
    private String masterId;
    private String level1DivisionId;
    private String goodsCategoryName;
    private String feeName;
    private String skuNo;
    private String level3DivisionId;
    private String serviceTypeName;
    private String skuNoIndex;

    private String divisionType;
    private String divisionTypeName;

    //    @ExcelProperty(value = "省",index = 4)
    private String province;

    //    @ExcelProperty(value = "市",index = 5)
    private String city;

    //    @ExcelProperty(value = "区",index = 6)
    private String district;

    //    @ExcelProperty(value = "街道",index = 7)
    private String street;

    //    @ExcelProperty(value = "起步价",index = 9)
    private String basePrice;

    // = masterId
    private String bizId;

    // "masterId"
    private String bizType;

    private String batchTaskId;

    private String bizTag;

    private String applyFlag;
}
