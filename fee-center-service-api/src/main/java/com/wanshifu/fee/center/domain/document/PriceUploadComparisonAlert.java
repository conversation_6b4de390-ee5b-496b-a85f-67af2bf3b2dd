package com.wanshifu.fee.center.domain.document;

import lombok.Data;
import lombok.experimental.FieldNameConstants;

/**
 * @author: <PERSON>
 * @create: 2025-06-30 17:01
 * @description: 上传价格告警对比
 */
@Data
@FieldNameConstants
public class PriceUploadComparisonAlert {

    /**
     * 对比场景编码
     */
    private String comparisonSceneCode;

    /**
     * 对比场景名称
     */
    private String comparisonSceneName;

    /**
     * 差异百分比，正数：超过阈值，负数：低于阈值
     */
    private Double differencePercentage;

    /**
     * 基准价格
     */
    private String benchmarkPrice;

    /**
     * 比较状态
     *
     * @see com.wanshifu.fee.center.domain.enums.PriceComparisonStatusEnum
     */
    private String priceComparisonStatus;

    /**
     * 未识别原因
     */
    private String unrecognizedReason;
}
