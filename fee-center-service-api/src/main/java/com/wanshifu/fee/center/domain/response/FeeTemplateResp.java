package com.wanshifu.fee.center.domain.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("feeTemplate response body")
public class FeeTemplateResp {

    @JSONField(serializeUsing = ToStringSerializer.class)
    @ApiModelProperty(value = "模板id")
    private Long templateId;

    @ApiModelProperty(value = "费用名称")
    private String attributeDisplayName;

    @ApiModelProperty(value = "sku_no")
    private String skuNo;

    @ApiModelProperty(value = "费用类型")
    private String feeTypeTag;

    @ApiModelProperty(value = "sku类型")
    private String skuType;

    @ApiModelProperty(value = "服务名称")
    private String serviceName;
}
