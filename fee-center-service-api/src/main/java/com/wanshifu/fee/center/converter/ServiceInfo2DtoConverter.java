package com.wanshifu.fee.center.converter;

import com.wanshifu.fee.center.domain.dto.*;
import com.wanshifu.framework.utils.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class ServiceInfo2DtoConverter {

    public static List<ServiceDTO> convertServiceInfosToServiceDTOs(List<CalculateServiceInfo> serviceInfos) {
        if (CollectionUtils.isEmpty(serviceInfos)) {
            return Collections.emptyList();
        }

        return serviceInfos.stream()
                .map(ServiceInfo2DtoConverter::convertServiceInfoToServiceDTO)
                .collect(Collectors.toList());
    }

    private static ServiceDTO convertServiceInfoToServiceDTO(CalculateServiceInfo serviceInfo) {
        if (serviceInfo == null) {
            return null;
        }
        ServiceDTO serviceDto = new ServiceDTO();
        serviceDto.setServiceId(serviceInfo.getServiceId());
        serviceDto.setExternalServiceId(serviceInfo.getExternalServiceId());

        List<ServiceAttribute> rootAttributeDetailList = serviceInfo.getRootAttributeDetailList();
        if (CollectionUtils.isEmpty(rootAttributeDetailList)) {
            return serviceDto;
        }
        List<AttributeDTO> attributeDtoList = getAttributeDtoList(rootAttributeDetailList);
        serviceDto.setAttributeDtoList(attributeDtoList);
        return serviceDto;
    }

    public static void toAttributeDTO(ServiceAttribute attribute, AttributeDTO attributeDTO) {
        if (attribute == null || attributeDTO == null) {
            return;
        }
        attributeDTO.setAttributeId(attribute.getAttributeId());
        attributeDTO.setAttributeKey(attribute.getAttributeKey());
        attributeDTO.setAttributeName(attribute.getAttributeName());
        attributeDTO.setAttributeTag(attribute.getAttributeTag());
        attributeDTO.setAttributePathNo(attribute.getAttributePathNo());
        List<ServiceAttributeValue> serviceAttributeValues = attribute.getChildList();
        if (CollectionUtils.isNotEmpty(serviceAttributeValues)) {
            List<AttributeValueDTO> valueDtoList = serviceAttributeValues.stream().map(attributeValue -> {
                AttributeValueDTO value = new AttributeValueDTO();
                value.setAttributeValueId(attributeValue.getAttributeValueId());
                value.setAttributeValueKey(attributeValue.getAttributeValueKey());
                value.setAttributeValueName(attributeValue.getAttributeValueName());
                value.setAttributePathNo(attributeValue.getAttributePathNo());
                value.setValue(attributeValue.getValue() == null ? null : attributeValue.getValue().trim());
                List<ServiceAttribute> childAttributeList = attributeValue.getChildList();
                if (CollectionUtils.isNotEmpty(childAttributeList)) {
                    List<AttributeDTO> attributeDtoList = getAttributeDtoList(childAttributeList);
                    value.setAttributeDtoList(attributeDtoList);
                }
                return value;
            }).collect(Collectors.toList());
            attributeDTO.setAttributeValueDtoList(valueDtoList);
        }
    }

    private static List<AttributeDTO> getAttributeDtoList(List<ServiceAttribute> childAttributeList) {
        List<AttributeDTO> childAttributeDtoList = new ArrayList<>();
        for (ServiceAttribute serviceAttribute : childAttributeList) {
            AttributeDTO childAttributeDto = new AttributeDTO();
            toAttributeDTO(serviceAttribute, childAttributeDto);
            childAttributeDtoList.add(childAttributeDto);
        }
        return childAttributeDtoList;
    }

}
