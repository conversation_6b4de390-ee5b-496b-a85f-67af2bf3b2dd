package com.wanshifu.fee.center.domain.request.calculate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@ApiModel("计算动态价请求参数")
@Data
public class CalculateServiceDynamicPriceRequest {

    @ApiModelProperty(value = "原价格", required = true)
    @NotNull(message = "原价格不能为空")
    private BigDecimal originalCost;

    @ApiModelProperty(value = "算价场景编码", required = true)
    @NotBlank(message = "算价场景编码不能为空")
    private String sceneCode;

    @ApiModelProperty(value = "子场景编码")
    private String subSceneCode;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "服务id列表", required = true)
    @NotEmpty(message = "服务id列表不能为空")
    private Set<Long> serviceIds;

    @ApiModelProperty(value = "动态指标参数列表", required = true)
    @NotEmpty(message = "动态指标参数列表不能为空")
    private List<DynamicIndicatorParam> indicatorParamList;

}
