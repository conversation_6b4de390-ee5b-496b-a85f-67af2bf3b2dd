package com.wanshifu.fee.center.domain.request.feeRule;


import com.wanshifu.framework.core.validation.annotation.ValueIn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Set;

@Data
@ApiModel("费用规则基本信息请求")
public class FeeRuleBaseInfoForMasterReq {

    @ApiModelProperty(value = "场景编码", required = true)
    @NotBlank(message = "场景编码不能为空")
    private String sceneCode;

    @ApiModelProperty(value = "业务标签", required = true)
    @NotBlank(message = "业务标签不能为空")
    private String bizTag;

    @ApiModelProperty(value = "师傅ID", required = true)
    // 查询活动价时，没有师傅id
//    @NotBlank(message = "师傅ID不能为空")
    private String masterId;

    @ApiModelProperty(value = "服务类目ID", required = true)
    @NotEmpty(message = "服务类目ID不能为空")
    private Set<String> serviceCategoryIdList;

    @ApiModelProperty(value = "地区维度", required = true)
    @NotBlank(message = "地区维度不能为空")
    @ValueIn(value = "country,province,city,district,street", message = "地区维度不合法")
    private String divisionType;

    @ApiModelProperty(value = "division ID，当divisionType不为全国时，必填")
//    @NotEmpty(message = "街道ID不能为空")
    private Set<String> divisionIdList;

    // 2024-08-14 迭代应当删除改字段
//    @ApiModelProperty(value = "费用类型标签", required = true)
//    private Map<String, Set<String>> streetIdMap;
}
