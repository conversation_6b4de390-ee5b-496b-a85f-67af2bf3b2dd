package com.wanshifu.fee.center.domain.dto;

import com.wanshifu.adapter.dto.service.ServiceFeeDto;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class CalculateServiceInfo {

    /**
     * 服务索引
     */
    private String index;

    /**
     * 关联的检索类目id
     */
    private Long searchServiceCategoryId;

    /**
     * 检索类目层级
     */
    private String serviceSearchPath;

    /**
     * 服务配置版本号
     */
    @NotNull
    private String version;

    /**
     * 服务类型 ID
     */
    private Long serviceTypeId;

    /**
     * 一级商品类目id
     */
    private Long goodsCategoryIdL1;

    /**
     * 是否支持区域一口价
     */
    private Boolean isSupportAreaPrice;
    /**
     * 一级服务类目id
     */
    private Long serviceCategoryIdL1;

    /**
     * 指派类型ID
     */
    private Long serviceModelId;

    /**
     * 商品图库id
     */
    private Long ugId;

    /**
     * 对应服务品类的服务id
     */
    private Long serviceId;

    /**
     * 外部服务id，为了解决同一次请求有多个相同服务
     */
    private String externalServiceId;

    /**
     * 名称，一般是 商品名 + 服务类型 +指派类型
     */
    private String serviceName;

    /**
     * 商品类目id，用于搜索
     * 原来的goods表的id
     */
    private Long goodsCategoryId;
    /**
     * 二级类目id
     */
    private Long goodsCategoryChildId;

    /**
     * 服务类目ID
     * 对应原来的serve表的id
     */
    private Long serviceCategoryId;

    /**
     * 商品
     */
    private List<ServiceAttribute> rootAttributeDetailList;


    /**
     * 服务费用：基础服务费 平台服务费，最低服务费
     */
    private List<ServiceFeeDto> serviceFeeDtoList;

    /**
     * 是否已经计算过了，用于多场景且有优先级的计算场景
     */
    private Boolean isCalculated = false;
}
