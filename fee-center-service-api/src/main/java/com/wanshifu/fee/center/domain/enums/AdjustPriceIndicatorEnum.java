package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@AllArgsConstructor
@Getter
public enum AdjustPriceIndicatorEnum {

    FIXED("fixed", "固定调价"),
    AVERAGE_PUSH_DISTANCE("average_push_distance", "平均推单距离"),
    EXPOSURE_COUNT("exposure_count", "曝光量"),
    ORDER_TIME_ELAPSED("order_time_elapsed", "距离下单时间"),
    REMOTE_AREA("remote_area", "偏远地区"),
    ;

    private final String code;
    private final String name;

    public static AdjustPriceIndicatorEnum fromCode(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (AdjustPriceIndicatorEnum value : AdjustPriceIndicatorEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static List<String> getAllCodes() {
        return Arrays.stream(values())
                .map(AdjustPriceIndicatorEnum::getCode)
                .collect(Collectors.toList());
    }
}
