package com.wanshifu.fee.center.domain.request.scene;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

@Data
@ApiModel("获取对比场景编码列表request body")
public class GetComparisonSceneCodeListReq {

    @ApiModelProperty(value = "标准SKU类型", required = true)
    @NotBlank(message = "标准SKU类型不能为空")
    private String skuType;

}
