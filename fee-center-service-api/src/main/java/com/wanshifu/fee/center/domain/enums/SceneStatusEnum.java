package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

import java.util.Objects;

@AllArgsConstructor
public enum SceneStatusEnum {
    /**
     * 可用
     */
    ACTIVE(0, "激活"),
    ;
    public final int code;
    public final String name;

    public static SceneStatusEnum fromCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (SceneStatusEnum value : SceneStatusEnum.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }

}
