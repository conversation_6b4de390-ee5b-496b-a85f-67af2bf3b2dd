package com.wanshifu.fee.center.domain.document;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Document
@Data
@FieldNameConstants
public class DynamicFeeRule extends BaseDocument {

    @Indexed(unique = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long dynamicFeeRuleId;

    /**
     * 调价单元不能为空(按服务:service，按sku:sku)
     *
     * @see com.wanshifu.fee.center.domain.enums.AdjustPriceUnitEnum
     */
    private String adjustPriceUnit;

    @Indexed(background = true)
    private String sceneCode;

    private String subSceneCode;

    @Indexed(background = true)
    private String strategyName;

    @Indexed(background = true)
    private String divisionType;

    private String divisionTypeName;

    private List<Long> divisionIds;

    private List<Long> userIds;

    private List<ServiceData> serviceDataList;

    @Indexed(background = true)
    private Date startTime;
    @Indexed(background = true)
    private Date endTime;

    @Indexed(background = true)
    private String feeTypeTag;

    private String feeTypeTagName;

    @Indexed(background = true)
    private String operator;

    /**
     * 备注
     */
    private String note;

    private DynamicCalculateRuleData dynamicCalculateRuleData;

    @Indexed(background = true)
    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private List<String> templateIds;

    // 业务标识（如 招募id...）
    private String bizTag;

    // 动态指标
    private List<DynamicPricingIndicator> dynamicPricingIndicators;

}
