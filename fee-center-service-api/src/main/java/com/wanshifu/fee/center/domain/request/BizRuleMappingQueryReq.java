package com.wanshifu.fee.center.domain.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.wanshifu.framework.core.page.Pager;
import lombok.Data;

import java.util.Map;

@Data
public class BizRuleMappingQueryReq extends Pager {
    private Long bizRuleMappingId;
    private Long templateId;
    private String sceneCode;
    // 在算费时需要进行组完整性校验
    private String group;
    private String sceneName;
    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private Map<String, String> fromBizRule;
    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private Map<String, String> toBizRule;
}
