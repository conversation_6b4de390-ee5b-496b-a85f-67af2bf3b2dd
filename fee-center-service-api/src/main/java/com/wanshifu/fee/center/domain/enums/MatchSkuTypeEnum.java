package com.wanshifu.fee.center.domain.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
public enum MatchSkuTypeEnum {

    CURRENT_SKU("currentSku", "当前sku"),
    OTHER_SKU("otherSku", "其它sku");

    private final String code;

    private final String name;

    MatchSkuTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static MatchSkuTypeEnum getByCode(String code) {
        for (MatchSkuTypeEnum value : MatchSkuTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 返回所有 code 的 List 形式
     */
    public static List<String> getAllCodes() {
        return Arrays.stream(values())
                .map(MatchSkuTypeEnum::getCode)
                .collect(Collectors.toList());
    }
}
