package com.wanshifu.fee.center.domain.request.sameday;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

@Data
@ApiModel("获取当日装价格request body")
public class GetPriceRequest {

    @ApiModelProperty(value = "一级类目id", required = true)
    @NotNull(message = "一级类目id不能为空")
    private Long level1GoodsCategoryId;

    @ApiModelProperty(value = "区县id", required = true)
    @NotBlank(message = "区县id不能为空")
    private String districtId;

    @ApiModelProperty(value = "详细地址", required = true)
    @NotBlank(message = "详细地址不能为空")
    private String addressDetail;

}
