package com.wanshifu.fee.center.domain.request.template;

import cn.hutool.core.util.StrUtil;
import com.wanshifu.framework.core.page.Pager;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("获取模板分页列表request body")
public class FeeTemplatePageListRequest extends Pager {

    @ApiModelProperty(value = "场景编码", required = true)
    @NotBlank(message = "场景编码不能为空")
    private String sceneCode;

    @ApiModelProperty(value = "服务类目id")
    private List<String> serviceCategoryIdList;

    @ApiModelProperty(value = "服务id")
    private List<String> serviceIdList;

    @ApiModelProperty(value = "一级商品类目id")
    private List<String> level1GoodsCategoryIdList;

    @ApiModelProperty(value = "费用类型标签")
    private List<String> feeTypeTagList;

    public void validate() {
        if (getPageNum() == null || getPageNum() <= 0 || getPageSize() == null || getPageSize() <= 0) {
            throw new IllegalArgumentException(StrUtil.format("分页参数不合法,pageNum={}, pageSize={}", getPageNum(), getPageSize()));
        }

        if (StringUtils.isBlank(sceneCode) &&
            CollectionUtils.isEmpty(serviceCategoryIdList) &&
            CollectionUtils.isEmpty(serviceIdList) &&
            CollectionUtils.isEmpty(level1GoodsCategoryIdList)) {

            throw new IllegalArgumentException("sceneCode, serviceCategoryId、serviceId、一级商品类目id不能同时为空");
        }

        int maxPageSize = 1000;
        if (getPageSize() > maxPageSize) {
            throw new IllegalArgumentException(StrUtil.format("每页不能超过{}条", maxPageSize));
        }
    }

}
