package com.wanshifu.fee.center.domain.request.formula;

import com.wanshifu.fee.center.domain.document.CalculateRuleData;
import com.wanshifu.fee.center.domain.enums.DivisionTypeEnum;
import com.wanshifu.fee.center.domain.enums.FormulaTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @author: <PERSON>
 * @create: 2024-02-27 14:19
 * @description: 公式 base request body
 */
@Data
@ApiModel("添加公式request body")
public class FormulaBaseReq {

    @NotBlank
    @Size(max = 40)
    @ApiModelProperty(value = "公式名称", required = true)
    private String formulaName;

    @NotBlank
    @ApiModelProperty("公式类别，单价公式：unit_price，总价公式：total_price")
    private String formulaType = FormulaTypeEnum.UNIT_PRICE.getCode();

    @NotBlank
    @ApiModelProperty("区域维度，默认【城市】")
    private String divisionType = DivisionTypeEnum.CITY.code;

    @NotNull
    @ApiModelProperty(value = "公式", required = true)
    private CalculateRuleData formulaContent;

    @Size(max = 6400)
    @ApiModelProperty("地区Id，多个用英文逗号分隔")
    private String divisionIds;

    @ApiModelProperty("业务Id，多个用英文逗号分隔")
    private String bizIds;

}
