package com.wanshifu.fee.center.domain.document.master2c;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Id;
import java.util.Date;
import java.util.List;

@Data
@Document
@FieldNameConstants
public class ServePriceOperationLog {

    @Id
    private String id;

    /**
     * ServePrice表的priceId，即每一条ServePrice只有一条日志
     */
    @Indexed(unique = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long priceId;

    private List<OperationLogDetail> operationLogDetailList;

    @Data
    @FieldNameConstants
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OperationLogDetail {

        @JSONField(serializeUsing = ToStringSerializer.class)
        private Long logId;

        /**
         * @see com.wanshifu.fee.center.domain.enums.master2c.ServePriceOperationTypeEnum
         */
        private String operationType;

        private String operator;

        private String remark;

        private Date operationTime;

    }
}
