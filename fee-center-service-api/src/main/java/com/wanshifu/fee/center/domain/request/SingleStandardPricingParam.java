package com.wanshifu.fee.center.domain.request;

import com.wanshifu.fee.center.domain.dto.AddressInfo;
import com.wanshifu.fee.center.domain.dto.ServiceDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "标准计价接口入参")
@Data
public class SingleStandardPricingParam extends StandardPricingCommonParam {

    @ApiModelProperty(value = "场景编码", required = true)
    @NotBlank(message = "场景编码不能为空")
    private String sceneCode;

    @ApiModelProperty(value = "服务信息")
    @NotNull(message = "服务信息不能为空")
    private ServiceDTO serviceDto;

    @ApiModelProperty(value = "完整地址信息")
    private AddressInfo addressInfo;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal goodsNumber;

    @ApiModelProperty(value = "计价任务id（每次http请求的唯一id）")
    private Long tid;

}
