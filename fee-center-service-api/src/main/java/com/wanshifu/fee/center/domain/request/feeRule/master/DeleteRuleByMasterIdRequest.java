package com.wanshifu.fee.center.domain.request.feeRule.master;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Set;

@Data
@ApiModel("根据师傅id删除计价规则request body")
public class DeleteRuleByMasterIdRequest {

    @NotBlank(message = "场景编码不能为空")
    @ApiModelProperty(value = "场景编码", required = true)
    private String sceneCode;

    @NotBlank(message = "师傅id不能为空")
    @ApiModelProperty(value = "师傅id", required = true)
    private String masterId;

    @NotBlank(message = "招募id不能为空")
    @ApiModelProperty(value = "招募id", required = true)
    private String recruitId;

    @NotEmpty(message = "街道id列表不能为空")
    @ApiModelProperty(value = "街道id列表", required = true)
    private Set<String> streetIds;

}
