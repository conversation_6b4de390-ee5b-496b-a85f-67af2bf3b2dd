package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

import java.util.Objects;

@AllArgsConstructor
public enum ExportTaskStatusEnum {
    /**
     *
     */
    SUCCESS(0, "导出完成"),
    /**
     * task已入库
     */
    EXPORT(1, "导出中"),
    /**
     *
     */
    FAIL(2, "导出失败");

    public final int code;
    public final String name;

    public static ExportTaskStatusEnum fromCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (ExportTaskStatusEnum value : ExportTaskStatusEnum.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
