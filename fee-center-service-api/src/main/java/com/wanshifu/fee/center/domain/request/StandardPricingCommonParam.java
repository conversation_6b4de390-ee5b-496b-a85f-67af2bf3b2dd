package com.wanshifu.fee.center.domain.request;

import com.wanshifu.fee.center.domain.dto.CoreAddressInfo;
import com.wanshifu.fee.center.domain.dto.BizInfo;
import com.wanshifu.fee.center.domain.dto.OrderBase;
import com.wanshifu.fee.center.domain.request.calculate.DynamicIndicatorParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel(description = "标准计价接口入参")
@Data
public class StandardPricingCommonParam {

    @ApiModelProperty(value = "订单基础信息")
    protected OrderBase orderBase;

//    @ApiModelProperty(value = "账户信息")
//    protected AccountInfo accountInfo;

    @ApiModelProperty(value = "核心地址信息")
    protected CoreAddressInfo coreAddressInfo;

    @ApiModelProperty(value = "业务信息")
    protected BizInfo bizInfo;

    @ApiModelProperty(value = "是否需要做计价模板与计价规则的完整性校验")
    protected Boolean needIntegrityValidation = true;

    @ApiModelProperty(value = "是否按场景优先级合并返回结果")
    protected Boolean mergeByPriority = false;

    @ApiModelProperty(value = "动态计价时间(该时间需要落在动态计价范围，才会计价动态价)")
    protected Date dynamicCalculateTime;

    @ApiModelProperty(value = "是否需要映射计价")
    protected Boolean isMappingPricing;

    @ApiModelProperty(value = "映射目标场景编码")
    protected String targetSceneCode;

    @ApiModelProperty(value = "子场景编码")
    protected String subSceneCode;

    @ApiModelProperty(value = "动态指标参数")
    List<DynamicIndicatorParam> dynamicIndicatorParamList;

}
