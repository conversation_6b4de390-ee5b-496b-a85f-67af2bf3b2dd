package com.wanshifu.fee.center.domain.request.feeRule;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * @author: <PERSON>
 * @create: 2024-05-07 17:14
 * @description: 根据业务Id查询、删除request body
 */
@Data
@ApiModel("根据业务Id查询、删除request body")
public class DeleteByBizIdReq {

    @ApiModelProperty(value = "场景code", example = "contract_master", required = true)
    @NotBlank(message = "场景code不能为空")
    private String sceneCode;

    @ApiModelProperty(value = "服务id", example = "1234", required = true)
    @NotBlank(message = "服务id不能为空")
    private String serviceId;

    @ApiModelProperty(value = "业务Id", required = true)
    @NotBlank(message = "业务Id不能为空")
    private String bizId;

    @ApiModelProperty(value = "业务标签")
    private String bizTag;

    @ApiModelProperty(value = "价格地区维度", required = true)
    @NotBlank(message = "价格地区维度不能为空")
    private String divisionType;
}
