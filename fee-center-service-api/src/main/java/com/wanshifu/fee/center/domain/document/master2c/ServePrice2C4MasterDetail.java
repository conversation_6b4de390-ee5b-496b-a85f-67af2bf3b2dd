package com.wanshifu.fee.center.domain.document.master2c;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.wanshifu.fee.center.domain.enums.MasterPrice2CActivationMethodEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Id;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Document
@ApiModel("服务价格")
public class ServePrice2C4MasterDetail {

    @Id
    private String id;

    @Indexed(unique = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long logId;

    @ApiModelProperty("服务价格id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long priceId;

    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("服务id")
    private Long serveId;

    @ApiModelProperty("服务名称")
    private String serveName;

    @ApiModelProperty("一级服务id")
    private Long level1ServeId;

    @ApiModelProperty("一级服务名称")
    private String level1ServeName;

    @ApiModelProperty("二级服务id")
    private Long level2ServeId;

    @ApiModelProperty("二级服务名称")
    private String level2ServeName;

    @ApiModelProperty("三级服务id")
    private Long level3ServeId;

    @ApiModelProperty("三级服务名称")
    private String level3ServeName;

    @NotNull(message = "优先级不能为空")
    private Integer priority;

    @ApiModelProperty("师傅侧基础价格")
    private BigDecimal masterBasePrice;

    private List<ServePrice2C4MasterDetail.ServeConfig> serveConfigs;

    private List<Long> cityIds;

    private ServePrice2C4MasterDetail.CityGroup cityGroup;

    /**
     * 生效方式
     *
     * @see MasterPrice2CActivationMethodEnum
     */
    @ApiModelProperty("生效方式")
    private String activationMethod;

    @ApiModelProperty("开始生效时间")
    private Date startTime;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("创建时间")
    private Date createTime;


    @Data
    public static class CityGroup {

        @ApiModelProperty("城市群组分类id")
        private String classifyId;

        @ApiModelProperty("城市群组id")
        private Long cityGroupId;

        @ApiModelProperty("城市群组城市id列表")
        private List<Long> cityIds;
    }

    @Data
    public static class ServeConfig {

        @ApiModelProperty("服务配置id")
        private Long serveConfigId;

        @ApiModelProperty("服务配置属性名称")
        private String attributeFullName;

        private String style;

        private String styleAttr;

        private String attachShowType;

        // 价格类型, 1:固定价格, 2:按单价, 3:按梯度自由定价, 4:基础+梯度定价
        private String priceType;

        private String inputType;

        private String minValue;

        private String maxValue;

        @ApiModelProperty("关联参数id")
        private Long relationId;

        @ApiModelProperty("关联参数名称")
        private String relationConfigName;

        @ApiModelProperty("师傅价格")
        private BigDecimal masterPrice;

        @ApiModelProperty("选项")
        private List<ServePrice2C4MasterDetail.ServeConfig.Option> options;

        @ApiModelProperty("附加选项")
        private List<ServePrice2C4MasterDetail.ServeConfig.Option> attaches;

        @Data
        public static class Option {

            @ApiModelProperty("选项id（外部）")
            private String externalId;

            private String name;

            private String minValue;

            private String maxValue;

            private String attachShowType;

            // 价格类型, 1:固定价格, 2:按单价, 3:按梯度自由定价, 4:基础+梯度定价
            private String priceType;

            private Integer sort;

            @ApiModelProperty("师傅价格")
            private BigDecimal masterPrice;

            private List<ServePrice.ServeConfig.Option> relationOptionList;
        }
    }
}
