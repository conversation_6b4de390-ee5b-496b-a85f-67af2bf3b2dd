package com.wanshifu.fee.center.domain.enums;

public enum RecruitActivityMasterBuildTypeEnum {

    CREATE_RECRUIT_ACTIVITY_MASTER("createRecruitActivityMaster", "创建招募活动师傅价，即有活动、师傅"),
    MODIFY_RECRUIT_ACTIVITY_MASTER("modifyRecruitActivityMaster", "修改招募活动师傅价，即有活动、师傅"),
    SAVE_RECRUIT_MASTER("saveRecruitMaster", "保存（新增或修改）招募师傅价，即无活动（活动id=0）、有师傅"),
    ;

    public final String code;
    public final String desc;

    RecruitActivityMasterBuildTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
