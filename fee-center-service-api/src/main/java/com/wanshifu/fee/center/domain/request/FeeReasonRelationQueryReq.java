package com.wanshifu.fee.center.domain.request;

import lombok.Data;

import java.util.List;

/**
 * @author: <PERSON>
 * @create: 2023-11-01 17:43
 * @description: 增减费用项关联平台SKU request body
 */
@Data
public class FeeReasonRelationQueryReq {

    /*
     * ------------下面是管理条件查询条件-----------
     */

    /**
     * 一级类目
     */
    private Long level1GoodsCategoryId;

    /**
     * 二级类目
     */
    private Long level2GoodsCategoryId;
    /**
     * 三级类目
     */
    private Long level3GoodsCategoryId;
    /**
     * 报价模式 2 / 4
     */
    private String serviceMode;
    /**
     * sku类型 全部 总包 用户
     */
    private String skuType;
    /**
     * 服务名称
     */
    private String serviceName;


    /*
     * ------------下面是反查条件-----------
     */
    private Long reasonId;
    //    private String skuNo;
    private List<Long> serviceIds;

    private String skuRuleScene;


}
