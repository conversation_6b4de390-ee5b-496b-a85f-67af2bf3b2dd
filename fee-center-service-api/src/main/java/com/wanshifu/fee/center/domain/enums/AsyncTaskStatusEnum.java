package com.wanshifu.fee.center.domain.enums;

import com.wanshifu.framework.utils.StringUtils;
import lombok.Getter;

@Getter
public enum AsyncTaskStatusEnum {

    RUNNING("running", "执行中"),

    SUCCESS("success", "执行成功"),

    FAIL("fail", "执行失败");

    private final String code;
    private final String desc;

    AsyncTaskStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AsyncTaskStatusEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (AsyncTaskStatusEnum statusEnum : AsyncTaskStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
