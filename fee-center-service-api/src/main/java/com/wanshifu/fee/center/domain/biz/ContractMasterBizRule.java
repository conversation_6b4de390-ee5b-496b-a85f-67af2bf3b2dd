package com.wanshifu.fee.center.domain.biz;

import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class ContractMasterBizRule {
    private String level4DivisionId;
    private String templateId;
    private String serviceModelId;
    private String attributeValueMax;
    private String pricingMode;
    private String feeInputType;
    private String goodsCategoryId;
    private String sceneCode;
    private String feeTypeTagName;
    private String price;
    private String skuAttributePathName;
    private String sceneId;
    private String serviceTypeId;
    private String serviceId;
    private String skuNumberPathNo;
    private String serviceCategoryId;
    private String group;
    private String level2DivisionId;
    private String skuNumberName;
    private String feeTypeTag;
    private String attributeValueMin;
    private String feeUnit;
    private String skuNumberExpress;
    private String sceneName;
    private String serviceName;
    private String skuType;
    private String masterInputPrice;
    private String attributeDisplayName;
    private String masterId;
    private String level1DivisionId;
    private String goodsCategoryName;
    private String feeName;
    private String skuNo;
    private String level3DivisionId;
    private String serviceTypeName;
    private String skuNoIndex;

    private String divisionType;
    private String divisionTypeName;

    private String province;

    private String city;

    private String district;

    private String street;

    private String batchTaskId;

    private String bizTag;

    private String applyFlag;

}
