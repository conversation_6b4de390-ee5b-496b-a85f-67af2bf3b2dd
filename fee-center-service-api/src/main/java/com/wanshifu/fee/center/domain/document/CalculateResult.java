package com.wanshifu.fee.center.domain.document;


import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;
import java.util.Map;


@EqualsAndHashCode(callSuper = true)
@Document
@FieldNameConstants
@Data
public class CalculateResult extends BaseDocument {

    @Indexed(unique = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long calculateResultId;
    @Indexed(background = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long tid;
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long feeRuleId;
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long templateId;
    private String sceneCode;
    // 在算费时需要进行组完整性校验
    @Indexed(background = true)
    private String group;
    private BigDecimal cost;
    private BigDecimal costMax;
    // 添加调价信息
    // 关联的调价规则id
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long dynamicFeeRuleId;
    // 调价后的价格
    private BigDecimal dynamicFeeCost;
    private BigDecimal dynamicFeeCostMax;
    // 动态价策略名称（优惠价名称）
    private String dynamicFeeStrategyName;

    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private Map<String, String> bizRule;

    private CalculateRuleData calculateRuleData;

    private DynamicCalculateRuleData dynamicCalculateRuleData;
}