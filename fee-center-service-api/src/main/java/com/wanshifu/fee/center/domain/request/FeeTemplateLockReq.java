package com.wanshifu.fee.center.domain.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

@Data
public class FeeTemplateLockReq {
    @NotEmpty
    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private List<Long> templateIds;
}
