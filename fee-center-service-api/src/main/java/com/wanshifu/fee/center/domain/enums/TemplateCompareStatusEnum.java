package com.wanshifu.fee.center.domain.enums;

import lombok.Getter;

@Getter
public enum TemplateCompareStatusEnum {

    ADD("add", "基础库已新增"),
    MODIFIED("modified", "计价数量变更"),
    UNMODIFIED("--", "未修改"), // 为了与旧版本兼容
    DELETED("deleted", "基础库已删除");

    private final String code;
    private final String desc;

    TemplateCompareStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
