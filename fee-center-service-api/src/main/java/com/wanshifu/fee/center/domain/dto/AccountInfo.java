package com.wanshifu.fee.center.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("账户信息")
@Data
public class AccountInfo {

    /**
     * @see com.wanshifu.fee.center.domain.enums.AccountTypeEnum
     */
    @ApiModelProperty(value = "账户类型，用户:user、师傅:master、总包:enterprise")
    private String accountType;

    @ApiModelProperty(value = "账户id")
    private Long accountId;
}
