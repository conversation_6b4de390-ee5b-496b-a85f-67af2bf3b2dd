package com.wanshifu.fee.center.domain.request.template;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

/**
 * @author: <PERSON>
 * @create: 2024-02-20 17:27
 * @description: 复制计价属性request body
 */
@Data
@ApiModel("复制计价属性request body")
public class CopyPricingAttributeReq {

    @NotBlank(message = "目标场景编号不能为空")
    @ApiModelProperty(value = "目标场景编号", required = true)
    private String targetSceneCode;

    @ApiModelProperty("目标场景名称")
    private String targetSceneName;

    @NotEmpty(message = "服务ID集合不能为空")
    @ApiModelProperty(value = "服务ID集合", required = true)
    private List<Long> ServiceIdList;

    @NotBlank(message = "源场景编号不能为空")
    @ApiModelProperty(value = "源场景编号", required = true)
    private String sourceSceneCode;
}
