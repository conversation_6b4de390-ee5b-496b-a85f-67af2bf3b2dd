package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

import java.util.Objects;

@AllArgsConstructor
public enum MappingStatusEnum {
    /**
     * 审核通过，不可操作
     */
    PASS(0, "审核通过"),
    /**
     * 锁定态业务端不可用，管理后台可用
     */
    AUDIT(1, "待审核"),
    /**
     * 已拒绝
     */
    REJECT(2, "拒绝");
    public final int code;
    public final String name;

    public static MappingStatusEnum fromCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (MappingStatusEnum value : MappingStatusEnum.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
