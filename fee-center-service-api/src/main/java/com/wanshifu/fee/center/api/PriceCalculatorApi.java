package com.wanshifu.fee.center.api;

import com.wanshifu.fee.center.config.FeeCenterEncoder;
import com.wanshifu.fee.center.domain.request.calculate.*;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        value = "fee-center-service",
        path = "priceCalculator",
        configuration = {DefaultDecoder.class, FeeCenterEncoder.class, DefaultErrorDecode.class},
        url = "${wanshifu.fee-center-service.url}")
public interface PriceCalculatorApi {

    @PostMapping("standardPricing")
    StandardPricingResponse standardPricing(@RequestBody @Validated StandardPricingRequest request);

    @PostMapping("calculateAndComparePrice")
    CalculateAndComparePriceResponse calculateAndComparePrice(@RequestBody @Validated CalculateAndComparePriceRequest request);

    /**
     * 计算动态价
     *
     * @param request 请求参数
     * @return 动态价计算结果
     */
    @PostMapping("calculateServiceDynamicPrice")
    CalculateServiceDynamicPriceResponse calculateServiceDynamicPrice(@RequestBody @Validated CalculateServiceDynamicPriceRequest request);
}

