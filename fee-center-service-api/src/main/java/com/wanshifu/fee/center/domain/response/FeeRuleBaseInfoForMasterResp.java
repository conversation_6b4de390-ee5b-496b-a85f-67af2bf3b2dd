package com.wanshifu.fee.center.domain.response;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("费用规则基本信息响应")
public class FeeRuleBaseInfoForMasterResp {

//    private Map<String, Set<Long>> feeRuleIdMap;

    private List<FeeRuleBaseInfoForMasterRespDetail> detailList;

    @Data
    public static class FeeRuleBaseInfoForMasterRespDetail {
        private String serviceCategoryId;
        private String level2DivisionId;
        private String level3DivisionId;
        private String level4DivisionId;
        private String feeTypeTag;
        private String feeTypeTagName;
        private String feeUnit;
        private String attributeDisplayName;
        private String masterInputPrice;
        private Long templateId;
        private Long feeRuleId;
    }
}
