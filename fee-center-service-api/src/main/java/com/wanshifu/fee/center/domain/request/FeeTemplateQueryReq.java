package com.wanshifu.fee.center.domain.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.wanshifu.framework.core.page.Pager;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class FeeTemplateQueryReq extends Pager {
    private Long templateId;
    private String sceneCode;
    // 在算费时需要进行组完整性校验
    private String group;
    private String sceneName;

    // 费用类型是否不能为空
    private Boolean feeTypeTagNotBlank = false;

    // 默认不限制, 1:锁定，0：可用
    private List<Integer> statusList;

    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private Map<String, String> bizRule;
}
