package com.wanshifu.fee.center.domain.request.feeRule.master;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Set;

@Data
@ApiModel("创建招募活动师傅request body")
public class CreateRecruitActivityMasterReq {

    @ApiModelProperty(value = "场景code")
//    @NotBlank(message = "场景code不能为空")
    // 这个参数无需调用方传，因为这个是定制接口，sceneCode固定为contract_master
    private String sceneCode;

    @ApiModelProperty(value = "师傅ID", required = true)
    @NotBlank(message = "师傅ID不能为空")
    private String masterId;

    @ApiModelProperty(value = "招募ID", required = true)
    @NotBlank(message = "招募ID不能为空")
    private String recruitId;

    @ApiModelProperty(value = "区域类型", required = true)
    @NotBlank(message = "区域类型不能为空")
    private String divisionType;

    @ApiModelProperty(value = "区域ID列表", required = true)
    @NotEmpty(message = "区域ID列表不能为空")
    private Set<String> streetIds;

    @ApiModelProperty(value = "服务类目ID列表", required = true)
    @NotEmpty(message = "服务类目ID列表不能为空")
    private Set<String> serviceCategoryIds;

    @ApiModelProperty(value = "是否为追加街道")
    private boolean isAddStreet = false;

}
