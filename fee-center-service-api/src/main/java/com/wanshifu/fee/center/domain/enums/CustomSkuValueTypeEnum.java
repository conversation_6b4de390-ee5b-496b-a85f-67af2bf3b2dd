package com.wanshifu.fee.center.domain.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
public enum CustomSkuValueTypeEnum {

    FIXED("fixed", "具体值"),

    RANGE("range", "区间"),
    ;

    private final String code;
    private final String desc;

    CustomSkuValueTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CustomSkuValueTypeEnum getByCode(String code) {
        for (CustomSkuValueTypeEnum value : CustomSkuValueTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 返回所有 code 的 List 形式
     */
    public static List<String> getAllCodes() {
        return Arrays.stream(values())
                .map(CustomSkuValueTypeEnum::getCode)
                .collect(Collectors.toList());
    }
}
