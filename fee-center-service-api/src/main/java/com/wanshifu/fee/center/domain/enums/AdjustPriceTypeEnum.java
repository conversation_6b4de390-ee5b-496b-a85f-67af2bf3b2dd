package com.wanshifu.fee.center.domain.enums;

import lombok.Getter;

import java.util.Objects;
import java.util.function.BiFunction;

@Getter
public enum AdjustPriceTypeEnum {

    FIXED("fixed", "固定金额", (basePrice, adjustValue) -> adjustValue),

    PERCENT("percent", "百分比", (basePrice, adjustValue) -> basePrice * adjustValue / 100),
    ;

    private final String code;
    private final String desc;
    private final BiFunction<Double, Double, Double> adjustPriceTypeFunction;

    AdjustPriceTypeEnum(String code, String desc, BiFunction<Double, Double, Double> adjustPriceTypeFunction) {
        this.code = code;
        this.desc = desc;
        this.adjustPriceTypeFunction = adjustPriceTypeFunction;
    }

    public static AdjustPriceTypeEnum fromCode(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (AdjustPriceTypeEnum value : AdjustPriceTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public Double getDeltaPrice(Double basePrice, Double adjustValue) {
        return adjustPriceTypeFunction.apply(basePrice, adjustValue);
    }
}
