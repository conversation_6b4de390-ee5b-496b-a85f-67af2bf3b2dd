package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;


/**
 * //支持 +,-,*,/,<,>,<=,>=,==,!=,<>【等同于!=】,%,mod【取模等同于%】,++,--,
 * //in【类似sql】,like【sql语法】,&&,||,!,等操作符
 * //支持for，break、continue、if then else 等标准的程序控制逻辑
 * <p>
 * 不支持try{}catch{}
 * 注释目前只支持 /**，不支持单行注释 //
 * 不支持java8的lambda表达式
 * 不支持for循环集合操作for(Item item:list)
 * 弱类型语言，请不要定义类型声明,更不要用Template（Map<String, List>之类的）
 * array的声明不一样
 * min,max,round,print,println,like,in 都是系统默认函数的关键字，请不要作为变量名
 * <p>
 * max(num1,num2)     用于对字段取最大值     max(2, 1) == 2
 * <p>
 * min(num1,num2)     用于对字段取最小值     min(2, 1) == 1
 * <p>
 * round(number,decimals)     对数值进行四舍五入     round(123.456,2) == 123.46
 * <p>
 * ceil(number)     向上取整     ceil(20.25) == 21
 */
@AllArgsConstructor
public enum ExpressOperatorEnum {
    IN("in", "在...中", false, true),
    LIKE("like", "模糊匹配", false, true),
    LT("<", "小于", false, true),
    LE("<=", "小于等于", false, true),
    EQ("==", "等于", false, true),
    NE("!=", "不等于", false, true),
    GT(">", "大于", false, true),
    GE(">=", "大于等于", false, true),

    AND("&&", "且", false, false),
    OR("||", "或", false, false),
    NOT("!", "非", true, false),
    ;
    public final String code;
    public final String name;
    public final boolean isSingleOperator;
    public final boolean useInExpress;

    public static ExpressOperatorEnum fromCode(String code) {
        for (ExpressOperatorEnum value : ExpressOperatorEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
