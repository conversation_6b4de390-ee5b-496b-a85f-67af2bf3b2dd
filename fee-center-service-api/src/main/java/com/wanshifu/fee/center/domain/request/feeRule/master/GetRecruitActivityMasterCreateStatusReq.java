package com.wanshifu.fee.center.domain.request.feeRule.master;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Max;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class GetRecruitActivityMasterCreateStatusReq {

    @NotBlank(message = "师傅id不能为空")
    private String masterId;

    @NotEmpty(message = "招募id不能为空")
    @Size(max = 100, message = "招募id不能超过100个，如果超过，请分页")
    private List<String> recruitIds;
}
