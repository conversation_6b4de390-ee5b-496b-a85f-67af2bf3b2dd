package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: <PERSON>
 * @create: 2025-06-30 16:55
 * @description: 上传价格告警-价格比较状态
 */
@Getter
@AllArgsConstructor
public enum PriceComparisonStatusEnum {

    NORMAL("normal", "未超过阈值"),

    ALERT("alert", "警告"),

    COMPARING("comparing", "正在对比"),

    UNRECOGNIZED("unrecognized", "无法识别")
    ;

    private final String code;
    private final String desc;

    public static PriceComparisonStatusEnum getByCode(String code) {
        for (PriceComparisonStatusEnum value : PriceComparisonStatusEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

}
