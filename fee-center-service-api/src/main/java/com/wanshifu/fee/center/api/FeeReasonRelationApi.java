package com.wanshifu.fee.center.api;


import com.wanshifu.fee.center.config.FeeCenterEncoder;
import com.wanshifu.fee.center.domain.document.FeeReasonRelation;
import com.wanshifu.fee.center.domain.request.FeeReasonRelationMaintainReq;
import com.wanshifu.fee.center.domain.request.FeeReasonRelationQueryReq;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(value = "fee-center-service", path = "feeReasonRelation", configuration = {DefaultDecoder.class, FeeCenterEncoder.class, DefaultErrorDecode.class}, url = "${wanshifu.fee-center-service.url}")
public interface FeeReasonRelationApi {

    @PostMapping("query")
    List<FeeReasonRelation> query(@RequestBody @Validated FeeReasonRelationQueryReq feeReasonRelationQueryReq);

    @PostMapping("maintain")
    List<FeeReasonRelation> maintain(@RequestBody @Validated FeeReasonRelationMaintainReq feeReasonRelationMaintainReq);

}
