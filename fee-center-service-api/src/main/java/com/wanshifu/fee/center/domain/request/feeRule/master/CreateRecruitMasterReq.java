package com.wanshifu.fee.center.domain.request.feeRule.master;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

@Data
@ApiModel("创建招募师傅")
public class CreateRecruitMasterReq {

    @ApiModelProperty(value = "场景code", required = true)
    @NotBlank(message = "场景code不能为空")
    private String sceneCode;

    @ApiModelProperty(value = "师傅id", required = true)
    @NotBlank(message = "师傅id不能为空")
    private String masterId;

    @ApiModelProperty(value = "招募id", required = true)
    @NotBlank(message = "招募id不能为空")
    private String recruitId;

    @ApiModelProperty(value = "费用规则信息", required = true)
    @NotEmpty(message = "费用规则信息不能为空")
    private List<CreateRecruitFeeRuleInfo> feeRuleInfoList;
}
