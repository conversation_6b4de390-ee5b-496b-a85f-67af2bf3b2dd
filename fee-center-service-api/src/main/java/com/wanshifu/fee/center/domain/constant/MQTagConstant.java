package com.wanshifu.fee.center.domain.constant;

public class MQTagConstant {

    // 自产自销tag
    public static final String RECRUIT_ACTIVITY_MASTER_ASYNC_CREATE = "recruitActivityMasterAsyncCreate";
    public static final String DELETE_RULE_BY_MASTER_ID_ASYNC = "deleteRuleByMasterIdAsync";
    public static final String DELETE_RECRUIT_MASTER_BATCH_ASYNC = "deleteRecruitMasterBatchAsync";

    public static final String PRE_GENERATE = "preGenerate";

    public static final String CREATE_RECRUIT_ACTIVITY = "createRecruitActivity";
    public static final String MODIFY_RECRUIT_ACTIVITY_MASTER_ASYNC = "modifyRecruitActivityMasterAsync";
    public static final String CREATE_RECRUIT_MASTER_ASYNC = "createRecruitMasterAsync";

    // 通知调用方tag
    public static final String NOTIFY_CALLER_CREATE_RECRUIT_MASTER = "notifyCallerCreateRecruitMaster";
    public static final String NOTIFY_CALLER_MODIFY_RECRUIT_ACTIVITY_MASTER = "notifyCallerModifyRecruitActivityMaster";
    public static final String NOTIFY_CALLER_CREATE_RECRUIT_ACTIVITY_MASTER = "createRecruitActivityMasterAsync";
    public static final String NOTIFY_CALLER_RECREATE_RECRUIT_MASTER = "recruitMasterAddStreetAsync";


}
