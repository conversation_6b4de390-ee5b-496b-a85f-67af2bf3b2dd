package com.wanshifu.fee.center.domain.response.sameday;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("获取当日装价格response body")
public class GetPriceResponse {

    @ApiModelProperty(value = "价格", required = true)
    private Integer price;

    @ApiModelProperty(value = "无价原因", required = true)
    private String reasonForPriceIsNull;

}
