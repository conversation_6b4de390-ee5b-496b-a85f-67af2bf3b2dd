package com.wanshifu.fee.center.domain.request.feeRule;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.wanshifu.fee.center.domain.document.CalculateRuleData;
import com.wanshifu.fee.center.domain.dto.FeeBizRuleDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class BatchAddRulesRequest {

    @NotEmpty(message = "feeRuleDataList不能为空")
    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private List<BatchAddRulesRequest.FeeRuleData> feeRuleDataList;

    @Data
    public static class FeeRuleData {

        @ApiModelProperty("场景编码")
        private String sceneCode;

        @ApiModelProperty("模板id")
        private Long templateId;

        @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
        @NotNull(message = "bizRuleDto不能为空")
        private FeeBizRuleDTO bizRuleDto;

        private CalculateRuleData calculateRuleData;
    }
}
