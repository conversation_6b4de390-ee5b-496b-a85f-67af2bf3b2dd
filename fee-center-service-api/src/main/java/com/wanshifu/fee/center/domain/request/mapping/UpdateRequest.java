package com.wanshifu.fee.center.domain.request.mapping;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("更新映射request body")
public class UpdateRequest extends SaveBaseRequest{

    @NotNull(message = "映射id不能为空")
    @JSONField(serializeUsing = ToStringSerializer.class)
    @ApiModelProperty(value = "映射id", required = true)
    private Long mappingId;

    public void validate() {
        Long templateId = getTemplateId();
        if (templateId == null) {
            String skuNo = getSkuNo();
            String serviceId = getServiceId();
            if (skuNo == null || serviceId == null) {
                throw new IllegalArgumentException("模板id和skuNo和serviceId不能同时为空");
            }
        }
    }

}
