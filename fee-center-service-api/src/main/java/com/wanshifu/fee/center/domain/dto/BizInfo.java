package com.wanshifu.fee.center.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("业务信息")
public class BizInfo {

    @ApiModelProperty(value = "业务标签")
    // 当前仅用于 师傅招募，值为：招募id/活动id
    private String bizTag;

    @ApiModelProperty(value = "业务id（实际使用的为userId或masterId）")
    private String bizId;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "师傅id")
    private String masterId;
}
