package com.wanshifu.fee.center.domain.document;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.mapping.Document;

@EqualsAndHashCode(callSuper = true)
@Document
@Data
@FieldNameConstants
@AllArgsConstructor
public class FlushDataLog extends BaseDocument{

    private Long bizId;

    private String failRReason;

}
