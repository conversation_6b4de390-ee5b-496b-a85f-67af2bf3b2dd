package com.wanshifu.fee.center.domain.request.feeRule;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Set;

/**
 * @author: <PERSON>
 * @create: 2024-01-11 14:31
 * @description: 根据地址id获取服务id request body
 */
@Data
@ApiModel
public class ServiceIdByDivisionIdsReq {

    /**
     * @see com.wanshifu.fee.center.domain.enums.DivisionTypeEnum
     */
    @ApiModelProperty(value = "地址code，DivisionTypeEnum的code", example = "street")
    @NotBlank(message = "地址code不能为空")
    private String divisionCode;

    @ApiModelProperty(value = "地址id集合", example = "[1,2,3]")
    private Set<String> divisionIdSet;
}
