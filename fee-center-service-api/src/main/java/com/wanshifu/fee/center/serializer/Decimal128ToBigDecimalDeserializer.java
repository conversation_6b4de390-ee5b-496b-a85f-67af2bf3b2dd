package com.wanshifu.fee.center.serializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.bson.types.Decimal128;

import java.io.IOException;
import java.math.BigDecimal;

public class Decimal128ToBigDecimalDeserializer  extends JsonDeserializer<BigDecimal> {
    @Override
    public BigDecimal deserialize(JsonParser p, DeserializationContext context) throws IOException {
        return Decimal128.parse(p.getValueAsString()).bigDecimalValue();
    }
}