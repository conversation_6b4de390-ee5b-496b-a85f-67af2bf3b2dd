package com.wanshifu.fee.center.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class SceneInfoModifyReq extends SceneInfoSaveReq {
    @NotNull
    private Long sceneId;

    @NotBlank
    private String sceneName;

    // 对接系统
    private List<String> integrationSys;

    // 价格维护类型
    private Integer priceMaintain;

    private String note;

    @ApiModelProperty(value = "计价订单范围")
    private List<String> orderRange;

    @ApiModelProperty(value = "是否映射计价")
    private Boolean isMappingPricing;

    @ApiModelProperty(value = "是否需要做 计价模板与计价规则的完整性校验")
    private Boolean needIntegrityValidation = true;

    @ApiModelProperty("地区维度")
    @NotBlank(message = "地区维度不能为空")
    private String divisionType;

    @ApiModelProperty("地区是否需要向上查找")
    @NotNull(message = "地区是否需要向上查找不能为空")
    private Boolean needSearchParent;

    @ApiModelProperty("金额类型")
    private String amountType;

}
