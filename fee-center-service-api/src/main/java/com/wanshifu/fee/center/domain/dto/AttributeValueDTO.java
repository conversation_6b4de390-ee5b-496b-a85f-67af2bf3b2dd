package com.wanshifu.fee.center.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("服务属性值信息")
@Data
public class AttributeValueDTO {

    @ApiModelProperty(value = "属性值id")
    private Long attributeValueId;

    @ApiModelProperty(value = "属性值名称")
    private String attributeValueName;

    @ApiModelProperty("属性值key")
    private String attributeValueKey;

    @ApiModelProperty(value = "属性值路径编码")
    private String attributePathNo;

    @ApiModelProperty(value = "属性值(用户填的)")
    private String value;

    @ApiModelProperty(value = "属性值列表")
    private List<AttributeDTO> attributeDtoList;

    private Boolean isExtracted = false;
}
