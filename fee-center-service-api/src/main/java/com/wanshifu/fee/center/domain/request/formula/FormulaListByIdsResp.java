package com.wanshifu.fee.center.domain.request.formula;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: <PERSON>
 * @create: 2024-03-07 16:03
 * @description: 根据ids查询列表response body
 */
@Data
@ApiModel("根据ids查询列表response body")
public class FormulaListByIdsResp {

    @ApiModelProperty(value = "公式ID", required = true)
    private String formulaId;

    @ApiModelProperty(value = "公式名称", required = true)
    private String formulaName;
}
