//package com.wanshifu.fee.center.domain.document;
//
//import lombok.Data;
//import lombok.experimental.FieldNameConstants;
//import org.springframework.data.mongodb.core.index.Indexed;
//import org.springframework.data.mongodb.core.mapping.Document;
//
//import javax.persistence.Id;
//import java.util.Date;
//
///**
// * <AUTHOR> yong
// * 同步大数据后需要删除的FeeRule
// * 此表数据来源：当次新增的feeRuleId会写入到该表
// * 用途：下一次同步完成后会删除上一次同步的数据
// */
////@Document
//@Data
//@FieldNameConstants
//public class FeeRuleFromBigdataToBeDelete {
//
//    @Id
//    private String id;
//
//    @Indexed(unique = true)
//    private Long feeRuleId;
//
//    private String sceneCode;
//
//    // 版本号，为了检查当前进行中的版本
//    private String versionNo;
//
//    private Date createTime;
//
//}
