package com.wanshifu.fee.center.domain.request.feeRule;

import com.wanshifu.fee.center.domain.request.BasePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.NotBlank;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("天天特价费用规则request body")
public class BargainPriceEverydayFeeRuleReq extends BasePageReq {

    @ApiModelProperty(value = "场景编码", required = true)
    @NotBlank(message = "场景编码不能为空")
    private String sceneCode;

    @ApiModelProperty(value = "服务id", required = true)
    @NotBlank(message = "服务Id不能为空")
    private String serviceId;

    @ApiModelProperty(value = "规格")
    private String skuNo;

    @ApiModelProperty(value = "城市id")
    private String level2DivisionId;

    @ApiModelProperty(value = "费用类型")
    private String feeTypeTag;

}
