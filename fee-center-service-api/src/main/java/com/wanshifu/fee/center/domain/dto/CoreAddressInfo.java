package com.wanshifu.fee.center.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@ApiModel("地址信息")
@Data
public class CoreAddressInfo {

    @NotNull(message = "divisionId不能为空")
    @ApiModelProperty(value = "地区id")
    private Long divisionId;

    @ApiModelProperty(value = "地区名称")
    private String divisionName;

    @ApiModelProperty(value = "地区类型")
    private String divisionType;

    @ApiModelProperty(value = "地址是否需要向上查找")
    private Boolean needSearchParent = false;
}
