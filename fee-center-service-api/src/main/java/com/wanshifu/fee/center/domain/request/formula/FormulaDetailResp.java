package com.wanshifu.fee.center.domain.request.formula;

import com.wanshifu.fee.center.domain.document.CalculateRuleData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: <PERSON>
 * @create: 2024-02-27 16:26
 * @description: 公式详情response body
 */
@Data
public class FormulaDetailResp {

    @ApiModelProperty("公式ID")
    private Long formulaId;

    @ApiModelProperty("公式名称")
    private String formulaName;

    @ApiModelProperty("公式类别")
    private String formulaType;

    @ApiModelProperty("地区维度")
    private String divisionType;

    @ApiModelProperty("公式表达式")
    private CalculateRuleData formulaContent;

    @ApiModelProperty("地区Id，多个用英文逗号分隔")
    private String divisionIds;

    @ApiModelProperty("已选地区数")
    private Integer divisionIdCount;

    @ApiModelProperty("业务Id，多个用英文逗号分隔")
    private String bizIds;

}
