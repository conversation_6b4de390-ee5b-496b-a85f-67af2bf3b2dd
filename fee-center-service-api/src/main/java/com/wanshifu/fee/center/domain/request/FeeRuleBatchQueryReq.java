package com.wanshifu.fee.center.domain.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;
import java.util.Map;

@Data
public class FeeRuleBatchQueryReq {
    private Long feeRuleId;
    private Long templateId;
    @NotBlank(message = "场景不能为空")
    private String sceneCode;
    // 在算费时需要进行组完整性校验
    private String group;
    private String sceneName;
    private String feeName;
    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private Map<String, List<String>> bizRule;
}
