package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

import java.util.Objects;


@AllArgsConstructor
public enum BizIdTypeEnum {
    // 无、师傅、商家、合约师傅……
    EMPTY("empty", "无"),
    MASTER_ID("masterId", "师傅"),
    USER_ID("userId", "商家"),
//    CONTRACT_MASTER_ID("contractMasterId","合约师傅"),

    ;

    public final String code;
    public final String name;

    public static BizIdTypeEnum fromCode(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (BizIdTypeEnum value : BizIdTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
