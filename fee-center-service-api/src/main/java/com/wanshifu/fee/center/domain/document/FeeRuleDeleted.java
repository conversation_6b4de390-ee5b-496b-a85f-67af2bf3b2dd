package com.wanshifu.fee.center.domain.document;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.Map;


@Document
@Data
@FieldNameConstants
public class FeeRuleDeleted extends BaseDocument {

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long feeRuleId;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long templateId;
    // 在算费时需要进行组完整性校验

    private String group;
    private int templateVersion;

    private String sceneCode;
    private String sceneName;
    private String feeName;

    // 为了价格根据大小排序
    private Double masterInputPriceDouble;

    private List<DynamicFeeRuleCalculateRuleData> dynamicFeeRuleCalculateRuleDataList;

    @J<PERSON>NField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private Map<String, String> bizRule;

    private CalculateRuleData calculateRuleData;


}
