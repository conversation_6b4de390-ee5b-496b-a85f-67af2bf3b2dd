package com.wanshifu.fee.center.domain.request.feeRule;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@ApiModel("价格和费用名称response body")
@Data
public class PriceAndFeeNameResp {

    @ApiModelProperty("服务id")
    private String serviceId;

    @ApiModelProperty("价格和费用详情列表")
    private List<PriceAndFeeName> priceAndFeeNameList;

    @Data
    @AllArgsConstructor
    public static class PriceAndFeeName {

        @ApiModelProperty("skuNo")
        private String skuNo;

        @ApiModelProperty("单价")
        private BigDecimal price;

        @ApiModelProperty("费用名称")
        private String attributeDisplayName;

        @ApiModelProperty("费用单位")
        private String feeUnit;
    }
}
