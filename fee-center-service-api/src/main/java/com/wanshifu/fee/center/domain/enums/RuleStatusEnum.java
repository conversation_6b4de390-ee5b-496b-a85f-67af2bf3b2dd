package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

import java.util.Objects;

@AllArgsConstructor
public enum RuleStatusEnum {
    /**
     * 可用
     */
    ACTIVE(0, "可用"),
    INACTIVE(1, "不可用"),
    ;
    public final int code;
    public final String name;

    public static RuleStatusEnum fromCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (RuleStatusEnum value : RuleStatusEnum.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }

}
