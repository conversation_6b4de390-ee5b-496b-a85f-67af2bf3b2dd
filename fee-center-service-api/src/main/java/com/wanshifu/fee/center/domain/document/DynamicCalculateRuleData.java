package com.wanshifu.fee.center.domain.document;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.wanshifu.framework.utils.CollectionUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Data
@FieldNameConstants
@ApiModel("动态计算规则数据")
public class DynamicCalculateRuleData {
    public static final String ORIGIN_PRICE = "ORIGIN_PRICE";
    /**
     * @see com.wanshifu.fee.center.domain.enums.DynamicTypeEnum
     */
    private String dynamicType;
    /**
     * @see com.wanshifu.fee.center.domain.enums.DynamicSymbolEnum
     */
    private String dynamicSymbol;
    private String adjustValue;

    private String express;
    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private List<String> expressionParamList;
    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private Map<String, String> expressionParamMap;

    @ApiModelProperty("公式ID")
    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private List<String> formulaIds;


    public boolean hasFormula() {
        return CollectionUtils.isNotEmpty(formulaIds);
    }
}