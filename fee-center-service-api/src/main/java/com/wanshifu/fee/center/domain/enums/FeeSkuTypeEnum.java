package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum FeeSkuTypeEnum {
    // 企业标准一口价、企业报价招标
//    standardSku - 标准sku
//    customSku - 自定义sku
    STANDARD_SKU("standardSku", "标准sku"),
    CUSTOM_SKU("customSku", "自定义sku"),
    ;
    public final String code;
    public final String name;

    public static FeeSkuTypeEnum fromCode(String code) {
        for (FeeSkuTypeEnum e : FeeSkuTypeEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
}
