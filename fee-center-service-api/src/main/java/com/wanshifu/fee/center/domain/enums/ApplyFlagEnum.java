package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum ApplyFlagEnum {

    NO_LIMIT("no_limit", "无限制"),

    NO_APPLY_IF_HAVE("no_apply_if_have", "无需算费");

    public final String code;
    public final String name;

    public static ApplyFlagEnum fromCode(String code) {
        for (ApplyFlagEnum e : ApplyFlagEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
}
