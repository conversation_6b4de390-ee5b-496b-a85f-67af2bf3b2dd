package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum AmountTypeEnum {
    // 固定价格/期间价，默认选择固定价格
    FIX_PRICE("fix_price", "固定价格"),
    RANGE_PRICE("range_price", "期间价"),
    ;
    public final String code;
    public final String name;

    public static AmountTypeEnum fromCode(String code) {
        for (AmountTypeEnum e : AmountTypeEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
}
