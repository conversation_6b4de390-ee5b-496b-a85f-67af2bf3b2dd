package com.wanshifu.fee.center.domain.enums;

import lombok.Getter;

@Getter
public enum MasterPrice2CActivationMethodEnum {

    SCHEDULED("scheduled", "定时生效");

    private final String method;
    private final String desc;

    MasterPrice2CActivationMethodEnum(String method, String desc) {
        this.method = method;
        this.desc = desc;
    }

    public static MasterPrice2CActivationMethodEnum fromMethod(String method) {
        for (MasterPrice2CActivationMethodEnum masterPrice2CActivationMethodEnum : MasterPrice2CActivationMethodEnum.values()) {
            if (masterPrice2CActivationMethodEnum.getMethod().equals(method)) {
                return masterPrice2CActivationMethodEnum;
            }
        }
        return null;
    }
}
