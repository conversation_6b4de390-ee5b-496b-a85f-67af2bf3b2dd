package com.wanshifu.fee.center.domain.request.calculate;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.wanshifu.fee.center.domain.document.CalculateResult;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(description = "标准计价接口返回参数")
public class StandardPricingResponse {

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long tid;
    private BigDecimal cost;
    private BigDecimal costMax;
    private BigDecimal dynamicFeeCost;
    private BigDecimal dynamicFeeCostMax;
    private BigDecimal basePrice;
    private boolean success = false;
    private String errorInfo;
    private List<StandardPricingResponse.ServiceResult> serviceResultList;

//    @Data
//    public static class SceneResult {
//        private String sceneCode;
//        private BigDecimal cost;
//        private BigDecimal dynamicFeeCost;
//        private BigDecimal basePrice;
//        // 算费是否成功
//        private boolean success = true;
//        // 如果失败为什么失败
//        private String errorInfo;
//        private List<StandardPricingResponse.ServiceResult> serviceResultList;
//        private List<CalculateResult> calculateResultList;
//    }

    @Data
    public static class ServiceResult {
        private String sceneCode;
        private String index;
        private String serviceId;
        private String externalServiceId;
        private BigDecimal cost;
        private BigDecimal costMax;
        private BigDecimal dynamicFeeCost;
        private BigDecimal dynamicFeeCostMax;
        private BigDecimal basePrice;
        private boolean success = false;
        private String errorInfo;
        private List<CalculateResult> calculateResultList;
    }
}
