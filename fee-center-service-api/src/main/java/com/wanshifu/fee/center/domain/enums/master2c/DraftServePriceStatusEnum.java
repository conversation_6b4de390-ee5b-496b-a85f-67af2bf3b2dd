package com.wanshifu.fee.center.domain.enums.master2c;

import lombok.Getter;

/**
 * C端师傅服务价格改价草稿状态枚举
 * <AUTHOR>
 * @date 2025/7/3 14:22
 */
@Getter
public enum DraftServePriceStatusEnum {

    /**
     * 待审核
     */
    PENDING_REVIEW("pending_review", "待审核"),

    /**
     * 待生效
     */
    PENDING_ACTIVATION("pending_activation", "待生效"),

    /**
     * 已上线
     */
    ONLINE("online", "已上线"),

    /**
     * 驳回
     */
    REJECTED("rejected", "驳回"),

    /**
     * 取消
     */
    CANCEL("cancel", "取消");

    private final String status;
    private final String desc;

    DraftServePriceStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static DraftServePriceStatusEnum fromStatus(String status) {
        for (DraftServePriceStatusEnum value : DraftServePriceStatusEnum.values()) {
            if (value.status.equals(status)) {
                return value;
            }
        }
        return null;
    }
}
