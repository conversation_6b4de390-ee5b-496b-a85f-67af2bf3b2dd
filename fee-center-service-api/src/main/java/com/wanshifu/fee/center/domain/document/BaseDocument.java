package com.wanshifu.fee.center.domain.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.Indexed;

import javax.persistence.Id;
import java.util.Date;

@Data
@FieldNameConstants
public abstract class BaseDocument {
    public static final int DEFAULT_SORT = 1;

    @Id
    private String id;

    //    @Indexed(background = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    //    @Indexed(background = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;
    // 逻辑删除？
    private boolean del;
    // 0 正常
    private Integer status;
    // 默认排在最后
    private int sort = DEFAULT_SORT;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改人
     */
    private String updateBy;
}
