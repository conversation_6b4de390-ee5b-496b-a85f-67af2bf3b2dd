package com.wanshifu.fee.center.domain.request;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: <PERSON>
 * @create: 2023-11-01 17:43
 * @description: 增减费用项关联平台SKU request body
 */
@Data
public class FeeReasonRelationMaintainReq {

    /**
     * reasonId
     */
    private Long reasonId;
    /**
     * 一级类目
     */
    private Long level1GoodsCategoryId;

    /**
     * 二级类目
     */
    private Long level2GoodsCategoryId;


    /**
     * 三级类目
     */
    private Long level3GoodsCategoryId;
    /**
     * 报价模式 2 / 4
     */
    private String serviceMode;
    /**
     * sku类型 全部 总包 用户
     */
    private String skuType;
    /**
     * 场景
     */
    private String skuRuleScene;
    /**
     * 服务名称
     */
    private String serviceName;


    private List<FeeReasonRelationData> feeReasonRelationDataList;


    @Data
    public static class FeeReasonRelationData {
        @NotNull(message = "原因项id不能为空")
        private Long reasonId;

        @NotNull(message = "一级商品类目id不能为空")
        private Long level1GoodsCategoryId;

        @NotNull(message = "一级商品类目名称不能为空")
        private String level1GoodsCategoryName;

        @NotNull(message = "二级商品类目id不能为空")
        private Long level2GoodsCategoryId;

        @NotNull(message = "二级商品类目名称不能为空")
        private String level2GoodsCategoryName;

        private Long level3GoodsCategoryId;

        private String level3GoodsCategoryName;

        @NotNull(message = "服务id不能为空")
        private Long serviceId;

        @NotBlank(message = "服务名称不能为空")
        private String serviceName;

        @NotBlank(message = "报价模式不能为空")
        private String serviceMode;

        @NotBlank(message = "SKU类型不能为空")
        private String skuType;

        @NotBlank(message = "SKU no不能为空")
        private String skuNo;

        @NotNull(message = "skuId不能为空")
        private Long skuId;

        @NotBlank(message = "SKU 路径名称不能为空")
        private String skuPathName;
        @NotBlank(message = " skuRuleScene不呢为空")
        private String skuRuleScene;
    }

}
