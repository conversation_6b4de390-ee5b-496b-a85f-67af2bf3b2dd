package com.wanshifu.fee.center.domain.request.feeRule;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: <PERSON>
 * @create: 2024-04-03 18:32
 * @description: 批量计价业务规则Request body
 */
@Data
@ApiModel("批量计价业务规则Request body")
public class BizRuleBatchReq {

    @ApiModelProperty(value = "业务id。例如师傅id，用户id等。为了兼容，故采用String类型", required = true)
    private String bizId;

    @ApiModelProperty(value = "招募id", required = true)
    private String bizTag;
}
