package com.wanshifu.fee.center.domain.request.mapping;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.wanshifu.framework.core.page.Pager;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class MappingPageListResponse extends Pager {

    @ApiModelProperty(value = "映射id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long mappingId;

    @ApiModelProperty("应用类型编码。pricing:计价，price_sync:价格同步，all:全部，none:无")
    private List<String> applyTypes;

    private TemplateInfo source;

    private TemplateInfo target;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    private String createBy;

    private String updateBy;

    @Data
    public static class TemplateInfo {

        @ApiModelProperty(value = "模板id")
        @JSONField(serializeUsing = ToStringSerializer.class)
        private Long templateId;

        @ApiModelProperty(value = "skuNo", required = true)
        private String skuNo;

        @ApiModelProperty(value = "模板sku类型")
        private String templateSkuType;

        // 场景的sku类型（definite_price：一口价，offer_price：报价招标），由于场景中的sku类型不可更改，所以这里冗余
        @ApiModelProperty(value = "场景的sku类型（definite_price：一口价，offer_price：报价招标）")
        private String sceneSkuType;

        @ApiModelProperty(value = "服务id", required = true)
        private String serviceId;

        @ApiModelProperty(value = "服务名称", required = true)
        private String serviceName;

        @ApiModelProperty(value = "属性展示名称")
        private String attributeDisplayName;

        @ApiModelProperty(value = "计价数量pathNo", required = true)
        private String skuNumberPathNo;

        @ApiModelProperty(value = "场景编码", required = true)
        private String sceneCode;

        @ApiModelProperty(value = "场景名称", required = true)
        private String sceneName;

        // 来源于服务品类
        @ApiModelProperty(value = "sku路径名")
        private String skuAttributePathName;

        // 来源于服务品类
        @ApiModelProperty(value = "计价数量名")
        private String skuNumberName;

        @ApiModelProperty(value = "计费单位")
        private String feeUnit;

        private String mappingType;

    }
}
