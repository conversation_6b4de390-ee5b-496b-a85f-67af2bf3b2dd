package com.wanshifu.fee.center.domain.document;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Set;

/**
 * @author: <PERSON>
 * @create: 2024-09-10 14:06
 * @description: 同步数据黑名单策略
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Document
@FieldNameConstants
public class SyncBlacklistStrategy extends BaseDocument{

    @Indexed(unique = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long strategyId;

    private String strategyName;

    private String sceneCode;

    /**
     * @see com.wanshifu.fee.center.domain.enums.DivisionTypeEnum
     */
    private String divisionType;

    /**
     * @see com.wanshifu.fee.center.domain.enums.LimitDivisionTypeEnum
     */
    private String limitDivisionType;

    private Set<Long> divisionIds;

    private Set<ServiceData> serviceDataSet;

    private String bizIds;

    private String operator;

}
