package com.wanshifu.fee.center.domain.dto;

import com.wanshifu.adapter.dto.service.BaseAttributeCommonDetailDto;
import lombok.Data;

import java.util.Objects;

@Data
public class ServiceAttribute extends BaseAttributeCommonDetailDto<ServiceAttributeValue> {

    //    @ApiModelProperty("属性路径ID")
    private Long attributePathId;

    //    @ApiModelProperty("属性ID")
    private Long attributeId;

    //    @ApiModelProperty("默认值")
    private String defaultValue;

    //    @ApiModelProperty("属性开发者标记，多个属性标记用逗号分隔")
    private String attributeTag;

    //    @ApiModelProperty("属性项key值")
    private String attributeKey;

    //    @ApiModelProperty("属性项名称")
    private String attributeName;

    //    @ApiModelProperty("商品库是否支持动态更新, 字段为null，代表属性不支持商品库，1：支持商品库动态更新，0：不支持商品库动态更新")
    private Boolean isDynamicUpdate;

    //    @ApiModelProperty("单位")
    private String unit;

    //    @ApiModelProperty("输入框提示")
    private String placeholder;

    //    @ApiModelProperty("数据输入来源，optionInput：选型输入，customInput：自定义输入，fileInput:文件上传")
    private String dataInputSource;

    //    @ApiModelProperty("属性是否可用，通过黑白名单控制属性的可用，1：是，0：否")
    private Integer isAvailable = 1;

    @Override
    public Long getId() {
        return super.id;
    }

    public Long getAttributePathId() {
        return Objects.isNull(id) ? getId() : attributePathId;
    }
}
