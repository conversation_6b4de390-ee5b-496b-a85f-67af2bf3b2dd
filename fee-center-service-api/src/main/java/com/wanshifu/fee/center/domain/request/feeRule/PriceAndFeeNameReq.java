package com.wanshifu.fee.center.domain.request.feeRule;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

@Data
public class PriceAndFeeNameReq {

    @NotBlank(message = "sceneCode不能为空")
    private String sceneCode;

    @NotEmpty(message = "serviceIds不能为空")
    private List<String> serviceIds;

    @NotBlank(message = "masterId不能为空")
    private String masterId;

    @NotBlank(message = "招募id不能为空")
    private String recruitId;
}
