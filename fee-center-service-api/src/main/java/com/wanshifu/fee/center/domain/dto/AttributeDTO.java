package com.wanshifu.fee.center.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("服务属性信息")
@Data
public class AttributeDTO {

    @ApiModelProperty(value = "属性id")
    private Long attributeId;

    @ApiModelProperty(value = "属性名称")
    private String attributeName;

    @ApiModelProperty(value = "属性key")
    private String attributeKey;

    @ApiModelProperty(value = "属性路径编码")
    private String attributePathNo;

    @ApiModelProperty(value = "属性标签")
    private String attributeTag;

    @ApiModelProperty(value = "属性值列表")
    private List<AttributeValueDTO> attributeValueDtoList;
}
