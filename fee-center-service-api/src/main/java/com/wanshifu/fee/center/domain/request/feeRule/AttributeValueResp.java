package com.wanshifu.fee.center.domain.request.feeRule;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@ApiModel("属性值response body")
public class AttributeValueResp {

    @ApiModelProperty("skuNo")
    private String skuNo;

    @ApiModelProperty("属性值名称(规格)")
    private String attributeDisplayName;
}
