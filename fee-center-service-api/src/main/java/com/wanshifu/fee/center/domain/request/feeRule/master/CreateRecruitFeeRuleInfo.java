package com.wanshifu.fee.center.domain.request.feeRule.master;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("创建招募师傅规则")
public class CreateRecruitFeeRuleInfo {

    @ApiModelProperty(value = "街道id", required = true)
    @NotEmpty(message = "街道id不能为空")
    private List<String> streetIds;

    @ApiModelProperty(value = "价格信息", required = true)
    @NotEmpty(message = "价格信息不能为空")
    private List<PriceInfo> priceInfos;

    @Data
    public static class PriceInfo {

        @ApiModelProperty(value = "模板id", required = true)
        @NotNull(message = "模板id不能为空")
        private Long templateId;

        @ApiModelProperty(value = "价格", required = true)
        @NotBlank(message = "价格不能为空")
        private BigDecimal price;
    }

}
