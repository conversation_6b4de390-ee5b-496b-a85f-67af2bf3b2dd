package com.wanshifu.fee.center.domain.request.strategy;

import com.wanshifu.fee.center.domain.request.BasePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel("同步数据黑名单策略分页查询request body")
@EqualsAndHashCode(callSuper = true)
@Data
public class SyncBlacklistStrategyPageReq extends BasePageReq {

    @ApiModelProperty(value = "策略名称")
    private String strategyName;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "场景编码")
    private String sceneCode;
}
