package com.wanshifu.fee.center.domain.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.wanshifu.fee.center.domain.document.CalculateRuleData;
import com.wanshifu.fee.center.domain.document.PriceUploadComparisonAlert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: <PERSON>
 * @create: 2023-09-12 14:27
 * @description: 计价规则（草稿）列表response body
 */
@Data
public class FeeRulePageResp {

    private String id;

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long feeRuleDraftId;

    private String serviceId;

    private String serviceName;

    /**
     * 定价ID
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long templateId;

    /**
     * 计价AP
     */
    private String skuNo;

    private String feeName;

    private String attributeDisplayName;

    /**
     * 价格维度code
     */
    private String divisionType;

    /**
     * 价格维度
     */
    private String divisionTypeName;

    private String province;

    private String city;

    private String district;

    private String street;

    private CalculateRuleData calculateRuleData;

    //    private String price;
    private String masterInputPrice;

    @ApiModelProperty("单价最大值")
    private String masterInputPriceMax;

    private String feeUnit;

    private String masterId;

    private String userId;

    private Date modifyTime;

    private String updateBy;
    
    private String bizTag;

    private BigDecimal basePrice;

    private PriceUploadComparisonAlert priceUploadComparisonAlert;

}
