package com.wanshifu.fee.center.api;

import com.wanshifu.fee.center.config.FeeCenterEncoder;
import com.wanshifu.fee.center.domain.request.sameday.GetPriceRequest;
import com.wanshifu.fee.center.domain.response.sameday.GetPriceResponse;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        value = "fee-center-service",
        path = "sameDayConfig",
        configuration = {DefaultDecoder.class, FeeCenterEncoder.class, DefaultErrorDecode.class},
        url = "${wanshifu.fee-center-service.url}")
public interface SameDayPriceConfigApi {

    @PostMapping("getPrice")
    GetPriceResponse getPrice(@RequestBody @Validated GetPriceRequest request);
}
