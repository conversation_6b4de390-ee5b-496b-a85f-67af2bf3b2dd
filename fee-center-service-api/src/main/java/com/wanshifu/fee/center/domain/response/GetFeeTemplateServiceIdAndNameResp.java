package com.wanshifu.fee.center.domain.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("获取计价模板服务id和名称返回体")
@AllArgsConstructor
@NoArgsConstructor
public class GetFeeTemplateServiceIdAndNameResp {

    @ApiModelProperty("服务ID")
    private String serviceId;

    @ApiModelProperty("服务名称")
    private String serviceName;
}
