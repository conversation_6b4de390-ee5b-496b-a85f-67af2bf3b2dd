package com.wanshifu.fee.center.domain.document;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@EqualsAndHashCode(callSuper = true)
@Data
@Document
@FieldNameConstants
public class RefreshPriceLog extends BaseDocument {

    private String sceneCode;

    @Indexed(background = true)
    private Long feeRuleId;

    private String originalPrice;

    private String newPrice;
}
