package com.wanshifu.fee.center.domain.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class LowestPriceResp {

    private Long level1GoodsCategoryId;

    private Long goodsCategoryId;

    private String goodsCategoryName;

    private Long serviceTypeId;

    private Long serviceId;

    private String serviceTypeName;

    @ApiModelProperty("补贴价")
    private Double subsidyPrice;

//    @ApiModelProperty("原始价")
//    private Double originPrice;

}
