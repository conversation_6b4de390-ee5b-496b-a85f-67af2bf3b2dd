package com.wanshifu.fee.center.domain.request.mapping;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.Map;

@ApiModel("批量添加映射request body")
@Data
public class BizRuleMappingBatchAddReq {

    @ApiModelProperty(value = "模板id", required = true)
    @NotNull(message = "模板id不能为空")
    private Long templateId;

    @ApiModelProperty(value = "场景编码", required = true)
    @NotBlank(message = "场景编码不能为空")
    private String sceneCode;

    @ApiModelProperty(value = "场景名称", required = true)
    @NotBlank(message = "场景名称不能为空")
    private String sceneName;

    @ApiModelProperty(value = "fromBizRule", required = true)
    @NotEmpty(message = "fromBizRule不能为空")
    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private Map<String, String> fromBizRule;

}
