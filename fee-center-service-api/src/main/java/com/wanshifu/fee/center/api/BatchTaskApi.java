package com.wanshifu.fee.center.api;


import com.wanshifu.fee.center.config.FeeCenterEncoder;
import com.wanshifu.fee.center.domain.document.BatchTaskInfo;
import com.wanshifu.fee.center.domain.request.BatchTaskQueryReq;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(value = "fee-center-service", path = "batchTask", configuration = {DefaultDecoder.class, FeeCenterEncoder.class, DefaultErrorDecode.class}, url = "${wanshifu.fee-center-service.url}")
public interface BatchTaskApi {

    @PostMapping("query")
    SimplePageInfo<BatchTaskInfo> query(@RequestBody @Validated BatchTaskQueryReq batchTaskQueryReq);
}
