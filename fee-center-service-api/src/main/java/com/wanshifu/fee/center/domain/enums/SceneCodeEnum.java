package com.wanshifu.fee.center.domain.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @author: <PERSON> Yong
 * @create: 2024-01-11 14:42
 * @description: 计价场景
 */
@Getter
public enum SceneCodeEnum {

    CONTRACT_MASTER("contract_master", "协议师傅"),

    MASTER_RECRUIT_BASE_PRICE("master_recruit_base_price", "师傅招募基础价"),

    MASTER_RECRUIT_ACTIVE_PRICE("master_recruit_active_price", "师傅招募活动价"),

    USER_OFFER_ORDER_AUTO_APPOINT_PRICE("user_offer_order_auto_appiont_price", "指定商家报价招标自动指派价"),

    USER_ORDER_OFFER_GUIDE_PRICE("user_order_offer_guide_price", "指定商家报价参考价"),

    PLATFORM_ORDER_OFFER_GUIDE_PRICE("platform_order_offer_guide_price", "平台订单报价参考价"),

    ENTERPRISE_ALL_CONTRACTORS_ATTRIBUTE_PRICE("enterprise_all_contractors_attribute_price", "总包全包商家计价属性"),

    ENTERPRISE_ORDER_OFFER_GUIDE_PRICE("enterprise_order_offer_guide_price", "总包报价参考价"),

    AUTO_RECEIVE_ORDER_GUIDE_PRICE("auto_receive_order_guide_price", "总包报价参考价"),

    PLATFORM_FIXED_PRICE("platform_fixed_price", "平台一口价"),

    PLATFORM_FIXED_PRICE_USER("platform_fixed_price_user", "商家定制一口价"),

    MASTER_AUTO_OFFER_PRICE("master_auto_offer_price", "师傅自动报价"),

    BARGAIN_PRICE_EVERYDAY("bargain_price_everyday", "天天特价"),

    BASE_SERVICE_PRICE("base_service_price", "基础服务计价属性"),

    CUSTOMER_FAVOR_CONTRACT_MASTER("customer_favor_contract_master", "商家收藏合作师傅"),

    USER_HISTORICAL_APPOINT_PRICE("user_historical_appoint_price", "商家历史指派价"),

    ENTERPRISE_FIXED_PRICE("enterprise_fixed_price", "总包给师傅一口价"),

    GUIDE_PRICE_FOR_MASTER_OFFER("guide_price_for_master_offer", "引导师傅报价"),

    ENTERPRISE_FIXED_PRICE_SPECIFIED("enterprise_fixed_price_specified", "总包指定商家给师傅一口价");

    private final String code;

    private final String name;

    SceneCodeEnum(String code, String name) {
        this.code = code;
        this.name = name;

    }

    public static SceneCodeEnum fromCode(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (SceneCodeEnum value : SceneCodeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
