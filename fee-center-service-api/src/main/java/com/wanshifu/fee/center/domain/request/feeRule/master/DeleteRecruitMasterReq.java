package com.wanshifu.fee.center.domain.request.feeRule.master;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

@ApiModel("删除招募师傅")
@Data
public class DeleteRecruitMasterReq {

    @ApiModelProperty(value = "招募活动Id", required = true)
    @NotBlank(message = "招募活动Id不能为空")
    private String recruitId;

    @ApiModelProperty(value = "师傅Id", required = true)
    @NotBlank(message = "师傅Id不能为空")
    private String masterId;
}
