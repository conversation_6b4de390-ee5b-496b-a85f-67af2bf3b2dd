package com.wanshifu.fee.center.domain.request.template;

import com.wanshifu.fee.center.domain.request.BasePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.NotBlank;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("通过serviceId获取计价模板分页列表request body")
public class GetServiceIdAndNameBySceneCodeAndServiceNameReq extends BasePageReq {

    @ApiModelProperty(value = "场景编号", required = true)
    @NotBlank(message = "场景编号不能为空")
    private String sceneCode;

    @ApiModelProperty(value = "服务名称", required = true)
    @NotBlank(message = "服务名称不能为空")
    private String serviceName;
}
