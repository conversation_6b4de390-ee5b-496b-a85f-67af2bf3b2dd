package com.wanshifu.fee.center.domain.enums;

import com.wanshifu.fee.center.domain.document.DynamicPricingIndicator;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.function.BiPredicate;

@Getter
@AllArgsConstructor
public enum IndicatorOptionalConditionEnum {

    GREATER_THAN("greater_than", "大于", ((fixedValue, iv) -> fixedValue > iv.getFixedValue())),
    LESS_THAN("less_than", "小于", ((fixedValue, iv) -> fixedValue < iv.getFixedValue())),
    GREATER_THAN_OR_EQUAL("greater_than_or_equal", "大于等于", ((fixedValue, iv) -> fixedValue >= iv.getFixedValue())),
    LESS_THAN_OR_EQUAL("less_than_or_equal", "小于等于", ((fixedValue, iv) -> fixedValue >= iv.getFixedValue())),
    EQUAL("equal", "等于", ((fixedValue, iv) -> fixedValue.equals(iv.getFixedValue()))),
    RANGE("range", "区间", ((fixedValue, iv) -> fixedValue >= iv.getMinValue() && fixedValue <= iv.getMaxValue())),
    // 自动计算需要额外处理
    AUTO_CALCULATE("auto_calculate", "自动计算", ((fixedValue, iv) -> false)),

    ALL("all", "全部", ((fixedValue, iv) -> true)),
    ;

    private final String code;
    private final String desc;
    private final BiPredicate<Double, DynamicPricingIndicator.IndicatorValue> matcher;

    public boolean matches(Double fixedValue, DynamicPricingIndicator.IndicatorValue iv) {
        if (fixedValue == null || iv == null) {
            return false;
        }

        switch (this) {
            case GREATER_THAN:
            case LESS_THAN:
            case GREATER_THAN_OR_EQUAL:
            case LESS_THAN_OR_EQUAL:
            case EQUAL:
                Double fixed = iv.getFixedValue();
                return fixed != null && matcher.test(fixedValue, iv);

            case RANGE:
                Double min = iv.getMinValue();
                Double max = iv.getMaxValue();
                return min != null && max != null && matcher.test(fixedValue, iv);

            case ALL:
                return true;

            default:
                return false;
        }
    }

    public static IndicatorOptionalConditionEnum fromCode(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (IndicatorOptionalConditionEnum value : IndicatorOptionalConditionEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
