package com.wanshifu.fee.center.domain.document;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@EqualsAndHashCode(callSuper = true)
@Document
@Data
@FieldNameConstants
public class BargainPriceEverydayLowestPrice extends BaseDocument{

    private String sceneCode;

    @Indexed(background = true)
    private Long level1GoodsCategoryId;

    private Long goodsCategoryId;

    private String goodsCategoryName;

    private Long serviceTypeId;

    @Indexed(background = true, unique = true)
    private Long serviceId;

    private String serviceTypeName;

    @ApiModelProperty("补贴价")
    private Double subsidyPrice;

}
