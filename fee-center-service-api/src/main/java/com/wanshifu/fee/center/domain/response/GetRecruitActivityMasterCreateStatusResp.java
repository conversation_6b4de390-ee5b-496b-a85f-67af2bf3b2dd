package com.wanshifu.fee.center.domain.response;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@ApiModel(description = "招募活动主表创建状态")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GetRecruitActivityMasterCreateStatusResp {

    private String masterId;

    private String recruitId;

    private String buildType;

    private String auditStatus;

    private Date auditTime;
}
