package com.wanshifu.fee.center.domain.request.feeRule;

import com.wanshifu.fee.center.domain.request.BasePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.NotBlank;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("预生成天天特价费用规则request body")
public class PreGenerateBargainPriceEverydayFeeRuleReq extends BasePageReq {

    @ApiModelProperty(value = "场景编码", required = true)
    @NotBlank(message = "场景编码不能为空")
    private String sceneCode;

}
