package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

import java.util.Objects;

@AllArgsConstructor
public enum FeeReasonRelationStatusEnum {
    /**
     * 可用
     */
    ACTIVE(0, "可用"),
    ;
    public final int code;
    public final String name;

    public static FeeReasonRelationStatusEnum fromCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (FeeReasonRelationStatusEnum value : FeeReasonRelationStatusEnum.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }

}
