package com.wanshifu.fee.center.domain.enums;

import lombok.Getter;

/**
 * @author: <PERSON>
 * @create: 2024-02-27 14:26
 * @description: 公式类别
 */
@Getter
public enum FormulaTypeEnum {

    UNIT_PRICE("unit_price", "单价公式"),
    TOTAL_PRICE("total_price", "总价公式");

    private final String code;

    private final String name;

    FormulaTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
