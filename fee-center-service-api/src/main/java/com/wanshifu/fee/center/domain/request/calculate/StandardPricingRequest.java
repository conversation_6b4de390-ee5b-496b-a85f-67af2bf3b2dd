package com.wanshifu.fee.center.domain.request.calculate;

import com.wanshifu.fee.center.domain.dto.ServiceDTO;
import com.wanshifu.fee.center.domain.request.StandardPricingCommonParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "标准计价接口入参")
@Data
public class StandardPricingRequest extends StandardPricingCommonParam {

    @ApiModelProperty(value = "场景编码", required = true)
    @NotEmpty(message = "场景编码不能为空")
    private List<String> sceneCodeList;

    @ApiModelProperty(value = "服务信息")
    private List<ServiceDTO> serviceDtoList;

    /**
     * 只有多场景才会合并计价的需求
     */
    public Boolean getMergeByPriority() {
        return mergeByPriority != null && sceneCodeList.size() >= 2;
    }
}
