package com.wanshifu.fee.center.domain.document;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.wanshifu.fee.center.domain.dto.ExpressInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Data
@FieldNameConstants
public class CalculateRuleData {
    private ExpressInfo expressInfo;
    private String express;

    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private List<String> expressionParamList;

    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private Map<String, String> expressionParamMap;

    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private List<Map<String, String>> expressionParamMapList;

    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private List<String> formulaIds;

    public void parseExpressionParam() {
        if (StringUtils.isBlank(express)) {
            return;
        }
        final String fixedParam = "masterInputPrice";
        if (express.contains(fixedParam)) {
            if (expressionParamList == null) {
                expressionParamList = new ArrayList<>();
                expressionParamList.add(fixedParam);
            } else if (!expressionParamList.contains(fixedParam)) {
                expressionParamList.add(fixedParam);
            }
        }
        List<String> paramList = parseAPNumbers(express);
        if (CollectionUtils.isNotEmpty(paramList)) {
            for (String param : paramList) {
                if (expressionParamList == null) {
                    expressionParamList = new ArrayList<>();
                    expressionParamList.add(param);
                } else if (!expressionParamList.contains(param)) {
                    expressionParamList.add(param);
                }
            }
        }
    }

    private List<String> parseAPNumbers(String input) {
        List<String> result = new ArrayList<>();

        Pattern pattern = Pattern.compile("AP\\d+");
        Matcher matcher = pattern.matcher(input);

        while (matcher.find()) {
            result.add(matcher.group());
        }

        return result;
    }

    public int getFormulaCount() {
        if (CollectionUtils.isEmpty(formulaIds)) {
            return 0;
        } else {
            return formulaIds.size();
        }
    }

    public boolean hasFormula() {
        return CollectionUtils.isNotEmpty(formulaIds);
    }
}