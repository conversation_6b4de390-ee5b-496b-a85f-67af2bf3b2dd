package com.wanshifu.fee.center.api;


import com.wanshifu.fee.center.config.FeeCenterEncoder;
import com.wanshifu.fee.center.domain.document.FeeTemplate;
import com.wanshifu.fee.center.domain.request.*;
import com.wanshifu.fee.center.domain.request.template.*;
import com.wanshifu.fee.center.domain.response.FeeTemplate4DeleteCustomResp;
import com.wanshifu.fee.center.domain.response.FeeTemplateResp;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 */
@FeignClient(value = "fee-center-service", path = "feeTemplate", configuration = {DefaultDecoder.class, FeeCenterEncoder.class, DefaultErrorDecode.class}, url = "${wanshifu.fee-center-service.url}")
public interface FeeTemplateApi {

    /**
     * 请使用queryByCondition
     */
    @PostMapping("query")
    @Deprecated
    SimplePageInfo<FeeTemplate> query(@RequestBody @Validated FeeTemplateQueryReq feeTemplateQueryReq);

    @PostMapping("queryByCondition")
    @Deprecated
    SimplePageInfo<FeeTemplate> queryByCondition(@RequestBody @Validated FeeTemplateQueryByConditionReq feeTemplateQueryByConditionReq);

    @PostMapping("queryTemplateByCondition")
    SimplePageInfo<FeeTemplateResp> queryTemplateByCondition(@RequestBody @Validated FeeTemplateReq req);

    @PostMapping("queryTemplateByCondition4DeleteCustomTemplate")
    SimplePageInfo<FeeTemplate4DeleteCustomResp> queryTemplateByCondition4DeleteCustomTemplate(@RequestBody @Validated FeeTemplate4DeleteCustomReq req);

    @PostMapping("batchQuery")
    @Deprecated
    SimplePageInfo<FeeTemplate> batchQuery(@RequestBody @Validated FeeTemplateBatchQueryReq feeTemplateBatchQueryReq);


    @PostMapping("getPageList")
    SimplePageInfo<TemplatePageListResponse> getPageList(@RequestBody @Validated FeeTemplatePageListRequest request);


//    @PostMapping("create")
//    List<FeeTemplate> create(@RequestBody @Validated FeeTemplateConfigureReq feeTemplateConfigureReq);

//    @PostMapping("del")
//    FeeTemplate del(@RequestBody @Validated FeeTemplateDelReq feeTemplateDelReq);

//    @PostMapping("modify")
//    List<FeeTemplate> modify(@RequestBody @Validated FeeTemplateConfigureReq feeTemplateConfigureReq);


    /**
     * 组合修改模板
     *
     * @param req request body
     * @return List<FeeTemplate>
     */
    @PostMapping("modifySynthetically")
    void modifySynthetically(@RequestBody @Validated FeeTemplateModifySyntheticallyReq req);

    @PostMapping("lock")
    void lock(@RequestBody @Validated FeeTemplateLockReq feeTemplateLockReq);

    @PostMapping("queryService")
    @Deprecated
    SimplePageInfo<FeeTemplate> queryService(@RequestBody @Validated FeeTemplateQueryByConditionReq feeTemplateQueryByConditionReq);

    @GetMapping("duplicateValidate")
    boolean duplicateValidate(@RequestParam("serveId") Long serveId, @RequestParam("sceneCode") String sceneCode);

    @PostMapping("copyPricingAttribute")
    CopyPricingAttributeResp copyPricingAttribute(@RequestBody @Valid CopyPricingAttributeReq req);

    @PostMapping("getTemplateBySkuNo")
    List<GetTemplateBySkuNoResp> getTemplateBySkuNo(@RequestBody @Valid GetTemplateBySkuNoReq req);

    @PostMapping("getServiceCategoryIds")
    Set<Long> getServiceCategoryIds(@RequestBody @Valid GetServiceCategoryIdsReq req);

}
