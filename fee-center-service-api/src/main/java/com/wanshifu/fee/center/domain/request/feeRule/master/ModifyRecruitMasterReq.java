package com.wanshifu.fee.center.domain.request.feeRule.master;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("修改招募师傅")
public class ModifyRecruitMasterReq {

    @ApiModelProperty(value = "师傅Id", required = true)
    @NotBlank(message = "师傅Id不能为空")
    private String masterId;

    @ApiModelProperty(value = "招募活动Id", required = true)
    @NotBlank(message = "招募活动Id不能为空")
    private String recruitId;

    @ApiModelProperty(value = "费用规则价格信息", required = true)
    @NotEmpty(message = "费用规则价格信息不能为空")
    private List<PriceInfo> priceInfos;

    @Data
    public static class PriceInfo {

        @ApiModelProperty(value = "模板id", required = true)
        @NotNull(message = "模板id不能为空")
        private Long templateId;

        @ApiModelProperty(value = "价格", required = true)
        @NotNull(message = "价格不能为空")
        private BigDecimal price;

        @ApiModelProperty(value = "街道id", required = true)
        @NotEmpty(message = "街道id不能为空")
        private List<String> streetIds;
    }
}
