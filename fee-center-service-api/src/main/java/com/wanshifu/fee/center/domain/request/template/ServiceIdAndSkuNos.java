package com.wanshifu.fee.center.domain.request.template;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ServiceIdAndSkuNos {

    @ApiModelProperty(value = "服务Id", required = true)
    @NotNull(message = "服务Id不能为空")
    private Long serviceId;

    @ApiModelProperty(value = "skuNo", required = true)
    @NotEmpty(message = "skuNos不能为空")
    private List<String> skuNos;
}
