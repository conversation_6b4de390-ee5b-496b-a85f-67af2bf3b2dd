package com.wanshifu.fee.center.domain.response.feerule;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "批量添加计价规则response body")
@Data
public class BatchAddRulesResponse {

    @ApiModelProperty(value = "规则id")
    private Long feeRuleId;

    @ApiModelProperty(value = "师傅id")
    private String masterId;

    @ApiModelProperty(value = "街道id")
    private String level4DivisionId;

    @ApiModelProperty(value = "服务类目id")
    private String serviceCategoryId;
}
