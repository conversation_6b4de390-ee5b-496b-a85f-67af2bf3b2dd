package com.wanshifu.fee.center.domain.document;

import com.wanshifu.fee.center.domain.enums.RecruitActivityMasterBuildTypeEnum;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * @author: <PERSON>
 * @create: 2024-08-23 14:06
 * @description: 创建师傅招募活动状态
 */
@Data
@Document
@FieldNameConstants
// 复合唯一索引
@CompoundIndex(name = "masterId_recruitId_unique_idx", def = "{'masterId': 1, 'recruitId': 1}", unique = true)
public class RecruitActivityMasterCreateStatus extends BaseDocument {

    /**
     * 师傅id
     */
    private String masterId;

    /**
     * 招募id
     */
    private String recruitId;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 构建类型
     * @see RecruitActivityMasterBuildTypeEnum
     */
    private String buildType;

    /**
     * 审核状态
     * @see com.wanshifu.fee.center.domain.enums.RecruitActivityMasterCreateStatusEnum
     */
    private String auditStatus;

    /**
     * 失败原因
     */
    private String failReason;

}
