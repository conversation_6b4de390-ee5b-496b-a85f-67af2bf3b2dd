package com.wanshifu.fee.center.domain.request.feeRule;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * @author: <PERSON>
 * @create: 2024-05-07 17:14
 * @description: 根据业务Id查询、删除response body
 */
@Data
@ApiModel("根据业务Id查询、删除response body")
@NoArgsConstructor
@AllArgsConstructor
public class QueryByBizIdResp {

    @ApiModelProperty(value = "虚拟id，为了前端多选之间互斥", required = true)
    private String id;

    @ApiModelProperty(value = "场景code", example = "contract_master", required = true)
    private String sceneCode;

    @ApiModelProperty(value = "业务Id", required = true)
    private String bizId;

    @ApiModelProperty(value = "业务标签", required = true)
    private String bizTag;

    @ApiModelProperty(value = "价格地区维度", required = true)
    private String divisionType;

    @ApiModelProperty(value = "价格地区维度名称", required = true)
    private String divisionTypeName;

    @ApiModelProperty(value = "服务Id", required = true)
    private String serviceId;

    @ApiModelProperty(value = "服务名称", required = true)
    private String serviceName;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        QueryByBizIdResp myObject = (QueryByBizIdResp) o;
        return Objects.equals(sceneCode, myObject.sceneCode) &&
                Objects.equals(bizId, myObject.bizId) &&
                Objects.equals(bizTag, myObject.bizTag) &&
                Objects.equals(serviceId, myObject.serviceId) &&
                Objects.equals(divisionType, myObject.divisionType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(sceneCode, bizId, bizTag, divisionType);
    }

}
