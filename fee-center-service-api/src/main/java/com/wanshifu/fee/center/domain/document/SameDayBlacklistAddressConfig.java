package com.wanshifu.fee.center.domain.document;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Set;

@EqualsAndHashCode(callSuper = true)
@Document
@Data
@FieldNameConstants
@CompoundIndex(def = "{'level1GoodsCategoryId': 1, 'districtId': 1}", unique = true)
public class SameDayBlacklistAddressConfig extends BaseDocument {

    @Indexed(unique = true)
    @J<PERSON>NField(serializeUsing = ToStringSerializer.class)
    private Long configId;

    private Long level1GoodsCategoryId;

    private String level1GoodsCategoryName;

    private String districtId;

    private String districtName;

    private Long serviceTypeId;

    private Set<String> keywords;

}
