package com.wanshifu.fee.center.domain.document;


import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;


/**
 * 导入任务
 */
@Document
@Data
@FieldNameConstants
public class SceneInfo extends BaseDocument {

    @Indexed(unique = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long sceneId;

    @Indexed(background = true)
    private String sceneName;

    @Indexed(unique = true)
    private String sceneCode;
    // 对接系统
    @Indexed(background = true)
    private List<String> integrationSys;

    // 适用业务
    @Indexed(background = true)
    private String applicableBiz;

    // 企业标准一口价、企业报价招标
    @Indexed(background = true)
    private String skuType;
    // 价格类型
    private String priceType;
    // 业务id类型
    private String bizIdType;
    //
    private List<String> orderRange;

    // 金额类型
    private String amountType;

    // 价格维护类型
    private Integer priceMaintain;

    private Boolean haveBasePrice;

    private String note;

    // 是否映射计价
    private Boolean isMappingPricing;

    private Boolean needSearchParent;

    private String divisionType;

    // 是否启用
    private Boolean enable = true;

    /**
     * 是否需要做 计价模板与计价规则的完整性校验
     */
    private Boolean needIntegrityValidation = true;

    private PriceUploadAlert priceUploadAlert;

    /**
     * 是否需要拉取大数据自定义SKU
     * on:开启
     * off:禁用
     */
    private String pullBigDataCustomSku;

    /**
     * 价格上传告警
     */
    @Data
    public static class PriceUploadAlert {
        /**
         * 价格上传是否告警
         */
        private Boolean enabled;

        /**
         * 价格上传告警 对比场景编码
         */
        private String comparisonSceneCode;

        /**
         * 大于对比价格的百分比
         */
        private Integer greaterThan;

        /**
         * 小于对比价格的百分比
         */
        private Integer lessThan;
    }
}
