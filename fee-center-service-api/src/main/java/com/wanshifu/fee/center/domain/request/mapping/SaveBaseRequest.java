package com.wanshifu.fee.center.domain.request.mapping;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.wanshifu.fee.center.domain.request.common.Create;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

@Data
@ApiModel("保存映射request body")
public class SaveBaseRequest {

    @ApiModelProperty(value = "模板id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    @NotNull(message = "模板id不能为空", groups = {Create.class})
    private Long templateId;

    @ApiModelProperty(value = "skuNo", required = true)
    @NotBlank(message = "skuNo不能为空")
    private String skuNo;

    @ApiModelProperty(value = "模板sku类型")
    private String templateSkuType;

    @ApiModelProperty(value = "服务id", required = true)
    @NotBlank(message = "serviceId不能为空")
    private String serviceId;

    @ApiModelProperty(value = "服务名称", required = true)
    @NotBlank(message = "serviceName不能为空")
    private String serviceName;

    @ApiModelProperty(value = "属性展示名称")
    private String attributeDisplayName;

    @ApiModelProperty(value = "计价数量pathNo", required = true)
    @NotBlank(message = "skuNumberPathNo不能为空")
    private String skuNumberPathNo;

    @ApiModelProperty(value = "场景编码", required = true)
    @NotBlank(message = "sceneCode不能为空")
    private String sceneCode;

    // 来源于服务品类
    @ApiModelProperty(value = "sku路径名", required = true)
    @NotBlank(message = "skuAttributePathName不能为空")
    private String skuAttributePathName;

    // 来源于服务品类
    @ApiModelProperty(value = "计价数量", required = true)
    @NotBlank(message = "计价数量不能为空")
    private String skuNumberName;

    @ApiModelProperty(value = "计费单位")
    private String feeUnit;

}
