package com.wanshifu.fee.center.api;

import com.wanshifu.fee.center.config.FeeCenterEncoder;
import com.wanshifu.fee.center.domain.enums.AsyncTaskStatusEnum;
import com.wanshifu.fee.center.domain.enums.AsyncTaskTypeEnum;
import com.wanshifu.spring.cloud.fegin.component.DefaultDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        value = "fee-center-service",
        path = "asyncTasksInfo",
        configuration = {DefaultDecoder.class, FeeCenterEncoder.class, DefaultErrorDecode.class},
        url = "${wanshifu.fee-center-service.url}")
public interface AsyncTasksInfoApi {

    /**
     * 根据任务ID查询任务状态
     *
     * @param taskId 任务ID
     * @return 状态
     * @see com.wanshifu.fee.center.domain.enums.AsyncTaskStatusEnum
     * @see com.wanshifu.fee.center.domain.enums.AsyncTaskTypeEnum
     */
    @GetMapping("getStatusByTaskId")
    AsyncTaskStatusEnum getStatusByTaskId(@RequestParam("taskId") String taskId, @RequestParam("taskType") AsyncTaskTypeEnum taskType);
}
