package com.wanshifu.fee.center.domain.request.template;

import com.wanshifu.fee.center.domain.dto.ServiceAttribute;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: <PERSON>
 * @create: 2024-04-16 11:09
 * @description: 根据SKU_NO获取模板信息Request body
 */
@Data
@ApiModel("根据SKU_NO获取模板信息Request body")
public class GetTemplateBySkuNoReq {

    @ApiModelProperty(value = "场景编码", required = true)
    @NotBlank(message = "场景编码不能为空")
    private String sceneCode;

    @ApiModelProperty(value = "费用类型")
    private String feeTypeTag;

    @ApiModelProperty(value = "服务信息", required = true)
    @NotEmpty(message = "服务信息不能为空")
    private List<ServiceInfo> serviceInfoList;

    @Data
    public static class ServiceInfo {

        @ApiModelProperty(value = "服务Id", required = true)
        @NotNull(message = "服务Id不能为空")
        private Long serviceId;

        @ApiModelProperty(value = "服务属性", required = true)
        @NotEmpty(message = "服务属性不能为空")
        private List<ServiceAttribute> rootAttributeDetailList;
    }

}
