package com.wanshifu.fee.center.domain.request;

import com.wanshifu.fee.center.domain.document.CalculateRuleData;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: <PERSON>
 * @create: 2023-09-08 17:13
 * @description: 计费规则（草稿）批量操作 request body
 */
@Data
public class FeeRuleDraftModifyReq {
    @NotNull
    private Long feeRuleDraftId;

    @NotNull
    private CalculateRuleData calculateRuleData;
}
