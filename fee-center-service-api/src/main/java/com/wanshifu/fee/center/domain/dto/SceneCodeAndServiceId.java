package com.wanshifu.fee.center.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SceneCodeAndServiceId {
    private String sceneCode;
    private String serviceId;

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        SceneCodeAndServiceId other = (SceneCodeAndServiceId) obj;
        return Objects.equals(sceneCode, other.sceneCode) && Objects.equals(serviceId, other.serviceId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(sceneCode, serviceId);
    }
}
