package com.wanshifu.fee.center.domain.document;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.Map;


@Document
@Data
@FieldNameConstants
public class FeeRule extends BaseDocument {

    @Indexed(unique = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long feeRuleId;
    @Indexed(background = true)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long templateId;
    // 在算费时需要进行组完整性校验
    @Indexed(background = true)
    private String group;
    private int templateVersion;
    @Indexed(background = true)
    private String sceneCode;
    private String sceneName;
    private String feeName;

    // 为了价格根据大小排序
    private Double masterInputPriceDouble;

    private List<DynamicFeeRuleCalculateRuleData> dynamicFeeRuleCalculateRuleDataList;

    @JSONField(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    private Map<String, String> bizRule;

    private CalculateRuleData calculateRuleData;


}
