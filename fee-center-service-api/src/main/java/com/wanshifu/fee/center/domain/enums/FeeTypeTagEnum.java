package com.wanshifu.fee.center.domain.enums;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum FeeTypeTagEnum {
    SERVICE_FEE("serviceFee", "服务费"),
    GOOD_SURCHARGE("goodSurcharge", "商品附加费"),
    STANDARD_SURCHARGE("standardSurcharge", "标准附加费"),
    ;
    public final String code;
    public final String name;

    public static FeeTypeTagEnum fromCode(String code) {
        for (FeeTypeTagEnum e : FeeTypeTagEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
}
