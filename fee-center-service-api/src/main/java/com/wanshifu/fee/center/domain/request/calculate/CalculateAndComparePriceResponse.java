package com.wanshifu.fee.center.domain.request.calculate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel("计价并比价返回参数")
@Data
@Document
public class CalculateAndComparePriceResponse {

    @ApiModelProperty(value = "计算&比价任务id")
    private Long calculateAndCompareTaskId;

    @ApiModelProperty(value = "销售价结果")
    private StandardPricingResponse salePriceResult;

    @ApiModelProperty(value = "服务价差列表")
    private List<ServicePriceDifference> servicePriceDifferenceList;

    @Indexed(name = "create_time_ttl_idx", background = true, expireAfterSeconds = 604800)
    @ApiModelProperty(value = "创建时间，7天后自动清除")
    private Date createTime = new Date();

    private String errorInfo;


    @Data
    public static class ServicePriceDifference {

        @ApiModelProperty(value = "服务id")
        private String serviceId;

        @ApiModelProperty(value = "服务价格差")
        private BigDecimal priceDifference;

        @ApiModelProperty(value = "无服务价格差原因")
        private String priceDifferenceAbsenceReason;
    }
}


